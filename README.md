# Investor App Platform

A comprehensive platform for investment management, asset tokenization, and client relationship management.

## Overview

The Investor App Platform is a modern web application designed to bridge traditional financial advisory services with DeFi capabilities. It provides a sophisticated interface for investment management, asset tokenization, and client relationship management.

## Documentation

- [Frontend Documentation](FRONTEND.md)
- [Backend Documentation](BACKEND.md)
- [Database Documentation](DATABASE.md)
- [Architecture Documentation](ARCHITECTURE.md)

## Features

### User Authentication and Profile Management

- JWT-based authentication with token refresh mechanism
- Session management with "Trust this device for 24 hours" option
- Profile customization with image upload via Cloudinary
- Role-based access control (Manager/Client)

### Dashboard

- Asset portfolio overview with balance history visualization
- Recent transactions and activity tracking
- Performance metrics and analytics
- Role-specific dashboard views

### Asset Tokenization

- Digital asset creation and management
- Token issuance and transfer capabilities
- Supporting document upload and management
- Blockchain transaction tracking

### Client Management (Manager Role)

- Client onboarding and profile management
- Client-manager relationship controls
- Client portfolio overview
- Communication tools

### KYC Verification

- Live selfie capture with device camera
- Document upload and verification
- Real-time face detection
- OCR for document data extraction

### Notifications System

- Real-time notification display
- Status management (read/unread)
- Action-based notifications (approvals, rejections)
- Auto-marking of non-action notifications as viewed

### Messaging

- Client-manager communication
- Broadcast capabilities for managers
- Attachment support via Cloudinary
- Sensitive information detection

### Vesting Management

- Vesting contract creation and management
- Beneficiary management
- Schedule visualization
- Contract status tracking

### Reports and Analytics

- Asset performance tracking
- Balance history visualization
- Data export capabilities
- Custom report generation

## Technology Stack

### Frontend

- **Vue.js 3**: Chosen for its Composition API, which provides better TypeScript integration, code organization, and reusability compared to the Options API.
- **Vite**: Selected as the build tool for its fast development server and optimized production builds.
- **Pinia**: Used for state management, replacing Vuex for its TypeScript support, simpler API, and better developer experience.
- **Vue Router**: Handles navigation with features like route guards for authentication.
- **Bootstrap 5**: Provides responsive UI components with customization for financial/DeFi aesthetics.
- **SCSS**: Used for custom styling with variables, mixins, and nested rules.
- **ApexCharts**: Chosen for data visualization due to its rich feature set for financial charts.

### Backend

- **Node.js**: Chosen for its non-blocking I/O model, which is ideal for handling multiple concurrent requests in a financial application.
- **Express.js**: Selected as the web framework for its simplicity, flexibility, and robust middleware ecosystem.
- **PostgreSQL**: Used as the primary database for its ACID compliance, JSON support, and reliability for financial data.
- **Sequelize**: ORM for database interaction, providing a clean API for database operations and migrations.
- **JSON Web Tokens (JWT)**: Implemented for secure, stateless authentication.
- **Cloudinary**: Used for media storage and management.
- **Winston**: Provides structured logging for monitoring and debugging.

## Architecture

The application follows a modern client-server architecture with a clear separation of concerns:

```
┌─────────────────┐      ┌─────────────────┐      ┌─────────────────┐
│                 │      │                 │      │                 │
│  Vue.js         │      │  Express.js     │      │  PostgreSQL     │
│  Frontend       │◄────►│  Backend API    │◄────►│  Database       │
│                 │      │                 │      │                 │
└─────────────────┘      └─────────────────┘      └─────────────────┘
        ▲                        ▲                        ▲
        │                        │                        │
        ▼                        ▼                        ▼
┌─────────────────┐      ┌─────────────────┐      ┌─────────────────┐
│                 │      │                 │      │                 │
│  Cloudinary     │      │  Blockchain     │      │  External       │
│  Media Storage  │      │  Integration    │      │  Services       │
│                 │      │                 │      │                 │
└─────────────────┘      └─────────────────┘      └─────────────────┘
```

## Getting Started

### Prerequisites

- Node.js (v18 or later)
- PostgreSQL database
- Cloudinary account
- Ethereum wallet (for blockchain integration)

### Frontend Setup

1. Navigate to the frontend directory:
   ```bash
   cd investor-app-frontend
   ```

2. Install dependencies:
   ```bash
   npm install
   ```

3. Create a `.env` file with the following variables:
   ```
   VITE_API_URL=http://localhost:3000
   VITE_CLOUDINARY_CLOUD_NAME=your_cloud_name
   VITE_CLOUDINARY_UPLOAD_PRESET=your_upload_preset
   ```

4. Start the development server:
   ```bash
   npm run dev
   ```

### Backend Setup

1. Navigate to the backend directory:
   ```bash
   cd investor-app-backend
   ```

2. Install dependencies:
   ```bash
   npm install
   ```

3. Create a `.env` file with the following variables:
   ```
   PORT=3000
   DATABASE_URL=postgres://username:password@localhost:5432/investor_app
   JWT_SECRET=your_jwt_secret
   CLOUDINARY_CLOUD_NAME=your_cloud_name
   CLOUDINARY_API_KEY=your_api_key
   CLOUDINARY_API_SECRET=your_api_secret
   CORS_ALLOWED_ORIGIN=http://localhost:5173
   ```

4. Start the development server:
   ```bash
   npm run dev
   ```

## Development

### Code Structure

#### Frontend Structure

```
src/
├── assets/          # Static assets
├── components/      # Reusable Vue components
├── directives/      # Custom Vue directives
├── router/          # Vue Router configuration
├── stores/          # Pinia stores for state management
├── services/        # API and external service integrations
├── utils/           # Utility functions and helpers
└── views/           # Page components
```

#### Backend Structure

```
src/
├── cloudinary/       # Cloudinary configuration
├── controllers/      # Request handlers
├── middlewares/      # Express middlewares
├── migrations/       # Database migrations
├── routes/           # API route definitions
├── services/         # Business logic services
├── setup/            # Database and model setup
├── utils/            # Utility functions
└── server.js         # Main application entry point
```

### Testing

#### Frontend Testing

```bash
# Run unit tests
npm run test:unit

# Run end-to-end tests
npm run test:e2e
```

#### Backend Testing

```bash
# Run tests
npm test
```

## Deployment

### Frontend Deployment

The frontend is configured for deployment to Vercel:

```bash
# Build for production
npm run build

# Deploy to Vercel
vercel
```

### Backend Deployment

The backend is configured for deployment to Vercel:

```bash
# Deploy to Vercel
vercel
```

## Contributing

1. Fork the repository
2. Create a feature branch: `git checkout -b feature/my-feature`
3. Commit your changes: `git commit -am 'Add my feature'`
4. Push to the branch: `git push origin feature/my-feature`
5. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Acknowledgements

- [Vue.js](https://vuejs.org/)
- [Express.js](https://expressjs.com/)
- [PostgreSQL](https://www.postgresql.org/)
- [Cloudinary](https://cloudinary.com/)
- [ApexCharts](https://apexcharts.com/)
- [Bootstrap](https://getbootstrap.com/)
