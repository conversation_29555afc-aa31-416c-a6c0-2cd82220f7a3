DATABASE_URL=
JWT_SECRET=
CORS_ALLOWED_ORIGIN=http://localhost:5173
EMAIL_USER=<EMAIL>
EMAIL_PASS=your-email-password
FRONTEND_URL=
STRIPE_SECRET_KEY=your_stripe_secret_key
STRIPE_PUBLIC_KEY=your_stripe_public_key
FACTOR_STUDIO_API_URL=https://api.factor.studio
ETH_RPC_URL=<your_rpc_endpoint>   # Ethereum testnet/mainnet RPC URL
WALLET_PRIVATE_KEY=<your_private_key>   # Private key of the signing wallet
DATABASE_URL_TEST=postgres://your_user:your_password@localhost:5432/your_test_db
JWT_SECRET_TEST=testsecret
CLOUDINARY_CLOUD_NAME=your_cloud_name
CLOUDINARY_API_KEY=your_api_key
CLOUDINARY_API_SECRET=your_api_secret

# Server Configuration
PORT=3000
NODE_ENV=development

# Database Configuration
DB_HOST=localhost
DB_PORT=5432
DB_NAME=investor_app
DB_USER=postgres
DB_PASSWORD=your_password

# JWT Configuration
JWT_EXPIRES_IN=24h

# Rate Limiting
RATE_LIMIT_MAX=100

# SMTP Configuration
SMTP_HOST=smtp.example.com
SMTP_PORT=465
SMTP_USER=your_smtp_user
SMTP_PASS=your_smtp_password
SMTP_FROM=<EMAIL>

# Logging
LOG_LEVEL=info