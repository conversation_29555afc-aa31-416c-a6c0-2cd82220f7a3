# Investor App Architecture Documentation

## Overview

The Investor App is a comprehensive platform designed for investment management, asset tokenization, and client relationship management. This document provides a detailed overview of the system architecture, design decisions, and implementation strategies.

## Table of Contents

- [System Architecture](#system-architecture)
- [Technology Stack](#technology-stack)
- [Design Principles](#design-principles)
- [Component Architecture](#component-architecture)
- [Data Flow](#data-flow)
- [Security Architecture](#security-architecture)
- [Scalability and Performance](#scalability-and-performance)
- [Integration Points](#integration-points)
- [Deployment Architecture](#deployment-architecture)
- [Development Workflow](#development-workflow)

## System Architecture

The Investor App follows a modern client-server architecture with a clear separation of concerns:

### High-Level Architecture

```
┌─────────────────┐      ┌─────────────────┐      ┌─────────────────┐
│                 │      │                 │      │                 │
│  Vue.js         │      │  Express.js     │      │  PostgreSQL     │
│  Frontend       │◄────►│  Backend API    │◄────►│  Database       │
│                 │      │                 │      │                 │
└─────────────────┘      └─────────────────┘      └─────────────────┘
        ▲                        ▲                        ▲
        │                        │                        │
        ▼                        ▼                        ▼
┌─────────────────┐      ┌─────────────────┐      ┌─────────────────┐
│                 │      │                 │      │                 │
│  Cloudinary     │      │  Blockchain     │      │  External       │
│  Media Storage  │      │  Integration    │      │  Services       │
│                 │      │                 │      │                 │
└─────────────────┘      └─────────────────┘      └─────────────────┘
```

### Key Components

1. **Frontend Application**: Vue.js 3 single-page application
2. **Backend API**: Express.js RESTful API
3. **Database**: PostgreSQL relational database
4. **Media Storage**: Cloudinary for image and document storage
5. **Blockchain Integration**: Ethereum-based asset tokenization
6. **External Services**: Email, KYC verification, etc.

### Communication Patterns

1. **Client-Server**: RESTful API communication
2. **Real-time Updates**: WebSocket for notifications (planned)
3. **Event-Driven**: Internal event system for decoupled components
4. **Asynchronous Processing**: Background jobs for long-running tasks

## Technology Stack

### Frontend Stack

- **Framework**: Vue.js 3 with Composition API
- **Build Tool**: Vite for fast development and optimized builds
- **State Management**: Pinia for reactive state management
- **Routing**: Vue Router for client-side routing
- **UI Framework**: Bootstrap 5 with custom SCSS
- **HTTP Client**: Axios for API communication
- **Charting**: ApexCharts for data visualization
- **Form Validation**: Vuelidate for form validation
- **Testing**: Vitest and Cypress for testing

### Backend Stack

- **Runtime**: Node.js for JavaScript runtime
- **Framework**: Express.js for API development
- **Database**: PostgreSQL for relational data storage
- **ORM**: Sequelize for database interaction
- **Authentication**: JWT for stateless authentication
- **File Storage**: Cloudinary for media storage
- **Logging**: Winston for structured logging
- **Task Scheduling**: node-cron for scheduled tasks
- **Testing**: Jest for unit and integration testing

### Infrastructure

- **Hosting**: Vercel for frontend and backend deployment
- **Database**: Neon Serverless Postgres
- **Media Storage**: Cloudinary
- **CI/CD**: GitHub Actions for continuous integration
- **Monitoring**: Custom monitoring solution
- **Version Control**: Git with GitHub

## Design Principles

The application follows these core design principles:

### 1. Separation of Concerns

- Clear separation between frontend and backend
- Modular architecture with focused components
- Business logic isolated in service layers

### 2. RESTful API Design

- Resource-oriented API endpoints
- Consistent request/response formats
- Proper HTTP method usage
- Stateless communication

### 3. Security by Design

- Authentication and authorization at all levels
- Input validation and sanitization
- Secure data storage and transmission
- Protection against common vulnerabilities

### 4. Responsive and Accessible UI

- Mobile-first responsive design
- Accessibility compliance
- Consistent user experience
- Progressive enhancement

### 5. Scalability and Performance

- Efficient database queries
- Optimized frontend assets
- Caching strategies
- Asynchronous processing for heavy tasks

### 6. Maintainability

- Consistent coding standards
- Comprehensive documentation
- Automated testing
- Clear dependency management

## Component Architecture

### Frontend Component Architecture

The frontend follows a component-based architecture:

```
src/
├── assets/          # Static assets
├── components/      # Reusable Vue components
│   ├── common/      # Shared UI components
│   ├── dashboard/   # Dashboard-specific components
│   ├── kyc/         # KYC verification components
│   └── ...
├── directives/      # Custom Vue directives
├── router/          # Vue Router configuration
├── stores/          # Pinia stores for state management
├── services/        # API and external service integrations
├── utils/           # Utility functions and helpers
└── views/           # Page components
```

#### Component Hierarchy

- **App.vue**: Root component
  - **NavBar.vue**: Navigation component
    - **ProfileTile.vue**: User profile display
    - **NotificationsDropdown.vue**: Notifications menu
    - **SessionExtensionButton.vue**: Session management
  - **RouterView**: Page content
    - **Dashboard.vue**: Main dashboard
      - **BalanceHistoryTile.vue**: Balance visualization
      - **AssetSummaryTile.vue**: Asset summary
      - **RecentActivityTile.vue**: Recent activity
    - **TokenisedAssets.vue**: Asset management
      - **TokeniseAssetModal.vue**: Asset creation
      - **AssetDetailModal.vue**: Asset details
    - **UserProfile.vue**: Profile management
      - **ProfileForm.vue**: Profile editing
      - **PasswordChangeForm.vue**: Password management
    - **ClientsPage.vue**: Client management
      - **ClientListTable.vue**: Client listing
      - **ClientDetailModal.vue**: Client details

### Backend Component Architecture

The backend follows a modular architecture:

```
src/
├── cloudinary/       # Cloudinary configuration
├── controllers/      # Request handlers
├── middlewares/      # Express middlewares
├── migrations/       # Database migrations
├── routes/           # API route definitions
├── services/         # Business logic services
├── setup/            # Database and model setup
├── utils/            # Utility functions
└── server.js         # Main application entry point
```

#### Module Organization

- **Routes**: Define API endpoints and request handling
- **Controllers**: Handle request processing and response formatting
- **Services**: Implement business logic and data operations
- **Models**: Define database schema and relationships
- **Middlewares**: Implement cross-cutting concerns
- **Utils**: Provide utility functions and helpers

## Data Flow

### Authentication Flow

```
┌─────────┐     ┌─────────────┐     ┌─────────────┐     ┌─────────────┐
│         │     │             │     │             │     │             │
│  User   │────►│  Frontend   │────►│  Backend    │────►│  Database   │
│         │     │             │     │             │     │             │
└─────────┘     └─────────────┘     └─────────────┘     └─────────────┘
     │                 ▲                   │                   │
     │                 │                   │                   │
     │                 │                   ▼                   │
     │                 │            ┌─────────────┐           │
     │                 │            │             │           │
     └────────────────┴────────────│  JWT Token  │◄──────────┘
                                   │             │
                                   └─────────────┘
```

1. User submits login credentials
2. Frontend sends credentials to backend
3. Backend validates credentials against database
4. Backend generates JWT token with user information
5. Token is returned to frontend and stored
6. Frontend includes token in subsequent requests

### Asset Tokenization Flow

```
┌─────────┐     ┌─────────────┐     ┌─────────────┐     ┌─────────────┐
│         │     │             │     │             │     │             │
│  User   │────►│  Frontend   │────►│  Backend    │────►│  Database   │
│         │     │             │     │             │     │             │
└─────────┘     └─────────────┘     └─────────────┘     └─────────────┘
                                           │
                                           ▼
                                   ┌─────────────┐     ┌─────────────┐
                                   │             │     │             │
                                   │ Cloudinary  │     │ Blockchain  │
                                   │             │     │             │
                                   └─────────────┘     └─────────────┘
```

1. User submits asset information and documents
2. Frontend sends data to backend
3. Backend uploads documents to Cloudinary
4. Backend creates blockchain transaction for asset
5. Backend stores asset information in database
6. Backend returns asset details to frontend

### Notification Flow

```
┌─────────────┐     ┌─────────────┐     ┌─────────────┐
│             │     │             │     │             │
│  Event      │────►│  Backend    │────►│  Database   │
│  Source     │     │             │     │             │
└─────────────┘     └─────────────┘     └─────────────┘
                           │
                           ▼
                   ┌─────────────┐     ┌─────────────┐
                   │             │     │             │
                   │  Frontend   │◄────│  User       │
                   │             │     │             │
                   └─────────────┘     └─────────────┘
```

1. Event occurs (asset approval, client link, etc.)
2. Backend creates notification in database
3. User fetches notifications on login or refresh
4. Frontend displays notifications to user
5. User interacts with notification
6. Frontend updates notification status via backend

## Security Architecture

The application implements a comprehensive security architecture:

### Authentication and Authorization

- **JWT-based Authentication**: Secure token-based authentication
- **Role-based Access Control**: Different permissions for managers and clients
- **Session Management**: Secure session handling with expiration
- **Password Security**: Secure password hashing with bcrypt

### Data Security

- **HTTPS**: Secure communication with TLS/SSL
- **Input Validation**: Validation of all user inputs
- **Output Encoding**: Prevention of XSS attacks
- **SQL Injection Protection**: Parameterized queries
- **CSRF Protection**: Protection against cross-site request forgery

### API Security

- **Rate Limiting**: Protection against brute force attacks
- **CORS Configuration**: Controlled cross-origin access
- **Security Headers**: Protection against common web vulnerabilities
- **Request Validation**: Validation of all API requests

### Infrastructure Security

- **Database Security**: Secure database configuration
- **Environment Isolation**: Separation of development and production
- **Secret Management**: Secure handling of secrets and credentials
- **Regular Updates**: Security patches and updates

## Scalability and Performance

The application is designed for scalability and performance:

### Frontend Performance

- **Code Splitting**: Lazy loading of components and routes
- **Asset Optimization**: Minification and compression of assets
- **Caching**: Proper caching of static assets
- **Lazy Loading**: On-demand loading of resources
- **Virtual Scrolling**: Efficient rendering of large lists

### Backend Scalability

- **Stateless Design**: Enables horizontal scaling
- **Connection Pooling**: Efficient database connection management
- **Query Optimization**: Efficient database queries
- **Caching**: Caching of frequently accessed data
- **Asynchronous Processing**: Non-blocking I/O for better concurrency

### Database Performance

- **Indexing**: Proper indexing for query performance
- **Query Optimization**: Efficient query design
- **Connection Management**: Optimized for serverless Postgres
- **Data Partitioning**: Partitioning for large tables
- **Monitoring**: Performance monitoring and optimization

## Integration Points

The application integrates with various external systems:

### Cloudinary Integration

- **Purpose**: Storage of profile images, documents, and attachments
- **Integration Method**: Cloudinary SDK
- **Data Flow**: Upload and retrieval of media files

### Blockchain Integration

- **Purpose**: Asset tokenization and verification
- **Integration Method**: Ethereum Web3.js library
- **Data Flow**: Creation and verification of blockchain transactions

### Email Integration

- **Purpose**: User notifications and communication
- **Integration Method**: SMTP service
- **Data Flow**: Sending of emails for various events

### KYC Verification

- **Purpose**: User identity verification
- **Integration Method**: Custom verification service
- **Data Flow**: Submission and verification of identity documents

## Deployment Architecture

The application uses a modern deployment architecture:

### Frontend Deployment

- **Platform**: Vercel for static site hosting
- **Build Process**: Vite build with optimization
- **Deployment Strategy**: Continuous deployment from Git
- **Environment Configuration**: Environment-specific builds

### Backend Deployment

- **Platform**: Vercel for serverless functions
- **Build Process**: Node.js build with optimization
- **Deployment Strategy**: Continuous deployment from Git
- **Environment Configuration**: Environment variables

### Database Deployment

- **Platform**: Neon Serverless Postgres
- **Deployment Strategy**: Managed database service
- **Scaling**: Automatic scaling based on demand
- **Backup**: Automated backup and recovery

## Development Workflow

The project follows a structured development workflow:

### Version Control

- **System**: Git with GitHub
- **Branching Strategy**: Feature branches with pull requests
- **Code Review**: Required reviews before merging
- **Commit Standards**: Conventional commits format

### Continuous Integration

- **Platform**: GitHub Actions
- **Checks**: Linting, testing, and build verification
- **Automation**: Automated testing on pull requests
- **Quality Gates**: Required passing checks before merging

### Development Environment

- **Local Setup**: Docker-based development environment
- **Hot Reloading**: Fast feedback during development
- **Environment Parity**: Development environment matches production
- **Tooling**: Consistent tooling across team members

### Testing Strategy

- **Unit Testing**: Testing of individual components
- **Integration Testing**: Testing of component interactions
- **End-to-End Testing**: Testing of complete user flows
- **Automated Testing**: Continuous testing in CI pipeline

---

This documentation provides a comprehensive overview of the Investor App architecture. For specific implementation details, refer to the codebase and component documentation.
