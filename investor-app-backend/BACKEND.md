# Investor App Backend Documentation

## Overview

The Investor App Backend is a robust Node.js/Express.js application that provides a comprehensive API for investment management, asset tokenization, and client relationship management. This documentation provides a detailed guide to the backend architecture, components, and implementation strategies.

## Table of Contents

- [Technology Stack](#technology-stack)
- [Architecture](#architecture)
- [Key Features](#key-features)
- [API Endpoints](#api-endpoints)
- [Database Schema](#database-schema)
- [Authentication and Authorization](#authentication-and-authorization)
- [Services](#services)
- [Middleware](#middleware)
- [Error Handling](#error-handling)
- [Security](#security)
- [Performance Optimization](#performance-optimization)
- [Testing](#testing)
- [Deployment](#deployment)
- [Best Practices](#best-practices)

## Technology Stack

### Core Technologies

- **Node.js**: Chosen for its non-blocking I/O model, which is ideal for handling multiple concurrent requests in a financial application.
- **Express.js**: Selected as the web framework for its simplicity, flexibility, and robust middleware ecosystem.
- **PostgreSQL**: Used as the primary database for its ACID compliance, JSON support, and reliability for financial data.
- **Sequelize**: ORM for database interaction, providing a clean API for database operations and migrations.
- **JSON Web Tokens (JWT)**: Implemented for secure, stateless authentication.

### Additional Libraries

- **bcryptjs**: Used for secure password hashing.
- **helmet**: Provides security headers to protect against common web vulnerabilities.
- **cors**: Manages Cross-Origin Resource Sharing for API access.
- **express-rate-limit**: Prevents abuse through rate limiting.
- **node-cron**: Handles scheduled tasks like balance updates and notifications.
- **cloudinary**: Manages file uploads for profile images and documents.
- **winston**: Provides structured logging for monitoring and debugging.

## Architecture

The backend follows a modular architecture organized by feature and responsibility:

```
src/
├── cloudinary/       # Cloudinary configuration
├── controllers/      # Request handlers
├── middlewares/      # Express middlewares
├── migrations/       # Database migrations
├── routes/           # API route definitions
├── services/         # Business logic services
├── setup/            # Database and model setup
├── utils/            # Utility functions
└── server.js         # Main application entry point
```

### Design Decisions

- **Modular Structure**: The application is organized by feature to improve maintainability and scalability.
- **Service Layer**: Business logic is encapsulated in service modules to separate concerns.
- **Repository Pattern**: Database access is abstracted through models and repositories.
- **Middleware Approach**: Common functionality like authentication is implemented as middleware.
- **Environment Configuration**: Configuration is managed through environment variables.

## Key Features

### Authentication and User Management

- JWT-based authentication with token refresh mechanism
- Role-based access control (Admin, Manager, Client)
- Secure password hashing and validation
- Magic link authentication option
- Session management with 24-hour expiration

### Profile Management

- User profile creation and updates
- Profile image upload with Cloudinary integration
- Multi-currency support for account balances

### Asset Tokenization

- Digital asset creation and management
- Token issuance and transfer capabilities
- Real-time asset valuation
- Blockchain transaction tracking
- Supporting document management

### Client Management

- Client onboarding and profile management
- Client-manager relationship management
- Investment tracking and reporting

### Notifications System

- Real-time notifications
- Email notifications for important events
- Customizable notification preferences
- Auto-marking of non-action notifications

### KYC Verification

- Document verification workflow
- Identity verification
- Status tracking and management

### Vesting Management

- Vesting contract creation and management
- Beneficiary management
- Schedule tracking
- Contract status updates

### Reporting and Analytics

- Asset performance tracking
- Balance history recording
- Data export capabilities
- Custom report generation

### Messaging System

- Client-manager communication
- Broadcast messaging capabilities
- Attachment handling
- Sensitive information detection

## API Endpoints

The API is organized into logical groups by feature:

### Authentication

- `POST /auth/register` - User registration
- `POST /auth/login` - User login
- `POST /auth/refresh-token` - Refresh JWT token
- `POST /auth/magic-link` - Request magic link authentication
- `POST /auth/verify-magic-link` - Verify magic link
- `POST /auth/logout` - User logout
- `GET /auth/check-session` - Validate session
- `POST /auth/extend-session` - Extend session validity

### Profile

- `GET /profile` - Get user profile
- `PUT /profile` - Update user profile
- `POST /profile/upload-image` - Upload profile image
- `PUT /profile/change-password` - Change password

### Asset Tokenization

- `POST /tokenisation/create` - Create new token
- `GET /tokenisation/assets` - List tokenized assets
- `PUT /tokenisation/transfer` - Transfer tokens
- `GET /tokenisation/history` - View transaction history
- `PUT /tokenisation/approve/:id` - Approve a proposed asset

### Client Management

- `GET /clients` - List clients
- `POST /clients/link` - Request to link a client
- `GET /clients/search` - Search clients
- `GET /clients/:id` - Get client details

### Notifications

- `GET /notifications` - List notifications
- `PUT /notifications/:id` - Update notification status
- `POST /notifications/mark-read` - Mark multiple notifications as read

### KYC Verification

- `POST /kyc/upload-selfie` - Upload selfie for verification
- `POST /kyc/upload-passport` - Upload passport for verification
- `GET /kyc/status` - Check verification status
- `POST /kyc/verify` - Submit verification request

### Vesting

- `POST /vesting/create` - Create vesting contract
- `GET /vesting/contracts` - List vesting contracts
- `GET /vesting/contracts/:id` - Get contract details
- `PUT /vesting/contracts/:id` - Update contract status

### Reports

- `GET /reports/assets` - Get asset reports
- `GET /reports/statistics` - Get asset statistics
- `GET /reports/balance-history` - Get balance history
- `POST /reports/export` - Export reports

### Messages

- `POST /messages` - Send message
- `GET /messages` - List messages
- `GET /messages/:id` - Get message details
- `PUT /messages/:id` - Update message status
- `POST /messages/attachments` - Upload message attachment

## Database Schema

The database schema is designed to support all application features with proper relationships and constraints:

### Core Tables

- **institutional_investors**: Stores user information
  - `id`: Primary key
  - `role`: User role (admin, manager, client)
  - `manager_id`: Reference to manager (for clients)
  - `company_name`: Company name
  - `contact_name`: Contact person name
  - `email`: Email address (unique)
  - `password`: Hashed password
  - `tax_id`: Tax identification
  - `address`: Physical address
  - `phone`: Contact phone
  - `profile_image`: Profile image URL
  - `created_at`: Creation timestamp

- **tokenised_assets**: Stores asset information
  - `id`: Primary key
  - `name`: Asset name
  - `type`: Asset type
  - `value`: Asset value (numeric)
  - `metadata`: Additional asset details (JSONB)
  - `supporting_documents`: Document URLs (array)
  - `user_id`: Owner reference
  - `blockchain_tx_hash`: Blockchain transaction hash
  - `created_at`: Creation timestamp

- **asset_approvals**: Tracks asset approval status
  - `id`: Primary key
  - `asset_id`: Reference to asset
  - `status`: Approval status
  - `blockchain_tx_hash`: Blockchain transaction hash
  - `created_at`: Creation timestamp

- **notifications**: Stores user notifications
  - `id`: Primary key
  - `user_id`: Reference to user
  - `type`: Notification type
  - `message`: Notification message
  - `status`: Status (pending, read, ignored)
  - `metadata`: Additional details (JSONB)
  - `created_at`: Creation timestamp
  - `updated_at`: Update timestamp

- **vesting_records**: Stores vesting contract information
  - `id`: Primary key
  - `asset_id`: Reference to asset
  - `contract_address`: Blockchain contract address
  - `vesting_schedule`: Schedule details (JSONB)
  - `beneficiaries`: Beneficiary information (JSONB)
  - `blockchain_tx_hash`: Blockchain transaction hash
  - `status`: Contract status
  - `created_at`: Creation timestamp
  - `updated_at`: Update timestamp

- **activities**: Tracks user activities
  - `id`: Primary key
  - `user_id`: Reference to user
  - `type`: Activity type
  - `message`: Activity description
  - `metadata`: Additional details (JSONB)
  - `status`: Status information
  - `created_at`: Creation timestamp
  - `updated_at`: Update timestamp

- **messages**: Stores communication messages
  - `id`: Primary key
  - `sender_id`: Reference to sender
  - `subject`: Message subject
  - `body`: Message content
  - `is_broadcast`: Broadcast flag
  - `contains_sensitive_info`: Sensitive info flag
  - `created_at`: Creation timestamp
  - `updated_at`: Update timestamp

### Relationships

- Clients are linked to managers (institutional_investors.manager_id)
- Assets are owned by users (tokenised_assets.user_id)
- Notifications are sent to users (notifications.user_id)
- Activities are performed by users (activities.user_id)
- Vesting contracts are linked to assets (vesting_records.asset_id)
- Messages are sent by users (messages.sender_id) to recipients (message_recipients.recipient_id)

### Database Design Principles

- **Normalization**: Tables are properly normalized to reduce redundancy
- **Indexing**: Appropriate indexes for performance optimization
- **Constraints**: Foreign key constraints for data integrity
- **JSON Support**: JSONB columns for flexible data storage
- **Timestamps**: Creation and update timestamps for auditing

## Authentication and Authorization

The authentication system is built with security and user experience in mind:

### Authentication Flow

1. **Registration**: User registers with email, password, and profile information
2. **Login**: User authenticates with credentials and receives JWT token
3. **Token Refresh**: Automatic token refresh to maintain session
4. **Session Management**: 24-hour sessions with extension capability
5. **Logout**: Token invalidation and session cleanup

### JWT Implementation

- **Token Structure**: Contains user ID, role, and expiration time
- **Token Signing**: Signed with secure secret key
- **Token Verification**: Verified on each protected request
- **Token Refresh**: Refreshed before expiration to maintain session
- **Remember Me**: Extended token validity (24 hours) when requested

### Authorization Middleware

- **authenticateToken**: Verifies JWT token and attaches user to request
- **requireManager**: Ensures user has manager role
- **requireClient**: Ensures user has client role
- **requireAdmin**: Ensures user has admin role

## Services

The application includes various services for business logic:

### Key Services

- **ActivityService**: Tracks and manages user activities
- **EmailService**: Handles email notifications
- **KycVerificationService**: Manages KYC verification process
- **NotificationService**: Handles notification creation and delivery
- **TokenizationService**: Manages asset tokenization process
- **VestingService**: Handles vesting contract management

### Service Design Principles

- **Single Responsibility**: Each service focuses on a specific domain
- **Dependency Injection**: Services receive dependencies through constructors
- **Error Handling**: Consistent error handling and reporting
- **Logging**: Comprehensive logging for monitoring and debugging
- **Transaction Management**: Database transactions for data integrity

## Middleware

The application includes various middleware for request processing:

### Key Middleware

- **Authentication**: Verifies JWT tokens and attaches user to request
- **Rate Limiting**: Prevents abuse through request rate limiting
- **Error Handling**: Catches and formats errors
- **Logging**: Logs request and response information
- **CORS**: Manages Cross-Origin Resource Sharing
- **Security Headers**: Adds security headers to responses
- **Body Parsing**: Parses request bodies in various formats
- **Compression**: Compresses responses for better performance

## Error Handling

The application includes a comprehensive error handling strategy:

### Error Types

- **Authentication Errors**: Invalid credentials, expired tokens, etc.
- **Authorization Errors**: Insufficient permissions
- **Validation Errors**: Invalid input data
- **Database Errors**: Connection issues, constraint violations, etc.
- **External Service Errors**: Issues with external services
- **Operational Errors**: Expected errors during normal operation
- **Programming Errors**: Unexpected errors due to bugs

### Error Handling Approach

- **Centralized Error Handling**: Global error handler middleware
- **Structured Error Responses**: Consistent error response format
- **Error Logging**: Comprehensive logging of errors
- **Error Classification**: Errors classified by type and severity
- **Client-Friendly Messages**: User-friendly error messages

## Security

The application includes various security measures:

### Security Features

- **JWT Authentication**: Secure token-based authentication
- **Password Hashing**: Secure password storage with bcrypt
- **Rate Limiting**: Protection against brute force attacks
- **CORS Protection**: Controlled cross-origin access
- **Security Headers**: Protection against common web vulnerabilities
- **Input Validation**: Validation of all user inputs
- **SQL Injection Protection**: Parameterized queries and ORM
- **XSS Protection**: Output encoding and content security policy
- **CSRF Protection**: Protection against cross-site request forgery
- **Sensitive Data Handling**: Secure handling of sensitive information

## Performance Optimization

The application includes various performance optimizations:

### Optimization Strategies

- **Connection Pooling**: Database connection pooling for better performance
- **Query Optimization**: Efficient database queries with proper indexing
- **Caching**: Caching of frequently accessed data
- **Compression**: Response compression for reduced bandwidth
- **Asynchronous Processing**: Non-blocking I/O for better concurrency
- **Pagination**: Pagination of large result sets
- **Efficient Data Structures**: Appropriate data structures for performance
- **Serverless Postgres Optimization**: Special handling for serverless database

## Testing

The application includes a testing strategy with:

### Testing Approaches

- **Unit Tests**: Testing individual functions and services
- **Integration Tests**: Testing API endpoints and database interactions
- **End-to-End Tests**: Testing complete user flows

### Testing Tools

- **Jest**: Testing framework for unit and integration tests
- **Supertest**: HTTP assertion library for API testing
- **Mock Services**: Mocking of external services for testing

## Deployment

The application is designed for deployment to various environments:

### Deployment Strategies

- **Containerization**: Docker containers for consistent deployment
- **Environment Variables**: Configuration through environment variables
- **CI/CD Integration**: Automated testing and deployment
- **Monitoring**: Application monitoring and alerting
- **Logging**: Centralized logging for troubleshooting
- **Scaling**: Horizontal scaling for increased load
- **Database Migration**: Automated database schema updates

## Best Practices

The codebase follows these best practices:

### Code Quality

- **ESLint**: Consistent code style and quality
- **Code Reviews**: Peer review process for code changes
- **Documentation**: Comprehensive code documentation
- **Modular Design**: Code organized into logical modules

### Security

- **Secure Coding**: Following secure coding practices
- **Regular Updates**: Keeping dependencies up to date
- **Security Audits**: Regular security reviews
- **Principle of Least Privilege**: Minimal permissions for components

### Performance

- **Profiling**: Regular performance profiling
- **Optimization**: Performance optimization based on profiling
- **Monitoring**: Performance monitoring in production
- **Load Testing**: Testing under expected load conditions

### Reliability

- **Error Handling**: Comprehensive error handling
- **Logging**: Detailed logging for troubleshooting
- **Monitoring**: Application monitoring and alerting
- **Backup and Recovery**: Regular database backups

---

This documentation provides a comprehensive overview of the Investor App Backend. For specific implementation details, refer to the codebase and comments within the files.
