# Investor App Database Documentation

## Overview

The Investor App uses a PostgreSQL database to store and manage all application data. This document provides a comprehensive guide to the database schema, relationships, and design decisions.

## Table of Contents

- [Database Technology](#database-technology)
- [Connection Management](#connection-management)
- [Schema Overview](#schema-overview)
- [Table Definitions](#table-definitions)
- [Relationships](#relationships)
- [Indexes](#indexes)
- [Views](#views)
- [Migrations](#migrations)
- [Query Optimization](#query-optimization)
- [Backup and Recovery](#backup-and-recovery)
- [Security](#security)

## Database Technology

### PostgreSQL

The application uses PostgreSQL as its primary database for several reasons:

1. **ACID Compliance**: Critical for financial data integrity
2. **JSON Support**: Native JSONB type for flexible data storage
3. **Advanced Features**: Rich feature set including views, triggers, and stored procedures
4. **Scalability**: Ability to handle growing data volumes
5. **Reliability**: Proven track record in production environments

### Serverless Deployment

The application connects to a serverless Neon Postgres database, which offers:

1. **Auto-scaling**: Automatic scaling based on demand
2. **Cost Efficiency**: Pay-per-use pricing model
3. **Zero Maintenance**: No need for database server management
4. **High Availability**: Built-in redundancy and failover
5. **Modern Architecture**: Separation of compute and storage

## Connection Management

### Connection Pool Configuration

The application uses a connection pool to efficiently manage database connections:

```javascript
export const pool = new Pool({
  connectionString: process.env.DATABASE_URL,
  ssl: {
    rejectUnauthorized: false,
    require: true
  },
  connectionTimeoutMillis: 5000,
  idleTimeoutMillis: 30000,
  max: 20,
  retryDelay: 1000,
  maxRetries: 3,
});
```

### Serverless Optimization

Special considerations for serverless Postgres:

1. **Connection Pooling**: Essential to avoid connection limits
2. **Connection Reuse**: Reusing connections to reduce overhead
3. **Timeout Handling**: Proper handling of connection timeouts
4. **Retry Logic**: Automatic retry for transient failures
5. **Error Handling**: Robust error handling for connection issues

## Schema Overview

The database schema is designed to support all application features with proper relationships and constraints. The main entities in the system include:

- **Users** (institutional_investors)
- **Assets** (tokenised_assets)
- **Vesting Contracts** (vesting_records)
- **Notifications** (notifications)
- **Activities** (activities)
- **Messages** (messages)
- **KYC Verifications** (kyc_verifications)

## Table Definitions

### institutional_investors

Stores user information including authentication details and profile data.

```sql
CREATE TABLE institutional_investors (
  id SERIAL PRIMARY KEY,
  role VARCHAR(50) NOT NULL DEFAULT 'client',
  manager_id INTEGER,
  company_name VARCHAR(255) NOT NULL,
  contact_name VARCHAR(255) NOT NULL,
  email VARCHAR(255) UNIQUE NOT NULL,
  password VARCHAR(255) NOT NULL,
  tax_id VARCHAR(50) NULL,
  address TEXT NOT NULL,
  phone VARCHAR(20) NOT NULL,
  profile_image VARCHAR(255),
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### tokenised_assets

Stores information about tokenized assets including their value and metadata.

```sql
CREATE TABLE tokenised_assets (
  id SERIAL PRIMARY KEY,
  name VARCHAR(255) NOT NULL,
  type VARCHAR(100) NOT NULL,
  value NUMERIC(20,2) NOT NULL,
  metadata JSONB,
  supporting_documents TEXT[],
  user_id INT NOT NULL REFERENCES institutional_investors(id) ON DELETE CASCADE,
  blockchain_tx_hash VARCHAR(255) UNIQUE NOT NULL,
  created_at TIMESTAMP DEFAULT NOW()
);
```

### asset_approvals

Tracks the approval status of tokenized assets.

```sql
CREATE TABLE asset_approvals (
  id SERIAL PRIMARY KEY,
  asset_id INT NOT NULL REFERENCES tokenised_assets(id) ON DELETE CASCADE,
  status VARCHAR(20) NOT NULL DEFAULT 'pending',
  blockchain_tx_hash VARCHAR(255) UNIQUE,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### ownership_records

Tracks ownership of assets, including partial ownership.

```sql
CREATE TABLE ownership_records (
  id SERIAL PRIMARY KEY,
  asset_id INT NOT NULL REFERENCES tokenised_assets(id) ON DELETE CASCADE,
  owner_id INT NOT NULL REFERENCES institutional_investors(id) ON DELETE CASCADE,
  ownership_percentage NUMERIC(5,2) NOT NULL,
  blockchain_tx_hash VARCHAR(255) NOT NULL,
  created_at TIMESTAMP DEFAULT NOW()
);
```

### vesting_records

Stores information about vesting contracts for assets.

```sql
CREATE TABLE vesting_records (
  id SERIAL PRIMARY KEY,
  asset_id INTEGER NOT NULL REFERENCES tokenised_assets(id) ON DELETE CASCADE,
  contract_address VARCHAR(255) NOT NULL,
  vesting_schedule JSONB NOT NULL,
  beneficiaries JSONB NOT NULL,
  blockchain_tx_hash VARCHAR(255) NOT NULL,
  status VARCHAR(20) NOT NULL DEFAULT 'active',
  created_at TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  CONSTRAINT valid_status CHECK (status IN ('active', 'completed', 'cancelled'))
);
```

### notifications

Stores notifications for users.

```sql
CREATE TABLE notifications (
  id SERIAL PRIMARY KEY,
  user_id INTEGER NOT NULL REFERENCES institutional_investors(id) ON DELETE CASCADE,
  type VARCHAR(50) NOT NULL,
  message TEXT NOT NULL,
  status VARCHAR(20) NOT NULL DEFAULT 'pending',
  metadata JSONB DEFAULT '{}'::jsonb,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);
```

### activities

Tracks user activities for auditing and reporting.

```sql
CREATE TABLE activities (
  id SERIAL PRIMARY KEY,
  user_id INTEGER NOT NULL REFERENCES institutional_investors(id) ON DELETE CASCADE,
  type VARCHAR(50) NOT NULL,
  message TEXT NOT NULL,
  metadata JSONB DEFAULT '{}'::jsonb,
  status VARCHAR(20) DEFAULT 'info',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### balance_history

Tracks user balance history for reporting and visualization.

```sql
CREATE TABLE balance_history (
  id SERIAL PRIMARY KEY,
  user_id INT NOT NULL REFERENCES institutional_investors(id) ON DELETE CASCADE,
  asset_type VARCHAR(100) NOT NULL DEFAULT 'all',
  pending_balance NUMERIC(20,2) NOT NULL DEFAULT 0,
  approved_balance NUMERIC(20,2) NOT NULL DEFAULT 0,
  total_balance NUMERIC(20,2) NOT NULL DEFAULT 0,
  recorded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### kyc_verifications

Stores KYC verification information.

```sql
CREATE TABLE kyc_verifications (
  id SERIAL PRIMARY KEY,
  user_id INTEGER NOT NULL REFERENCES institutional_investors(id) ON DELETE CASCADE,
  status VARCHAR(50) NOT NULL DEFAULT 'pending',
  selfie_url VARCHAR(255),
  passport_url VARCHAR(255),
  metadata JSONB,
  submitted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  CONSTRAINT valid_status CHECK (status IN ('pending', 'verified', 'failed'))
);
```

### messages

Stores messages between users.

```sql
CREATE TABLE messages (
  id SERIAL PRIMARY KEY,
  sender_id INTEGER NOT NULL REFERENCES institutional_investors(id) ON DELETE CASCADE,
  subject VARCHAR(255) NOT NULL,
  body TEXT NOT NULL,
  is_broadcast BOOLEAN DEFAULT FALSE,
  contains_sensitive_info BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);
```

### message_recipients

Tracks message recipients for individual and broadcast messages.

```sql
CREATE TABLE message_recipients (
  id SERIAL PRIMARY KEY,
  message_id INTEGER NOT NULL REFERENCES messages(id) ON DELETE CASCADE,
  recipient_id INTEGER NOT NULL REFERENCES institutional_investors(id) ON DELETE CASCADE,
  status VARCHAR(20) NOT NULL DEFAULT 'unread',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  CONSTRAINT unique_message_recipient UNIQUE (message_id, recipient_id)
);
```

### message_attachments

Stores attachments for messages.

```sql
CREATE TABLE message_attachments (
  id SERIAL PRIMARY KEY,
  message_id INTEGER NOT NULL REFERENCES messages(id) ON DELETE CASCADE,
  file_name VARCHAR(255) NOT NULL,
  file_url VARCHAR(255) NOT NULL,
  file_type VARCHAR(100),
  file_size INTEGER,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);
```

## Relationships

The database schema includes various relationships between tables:

### One-to-Many Relationships

- **Manager to Clients**: A manager can have multiple clients (institutional_investors.manager_id)
- **User to Assets**: A user can own multiple assets (tokenised_assets.user_id)
- **User to Notifications**: A user can have multiple notifications (notifications.user_id)
- **User to Activities**: A user can have multiple activities (activities.user_id)
- **Asset to Vesting Records**: An asset can have one vesting record (vesting_records.asset_id)
- **User to KYC Verifications**: A user can have multiple KYC verification attempts (kyc_verifications.user_id)
- **User to Messages**: A user can send multiple messages (messages.sender_id)
- **Message to Recipients**: A message can have multiple recipients (message_recipients.message_id)
- **Message to Attachments**: A message can have multiple attachments (message_attachments.message_id)

### Many-to-Many Relationships

- **Assets to Owners**: Assets can have multiple owners through ownership_records
- **Messages to Recipients**: Messages can have multiple recipients through message_recipients

## Indexes

The database includes various indexes for performance optimization:

```sql
-- User indexes
CREATE INDEX idx_institutional_investors_role ON institutional_investors(role);
CREATE INDEX idx_institutional_investors_manager_id ON institutional_investors(manager_id);

-- Asset indexes
CREATE INDEX idx_tokenised_assets_user_id ON tokenised_assets(user_id);
CREATE INDEX idx_tokenised_assets_type ON tokenised_assets(type);

-- Notification indexes
CREATE INDEX idx_notifications_user_id ON notifications(user_id);
CREATE INDEX idx_notifications_status ON notifications(status);

-- Activity indexes
CREATE INDEX idx_activities_user_id ON activities(user_id);
CREATE INDEX idx_activities_type ON activities(type);

-- KYC verification indexes
CREATE INDEX idx_kyc_verifications_user_id ON kyc_verifications(user_id);
CREATE INDEX idx_kyc_verifications_status ON kyc_verifications(status);

-- Message indexes
CREATE INDEX idx_messages_sender_id ON messages(sender_id);
CREATE INDEX idx_message_recipients_recipient_id ON message_recipients(recipient_id);
CREATE INDEX idx_message_recipients_status ON message_recipients(status);
```

## Views

The database includes views for common queries:

### vested_assets_view

Combines asset and vesting information for easier querying.

```sql
CREATE VIEW vested_assets_view AS
SELECT 
  ta.id,
  ta.name,
  ta.type,
  ta.value,
  ta.metadata,
  ta.supporting_documents,
  ta.blockchain_tx_hash,
  ta.created_at,
  vr.contract_address,
  vr.vesting_schedule,
  vr.beneficiaries,
  vr.status as vesting_status
FROM tokenised_assets ta
LEFT JOIN vesting_records vr ON ta.id = vr.asset_id;
```

### beneficiary_vesting_details

Expands the beneficiaries JSON array for easier querying.

```sql
CREATE VIEW beneficiary_vesting_details AS
SELECT 
  va.*,
  b->>'beneficiary_id' as beneficiary_id,
  b->>'name' as beneficiary_name,
  (b->>'percentage')::numeric as vesting_percentage
FROM vested_assets_view va,
jsonb_array_elements(va.beneficiaries) as b;
```

## Migrations

The application includes a migration system for database schema updates:

### Migration Process

1. **Create Migration**: Define schema changes in a migration file
2. **Apply Migration**: Execute migration during application startup
3. **Version Tracking**: Track applied migrations to prevent duplication

### Migration Examples

#### Create Messaging Tables

```javascript
export const createMessagingTables = async () => {
  try {
    logger.info('Creating messaging tables...');

    // Create messages table
    await pool.query(`
      CREATE TABLE IF NOT EXISTS messages (
        id SERIAL PRIMARY KEY,
        sender_id INTEGER NOT NULL REFERENCES institutional_investors(id) ON DELETE CASCADE,
        subject VARCHAR(255) NOT NULL,
        body TEXT NOT NULL,
        is_broadcast BOOLEAN DEFAULT FALSE,
        contains_sensitive_info BOOLEAN DEFAULT FALSE,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
      );
    `);

    // Create message recipients table
    await pool.query(`
      CREATE TABLE IF NOT EXISTS message_recipients (
        id SERIAL PRIMARY KEY,
        message_id INTEGER NOT NULL REFERENCES messages(id) ON DELETE CASCADE,
        recipient_id INTEGER NOT NULL REFERENCES institutional_investors(id) ON DELETE CASCADE,
        status VARCHAR(20) NOT NULL DEFAULT 'unread',
        created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
        CONSTRAINT unique_message_recipient UNIQUE (message_id, recipient_id)
      );
    `);

    // Create message attachments table
    await pool.query(`
      CREATE TABLE IF NOT EXISTS message_attachments (
        id SERIAL PRIMARY KEY,
        message_id INTEGER NOT NULL REFERENCES messages(id) ON DELETE CASCADE,
        file_name VARCHAR(255) NOT NULL,
        file_url VARCHAR(255) NOT NULL,
        file_type VARCHAR(100),
        file_size INTEGER,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
      );
    `);

    logger.info('Messaging tables created successfully');
  } catch (error) {
    logger.error('Error creating messaging tables:', error);
    throw error;
  }
};
```

## Query Optimization

The application includes various query optimization techniques:

### Optimization Strategies

1. **Proper Indexing**: Indexes on frequently queried columns
2. **Query Planning**: Analysis of query execution plans
3. **Pagination**: Limiting result sets for large queries
4. **Efficient Joins**: Optimized join strategies
5. **JSONB Indexing**: Indexing of JSONB fields for faster queries
6. **Materialized Views**: For complex, frequently-accessed queries
7. **Query Parameterization**: Preventing SQL injection and enabling query caching

### Example Optimized Query

```javascript
// Efficient query with pagination and filtering
const getAssetReports = async (req, res) => {
  try {
    const { 
      clientIds, 
      assetType, 
      approvalStatus, 
      startDate, 
      endDate, 
      minValue, 
      maxValue,
      page = 1,
      limit = 20
    } = req.query;

    // Base query with joins
    let query = `
      WITH user_assets AS (
        SELECT DISTINCT ta.id
        FROM tokenised_assets ta
        LEFT JOIN ownership_records ownership ON ta.id = ownership.asset_id
        WHERE (
          ta.user_id = $1
          OR ownership.owner_id = $1
          ${req.user.role === 'manager' ? 'OR 1=1' : ''}
        )
      )
      SELECT 
        ta.id,
        ta.name,
        ta.type,
        ta.value,
        ta.created_at,
        ta.blockchain_tx_hash,
        ii.company_name as owner_name,
        aa.status as approval_status
      FROM tokenised_assets ta
      JOIN user_assets ua ON ta.id = ua.id
      JOIN institutional_investors ii ON ta.user_id = ii.id
      LEFT JOIN asset_approvals aa ON ta.id = aa.asset_id
      WHERE 1=1
    `;

    // Add filters
    const queryParams = [req.user.id];
    let paramIndex = 2;

    // Apply filters...

    // Add pagination
    const offset = (page - 1) * limit;
    query += ` LIMIT $${paramIndex++} OFFSET $${paramIndex++}`;
    queryParams.push(limit, offset);

    // Execute query
    const result = await pool.query(query, queryParams);
    
    // Get total count for pagination
    const countQuery = `
      WITH user_assets AS (
        SELECT DISTINCT ta.id
        FROM tokenised_assets ta
        LEFT JOIN ownership_records ownership ON ta.id = ownership.asset_id
        WHERE (
          ta.user_id = $1
          OR ownership.owner_id = $1
          ${req.user.role === 'manager' ? 'OR 1=1' : ''}
        )
      )
      SELECT COUNT(*) as total
      FROM tokenised_assets ta
      JOIN user_assets ua ON ta.id = ua.id
      JOIN institutional_investors ii ON ta.user_id = ii.id
      LEFT JOIN asset_approvals aa ON ta.id = aa.asset_id
      WHERE 1=1
    `;
    
    // Apply the same filters to count query...
    
    const countResult = await pool.query(countQuery, countParams);
    const total = parseInt(countResult.rows[0].total);
    
    // Format and return response
    res.json({
      data: result.rows,
      pagination: {
        total,
        page: parseInt(page),
        limit: parseInt(limit),
        pages: Math.ceil(total / limit)
      }
    });
  } catch (error) {
    logger.error('Error fetching asset reports:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
};
```

## Backup and Recovery

The application includes a backup and recovery strategy:

### Backup Strategy

1. **Regular Backups**: Automated daily backups
2. **Point-in-Time Recovery**: Transaction log backups for point-in-time recovery
3. **Offsite Storage**: Backups stored in secure, offsite location
4. **Encryption**: Backup encryption for sensitive data
5. **Retention Policy**: Backup retention based on data importance

### Recovery Procedures

1. **Disaster Recovery Plan**: Documented recovery procedures
2. **Recovery Testing**: Regular testing of recovery procedures
3. **Failover Strategy**: Automated failover for high availability
4. **Data Validation**: Validation of recovered data
5. **Recovery Time Objective (RTO)**: Defined recovery time goals

## Security

The database includes various security measures:

### Security Features

1. **Encryption**: Data encryption at rest and in transit
2. **Access Control**: Role-based access control
3. **Password Security**: Secure password storage with bcrypt
4. **SQL Injection Prevention**: Parameterized queries
5. **Audit Logging**: Tracking of database changes
6. **Connection Security**: SSL/TLS for database connections
7. **Least Privilege**: Minimal permissions for database users
8. **Regular Updates**: Security patches and updates

### Sensitive Data Handling

1. **Data Classification**: Classification of data sensitivity
2. **Data Masking**: Masking of sensitive data in logs and reports
3. **Access Logging**: Logging of access to sensitive data
4. **Retention Policies**: Limited retention of sensitive data
5. **Secure Deletion**: Secure deletion of sensitive data

---

This documentation provides a comprehensive overview of the Investor App database. For specific implementation details, refer to the codebase and database schema files.
