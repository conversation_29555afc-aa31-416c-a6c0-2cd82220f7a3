{"name": "investor-app-backend", "version": "1.0.0", "description": "Backend for the investor application", "type": "module", "main": "src/server.js", "scripts": {"start": "node src/server.js", "dev": "nodemon src/server.js", "test": "jest", "download-models": "node scripts/download-models.js"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@aws-sdk/client-s3": "^3.525.0", "bcryptjs": "^2.4.3", "body-parser": "^1.20.2", "cloudinary": "^1.41.3", "compression": "^1.7.4", "cors": "^2.8.5", "dotenv": "^16.4.5", "ethers": "^6.11.1", "exceljs": "^4.4.0", "express": "^4.18.3", "express-mongo-sanitize": "^2.2.0", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "face-api.js": "^0.22.2", "file-saver": "^2.0.5", "form-data": "^4.0.0", "helmet": "^7.1.0", "hpp": "^0.2.3", "jsonwebtoken": "^9.0.2", "mailgun.js": "^9.4.1", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "multer-storage-cloudinary": "^4.0.0", "node-cron": "^3.0.3", "papaparse": "^5.5.2", "pdfkit": "^0.17.0", "pg": "^8.11.3", "sequelize": "^6.37.1", "serverless-http": "^3.2.0", "sharp": "^0.33.5", "tesseract.js": "^5.1.1", "uuid": "^9.0.1", "winston": "^3.11.0", "xlsx": "^0.18.5", "xmlbuilder2": "^3.1.1", "xss-clean": "^0.1.4"}, "devDependencies": {"jest": "^29.7.0", "nodemon": "^3.1.0"}}