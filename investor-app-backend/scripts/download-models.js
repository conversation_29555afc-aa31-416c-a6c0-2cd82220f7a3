import fs from 'fs';
import path from 'path';
import https from 'https';

const MODEL_URLS = {
  'ssd_mobilenetv1_model-weights_manifest.json': 'https://github.com/justadudewhohacks/face-api.js/raw/master/weights/ssd_mobilenetv1_model-weights_manifest.json',
  'ssd_mobilenetv1_model-shard1': 'https://github.com/justadudewhohacks/face-api.js/raw/master/weights/ssd_mobilenetv1_model-shard1',
  'face_landmark_68_model-weights_manifest.json': 'https://github.com/justadudewhohacks/face-api.js/raw/master/weights/face_landmark_68_model-weights_manifest.json',
  'face_landmark_68_model-shard1': 'https://github.com/justadudewhohacks/face-api.js/raw/master/weights/face_landmark_68_model-shard1',
  'face_recognition_model-weights_manifest.json': 'https://github.com/justadudewhohacks/face-api.js/raw/master/weights/face_recognition_model-weights_manifest.json',
  'face_recognition_model-shard1': 'https://github.com/justadudewhohacks/face-api.js/raw/master/weights/face_recognition_model-shard1'
};

const MODELS_DIR = path.join(process.cwd(), 'models');

// Create models directory if it doesn't exist
if (!fs.existsSync(MODELS_DIR)) {
  fs.mkdirSync(MODELS_DIR);
}

// Download file function
const downloadFile = (url, filename) => {
  return new Promise((resolve, reject) => {
    const filepath = path.join(MODELS_DIR, filename);
    const file = fs.createWriteStream(filepath);

    https.get(url, response => {
      response.pipe(file);
      file.on('finish', () => {
        file.close();
        console.log(`Downloaded: ${filename}`);
        resolve();
      });
    }).on('error', err => {
      fs.unlink(filepath, () => {}); // Delete the file if there was an error
      reject(err);
    });
  });
};

// Download all models
const downloadModels = async () => {
  console.log('Starting model downloads...');
  
  for (const [filename, url] of Object.entries(MODEL_URLS)) {
    try {
      await downloadFile(url, filename);
    } catch (error) {
      console.error(`Error downloading ${filename}:`, error);
    }
  }
  
  console.log('All model downloads completed!');
};

// Run the download
downloadModels().catch(console.error); 