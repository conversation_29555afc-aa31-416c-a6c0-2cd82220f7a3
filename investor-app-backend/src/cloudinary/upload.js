import { v2 as cloudinary } from 'cloudinary';
import { CloudinaryStorage } from 'multer-storage-cloudinary';
import multer from 'multer';
import { logger } from '../utils/logger.js';

// Validate Cloudinary configuration
const validateCloudinaryConfig = () => {
  const requiredEnvVars = ['CLOUDINARY_CLOUD_NAME', 'CLOUDINARY_API_KEY', 'CLOUDINARY_API_SECRET'];
  const missingVars = requiredEnvVars.filter(varName => !process.env[varName]);
  
  if (missingVars.length > 0) {
    const error = new Error(`Missing required Cloudinary environment variables: ${missingVars.join(', ')}`);
    logger.error('Cloudinary configuration error:', error);
    throw error;
  }
};

// Configure Cloudinary
let isConfigured = false;
try {
  validateCloudinaryConfig();
  cloudinary.config({
    cloud_name: process.env.CLOUDINARY_CLOUD_NAME,
    api_key: process.env.CLOUDINARY_API_KEY,
    api_secret: process.env.CLOUDINARY_API_SECRET
  });
  isConfigured = true;
  logger.info('Cloudinary configured successfully');
} catch (error) {
  logger.error('Failed to configure Cloudinary:', error);
  // Don't throw here, let the route handler deal with it
}

// Configure multer storage
const storage = new CloudinaryStorage({
  cloudinary: cloudinary,
  params: {
    folder: 'kyc_verifications',
    allowed_formats: ['jpg', 'jpeg', 'png'],
    transformation: [{ width: 1280, height: 720, crop: 'limit' }]
  }
});

// Create multer upload instance
export const upload = multer({ storage: storage });

// Helper function to upload buffer to Cloudinary
export const uploadToCloudinary = (buffer, folder) => {
  return new Promise((resolve, reject) => {
    if (!isConfigured) {
      const error = new Error('Cloudinary is not properly configured');
      logger.error('Upload error:', error);
      reject(error);
      return;
    }

    try {
      const uploadStream = cloudinary.uploader.upload_stream(
        {
          folder: folder,
          resource_type: 'auto',
          transformation: [{ width: 1280, height: 720, crop: 'limit' }]
        },
        (error, result) => {
          if (error) {
            logger.error('Cloudinary upload error:', error);
            reject(error);
          } else {
            logger.info('File uploaded successfully to Cloudinary');
            resolve(result.secure_url);
          }
        }
      );

      // Convert buffer to stream and pipe to Cloudinary
      const stream = require('stream');
      const bufferStream = new stream.PassThrough();
      bufferStream.end(buffer);
      bufferStream.pipe(uploadStream);
    } catch (error) {
      logger.error('Error in uploadToCloudinary:', error);
      reject(error);
    }
  });
}; 