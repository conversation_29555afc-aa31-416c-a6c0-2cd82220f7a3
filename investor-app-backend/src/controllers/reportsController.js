import { pool } from '../setup/databases.js';
import { logger } from '../utils/logger.js';
import XLSX from 'xlsx';
import <PERSON> from 'papapar<PERSON>';
import PDFDocument from 'pdfkit';

export const getAssetReports = async (req, res) => {
  try {
    const { 
      clientIds, 
      assetType, 
      approvalStatus, 
      startDate, 
      endDate, 
      minValue, 
      maxValue 
    } = req.query;

    // Base query with joins
    let query = `
      WITH user_assets AS (
        SELECT DISTINCT ta.id
        FROM tokenised_assets ta
        LEFT JOIN ownership_records ownership ON ta.id = ownership.asset_id
        WHERE (
          ta.user_id = $1
          OR ownership.owner_id = $1
          ${req.user.role === 'manager' ? 'OR 1=1' : ''}
        )
      )
      SELECT 
        ta.id,
        ta.name,
        ta.type,
        COALESCE(ta.value, 0) as value,
        ta.metadata,
        ta.created_at,
        COALESCE(aa.status, 'pending') as approval_status,
        COALESCE(ownership.ownership_percentage, 0) as ownership_percentage,
        ii.company_name as owner_company,
        ta.user_id as creator_id
      FROM tokenised_assets ta
      INNER JOIN user_assets ua ON ta.id = ua.id
      LEFT JOIN asset_approvals aa ON ta.id = aa.asset_id
      LEFT JOIN ownership_records ownership ON ta.id = ownership.asset_id
      LEFT JOIN institutional_investors ii ON ownership.owner_id = ii.id
      WHERE 1=1
    `;

    const queryParams = [req.user.id];

    // Add role-based filtering for managers
    if (req.user.role === 'manager' && clientIds && clientIds.length > 0) {
      const ids = Array.isArray(clientIds) ? clientIds : clientIds.split(',').map(id => parseInt(id.trim()));
      query += ` AND ownership.owner_id = ANY($${queryParams.length + 1})`;
      queryParams.push(ids);
    }

    // Add other filters
    if (assetType) {
      query += ` AND ta.type = $${queryParams.length + 1}`;
      queryParams.push(assetType);
    }

    if (approvalStatus) {
      query += ` AND COALESCE(aa.status, 'pending') = $${queryParams.length + 1}`;
      queryParams.push(approvalStatus);
    }

    if (startDate) {
      query += ` AND ta.created_at >= $${queryParams.length + 1}`;
      queryParams.push(startDate);
    }

    if (endDate) {
      query += ` AND ta.created_at <= $${queryParams.length + 1}`;
      queryParams.push(endDate);
    }

    if (minValue) {
      query += ` AND ta.value >= $${queryParams.length + 1}`;
      queryParams.push(minValue);
    }

    if (maxValue) {
      query += ` AND ta.value <= $${queryParams.length + 1}`;
      queryParams.push(maxValue);
    }

    // Order by creation date
    query += ` ORDER BY ta.created_at DESC`;

    const result = await pool.query(query, queryParams);
    
    // Format the response data
    const formattedData = result.rows.map(row => ({
      ...row,
      value: parseFloat(row.value),
      ownership_percentage: parseFloat(row.ownership_percentage),
      created_at: new Date(row.created_at).toISOString(),
      is_creator: row.creator_id === req.user.id
    }));

    res.json(formattedData);
  } catch (error) {
    logger.error('Error fetching asset reports:', error);
    res.status(500).json({ 
      error: 'Internal server error',
      message: 'Failed to fetch asset reports. Please try again later.'
    });
  }
};

export const getAssetStatistics = async (req, res) => {
  try {
    const { startDate, endDate } = req.query;
    
    let query = `
      WITH user_assets AS (
        SELECT DISTINCT ta.id
        FROM tokenised_assets ta
        LEFT JOIN ownership_records ownership ON ta.id = ownership.asset_id
        WHERE (
          ta.user_id = $1
          OR ownership.owner_id = $1
          ${req.user.role === 'manager' ? 'OR 1=1' : ''}
        )
      )
      SELECT 
        COUNT(*) as total_assets,
        COALESCE(SUM(ta.value), 0) as total_value,
        COALESCE(AVG(ta.value), 0) as average_value,
        COUNT(CASE WHEN COALESCE(aa.status, 'pending') = 'approved' THEN 1 END) as approved_assets,
        COUNT(CASE WHEN COALESCE(aa.status, 'pending') = 'pending' THEN 1 END) as pending_assets
      FROM tokenised_assets ta
      INNER JOIN user_assets ua ON ta.id = ua.id
      LEFT JOIN asset_approvals aa ON ta.id = aa.asset_id
      LEFT JOIN ownership_records ownership ON ta.id = ownership.asset_id
      WHERE 1=1
    `;

    const queryParams = [req.user.id];

    if (startDate) {
      query += ` AND ta.created_at >= $${queryParams.length + 1}`;
      queryParams.push(startDate);
    }

    if (endDate) {
      query += ` AND ta.created_at <= $${queryParams.length + 1}`;
      queryParams.push(endDate);
    }

    const result = await pool.query(query, queryParams);
    const stats = result.rows[0] || {
      total_assets: 0,
      total_value: 0,
      average_value: 0,
      approved_assets: 0,
      pending_assets: 0
    };

    // Calculate approval rate
    const approvalRate = stats.total_assets > 0 
      ? (stats.approved_assets / stats.total_assets) * 100 
      : 0;

    // Format the response
    const response = {
      totalAssets: parseInt(stats.total_assets),
      totalValue: parseFloat(stats.total_value),
      averageValue: parseFloat(stats.average_value),
      approvalRate: parseFloat(approvalRate.toFixed(2))
    };

    res.json(response);
  } catch (error) {
    logger.error('Error fetching asset statistics:', error);
    res.status(500).json({ 
      error: 'Internal server error',
      message: 'Failed to fetch asset statistics. Please try again later.'
    });
  }
};

export const getBalanceHistory = async (req, res) => {
  try {
    const { startDate, endDate } = req.query;
    
    let query = `
      SELECT 
        recorded_at,
        asset_type,
        pending_balance,
        approved_balance,
        total_balance
      FROM balance_history
      WHERE user_id = $1
    `;

    const queryParams = [req.user.id];

    if (startDate) {
      query += ` AND recorded_at >= $${queryParams.length + 1}`;
      queryParams.push(startDate);
    }

    if (endDate) {
      query += ` AND recorded_at <= $${queryParams.length + 1}`;
      queryParams.push(endDate);
    }

    query += ` ORDER BY recorded_at ASC`;

    const result = await pool.query(query, queryParams);
    res.json(result.rows);
  } catch (error) {
    logger.error('Error fetching balance history:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
};

export const getAssetDetails = async (req, res) => {
  try {
    const { assetId } = req.params;

    // Base query with all necessary joins
    const query = `
      WITH user_assets AS (
        SELECT DISTINCT ta.id
        FROM tokenised_assets ta
        LEFT JOIN ownership_records ownership ON ta.id = ownership.asset_id
        WHERE (
          ta.user_id = $1
          OR ownership.owner_id = $1
          ${req.user.role === 'manager' ? 'OR 1=1' : ''}
        )
      )
      SELECT 
        ta.*,
        COALESCE(aa.status, 'pending') as approval_status,
        COALESCE(aa.blockchain_tx_hash, '') as approval_tx_hash,
        COALESCE(aa.created_at, NULL) as approval_date,
        COALESCE(ta.value, 0) as value,
        COALESCE(ta.metadata, '{}'::jsonb) as metadata,
        COALESCE(ta.supporting_documents, ARRAY[]::text[]) as supporting_documents,
        COALESCE(ta.blockchain_tx_hash, '') as blockchain_tx_hash,
        COALESCE(ta.created_at, CURRENT_TIMESTAMP) as created_at,
        COALESCE(ta.user_id, NULL) as creator_id,
        COALESCE(
          json_agg(
            json_build_object(
              'id', ownership.id,
              'owner_id', ownership.owner_id,
              'owner_name', ii.company_name,
              'ownership_percentage', ownership.ownership_percentage,
              'blockchain_tx_hash', ownership.blockchain_tx_hash,
              'created_at', ownership.created_at
            )
          ) FILTER (WHERE ownership.id IS NOT NULL),
          '[]'::json
        ) as ownership_records
      FROM tokenised_assets ta
      INNER JOIN user_assets ua ON ta.id = ua.id
      LEFT JOIN asset_approvals aa ON ta.id = aa.asset_id
      LEFT JOIN ownership_records ownership ON ta.id = ownership.asset_id
      LEFT JOIN institutional_investors ii ON ownership.owner_id = ii.id
      WHERE ta.id = $2
      GROUP BY ta.id, aa.status, aa.blockchain_tx_hash, aa.created_at
    `;

    const result = await pool.query(query, [req.user.id, assetId]);
    
    if (result.rows.length === 0) {
      return res.status(404).json({ error: 'Asset not found' });
    }

    // Format the response data
    const asset = result.rows[0];
    const formattedAsset = {
      ...asset,
      value: parseFloat(asset.value),
      metadata: typeof asset.metadata === 'string' ? JSON.parse(asset.metadata) : asset.metadata,
      supporting_documents: Array.isArray(asset.supporting_documents) ? asset.supporting_documents : [],
      ownership_records: typeof asset.ownership_records === 'string' ? JSON.parse(asset.ownership_records) : asset.ownership_records,
      created_at: new Date(asset.created_at).toISOString(),
      approval_date: asset.approval_date ? new Date(asset.approval_date).toISOString() : null,
      is_creator: asset.creator_id === req.user.id
    };

    res.json(formattedAsset);
  } catch (error) {
    logger.error('Error fetching asset details:', error);
    res.status(500).json({ 
      error: 'Internal server error',
      message: 'Failed to fetch asset details. Please try again later.'
    });
  }
};

export const exportReport = async (req, res) => {
  try {
    const { format, data, dateRange, filters } = req.body;

    // Base query with all necessary joins
    const query = `
      WITH user_assets AS (
        SELECT DISTINCT ta.id
        FROM tokenised_assets ta
        LEFT JOIN ownership_records ownership ON ta.id = ownership.asset_id
        WHERE (
          ta.user_id = $1
          OR ownership.owner_id = $1
          ${req.user.role === 'manager' ? 'OR 1=1' : ''}
        )
      )
      SELECT 
        ta.*,
        COALESCE(aa.status, 'pending') as approval_status,
        COALESCE(aa.blockchain_tx_hash, '') as approval_tx_hash,
        COALESCE(aa.created_at, NULL) as approval_date,
        COALESCE(ta.value, 0) as value,
        COALESCE(ta.metadata, '{}'::jsonb) as metadata,
        COALESCE(ta.supporting_documents, ARRAY[]::text[]) as supporting_documents,
        COALESCE(ta.blockchain_tx_hash, '') as blockchain_tx_hash,
        COALESCE(ta.created_at, CURRENT_TIMESTAMP) as created_at,
        COALESCE(ta.user_id, NULL) as creator_id,
        COALESCE(
          json_agg(
            json_build_object(
              'id', ownership.id,
              'owner_id', ownership.owner_id,
              'owner_name', ii.company_name,
              'ownership_percentage', ownership.ownership_percentage,
              'blockchain_tx_hash', ownership.blockchain_tx_hash,
              'created_at', ownership.created_at
            )
          ) FILTER (WHERE ownership.id IS NOT NULL),
          '[]'::json
        ) as ownership_records
      FROM tokenised_assets ta
      INNER JOIN user_assets ua ON ta.id = ua.id
      LEFT JOIN asset_approvals aa ON ta.id = aa.asset_id
      LEFT JOIN ownership_records ownership ON ta.id = ownership.asset_id
      LEFT JOIN institutional_investors ii ON ownership.owner_id = ii.id
      WHERE 1=1
    `;

    const queryParams = [req.user.id];

    // Add date range filter
    if (dateRange?.start) {
      query += ` AND ta.created_at >= $${queryParams.length + 1}`;
      queryParams.push(dateRange.start);
    }
    if (dateRange?.end) {
      query += ` AND ta.created_at <= $${queryParams.length + 1}`;
      queryParams.push(dateRange.end);
    }

    // Add other filters
    if (filters?.assetType) {
      query += ` AND ta.type = $${queryParams.length + 1}`;
      queryParams.push(filters.assetType);
    }
    if (filters?.approvalStatus) {
      query += ` AND COALESCE(aa.status, 'pending') = $${queryParams.length + 1}`;
      queryParams.push(filters.approvalStatus);
    }
    if (filters?.minValue) {
      query += ` AND ta.value >= $${queryParams.length + 1}`;
      queryParams.push(filters.minValue);
    }
    if (filters?.maxValue) {
      query += ` AND ta.value <= $${queryParams.length + 1}`;
      queryParams.push(filters.maxValue);
    }

    query += ` GROUP BY ta.id, aa.status, aa.blockchain_tx_hash, aa.created_at`;

    const result = await pool.query(query, queryParams);
    
    // Format the data based on selected data types
    const formattedData = {
      assets: result.rows.map(asset => ({
        id: asset.id,
        name: asset.name,
        type: asset.type,
        value: parseFloat(asset.value),
        status: asset.approval_status,
        created_at: new Date(asset.created_at).toISOString(),
        metadata: typeof asset.metadata === 'string' ? JSON.parse(asset.metadata) : asset.metadata,
        supporting_documents: Array.isArray(asset.supporting_documents) ? asset.supporting_documents : [],
        ownership_records: typeof asset.ownership_records === 'string' ? JSON.parse(asset.ownership_records) : asset.ownership_records
      }))
    };

    // Add statistics if requested
    if (data.includes('statistics')) {
      const statsQuery = `
        SELECT 
          COUNT(*) as total_assets,
          COALESCE(SUM(ta.value), 0) as total_value,
          COALESCE(AVG(ta.value), 0) as average_value,
          COUNT(CASE WHEN COALESCE(aa.status, 'pending') = 'approved' THEN 1 END) as approved_assets,
          COUNT(CASE WHEN COALESCE(aa.status, 'pending') = 'pending' THEN 1 END) as pending_assets
        FROM tokenised_assets ta
        INNER JOIN user_assets ua ON ta.id = ua.id
        LEFT JOIN asset_approvals aa ON ta.id = aa.asset_id
        WHERE 1=1
      `;

      const statsParams = [req.user.id];
      if (dateRange?.start) {
        statsQuery += ` AND ta.created_at >= $${statsParams.length + 1}`;
        statsParams.push(dateRange.start);
      }
      if (dateRange?.end) {
        statsQuery += ` AND ta.created_at <= $${statsParams.length + 1}`;
        statsParams.push(dateRange.end);
      }

      const statsResult = await pool.query(statsQuery, statsParams);
      const stats = statsResult.rows[0];

      formattedData.statistics = {
        totalAssets: parseInt(stats.total_assets),
        totalValue: parseFloat(stats.total_value),
        averageValue: parseFloat(stats.average_value),
        approvalRate: stats.total_assets > 0 
          ? (parseInt(stats.approved_assets) / parseInt(stats.total_assets)) * 100 
          : 0
      };
    }

    // Generate the export file based on format
    let exportBuffer;
    let contentType;
    let filename;

    switch (format) {
      case 'xlsx':
        const workbook = XLSX.utils.book_new();
        
        // Create sheets for each data type
        if (formattedData.assets) {
          const assetsSheet = XLSX.utils.json_to_sheet(formattedData.assets);
          XLSX.utils.book_append_sheet(workbook, assetsSheet, 'Assets');
        }
        
        if (formattedData.statistics) {
          const statsSheet = XLSX.utils.json_to_sheet([formattedData.statistics]);
          XLSX.utils.book_append_sheet(workbook, statsSheet, 'Statistics');
        }

        exportBuffer = XLSX.write(workbook, { type: 'buffer', bookType: 'xlsx' });
        contentType = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
        filename = 'financial_report.xlsx';
        break;

      case 'csv':
        const csvData = Papa.unparse(formattedData.assets);
        exportBuffer = Buffer.from(csvData);
        contentType = 'text/csv';
        filename = 'financial_report.csv';
        break;

      case 'pdf':
        // Generate PDF using a PDF library
        const doc = new PDFDocument();
        const chunks = [];
        
        doc.on('data', chunk => chunks.push(chunk));
        doc.on('end', () => {
          exportBuffer = Buffer.concat(chunks);
        });

        // Add content to PDF
        doc.fontSize(16).text('Financial Report', { align: 'center' });
        doc.moveDown();
        
        // Add assets table
        doc.fontSize(12).text('Assets');
        const table = {
          headers: ['Name', 'Type', 'Value', 'Status', 'Created'],
          rows: formattedData.assets.map(asset => [
            asset.name,
            asset.type,
            `$${asset.value.toLocaleString()}`,
            asset.status,
            new Date(asset.created_at).toLocaleDateString()
          ])
        };
        
        // Draw table
        let y = doc.y;
        const cellWidth = doc.page.width / table.headers.length;
        
        // Headers
        table.headers.forEach((header, i) => {
          doc.text(header, i * cellWidth, y);
        });
        
        // Rows
        table.rows.forEach(row => {
          y += 20;
          row.forEach((cell, i) => {
            doc.text(cell, i * cellWidth, y);
          });
        });

        doc.end();
        contentType = 'application/pdf';
        filename = 'financial_report.pdf';
        break;

      case 'qbo':
        // Generate QuickBooks Online format
        const qboData = formattedData.assets.map(asset => ({
          '!TRNS': {
            TRNSID: asset.id,
            TRNSTYPE: 'INVOICE',
            DATE: new Date(asset.created_at).toLocaleDateString(),
            ACCNT: 'Accounts Receivable',
            NAME: asset.name,
            AMOUNT: asset.value,
            DOCNUM: asset.id,
            MEMO: `Asset Type: ${asset.type}, Status: ${asset.status}`
          }
        }));

        exportBuffer = Buffer.from(JSON.stringify(qboData));
        contentType = 'application/x-qbo';
        filename = 'financial_report.qbo';
        break;

      case 'ofx':
        // Generate OFX format
        const ofxData = `OFXHEADER:100
DATA:OFXSGML
VERSION:102
SECURITY:NONE
ENCODING:USASCII
CHARSET:1252
COMPRESSION:NONE
OLDFILEUID:NONE
NEWFILEUID:NONE

<OFX>
  <SIGNONMSGSRSV1>
    <SONRS>
      <STATUS>
        <CODE>0
        <SEVERITY>INFO
      </STATUS>
      <DTSERVER>${new Date().toISOString()}
      <LANGUAGE>ENG
    </SONRS>
  </SIGNONMSGSRSV1>
  <BANKMSGSRSV1>
    <STMTTRNRS>
      <TRNUID>0
      <STATUS>
        <CODE>0
        <SEVERITY>INFO
      </STATUS>
      <STMTRS>
        <CURDEF>USD
        <BANKACCTFROM>
          <BANKID>*********
          <ACCTID>*********
          <ACCTTYPE>CHECKING
        </BANKACCTFROM>
        <BANKTRANLIST>
          ${formattedData.assets.map(asset => `
          <STMTTRN>
            <TRNTYPE>CREDIT
            <DTPOSTED>${new Date(asset.created_at).toISOString()}
            <TRNAMT>${asset.value}
            <FITID>${asset.id}
            <NAME>${asset.name}
            <MEMO>Asset Type: ${asset.type}, Status: ${asset.status}
          </STMTTRN>
          `).join('')}
        </BANKTRANLIST>
      </STMTRS>
    </STMTTRNRS>
  </BANKMSGSRSV1>
</OFX>`;

        exportBuffer = Buffer.from(ofxData);
        contentType = 'application/x-ofx';
        filename = 'financial_report.ofx';
        break;

      default:
        throw new Error('Unsupported export format');
    }

    // Set response headers
    res.setHeader('Content-Type', contentType);
    res.setHeader('Content-Disposition', `attachment; filename="${filename}"`);
    
    // Send the file
    res.send(exportBuffer);
  } catch (error) {
    logger.error('Error exporting report:', error);
    res.status(500).json({ 
      error: 'Internal server error',
      message: 'Failed to export report. Please try again later.'
    });
  }
}; 