import jwt from 'jsonwebtoken';
import { User } from '../setup/models.js';
import rateLimit from 'express-rate-limit';

// Rate limiter for password reset attempts
export const passwordResetLimiter = rateLimit({
    windowMs: 60 * 60 * 1000, // 1 hour window
    max: 5, // limit each IP to 5 requests per windowMs
    message: { error: 'Too many password reset attempts. Please try again in an hour.' }
});

// Rate limiter for magic link requests
export const magicLinkLimiter = rateLimit({
    windowMs: 5 * 60 * 1000, // 5 minutes window
    max: 3, // limit each IP to 3 requests per windowMs
    message: { error: 'Too many magic link requests. Please try again in 5 minutes.' }
});

export const authenticateToken = async (req, res, next) => {
    try {
        const authHeader = req.headers.authorization;
        if (!authHeader) {
            return res.status(401).json({ success: false, message: 'No token provided' });
        }

        const token = authHeader.split(' ')[1];
        const decoded = jwt.verify(token, process.env.JWT_SECRET);

        // Get user from database to ensure they still exist and are active
        const user = await User.findByPk(decoded.id);
        if (!user) {
            return res.status(401).json({ success: false, message: 'User not found' });
        }

        // Always check if token needs refresh when rememberMe is true
        // For rememberMe=true tokens, refresh if older than 12 hours to ensure it never expires
        // For regular tokens, refresh if older than 1 hour
        if (decoded.iat) {
            const tokenAge = Math.floor(Date.now() / 1000) - decoded.iat;
            const isRememberMe = decoded.rememberMe === true;
            // For rememberMe tokens, refresh if older than 18 hours to ensure it never expires
            // For regular tokens, refresh if older than 1 hour
            const refreshThreshold = isRememberMe ? 18 * 60 * 60 : 1 * 60 * 60; // 18 hours or 1 hour

            if (tokenAge > refreshThreshold) {
                // Generate new token with appropriate expiry
                const expiresIn = isRememberMe ? '24h' : '2h';
                const newToken = jwt.sign(
                    {
                        id: user.id,
                        email: user.email,
                        role: user.role,
                        rememberMe: isRememberMe
                    },
                    process.env.JWT_SECRET,
                    { expiresIn }
                );

                // Add new token to response header
                res.setHeader('X-New-Token', newToken);
            }
        }

        // Add user data to request
        req.user = {
            id: user.id,
            email: user.email,
            role: user.role,
            contact_name: user.contact_name
        };

        next();
    } catch (error) {
        if (error instanceof jwt.JsonWebTokenError) {
            return res.status(403).json({ success: false, message: 'Invalid token' });
        }
        return res.status(500).json({ success: false, message: 'Internal server error' });
    }
};

export const requireRole = (...roles) => {
    return (req, res, next) => {
        if (!req.user) {
            return res.status(401).json({ success: false, message: 'Authentication required' });
        }

        if (!roles.includes(req.user.role)) {
            return res.status(403).json({ success: false, message: 'Insufficient permissions' });
        }

        next();
    };
};

// Middleware to require manager role
export const requireManager = (req, res, next) => {
    if (!req.user) {
        return res.status(401).json({ success: false, message: 'Authentication required' });
    }

    if (req.user.role !== 'manager') {
        return res.status(403).json({ success: false, message: 'Manager access required' });
    }

    next();
};

// Middleware to require client role
export const requireClient = (req, res, next) => {
    if (!req.user) {
        return res.status(401).json({ success: false, message: 'Authentication required' });
    }

    if (req.user.role !== 'client') {
        return res.status(403).json({ success: false, message: 'Client access required' });
    }

    next();
};