import { body, validationResult } from 'express-validator';
import { sanitizeInput } from '../utils/sanitizer.js';

// Input validation middleware
export const validateInput = (req, res, next) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({ errors: errors.array() });
  }
  next();
};

// Sanitize all input data
export const sanitizeAllInput = (req, res, next) => {
  if (req.body) {
    req.body = sanitizeInput(req.body);
  }
  if (req.query) {
    req.query = sanitizeInput(req.query);
  }
  if (req.params) {
    req.params = sanitizeInput(req.params);
  }
  next();
};

// Common validation rules
export const commonValidationRules = {
  email: body('email')
    .isEmail()
    .normalizeEmail()
    .withMessage('Please enter a valid email address'),
  
  password: body('password')
    .isLength({ min: 8 })
    .withMessage('Password must be at least 8 characters long')
    .matches(/\d/)
    .withMessage('Password must contain at least one number')
    .matches(/[a-z]/)
    .withMessage('Password must contain at least one lowercase letter')
    .matches(/[A-Z]/)
    .withMessage('Password must contain at least one uppercase letter')
    .matches(/[!@#$%^&*(),.?":{}|<>]/)
    .withMessage('Password must contain at least one special character'),
  
  phone: body('phone')
    .matches(/^\+?[1-9]\d{1,14}$/)
    .withMessage('Please enter a valid phone number'),
  
  companyName: body('company_name')
    .trim()
    .isLength({ min: 2, max: 255 })
    .withMessage('Company name must be between 2 and 255 characters'),
  
  contactName: body('contact_name')
    .trim()
    .isLength({ min: 2, max: 255 })
    .withMessage('Contact name must be between 2 and 255 characters'),
  
  address: body('address')
    .trim()
    .isLength({ min: 5, max: 1000 })
    .withMessage('Address must be between 5 and 1000 characters'),
  
  taxId: body('tax_id')
    .optional()
    .trim()
    .isLength({ min: 5, max: 50 })
    .withMessage('Tax ID must be between 5 and 50 characters'),
}; 