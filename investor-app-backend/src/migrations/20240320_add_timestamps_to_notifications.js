import { DataTypes } from 'sequelize';

export async function up(queryInterface) {
  // Add user_id column if it doesn't exist
  await queryInterface.addColumn('notifications', 'user_id', {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: 'institutional_investors',
      key: 'id'
    },
    onUpdate: 'CASCADE',
    onDelete: 'CASCADE'
  }).catch(error => {
    // If column already exists, continue
    if (error.message.includes('column "user_id" of relation "notifications" already exists')) {
      console.log('user_id column already exists, skipping...');
    } else {
      throw error;
    }
  });

  // Add metadata column if it doesn't exist
  await queryInterface.addColumn('notifications', 'metadata', {
    type: DataTypes.JSONB,
    defaultValue: {}
  }).catch(error => {
    if (error.message.includes('column "metadata" of relation "notifications" already exists')) {
      console.log('metadata column already exists, skipping...');
    } else {
      throw error;
    }
  });

  // Add timestamp columns
  await queryInterface.addColumn('notifications', 'created_at', {
    type: DataTypes.DATE,
    allowNull: false,
    defaultValue: DataTypes.NOW
  }).catch(error => {
    if (error.message.includes('column "created_at" of relation "notifications" already exists')) {
      console.log('created_at column already exists, skipping...');
    } else {
      throw error;
    }
  });

  await queryInterface.addColumn('notifications', 'updated_at', {
    type: DataTypes.DATE,
    allowNull: false,
    defaultValue: DataTypes.NOW
  }).catch(error => {
    if (error.message.includes('column "updated_at" of relation "notifications" already exists')) {
      console.log('updated_at column already exists, skipping...');
    } else {
      throw error;
    }
  });
}

export async function down(queryInterface) {
  await queryInterface.removeColumn('notifications', 'updated_at');
  await queryInterface.removeColumn('notifications', 'created_at');
  await queryInterface.removeColumn('notifications', 'metadata');
  await queryInterface.removeColumn('notifications', 'user_id');
} 