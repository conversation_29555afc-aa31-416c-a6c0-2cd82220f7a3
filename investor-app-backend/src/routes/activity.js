import express from 'express';
import { ActivityService } from '../services/activityService.js';
import { authenticateToken } from '../middlewares/auth.js';
import { Activity, User } from '../setup/models.js';

const router = express.Router();

// Get user's recent activities
router.get('/user', authenticateToken, async (req, res) => {
  try {
    console.log('Fetching activities for user:', req.user.id);
    
    // First check if the user exists
    const user = await User.findByPk(req.user.id);
    if (!user) {
      console.error('User not found:', req.user.id);
      return res.status(404).json({ success: false, message: 'User not found' });
    }

    // Test the Activity model directly
    const testActivity = await Activity.findOne({
      where: { user_id: req.user.id }
    });
    console.log('Test activity query result:', testActivity ? 'Found activities' : 'No activities found');

    const activities = await ActivityService.getUserActivities(req.user.id);
    console.log('Activities fetched successfully:', activities.length);
    res.json({ success: true, activities });
  } catch (error) {
    console.error('Detailed error in fetching user activities:', {
      error: error.message,
      stack: error.stack,
      userId: req.user?.id,
      tableName: Activity.getTableName(),
      modelName: Activity.name
    });
    res.status(500).json({ 
      success: false, 
      message: 'Error fetching activities',
      error: error.message 
    });
  }
});

// Get all recent activities (for managers/admins)
router.get('/all', authenticateToken, async (req, res) => {
  try {
    // Check if user is admin/manager
    if (req.user.role !== 'manager' && req.user.role !== 'admin') {
      return res.status(403).json({ success: false, message: 'Unauthorized access' });
    }
    
    console.log('Fetching all activities');
    const activities = await ActivityService.getRecentActivities();
    console.log('All activities fetched successfully:', activities.length);
    res.json({ success: true, activities });
  } catch (error) {
    console.error('Detailed error in fetching all activities:', {
      error: error.message,
      stack: error.stack,
      userRole: req.user?.role,
      tableName: Activity.getTableName(),
      modelName: Activity.name
    });
    res.status(500).json({ 
      success: false, 
      message: 'Error fetching activities',
      error: error.message 
    });
  }
});

export default router;