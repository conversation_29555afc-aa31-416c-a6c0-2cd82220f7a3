import express from 'express';
import jwt from 'jsonwebtoken';
import pg from 'pg';
import dotenv from 'dotenv';
import axios from 'axios';

// Load environment variables
dotenv.config();

// PostgreSQL setup
const { Pool } = pg;
const pool = new Pool({ connectionString: process.env.DATABASE_URL });

const router = express.Router();

// Middleware to authenticate token
const authenticateToken = (req, res, next) => {
    const authHeader = req.headers.authorization;
    if (!authHeader) return res.status(401).json({ error: 'No token provided.' });

    const token = authHeader.split(' ')[1];
    jwt.verify(token, process.env.JWT_SECRET, (err, decoded) => {
        if (err) return res.status(403).json({ error: 'Invalid token.' });
        req.user = decoded;
        next();
    });
};

// Tokenise a new asset using Factor Studio and store in database
router.post('/tokenise', authenticateToken, async (req, res) => {
    const { name, type, value, ownership } = req.body;

    if (!name || !type || !value || !ownership) {
        return res.status(400).json({ error: 'All fields are required.' });
    }

    // Demo Mode: Return demonstration payload if enabled
    if (process.env.DEMO_MODE === 'true') {
        const demoTokenId = `demo-${Date.now()}`;
        const demoAsset = {
            id: demoTokenId,
            name,
            type,
            value,
            ownership,
            token_id: demoTokenId,
            user_id: req.user.id,
            created_at: new Date().toISOString()
        };
        return res.status(201).json({
            message: 'Demo asset tokenised successfully.',
            meta: 'This is a demonstration payload. Real backend integrations are not available yet.',
            asset: demoAsset
        });
    }

    try {
        // Step 1: Send asset data to Factor Studio API for tokenisation
        const factorResponse = await axios.post(process.env.FACTOR_STUDIO_API_URL, {
            name,
            type,
            value,
            ownership
        }, {
            headers: { Authorization: `Bearer ${process.env.FACTOR_STUDIO_API_KEY}` }
        });

        if (!factorResponse.data.tokenId) {
            return res.status(500).json({ error: 'Tokenisation failed with Factor Studio.' });
        }

        const tokenId = factorResponse.data.tokenId;

        // Step 2: Store tokenised asset details in the database
        const result = await pool.query(
            `INSERT INTO tokenised_assets (name, type, value, ownership, token_id, user_id) 
             VALUES ($1, $2, $3, $4, $5, $6) RETURNING *`,
            [name, type, value, ownership, tokenId, req.user.id]
        );

        res.status(201).json({ message: 'Asset tokenised successfully.', asset: result.rows[0] });

    } catch (error) {
        console.error('Error tokenising asset:', error);
        res.status(500).json({ error: 'Failed to tokenise asset.' });
    }
});

router.get('/:tokenId', authenticateToken, async (req, res) => {
    const { tokenId } = req.params;

    if (!process.env.FACTOR_STUDIO_API_URL) {
        console.error('Error: FACTOR_STUDIO_API_URL is not defined.');
        return res.status(500).json({ error: 'Factor Studio API URL is not configured.' });
    }

    try {
        // Construct the valid URL
        const factorApiUrl = `${process.env.FACTOR_STUDIO_API_URL}/refresh/${tokenId}`;

        console.log(`Fetching data from Factor Studio: ${factorApiUrl}`);

        // Fetch asset details from Factor Studio
        const factorResponse = await axios.get(factorApiUrl, {
            headers: { Authorization: `Bearer ${process.env.FACTOR_STUDIO_API_KEY}` }
        });

        // Fetch local asset record from database
        const dbResponse = await pool.query(
            'SELECT * FROM tokenised_assets WHERE token_id = $1 AND user_id = $2',
            [tokenId, req.user.id]
        );

        if (dbResponse.rows.length === 0) {
            return res.status(404).json({ error: 'Tokenised asset not found in database.' });
        }

        res.status(200).json({
            factorData: factorResponse.data,
            localData: dbResponse.rows[0]
        });

    } catch (error) {
        console.error('Error fetching tokenised asset details:', error.message);
        res.status(500).json({ error: 'Failed to fetch asset details from Factor Studio.' });
    }
});

// Get all tokenised assets
router.get('/', authenticateToken, async (req, res) => {
    try {
        const result = await pool.query(
            'SELECT * FROM tokenised_assets WHERE user_id = $1 ORDER BY created_at DESC',
            [req.user.id]
        );

        if (result.rows.length === 0) {
            return res.status(404).json({ error: 'No tokenised assets found.' });
        }

        res.status(200).json({ assets: result.rows });
    } catch (error) {
        console.error('Error fetching tokenised assets:', error);
        res.status(500).json({ error: 'Failed to fetch tokenised assets.' });
    }
});

// **New Route: Refresh Dashboard Data**
router.get('/refresh', authenticateToken, async (req, res) => {
    try {
        const userId = req.user.id;
        console.log(`Refreshing dashboard for user: ${userId}`);

        // Fetch latest tokenised assets (limit to 5)
        let assets = [];
        try {
            const assetsResult = await pool.query(
                'SELECT * FROM tokenised_assets WHERE user_id = $1 ORDER BY created_at DESC LIMIT 5',
                [userId]
            );
            assets = assetsResult.rows || [];
        } catch (error) {
            console.warn("Warning: Could not fetch assets.", error.message);
        }

        // Fetch asset summary statistics
        let assetStats = { total_assets: 0, total_value: 0 };
        try {
            const statsResult = await pool.query(
                `SELECT COUNT(*) AS total_assets, 
                        COALESCE(SUM(value), 0) AS total_value
                 FROM tokenised_assets 
                 WHERE user_id = $1`,
                [userId]
            );
            assetStats = statsResult.rows.length > 0 ? statsResult.rows[0] : assetStats;
        } catch (error) {
            console.warn("Warning: Could not fetch asset stats.", error.message);
        }

        // Fetch latest transactions, handle case where table is empty
        let transactions = [];
        try {
            const transactionsResult = await pool.query(
                'SELECT * FROM transactions WHERE user_id = $1 ORDER BY created_at DESC LIMIT 5',
                [userId]
            );
            transactions = transactionsResult.rows || [];
        } catch (error) {
            console.warn('Warning: Could not fetch transactions.', error.message);
        }

        // Return successful response even if some queries failed
        res.status(200).json({
            message: "Dashboard refreshed successfully.",
            assets: assets,
            stats: assetStats,
            transactions: transactions
        });

    } catch (error) {
        console.error('Error refreshing dashboard:', error.message);
        res.status(200).json({
            message: "Dashboard refresh encountered an issue.",
            assets: [],
            stats: { total_assets: 0, total_value: 0 },
            transactions: []
        });
    }
});

router.get('/performance', authenticateToken, async (req, res) => {
    try {
        const userId = req.user.id;

        // Fetch aggregated data grouped by month
        const performanceData = await pool.query(
            `SELECT DATE_TRUNC('month', created_at) AS month,
                    SUM(CASE WHEN type = 'income' THEN amount ELSE 0 END) AS total_income,
                    SUM(CASE WHEN type = 'expense' THEN amount ELSE 0 END) AS total_expense,
                    COUNT(*) AS total_transactions
             FROM transactions 
             WHERE user_id = $1
             GROUP BY month
             ORDER BY month DESC`,
            [userId]
        );

        res.status(200).json({ performance: performanceData.rows });

    } catch (error) {
        console.error('Error fetching investment performance:', error);
        res.status(500).json({ error: 'Failed to retrieve performance data.' });
    }
});

export default router;