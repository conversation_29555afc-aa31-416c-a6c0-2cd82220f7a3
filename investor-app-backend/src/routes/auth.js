// auth.js
import express from 'express';
import bcrypt from 'bcryptjs';
import jwt from 'jsonwebtoken';
import crypto from 'crypto';
import { User } from '../setup/models.js';
import { authenticateToken, magicLinkLimiter } from '../middlewares/auth.js';
import { ActivityService, ACTIVITY_TYPES } from '../services/activityService.js';
import { pool } from '../setup/databases.js';
import EmailService from '../services/emailService.js';

const router = express.Router();

// Register institutional investor
// Register user
router.post('/register', async (req, res) => {
    const { role, companyName, contactName, email, password, taxId = null, address, phone, managerId } = req.body;


    // Validate required fields
    if (!role || !contactName || !email || !password || !phone) {
        return res.status(400).json({ error: 'Missing required fields.' });
    }

    if (!['manager', 'client'].includes(role)) {
        return res.status(400).json({ error: 'Invalid role specified. Must be "client" or "manager".' });
    }

    if (role === 'manager' && (!companyName || !address)) {
        return res.status(400).json({ error: 'Company name and address are required for managers.' });
    }

    try {
        // Check if email already exists
        const existingUser = await User.findOne({ where: { email } });
        if (existingUser) {
            return res.status(400).json({ error: 'An account with this email already exists.' });
        }

        // Hash the password
        const hashedPassword = await bcrypt.hash(password, 12);

        // Create new user
        const newUser = await User.create({
            role,
            company_name: role === 'client' ? 'N/A' : companyName,
            contact_name: contactName,
            email,
            password: hashedPassword,
            tax_id: taxId,
            address: role === 'client' ? 'N/A' : address,
            phone,
            manager_id: role === 'client' && managerId ? managerId : null
        });

        // Generate JWT Token
        const token = jwt.sign(
            { userId: newUser.id, role: newUser.role },
            process.env.JWT_SECRET,
            { expiresIn: '2h' }
        );

        res.status(201).json({ token, user: newUser });

    } catch (err) {
        res.status(500).json({ error: 'Database error while registering user.' });
    }
});

// Login route
router.post('/login', async (req, res) => {
    try {
        const { email, password, rememberMe } = req.body;
        const user = await User.findOne({ where: { email } });

        if (!user) {
            return res.status(401).json({ success: false, message: 'Invalid credentials' });
        }

        const validPassword = await bcrypt.compare(password, user.password);
        if (!validPassword) {
            return res.status(401).json({ success: false, message: 'Invalid credentials' });
        }

        // Set token expiration based on remember me flag
        const tokenExpiry = rememberMe ? '24h' : '2h';
        const token = jwt.sign(
            {
                id: user.id,
                email: user.email,
                role: user.role,
                rememberMe: !!rememberMe
            },
            process.env.JWT_SECRET,
            { expiresIn: tokenExpiry }
        );

        // Log login activity
        await ActivityService.logActivity(
            user.id,
            ACTIVITY_TYPES.LOGIN,
            'User logged in successfully',
            { email: user.email, rememberMe: !!rememberMe }
        );

        res.json({
            success: true,
            token,
            user: {
                id: user.id,
                email: user.email,
                role: user.role,
                contact_name: user.contact_name
            },
            expiresIn: tokenExpiry
        });
    } catch (error) {
        res.status(500).json({ success: false, message: 'Error during login' });
    }
});

// Request magic link
router.post('/magic-link/request', magicLinkLimiter, async (req, res) => {
    try {
        const { email } = req.body;
        const user = await User.findOne({ where: { email } });

        if (!user) {
            return res.status(404).json({
                success: false,
                message: 'If an account exists with this email, a magic link will be sent.'
            });
        }

        // Check for recent magic link requests
        const recentRequest = await pool.query(
            'SELECT created_at FROM magic_link_tokens WHERE user_id = $1 AND created_at > NOW() - INTERVAL \'5 minutes\' ORDER BY created_at DESC LIMIT 1',
            [user.id]
        );

        if (recentRequest.rows.length > 0) {
            const timeLeft = Math.ceil((new Date(recentRequest.rows[0].created_at) + 5 * 60 * 1000 - new Date()) / 1000);
            return res.status(429).json({
                success: false,
                message: `Please wait ${timeLeft} seconds before requesting another magic link.`,
                timeLeft
            });
        }

        // Generate a secure random token
        const token = crypto.randomBytes(32).toString('hex');
        const expiry = new Date(Date.now() + 15 * 60 * 1000); // 15 minutes

        // Store the token in the database
        await pool.query(
            'INSERT INTO magic_link_tokens (user_id, token, expiry) VALUES ($1, $2, $3)',
            [user.id, token, expiry]
        );

        // Create magic link URL
        const magicLink = `${process.env.FRONTEND_URL}/auth/magic-link?token=${token}`;

        // Send email with magic link using EmailService
        await EmailService.sendMagicLink(user.email, magicLink);

        // Log magic link request activity
        await ActivityService.logActivity(
            user.id,
            ACTIVITY_TYPES.MAGIC_LINK_REQUEST,
            'Magic link requested',
            { email: user.email }
        );

        res.json({
            success: true,
            message: 'If an account exists with this email, a magic link will be sent.',
            cooldown: 300 // 5 minutes in seconds
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            message: 'Error sending magic link'
        });
    }
});

// Validate magic link
router.post('/magic-link/validate', async (req, res) => {
    try {
        const { token } = req.body;

        // Find the token in the database
        const result = await pool.query(
            `SELECT ml.*, u.email, u.role, u.contact_name
             FROM magic_link_tokens ml
             JOIN institutional_investors u ON ml.user_id = u.id
             WHERE ml.token = $1 AND ml.used = FALSE AND ml.expiry > NOW()`,
            [token]
        );

        if (result.rows.length === 0) {
            return res.status(400).json({
                success: false,
                message: 'Invalid or expired magic link. Please request a new one.'
            });
        }

        const magicLink = result.rows[0];
        const user = {
            id: magicLink.user_id,
            email: magicLink.email,
            role: magicLink.role,
            contact_name: magicLink.contact_name
        };

        // Mark token as used
        await pool.query(
            'UPDATE magic_link_tokens SET used = TRUE WHERE id = $1',
            [magicLink.id]
        );

        // Generate JWT token with rememberMe set to true for magic link logins
        // This ensures the session lasts for 24 hours
        const jwtToken = jwt.sign(
            {
                id: user.id,
                email: user.email,
                role: user.role,
                rememberMe: true
            },
            process.env.JWT_SECRET,
            { expiresIn: '24h' }
        );

        // Log successful magic link login
        await ActivityService.logActivity(
            user.id,
            ACTIVITY_TYPES.LOGIN,
            'User logged in via magic link',
            { email: user.email }
        );

        res.json({
            success: true,
            token: jwtToken,
            user
        });
    } catch (error) {
        res.status(500).json({ success: false, message: 'Error validating magic link' });
    }
});

// Logout route
router.post('/logout', authenticateToken, async (req, res) => {
    try {
        // Log logout activity
        await ActivityService.logActivity(
            req.user.id,
            ACTIVITY_TYPES.LOGOUT,
            'User logged out',
            { email: req.user.email }
        );

        res.json({ success: true, message: 'Logged out successfully' });
    } catch (error) {
        res.status(500).json({ success: false, message: 'Error during logout' });
    }
});

// Password Reset Request
router.post('/password/reset/request', async (req, res) => {
    const { email } = req.body;
    if (!email) return res.status(400).json({ error: 'Email is required.' });

    try {
        const user = await User.findOne({ where: { email } });
        if (!user) return res.status(404).json({ error: 'User not found.' });

        const resetToken = crypto.randomBytes(32).toString('hex');
        const hashedToken = crypto.createHash('sha256').update(resetToken).digest('hex');
        const expiry = new Date(Date.now() + 15 * 60 * 1000); // 15 minutes expiry

        await pool.query('INSERT INTO password_resets (user_id, token, expiry) VALUES ($1, $2, $3)',
            [user.id, hashedToken, expiry]);

        const resetURL = `${process.env.FRONTEND_URL}/password-reset/${resetToken}`;
        await EmailService.sendPasswordReset(email, resetURL);

        res.status(200).json({ message: 'Password reset link sent.' });
    } catch (err) {
        console.error(err);
        res.status(500).json({ error: 'Server error.' });
    }
});

// Password Reset Confirmation
router.post('/password/reset/confirm', async (req, res) => {
    const { token, newPassword } = req.body;
    if (!token || !newPassword) return res.status(400).json({ error: 'Token and new password are required.' });

    try {
        const hashedToken = crypto.createHash('sha256').update(token).digest('hex');
        const result = await pool.query('SELECT user_id FROM password_resets WHERE token = $1 AND expiry > NOW()', [hashedToken]);

        if (result.rows.length === 0) return res.status(400).json({ error: 'Invalid or expired token.' });

        const userId = result.rows[0].user_id;
        const hashedPassword = await bcrypt.hash(newPassword, 12);

        await User.update(
            { password: hashedPassword },
            { where: { id: userId } }
        );

        await pool.query('DELETE FROM password_resets WHERE user_id = $1', [userId]);

        res.status(200).json({ message: 'Password updated successfully.' });
    } catch (err) {
        console.error(err);
        res.status(500).json({ error: 'Server error' });
    }
});

// Check session validity
router.get('/check-session', authenticateToken, async (req, res) => {
    try {
        // If the authenticateToken middleware passes, the token is valid
        // and the user exists in the database

        // Check if we need to manually refresh the token
        // This is a backup in case the middleware didn't refresh it
        const authHeader = req.headers.authorization;
        if (authHeader) {
            const token = authHeader.split(' ')[1];
            const decoded = jwt.verify(token, process.env.JWT_SECRET);

            // If this is a rememberMe session and no new token was set yet, consider refreshing
            if (decoded.rememberMe && !res.getHeader('X-New-Token')) {
                const tokenAge = Math.floor(Date.now() / 1000) - decoded.iat;
                // If token is older than 12 hours, refresh it
                if (tokenAge > 12 * 60 * 60) {
                    const newToken = jwt.sign(
                        {
                            id: req.user.id,
                            email: req.user.email,
                            role: req.user.role,
                            rememberMe: true
                        },
                        process.env.JWT_SECRET,
                        { expiresIn: '24h' }
                    );

                    res.setHeader('X-New-Token', newToken);
                }
            }
        }

        res.json({
            valid: true,
            user: {
                id: req.user.id,
                email: req.user.email,
                role: req.user.role
            }
        });
    } catch (error) {
        res.status(500).json({ valid: false, message: 'Error checking session' });
    }
});

// Extend session endpoint
router.post('/extend-session', authenticateToken, async (req, res) => {
    try {
        // Generate a new token with 24-hour expiry
        const newToken = jwt.sign(
            {
                id: req.user.id,
                email: req.user.email,
                role: req.user.role,
                rememberMe: true
            },
            process.env.JWT_SECRET,
            { expiresIn: '24h' }
        );

        // Log session extension activity
        await ActivityService.logActivity(
            req.user.id,
            ACTIVITY_TYPES.LOGIN,
            'User extended session for 24 hours',
            { email: req.user.email }
        );


        // Return the new token
        res.json({
            success: true,
            token: newToken,
            message: 'Session extended for 24 hours'
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            message: 'Error extending session'
        });
    }
});

export default router;