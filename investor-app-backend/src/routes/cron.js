import express from 'express';
import { fetchExchangeRates } from '../services/exchangeRateService.js';

const router = express.Router();

router.get('/update-exchange-rates', async (req, res) => {
    try {
        await fetchExchangeRates();
        res.status(200).json({ message: "Exchange rates updated successfully" });
    } catch (error) {
        console.error("[EXCHANGE] Error fetching exchange rates:", error);
        res.status(500).json({ error: "Failed to update exchange rates" });
    }
});

export default router;