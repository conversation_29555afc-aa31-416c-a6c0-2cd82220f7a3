import express from 'express';
const fetch = globalThis.fetch;
import { pool } from '../setup/databases.js';

const router = express.Router();

// Get exchange rates for a specific base currency
router.get('/:base', async (req, res) => {
    const baseCurrency = req.params.base.toUpperCase();

    try {
        const result = await pool.query(
            `SELECT target_currency, rate, timestamp 
             FROM exchange_rates 
             WHERE base_currency = $1 
             ORDER BY target_currency`,
            [baseCurrency]
        );

        if (result.rows.length === 0) {
            return res.status(404).json({ error: 'No exchange rates found for this base currency' });
        }

        res.json({ baseCurrency, rates: result.rows });
    } catch (err) {
        console.error('[EXCHANGE] Database error:', err);
        res.status(500).json({ error: 'Database error retrieving exchange rates' });
    }
});

export default router;