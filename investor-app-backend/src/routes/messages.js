import express from 'express';
import { authenticateToken, requireManager, requireClient } from '../middlewares/auth.js';
import { pool } from '../setup/databases.js';
import { logger } from '../utils/logger.js';
import multer from 'multer';
import cloudinary from '../cloudinary/cloudinaryConfig.js';
import { SensitiveInfoDetector } from '../utils/sensitiveInfoDetector.js';
import { Readable } from 'stream';

const router = express.Router();

// Configure multer for memory storage
const storage = multer.memoryStorage();
const upload = multer({
  storage: storage,
  limits: {
    fileSize: 10 * 1024 * 1024, // 10MB limit
    files: 5 // Maximum 5 files per message
  }
});

/**
 * @route GET /messages
 * @description Get messages for the logged-in user (inbox)
 * @access Private
 */
router.get('/', authenticateToken, async (req, res) => {
  try {
    const userId = req.user.id;
    const { folder = 'inbox', page = 1, limit = 20 } = req.query;
    const offset = (page - 1) * limit;
    
    let query, params;
    
    if (folder === 'inbox') {
      // Get messages where user is a recipient
      query = `
        SELECT m.*, mr.status, mr.id as recipient_id,
               sender.contact_name as sender_name, sender.email as sender_email,
               (SELECT COUNT(*) FROM message_attachments WHERE message_id = m.id) as attachment_count
        FROM messages m
        JOIN message_recipients mr ON m.id = mr.message_id
        JOIN institutional_investors sender ON m.sender_id = sender.id
        WHERE mr.recipient_id = $1
        ORDER BY m.created_at DESC
        LIMIT $2 OFFSET $3
      `;
      params = [userId, limit, offset];
    } else if (folder === 'sent') {
      // Get messages sent by the user
      query = `
        SELECT m.*, 
               (SELECT COUNT(*) FROM message_recipients WHERE message_id = m.id) as recipient_count,
               (SELECT COUNT(*) FROM message_attachments WHERE message_id = m.id) as attachment_count
        FROM messages m
        WHERE m.sender_id = $1
        ORDER BY m.created_at DESC
        LIMIT $2 OFFSET $3
      `;
      params = [userId, limit, offset];
    } else {
      return res.status(400).json({ error: 'Invalid folder specified' });
    }
    
    const result = await pool.query(query, params);
    
    // Get total count for pagination
    const countQuery = folder === 'inbox'
      ? 'SELECT COUNT(*) FROM message_recipients WHERE recipient_id = $1'
      : 'SELECT COUNT(*) FROM messages WHERE sender_id = $1';
    
    const countResult = await pool.query(countQuery, [userId]);
    const totalCount = parseInt(countResult.rows[0].count);
    
    res.json({
      messages: result.rows,
      pagination: {
        total: totalCount,
        page: parseInt(page),
        limit: parseInt(limit),
        pages: Math.ceil(totalCount / limit)
      }
    });
  } catch (error) {
    logger.error('Error fetching messages:', error);
    res.status(500).json({ error: 'Failed to fetch messages' });
  }
});

/**
 * @route GET /messages/action/:id
 * @description Get a specific message by ID
 * @access Private
 */
router.get('/action/:id', authenticateToken, async (req, res) => {
  try {
    const { id } = req.params;
    const userId = req.user.id;
    
    // Get message with sender and recipient details
    const messageQuery = `
      SELECT m.*, 
             sender.contact_name as sender_name, sender.email as sender_email,
             sender.role as sender_role, sender.profile_image as sender_profile_image,
             CASE 
               WHEN m.sender_id = $1 THEN true
               ELSE false
             END as is_sender
      FROM messages m
      JOIN institutional_investors sender ON m.sender_id = sender.id
      WHERE m.id = $2
    `;
    
    const messageResult = await pool.query(messageQuery, [userId, id]);
    
    if (messageResult.rows.length === 0) {
      return res.status(404).json({ error: 'Message not found' });
    }
    
    const message = messageResult.rows[0];
    
    // Check if user has access to this message (either sender or recipient)
    if (message.sender_id !== userId) {
      const accessCheck = await pool.query(
        'SELECT id FROM message_recipients WHERE message_id = $1 AND recipient_id = $2',
        [id, userId]
      );
      
      if (accessCheck.rows.length === 0) {
        return res.status(403).json({ error: 'You do not have permission to view this message' });
      }
      
      // If user is recipient and viewing the message, mark it as read if it's not already
      if (accessCheck.rows.length > 0) {
        await pool.query(
          'UPDATE message_recipients SET status = $1, updated_at = NOW() WHERE id = $2 AND status = $3',
          ['read', accessCheck.rows[0].id, 'unread']
        );
      }
    }
    
    // Get recipients
    const recipientsQuery = `
      SELECT mr.id, mr.status, mr.recipient_id,
             u.contact_name, u.email, u.role, u.profile_image
      FROM message_recipients mr
      JOIN institutional_investors u ON mr.recipient_id = u.id
      WHERE mr.message_id = $1
    `;
    
    const recipientsResult = await pool.query(recipientsQuery, [id]);
    
    // Get attachments
    const attachmentsQuery = `
      SELECT id, file_name, file_url, file_type, file_size, created_at
      FROM message_attachments
      WHERE message_id = $1
    `;
    
    const attachmentsResult = await pool.query(attachmentsQuery, [id]);
    
    // Combine all data
    const messageData = {
      ...message,
      recipients: recipientsResult.rows,
      attachments: attachmentsResult.rows
    };
    
    res.json(messageData);
  } catch (error) {
    logger.error('Error fetching message details:', error);
    res.status(500).json({ error: 'Failed to fetch message details' });
  }
});

/**
 * @route POST /messages
 * @description Send a new message
 * @access Private
 */
router.post('/', authenticateToken, upload.array('attachments', 5), async (req, res) => {
  const client = await pool.connect();
  
  try {
    await client.query('BEGIN');
    
    const { subject, body, recipientIds, isBroadcast = false } = req.body;
    const senderId = req.user.id;
    const senderRole = req.user.role;
    
    // Validate required fields
    if (!subject || !body) {
      return res.status(400).json({ error: 'Subject and body are required' });
    }
    
    // Parse recipient IDs
    let recipients = [];
    if (recipientIds) {
      try {
        recipients = Array.isArray(recipientIds) 
          ? recipientIds 
          : JSON.parse(recipientIds);
      } catch (e) {
        return res.status(400).json({ error: 'Invalid recipient format' });
      }
    }
    
    if (recipients.length === 0) {
      return res.status(400).json({ error: 'At least one recipient is required' });
    }
    
    // Check if sender is allowed to message these recipients
    if (senderRole === 'manager') {
      // Managers can only message their clients
      const clientsResult = await client.query(
        'SELECT id FROM institutional_investors WHERE manager_id = $1',
        [senderId]
      );
      
      const validClientIds = clientsResult.rows.map(row => row.id);
      
      // For broadcast messages, filter to only include valid clients
      if (isBroadcast) {
        recipients = recipients.filter(id => validClientIds.includes(parseInt(id)));
        
        if (recipients.length === 0) {
          return res.status(400).json({ error: 'No valid recipients found for broadcast message' });
        }
      } else {
        // For direct messages, check if all recipients are valid
        const invalidRecipients = recipients.filter(id => !validClientIds.includes(parseInt(id)));
        
        if (invalidRecipients.length > 0) {
          return res.status(403).json({ 
            error: 'You can only message clients linked to your account',
            invalidRecipients
          });
        }
      }
    } else if (senderRole === 'client') {
      // Clients can only message their manager
      const managerResult = await client.query(
        'SELECT manager_id FROM institutional_investors WHERE id = $1',
        [senderId]
      );
      
      if (!managerResult.rows[0] || !managerResult.rows[0].manager_id) {
        return res.status(403).json({ error: 'You do not have a manager assigned to your account' });
      }
      
      const managerId = managerResult.rows[0].manager_id;
      
      // Clients cannot send broadcast messages
      if (isBroadcast) {
        return res.status(403).json({ error: 'Clients cannot send broadcast messages' });
      }
      
      // Ensure the client is only messaging their manager
      if (recipients.length !== 1 || parseInt(recipients[0]) !== managerId) {
        return res.status(403).json({ error: 'Clients can only message their assigned manager' });
      }
    }
    
    // Check for sensitive information
    const sensitiveInfoResults = SensitiveInfoDetector.detect(body);
    const containsSensitiveInfo = sensitiveInfoResults.containsSensitiveInfo;
    
    // Create the message
    const messageResult = await client.query(
      `INSERT INTO messages 
       (sender_id, subject, body, is_broadcast, contains_sensitive_info, created_at, updated_at)
       VALUES ($1, $2, $3, $4, $5, NOW(), NOW())
       RETURNING id`,
      [senderId, subject, body, isBroadcast, containsSensitiveInfo]
    );
    
    const messageId = messageResult.rows[0].id;
    
    // Add recipients
    for (const recipientId of recipients) {
      await client.query(
        `INSERT INTO message_recipients
         (message_id, recipient_id, status, created_at, updated_at)
         VALUES ($1, $2, $3, NOW(), NOW())`,
        [messageId, recipientId, 'unread']
      );
      
      // Create notification for each recipient
      await client.query(
        `INSERT INTO notifications
         (user_id, type, message, status, metadata, created_at, updated_at)
         VALUES ($1, $2, $3, $4, $5, NOW(), NOW())`,
        [
          recipientId, 
          'new_message', 
          `You have received a new message: ${subject}`,
          'pending',
          JSON.stringify({ messageId, senderId, senderRole })
        ]
      );
    }
    
    // Handle file uploads if any
    if (req.files && req.files.length > 0) {
      for (const file of req.files) {
        try {
          // Upload file to Cloudinary
          const result = await new Promise((resolve, reject) => {
            const uploadStream = cloudinary.uploader.upload_stream(
              {
                folder: 'message_attachments',
                resource_type: 'auto',
                public_id: `msg_${messageId}_${Date.now()}_${file.originalname.replace(/[^a-zA-Z0-9]/g, '_')}`
              },
              (error, result) => {
                if (error) reject(error);
                else resolve(result);
              }
            );
            
            const stream = Readable.from(file.buffer);
            stream.pipe(uploadStream);
          });
          
          // Save attachment record
          await client.query(
            `INSERT INTO message_attachments
             (message_id, file_name, file_url, file_type, file_size, created_at)
             VALUES ($1, $2, $3, $4, $5, NOW())`,
            [
              messageId,
              file.originalname,
              result.secure_url,
              file.mimetype,
              file.size
            ]
          );
        } catch (uploadError) {
          logger.error('Error uploading file to Cloudinary:', uploadError);
          // Continue with other files even if one fails
        }
      }
    }
    
    await client.query('COMMIT');
    
    res.status(201).json({
      id: messageId,
      subject,
      recipientCount: recipients.length,
      attachmentCount: req.files ? req.files.length : 0,
      containsSensitiveInfo,
      sensitiveInfoWarnings: SensitiveInfoDetector.getWarningMessages(sensitiveInfoResults)
    });
  } catch (error) {
    await client.query('ROLLBACK');
    logger.error('Error sending message:', error);
    res.status(500).json({ error: 'Failed to send message' });
  } finally {
    client.release();
  }
});

/**
 * @route GET /messages/clients
 * @description Get list of clients that a manager can message
 * @access Private (Managers only)
 */
router.get('/clients', authenticateToken, requireManager, async (req, res) => {
  try {
    const managerId = req.user.id;
    
    const result = await pool.query(
      `SELECT id, contact_name, email, profile_image
       FROM institutional_investors
       WHERE manager_id = $1
       ORDER BY contact_name`,
      [managerId]
    );
    
    res.json(result.rows);
  } catch (error) {
    logger.error('Error fetching clients for messaging:', error);
    res.status(500).json({ error: 'Failed to fetch clients' });
  }
});

/**
 * @route GET /messages/manager
 * @description Get manager details for a client to message
 * @access Private (Clients only)
 */
router.get('/manager', authenticateToken, requireClient, async (req, res) => {
  try {
    const clientId = req.user.id;
    
    const result = await pool.query(
      `SELECT m.id, m.contact_name, m.email, m.profile_image
       FROM institutional_investors c
       JOIN institutional_investors m ON c.manager_id = m.id
       WHERE c.id = $1`,
      [clientId]
    );
    
    if (result.rows.length === 0) {
      return res.status(404).json({ error: 'No manager assigned to your account' });
    }
    
    res.json(result.rows[0]);
  } catch (error) {
    logger.error('Error fetching manager for messaging:', error);
    res.status(500).json({ error: 'Failed to fetch manager details' });
  }
});

/**
 * @route PUT /messages/action/:id/read
 * @description Mark a message as read
 * @access Private
 */
router.put('/action/:id/read', authenticateToken, async (req, res) => {
  try {
    const { id } = req.params;
    const userId = req.user.id;
    
    // Check if user is a recipient of this message
    const recipientCheck = await pool.query(
      'SELECT id FROM message_recipients WHERE message_id = $1 AND recipient_id = $2',
      [id, userId]
    );
    
    if (recipientCheck.rows.length === 0) {
      return res.status(403).json({ error: 'You are not a recipient of this message' });
    }
    
    // Update status to read
    await pool.query(
      'UPDATE message_recipients SET status = $1, updated_at = NOW() WHERE id = $2',
      ['read', recipientCheck.rows[0].id]
    );
    
    res.json({ success: true });
  } catch (error) {
    logger.error('Error marking message as read:', error);
    res.status(500).json({ error: 'Failed to update message status' });
  }
});

/**
 * @route DELETE /messages/action/:id
 * @description Archive a message (soft delete)
 * @access Private
 */
router.delete('/action/:id', authenticateToken, async (req, res) => {
  try {
    const { id } = req.params;
    const userId = req.user.id;
    
    // Check if user is sender or recipient
    const messageCheck = await pool.query(
      'SELECT sender_id FROM messages WHERE id = $1',
      [id]
    );
    
    if (messageCheck.rows.length === 0) {
      return res.status(404).json({ error: 'Message not found' });
    }
    
    const isSender = messageCheck.rows[0].sender_id === userId;
    
    if (isSender) {
      // If sender, we don't actually delete the message, just mark it as archived for the sender
      // This is a placeholder for future functionality - for now, we'll just return success
      res.json({ success: true, message: 'Message archived' });
    } else {
      // If recipient, update the recipient status to archived
      const recipientCheck = await pool.query(
        'SELECT id FROM message_recipients WHERE message_id = $1 AND recipient_id = $2',
        [id, userId]
      );
      
      if (recipientCheck.rows.length === 0) {
        return res.status(403).json({ error: 'You are not a recipient of this message' });
      }
      
      await pool.query(
        'UPDATE message_recipients SET status = $1, updated_at = NOW() WHERE id = $2',
        ['archived', recipientCheck.rows[0].id]
      );
      
      res.json({ success: true, message: 'Message archived' });
    }
  } catch (error) {
    logger.error('Error archiving message:', error);
    res.status(500).json({ error: 'Failed to archive message' });
  }
});

/**
 * @route POST /messages/check-sensitive
 * @description Check if a message contains sensitive information
 * @access Private
 */
router.post('/check-sensitive', authenticateToken, async (req, res) => {
  try {
    const { text } = req.body;
    
    if (!text) {
      return res.status(400).json({ error: 'Text content is required' });
    }
    
    const results = SensitiveInfoDetector.detect(text);
    const warnings = SensitiveInfoDetector.getWarningMessages(results);
    
    res.json({
      containsSensitiveInfo: results.containsSensitiveInfo,
      warnings,
      detectionDetails: results.detections
    });
  } catch (error) {
    logger.error('Error checking for sensitive information:', error);
    res.status(500).json({ error: 'Failed to analyze text' });
  }
});

export default router;
