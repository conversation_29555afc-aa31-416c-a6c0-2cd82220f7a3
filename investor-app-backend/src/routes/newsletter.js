import express from 'express';
import pg from 'pg';
import dotenv from 'dotenv';
import { body, validationResult } from 'express-validator';
import EmailService from '../services/emailService.js';

dotenv.config();

const { Pool } = pg;
const pool = new Pool({ connectionString: process.env.DATABASE_URL });

const router = express.Router();

// Subscribe to newsletter
router.post('/subscribe',
  body('email').isEmail().normalizeEmail(),
  async (req, res) => {
    // Validate request
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    const { email } = req.body;

    try {
      // Check if email already exists
      const existingSubscription = await pool.query(
        'SELECT * FROM newsletter_subscriptions WHERE email = $1',
        [email]
      );

      if (existingSubscription.rows.length > 0) {
        if (existingSubscription.rows[0].is_active) {
          return res.status(400).json({ 
            error: '<PERSON><PERSON> already subscribed to newsletter' 
          });
        } else {
          // Reactivate subscription
          await pool.query(
            'UPDATE newsletter_subscriptions SET is_active = true WHERE email = $1',
            [email]
          );
          // Send welcome email for reactivated subscription
          await EmailService.sendNewsletterSubscriptionConfirmation(email);
          return res.json({ 
            message: 'Newsletter subscription reactivated successfully' 
          });
        }
      }

      // Add new subscription
      await pool.query(
        'INSERT INTO newsletter_subscriptions (email) VALUES ($1)',
        [email]
      );

      // Send welcome email for new subscription
      await EmailService.sendNewsletterSubscriptionConfirmation(email);

      res.json({ message: 'Successfully subscribed to newsletter' });
    } catch (error) {
      console.error('Newsletter subscription error:', error);
      res.status(500).json({ 
        error: 'An error occurred while processing your subscription' 
      });
    }
  }
);

export default router; 