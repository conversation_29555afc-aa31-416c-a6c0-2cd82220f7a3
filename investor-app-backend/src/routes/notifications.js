import express from 'express';
import pg from 'pg';
import dotenv from 'dotenv';
import { authenticateToken } from '../middlewares/auth.js';

dotenv.config();

const router = express.Router();
const { Pool } = pg;
const pool = new Pool({ connectionString: process.env.DATABASE_URL });

// ✅ **Fetch Notifications for Logged-in User**
router.get('/', authenticateToken, async (req, res) => {
    try {
        if (!req.user || !req.user.id) {
            return res.status(403).json({ error: "Invalid session. User ID missing in token." });
        }

        const userId = req.user.id;
        const includeIgnored = req.query.includeIgnored === 'true';
        const autoMarkAsRead = req.query.autoMarkAsRead !== 'false'; // Default to true if not specified

        const query = includeIgnored
            ? "SELECT * FROM notifications WHERE user_id = $1 ORDER BY created_at DESC"
            : "SELECT * FROM notifications WHERE status != 'ignored' AND user_id = $1 ORDER BY created_at DESC";

        const result = await pool.query(query, [userId]);

        // Auto-mark notifications without action options as read
        if (autoMarkAsRead) {
            const notificationsToMark = result.rows
                .filter(notification =>
                    notification.status === 'pending' &&
                    !['link_request', 'approval_request', 'action_required'].includes(notification.type)
                )
                .map(notification => notification.id);

            if (notificationsToMark.length > 0) {
                await pool.query(
                    `UPDATE notifications SET status = 'read' WHERE id = ANY($1) AND user_id = $2`,
                    [notificationsToMark, userId]
                );

                // Refresh the notifications after marking some as read
                const updatedResult = await pool.query(query, [userId]);
                res.json({ notifications: updatedResult.rows });
                return;
            }
        }

        res.json({ notifications: result.rows });
    } catch (error) {
        res.status(500).json({ error: 'Internal server error' });
    }
});

// ✅ **Mark Multiple Notifications as Read**
router.put('/batch/mark-as-read', authenticateToken, async (req, res) => {
    try {
        const { ids } = req.body;
        const userId = req.user.id;

        if (!ids || !Array.isArray(ids) || ids.length === 0) {
            return res.status(400).json({ error: "Invalid request: 'ids' must be a non-empty array." });
        }


        // Ensure all notifications belong to the user
        const result = await pool.query(
            'SELECT COUNT(*) FROM notifications WHERE id = ANY($1) AND user_id = $2',
            [ids, userId]
        );

        if (parseInt(result.rows[0].count) !== ids.length) {
            return res.status(403).json({
                error: "Unauthorized: One or more notifications do not belong to the user."
            });
        }

        // ✅ **Update Notification Status for all IDs**
        await pool.query(
            `UPDATE notifications SET status = 'read' WHERE id = ANY($1) AND user_id = $2`,
            [ids, userId]
        );

        res.json({ message: `${ids.length} notifications marked as read` });
    } catch (error) {
        res.status(500).json({ error: "Internal server error" });
    }
});

// ✅ **Mark Notification as Read or Ignored**
router.put('/:id', authenticateToken, async (req, res) => {
    try {
        const { id } = req.params;
        const { status } = req.body;
        const userId = req.user.id;


        if (!['read', 'ignored'].includes(status)) {
            return res.status(400).json({ error: "Invalid status value." });
        }

        // Ensure the notification belongs to the user
        const result = await pool.query(
            'SELECT * FROM notifications WHERE id = $1 AND user_id = $2',
            [id, userId]
        );

        if (result.rowCount === 0) {
            return res.status(403).json({ error: "Unauthorized: Notification does not belong to the user." });
        }

        // ✅ **Update Notification Status**
        await pool.query(
            `UPDATE notifications SET status = $1 WHERE id = $2`,
            [status, id]
        );

        res.json({ message: `Notification marked as ${status}` });
    } catch (error) {
        res.status(500).json({ error: "Internal server error" });
    }
});

// ✅ **Approve a Link Request**
router.post('/confirm-link', authenticateToken, async (req, res) => {
    try {
        const { notificationId } = req.body;
        const clientId = req.user.id;

        // Check if notification exists
        const notificationCheck = await pool.query(
            `SELECT * FROM notifications WHERE id = $1 AND user_id = $2 AND type = 'link_request' AND status = 'pending'`,
            [notificationId, clientId]
        );

        if (notificationCheck.rows.length === 0) {
            return res.status(400).json({ error: 'Invalid or expired link request.' });
        }

        const managerId = notificationCheck.rows[0].sender_id;

        // ✅ **Link Client to Manager**
        await pool.query(
            `UPDATE institutional_investors SET manager_id = $1 WHERE id = $2`,
            [managerId, clientId]
        );

        // ✅ **Mark Notification as Approved**
        await pool.query(
            `UPDATE notifications SET status = 'approved' WHERE id = $1`,
            [notificationId]
        );

        // ✅ **Send Notification to Manager**
        await pool.query(
            `INSERT INTO notifications (user_id, type, message, status)
             VALUES ($1, 'approval', 'A client has accepted your link request.', 'pending')`,
            [managerId]
        );

        res.json({ message: 'Client successfully linked to manager.' });
    } catch (error) {
        res.status(500).json({ error: 'Internal server error' });
    }
});

// ✅ **Ignore a Link Request**
router.post('/ignore-link', authenticateToken, async (req, res) => {
    try {
        const { notificationId } = req.body;
        const clientId = req.user.id;

        if (!clientId) {
            return res.status(401).json({ error: "Unauthorized: No valid user session." });
        }


        // Ensure notification exists
        const notificationCheck = await pool.query(
            `SELECT * FROM notifications WHERE id = $1 AND user_id = $2 AND type = 'link_request' AND status = 'pending'`,
            [notificationId, clientId]
        );

        if (notificationCheck.rows.length === 0) {
            return res.status(400).json({ error: "Invalid or expired link request." });
        }

        // ✅ Mark the notification as "ignored"
        await pool.query(`UPDATE notifications SET status = 'ignored' WHERE id = $1`, [notificationId]);

        res.json({ message: `Link request ${notificationId} ignored successfully.` });
    } catch (error) {
        res.status(500).json({ error: "Internal server error." });
    }
});

export default router;