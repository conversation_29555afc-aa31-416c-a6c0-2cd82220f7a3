// profile.js
import express from 'express';
import bcrypt from 'bcryptjs';
import { authenticateToken } from '../middlewares/auth.js';
import multer from 'multer';
import cloudinary from '../cloudinary/cloudinaryConfig.js';
import { pool } from '../setup/databases.js';

const router = express.Router();
// Configure multer storage in memory
const storage = multer.memoryStorage();
const upload = multer({ storage });

// ✅ Fetch user profile (Refactored)
router.get('/', authenticateToken, async (req, res) => {
    try {
      console.log(`[PROFILE] Fetching profile for user ID: ${req.user.id}`);
  
      const query = `
        SELECT 
          ii.id, 
          ii.company_name, 
          ii.contact_name, 
          ii.email, 
          ii.tax_id, 
          ii.address, 
          ii.phone, 
          ii.role, 
          ii.membership, 
          ii.profile_image, 
          ii.manager_id,
          m.company_name AS manager_company_name, 
          m.contact_name AS manager_contact_name, 
          m.email AS manager_email, 
          m.profile_image AS manager_profile_image
        FROM institutional_investors ii
        LEFT JOIN institutional_investors m ON ii.manager_id = m.id
        WHERE ii.id = $1
      `;
  
      const result = await pool.query(query, [req.user.id]);
  
      if (result.rows.length === 0) {
        console.warn(`[PROFILE] User not found: ${req.user.id}`);
        return res.status(404).json({ error: 'User not found' });
      }
  
      res.json({ user: result.rows[0] });
    } catch (err) {
      console.error('[PROFILE] Database error while fetching profile:', err);
      res.status(500).json({ error: 'Database error while fetching profile.' });
    }
  });

// ✅ Update user profile (Refactored)
router.put('/', authenticateToken, async (req, res) => {
    const { company_name, contact_name, email, tax_id, address, phone } = req.body;

    if (!company_name && !contact_name && !email && !tax_id && !address && !phone) {
        return res.status(400).json({ error: 'At least one field must be provided for update.' });
    }

    try {
        if (email) {
            const emailCheck = await pool.query(
                'SELECT id FROM institutional_investors WHERE email = $1 AND id != $2',
                [email, req.user.id]
            );
            if (emailCheck.rows.length > 0) {
                return res.status(409).json({ error: 'Email already in use.' });
            }
        }

        // ✅ Build dynamic update query
        const updateFields = [];
        const updateValues = [];
        let index = 1;

        if (company_name) { updateFields.push(`company_name = $${index++}`); updateValues.push(company_name); }
        if (contact_name) { updateFields.push(`contact_name = $${index++}`); updateValues.push(contact_name); }
        if (email) { updateFields.push(`email = $${index++}`); updateValues.push(email); }
        if (tax_id) { updateFields.push(`tax_id = $${index++}`); updateValues.push(tax_id); }
        if (address) { updateFields.push(`address = $${index++}`); updateValues.push(address); }
        if (phone) { updateFields.push(`phone = $${index++}`); updateValues.push(phone); }

        updateValues.push(req.user.id);

        const query = `
            UPDATE institutional_investors 
            SET ${updateFields.join(', ')}
            WHERE id = $${index} 
            RETURNING id, company_name, contact_name, email, tax_id, address, phone`;

        const result = await pool.query(query, updateValues);

        if (result.rows.length === 0) {
            console.warn(`[PROFILE] Update failed: User not found for ID ${req.user.id}`);
            return res.status(404).json({ error: 'User not found' });
        }

        console.log('[PROFILE] Profile updated successfully:', result.rows[0]);

        res.json({ message: 'Profile updated successfully.', user: result.rows[0] });
    } catch (err) {
        console.error('[PROFILE] Database error while updating profile:', err);
        res.status(500).json({ error: 'Database error while updating profile.' });
    }
});

// POST /profile/upload - Upload a profile picture
router.post('/upload', authenticateToken, upload.single('profileImage'), async (req, res) => {
    try {
      // Check if file exists
      if (!req.file) {
        return res.status(400).json({ error: 'No file uploaded.' });
      }
      
      // Upload file to Cloudinary using upload_stream
      const stream = cloudinary.uploader.upload_stream(
        { folder: 'profile_pictures', public_id: `user_${req.user.id}_${Date.now()}` },
        async (error, result) => {
          if (error) {
            console.error('Cloudinary upload error:', error);
            return res.status(500).json({ error: 'Cloudinary upload failed.' });
          }
  
          // Update the user's profile_image column with the new image URL
          try {
            const updateResult = await pool.query(
              `UPDATE institutional_investors SET profile_image = $1 WHERE id = $2 RETURNING profile_image`,
              [result.secure_url, req.user.id]
            );
            if (updateResult.rows.length === 0) {
              return res.status(404).json({ error: 'User not found while updating profile image.' });
            }
            return res.status(200).json({ message: 'Upload successful.', imageUrl: updateResult.rows[0].profile_image });
          } catch (dbError) {
            console.error('Database error updating profile image:', dbError);
            return res.status(500).json({ error: 'Database error while updating profile image.' });
          }
        }
      );
    
      // Write the file buffer to the Cloudinary stream
      stream.end(req.file.buffer);
    
    } catch (err) {
      console.error('Profile picture upload error:', err);
      res.status(500).json({ error: 'An error occurred while uploading the image.' });
    }
});

export default router;