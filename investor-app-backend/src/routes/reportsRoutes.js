import express from 'express';
import { authenticateToken } from '../middlewares/auth.js';
import { 
  getAssetReports, 
  getAssetStatistics, 
  getBalanceHistory,
  getAssetDetails,
  exportReport
} from '../controllers/reportsController.js';

const router = express.Router();

// All routes require authentication
router.use(authenticateToken);

// Get asset reports with filtering options
router.get('/assets', getAssetReports);

// Get detailed asset information
router.get('/assets/:assetId', getAssetDetails);

// Get asset statistics and aggregations
router.get('/statistics', getAssetStatistics);

// Get balance history for the authenticated user
router.get('/balance-history', getBalanceHistory);

// Export reports
router.post('/export', exportReport);

export default router; 