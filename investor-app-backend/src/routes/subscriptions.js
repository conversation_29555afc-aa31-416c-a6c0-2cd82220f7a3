// subscriptions.js
import express from 'express';
import jwt from 'jsonwebtoken';
import bcrypt from 'bcryptjs';
import dotenv from 'dotenv';
import pg from 'pg';

// Initialize environment variables
dotenv.config();

// PostgreSQL setup
const { Pool } = pg;
const pool = new Pool({ connectionString: process.env.DATABASE_URL });

const router = express.Router();

// Middleware to authenticate token
const authenticateToken = (req, res, next) => {
    const authHeader = req.headers.authorization;
    if (!authHeader) return res.status(401).json({ error: 'No token provided.' });

    const token = authHeader.split(' ')[1];
    jwt.verify(token, process.env.JWT_SECRET, (err, decoded) => {
        if (err) return res.status(403).json({ error: 'Invalid token.' });
        req.user = decoded;
        next();
    });
};

// **Create a New Subscription**
router.post('/create', authenticateToken, async (req, res) => {
    const { paymentMethod, planId } = req.body;

    if (!paymentMethod || !planId) {
        return res.status(400).json({ error: 'Payment method and plan ID are required.' });
    }

    try {
        let subscriptionId = null;
        let paymentStatus = 'active';

        if (paymentMethod === 'stripe') {
            // Check if user already has a Stripe customer ID
            const userResult = await pool.query('SELECT stripe_customer_id FROM institutional_investors WHERE id = $1', [req.user.id]);
            let stripeCustomerId = userResult.rows[0]?.stripe_customer_id;

            if (!stripeCustomerId) {
                const customer = await stripe.customers.create({
                    email: req.user.email,
                    name: req.user.name,
                });
                stripeCustomerId = customer.id;

                // Store customer ID in database
                await pool.query('UPDATE institutional_investors SET stripe_customer_id = $1 WHERE id = $2', [stripeCustomerId, req.user.id]);
            }

            // Create Stripe Subscription
            const subscription = await stripe.subscriptions.create({
                customer: stripeCustomerId,
                items: [{ price: planId }],
                payment_behavior: 'default_incomplete',
                expand: ['latest_invoice.payment_intent'],
            });

            subscriptionId = subscription.id;
            paymentStatus = subscription.status;
        } else {
            return res.status(400).json({ error: 'Unsupported payment method.' });
        }

        // Store subscription in DB
        await pool.query(
            'INSERT INTO subscriptions (user_id, payment_method, stripe_subscription_id, status) VALUES ($1, $2, $3, $4)',
            [req.user.id, paymentMethod, subscriptionId, paymentStatus]
        );

        res.status(200).json({
            message: 'Subscription created successfully.',
            subscriptionId,
            status: paymentStatus,
        });
    } catch (error) {
        console.error('Error creating subscription:', error);
        res.status(500).json({ error: 'Server error. Please try again later.' });
    }
});

// **Get User's Active Subscription**
router.get('/', authenticateToken, async (req, res) => {
    try {
        const result = await pool.query(
            'SELECT * FROM subscriptions WHERE user_id = $1 AND status IN ($2, $3)',
            [req.user.id, 'active', 'trialing']
        );

        if (result.rows.length === 0) {
            return res.status(404).json({ error: 'No active subscription found.' });
        }

        res.status(200).json(result.rows[0]);
    } catch (error) {
        console.error('Error retrieving subscription:', error);
        res.status(500).json({ error: 'Server error. Please try again later.' });
    }
});

// **Cancel Subscription**
router.post('/cancel', authenticateToken, async (req, res) => {
    const { subscriptionId } = req.body;

    if (!subscriptionId) {
        return res.status(400).json({ error: 'Subscription ID is required.' });
    }

    try {
        await stripe.subscriptions.del(subscriptionId);

        await pool.query(
            'UPDATE subscriptions SET status = $1 WHERE stripe_subscription_id = $2 AND user_id = $3',
            ['cancelled', subscriptionId, req.user.id]
        );

        res.status(200).json({ message: 'Subscription cancelled successfully.' });
    } catch (error) {
        console.error('Error cancelling subscription:', error);
        res.status(500).json({ error: 'Server error. Please try again later.' });
    }
});

// **Upgrade or Downgrade Subscription**
router.post('/upgrade', authenticateToken, async (req, res) => {
    const { subscriptionId, newPlanId } = req.body;

    if (!subscriptionId || !newPlanId) {
        return res.status(400).json({ error: 'Subscription ID and new plan ID are required.' });
    }

    try {
        const subscription = await stripe.subscriptions.retrieve(subscriptionId);

        const updatedSubscription = await stripe.subscriptions.update(subscriptionId, {
            cancel_at_period_end: false,
            proration_behavior: 'create_prorations',
            items: [{
                id: subscription.items.data[0].id,
                price: newPlanId,
            }],
        });

        await pool.query(
            'UPDATE subscriptions SET plan_id = $1 WHERE stripe_subscription_id = $2 AND user_id = $3',
            [newPlanId, subscriptionId, req.user.id]
        );

        res.status(200).json({ message: 'Subscription updated successfully.', updatedSubscription });
    } catch (error) {
        console.error('Error upgrading subscription:', error);
        res.status(500).json({ error: 'Server error. Please try again later.' });
    }
});

// **Check Subscription Status**
router.get('/status', authenticateToken, async (req, res) => {
    try {
        const result = await pool.query(
            'SELECT stripe_subscription_id, status FROM subscriptions WHERE user_id = $1',
            [req.user.id]
        );

        if (result.rows.length === 0) {
            return res.status(404).json({ error: 'No subscription found.' });
        }

        const subscriptionId = result.rows[0].stripe_subscription_id;
        const stripeSubscription = await stripe.subscriptions.retrieve(subscriptionId);

        res.status(200).json({
            subscriptionId,
            status: stripeSubscription.status,
        });
    } catch (error) {
        console.error('Error checking subscription status:', error);
        res.status(500).json({ error: 'Server error. Please try again later.' });
    }
});

export default router;