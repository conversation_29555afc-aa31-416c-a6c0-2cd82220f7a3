import express from 'express';
import { authenticateToken } from '../middlewares/auth.js';
import vestingService from '../services/vestingService.js';
import { logger } from '../utils/logger.js';
import { pool } from '../setup/databases.js';

const router = express.Router();

// Get user's vesting records
router.get('/user', authenticateToken, async (req, res) => {
  try {
    let vestingRecords;
    // If the logged-in user is a manager, retrieve records for all connected clients
    if (req.user.role === 'manager') {
      vestingRecords = await vestingService.getManagerVestingRecords(req.user.id);
    } else {
      // For regular users, retrieve only their own vesting records
      const userId = req.user.id;
      vestingRecords = await vestingService.getUserVestingRecords(userId);
    }

    res.json({
      success: true,
      vestingRecords
    });
  } catch (error) {
    logger.error('Error in vesting records route:', error);
    res.status(500).json({
      success: false,
      message: 'Error fetching vesting records',
      error: error.message
    });
  }
});

// Create new vesting record
router.post('/', authenticateToken, async (req, res) => {
  try {
    const { assetId, contractAddress, vestingSchedule, beneficiaries, blockchainTxHash } = req.body;
    
    const vestingRecord = await vestingService.createVestingRecord(
      assetId,
      contractAddress,
      vestingSchedule,
      beneficiaries,
      blockchainTxHash
    );
    
    res.json({
      success: true,
      vestingRecord
    });
  } catch (error) {
    logger.error('Error creating vesting record:', error);
    res.status(500).json({
      success: false,
      message: 'Error creating vesting record',
      error: error.message
    });
  }
});

// Get vesting record by ID
router.get('/:recordId', authenticateToken, async (req, res) => {
  try {
    const { recordId } = req.params;
    const vestingRecord = await vestingService.getVestingRecordById(recordId);
    
    if (!vestingRecord) {
      return res.status(404).json({
        success: false,
        message: 'Vesting record not found'
      });
    }
    
    res.json({
      success: true,
      vestingRecord
    });
  } catch (error) {
    logger.error('Error fetching vesting record:', error);
    res.status(500).json({
      success: false,
      message: 'Error fetching vesting record',
      error: error.message
    });
  }
});

// Update vesting record status
router.patch('/:recordId/status', authenticateToken, async (req, res) => {
  try {
    const { recordId } = req.params;
    const { status } = req.body;
    
    if (!['active', 'completed', 'cancelled'].includes(status)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid status. Must be one of: active, completed, cancelled'
      });
    }
    
    const vestingRecord = await vestingService.updateVestingRecordStatus(
      recordId,
      status
    );
    
    res.json({
      success: true,
      vestingRecord
    });
  } catch (error) {
    logger.error('Error updating vesting record:', error);
    res.status(500).json({
      success: false,
      message: 'Error updating vesting record',
      error: error.message
    });
  }
});

// Get vesting schedule for a specific asset
router.get("/:id/schedule", authenticateToken, async (req, res) => {
  try {
    const { id } = req.params;
    const userId = req.user.id;
    const userRole = req.user.role;

    console.log('Fetching vesting schedule:', {
      id,
      userId,
      userRole
    });

    // First, check if the vesting record exists
    const checkQuery = `
      SELECT vr.*, ta.name as asset_name
      FROM vesting_records vr
      JOIN tokenised_assets ta ON vr.asset_id = ta.id
      WHERE vr.id = $1 OR vr.asset_id = $1
    `;
    const checkResult = await pool.query(checkQuery, [id]);
    
    console.log('Checking vesting record existence:', {
      exists: checkResult.rows.length > 0,
      record: checkResult.rows[0]
    });

    if (checkResult.rows.length === 0) {
      return res.status(404).json({ 
        error: "Vesting schedule not found.",
        details: "No vesting record exists for this ID"
      });
    }

    const vestingRecord = checkResult.rows[0];
    const assetId = vestingRecord.asset_id;

    // Verify access permissions
    let query;
    let params;

    if (userRole === "manager") {
      // Manager can view schedule if they manage any of the beneficiaries
      query = `
        SELECT 
          vr.vesting_schedule,
          vr.beneficiaries,
          vr.status AS vesting_status,
          vr.contract_address,
          vr.created_at AS vesting_start_date,
          vr.updated_at AS last_updated,
          ta.name AS asset_name,
          ta.value AS asset_value,
          ta.type AS asset_type,
          beneficiary_ii.id as beneficiary_id,
          beneficiary_ii.manager_id
        FROM vesting_records vr
        JOIN tokenised_assets ta ON vr.asset_id = ta.id
        CROSS JOIN LATERAL jsonb_array_elements(vr.beneficiaries) AS b
        JOIN institutional_investors beneficiary_ii ON (b->>'beneficiary_id')::integer = beneficiary_ii.id
        WHERE vr.id = $1 AND beneficiary_ii.manager_id = $2
      `;
      params = [vestingRecord.id, userId];
    } else {
      // Client can view schedule if they are a beneficiary
      query = `
        SELECT 
          vr.vesting_schedule,
          vr.beneficiaries,
          vr.status AS vesting_status,
          vr.contract_address,
          vr.created_at AS vesting_start_date,
          vr.updated_at AS last_updated,
          ta.name AS asset_name,
          ta.value AS asset_value,
          ta.type AS asset_type
        FROM vesting_records vr
        JOIN tokenised_assets ta ON vr.asset_id = ta.id,
        jsonb_array_elements(vr.beneficiaries) AS b
        WHERE vr.id = $1 
        AND (b->>'beneficiary_id')::INTEGER = $2
      `;
      params = [vestingRecord.id, userId];
    }

    console.log('Executing query:', {
      query,
      params
    });

    const result = await pool.query(query, params);

    console.log('Query result:', {
      rowCount: result.rows.length,
      firstRow: result.rows[0]
    });

    if (result.rows.length === 0) {
      return res.status(403).json({ 
        error: "Access denied.",
        details: "You do not have permission to view this vesting schedule"
      });
    }

    const vestingData = result.rows[0];
    
    // Calculate current vesting progress
    const startDate = new Date(vestingData.vesting_start_date);
    const currentDate = new Date();
    const schedule = vestingData.vesting_schedule;
    
    const elapsedDays = Math.floor((currentDate - startDate) / (1000 * 60 * 60 * 24));
    const totalDays = schedule.duration;
    const cliffDays = schedule.cliff_period;
    
    let vestedPercentage = 0;
    if (elapsedDays >= totalDays) {
      vestedPercentage = 100;
    } else if (elapsedDays > cliffDays) {
      vestedPercentage = Math.min(100, ((elapsedDays - cliffDays) / (totalDays - cliffDays)) * 100);
    }

    // Generate chart data points
    const chartDataPoints = [];
    const stepSize = Math.max(1, Math.floor(totalDays / 20)); // Generate ~20 points for smooth curve
    
    for (let day = 0; day <= totalDays; day += stepSize) {
      let pointVestedPercentage = 0;
      if (day >= totalDays) {
        pointVestedPercentage = 100;
      } else if (day > cliffDays) {
        pointVestedPercentage = Math.min(100, ((day - cliffDays) / (totalDays - cliffDays)) * 100);
      }
      
      const pointDate = new Date(startDate);
      pointDate.setDate(pointDate.getDate() + day);
      
      chartDataPoints.push({
        date: pointDate.toISOString(),
        vested_percentage: pointVestedPercentage,
        vested_amount: (vestingData.asset_value * pointVestedPercentage) / 100
      });
    }

    // Format the response
    const response = {
      asset: {
        id: assetId,
        name: vestingData.asset_name,
        type: vestingData.asset_type,
        value: vestingData.asset_value
      },
      vesting: {
        status: vestingData.vesting_status,
        contract_address: vestingData.contract_address,
        start_date: vestingData.vesting_start_date,
        last_updated: vestingData.last_updated,
        schedule: {
          ...vestingData.vesting_schedule,
          current_progress: {
            elapsed_days: elapsedDays,
            vested_percentage: vestedPercentage,
            remaining_days: Math.max(0, totalDays - elapsedDays)
          }
        },
        beneficiaries: vestingData.beneficiaries.map(b => ({
          id: b.beneficiary_id,
          name: b.name,
          percentage: b.percentage,
          vested_amount: (vestingData.asset_value * b.percentage / 100) * (vestedPercentage / 100)
        })),
        chart_data: chartDataPoints
      }
    };

    res.json(response);
  } catch (error) {
    console.error("Error fetching vesting schedule:", error);
    res.status(500).json({ 
      error: "Failed to fetch vesting schedule.",
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

// Create new vesting contract
router.post('/', authenticateToken, async (req, res) => {
  try {
    const { assetId, vestingSchedule } = req.body;
    const userId = req.user.id;

    // Validate request
    if (!assetId || !vestingSchedule) {
      return res.status(400).json({ error: 'Missing required fields' });
    }

    // Validate vesting schedule
    if (!vestingSchedule.duration || !vestingSchedule.cliff_period || !vestingSchedule.release_frequency || !vestingSchedule.start_date) {
      return res.status(400).json({ error: 'Invalid vesting schedule' });
    }

    // Validate beneficiaries
    if (!vestingSchedule.beneficiaries || vestingSchedule.beneficiaries.length === 0) {
      return res.status(400).json({ error: 'At least one beneficiary is required' });
    }

    const totalPercentage = vestingSchedule.beneficiaries.reduce((sum, b) => sum + b.percentage, 0);
    if (totalPercentage !== 100) {
      return res.status(400).json({ error: 'Beneficiary percentages must sum to 100%' });
    }

    // Check if user is manager
    const userResult = await pool.query(
      'SELECT role FROM users WHERE id = $1',
      [userId]
    );

    if (userResult.rows[0].role !== 'manager') {
      return res.status(403).json({ error: 'Only managers can create vesting contracts' });
    }

    // Check if asset exists and belongs to user
    const assetResult = await pool.query(
      'SELECT * FROM tokenised_assets WHERE id = $1 AND created_by = $2',
      [assetId, userId]
    );

    if (assetResult.rows.length === 0) {
      return res.status(404).json({ error: 'Asset not found or access denied' });
    }

    // Check for existing vesting contract
    const existingVestingResult = await pool.query(
      'SELECT * FROM vesting_records WHERE asset_id = $1 AND status = $2',
      [assetId, 'active']
    );

    if (existingVestingResult.rows.length > 0) {
      // Invalidate existing vesting contract
      await pool.query(
        'UPDATE vesting_records SET status = $1 WHERE asset_id = $2 AND status = $3',
        ['invalidated', assetId, 'active']
      );
    }

    // Generate mock contract address (in production, this would be a real smart contract address)
    const mockContractAddress = `0x${Array(40).fill('0123456789abcdef').map(x => x[Math.floor(Math.random() * x.length)]).join('')}`;

    // Calculate end date based on duration
    const startDate = new Date(vestingSchedule.start_date);
    const endDate = new Date(startDate);
    endDate.setDate(endDate.getDate() + vestingSchedule.duration);

    // Create vesting record
    const vestingResult = await pool.query(
      `INSERT INTO vesting_records (
        asset_id, contract_address, start_date, end_date, 
        cliff_period, release_frequency, status, created_by
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
      RETURNING id`,
      [
        assetId,
        mockContractAddress,
        vestingSchedule.start_date,
        endDate.toISOString(),
        vestingSchedule.cliff_period,
        vestingSchedule.release_frequency,
        'active',
        userId
      ]
    );

    const vestingId = vestingResult.rows[0].id;

    // Create beneficiary records
    for (const beneficiary of vestingSchedule.beneficiaries) {
      await pool.query(
        `INSERT INTO vesting_beneficiaries (
          vesting_record_id, beneficiary_id, percentage
        ) VALUES ($1, $2, $3)`,
        [vestingId, beneficiary.beneficiary_id, beneficiary.percentage]
      );
    }

    // Update tokenised_assets with vesting_record_id
    await pool.query(
      'UPDATE tokenised_assets SET vesting_record_id = $1 WHERE id = $2',
      [vestingId, assetId]
    );

    // Fetch the created vesting record with all details
    const result = await pool.query(
      `SELECT vr.*, ta.name as asset_name, ta.type as asset_type, ta.value as asset_value,
              json_agg(json_build_object(
                'beneficiary_id', vb.beneficiary_id,
                'percentage', vb.percentage,
                'name', c.company_name
              )) as beneficiaries
       FROM vesting_records vr
       JOIN tokenised_assets ta ON vr.asset_id = ta.id
       JOIN vesting_beneficiaries vb ON vr.id = vb.vesting_record_id
       JOIN clients c ON vb.beneficiary_id = c.id
       WHERE vr.id = $1
       GROUP BY vr.id, ta.name, ta.type, ta.value`,
      [vestingId]
    );

    res.status(201).json({
      message: 'Vesting contract created successfully',
      vesting: result.rows[0]
    });

  } catch (error) {
    console.error('Error creating vesting contract:', error);
    res.status(500).json({ 
      error: 'Failed to create vesting contract',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

export default router; 