import formData from 'form-data';
import Mailgun from 'mailgun.js';
import { logger } from '../utils/logger.js';

// Validate Mailgun configuration
const validateMailgunConfig = () => {
    const requiredEnvVars = ['MAILGUN_API_KEY', 'MAILGUN_DOMAIN', 'MAIL_SENDER_ADDRESS'];
    const missingVars = requiredEnvVars.filter(varName => !process.env[varName]);
    
    if (missingVars.length > 0) {
        const error = new Error(`Missing required Mailgun environment variables: ${missingVars.join(', ')}`);
        logger.error('Mailgun configuration error:', error);
        throw error;
    }
};

// Initialize Mailgun
let mg;
try {
    validateMailgunConfig();
    const mailgun = new Mailgun(formData);
    mg = mailgun.client({
        username: 'api',
        key: process.env.MAILGUN_API_KEY,
        url: 'https://api.eu.mailgun.net', // ✅ EU endpoint

    });
} catch (error) {
    logger.error('Failed to initialize Mailgun:', error);
    throw error;
}

class EmailService {
    static async sendEmail({ to, subject, html, text, attachments = [] }) {
        try {
            if (!mg) {
                throw new Error('Mailgun client not initialized');
            }

            const messageData = {
                from: process.env.MAIL_SENDER_ADDRESS,
                to,
                subject,
                html,
                text,
                attachments
            };

            const response = await mg.messages.create(process.env.MAILGUN_DOMAIN, messageData);
            logger.info('Email sent successfully', { messageId: response.id, to });
            return response;
        } catch (error) {
            logger.error('Error sending email:', {
                error: error.message,
                details: error.details,
                status: error.status,
                type: error.type,
                stack: error.stack
            });
            throw error;
        }
    }

    static async sendMagicLink(email, magicLink) {
        const html = `
            <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
                <h1 style="color: #2c3e50;">Login to Your Account</h1>
                <p style="color: #34495e; font-size: 16px;">Click the button below to sign in to your account. This link will expire in 15 minutes.</p>
                <div style="text-align: center; margin: 30px 0;">
                    <a href="${magicLink}" 
                       style="background-color: #3498db; color: white; padding: 12px 24px; text-decoration: none; border-radius: 5px; display: inline-block;">
                        Sign In
                    </a>
                </div>
                <p style="color: #7f8c8d; font-size: 14px;">If you didn't request this link, please ignore this email.</p>
                <p style="color: #7f8c8d; font-size: 14px;">Please check your spam folder if you don't see this email in your inbox.</p>
                <hr style="border: none; border-top: 1px solid #eee; margin: 20px 0;">
                <p style="color: #95a5a6; font-size: 12px; text-align: center;">
                    This is an automated message, please do not reply to this email.
                </p>
            </div>
        `;

        return this.sendEmail({
            to: email,
            subject: 'Your Magic Link for Login',
            html
        });
    }

    static async sendPasswordReset(email, resetURL) {
        const html = `
            <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
                <h1 style="color: #2c3e50;">Password Reset Request</h1>
                <p style="color: #34495e; font-size: 16px;">Click the button below to reset your password. This link will expire in 15 minutes.</p>
                <div style="text-align: center; margin: 30px 0;">
                    <a href="${resetURL}" 
                       style="background-color: #3498db; color: white; padding: 12px 24px; text-decoration: none; border-radius: 5px; display: inline-block;">
                        Reset Password
                    </a>
                </div>
                <p style="color: #7f8c8d; font-size: 14px;">If you didn't request this password reset, please ignore this email.</p>
                <p style="color: #7f8c8d; font-size: 14px;">Please check your spam folder if you don't see this email in your inbox.</p>
                <hr style="border: none; border-top: 1px solid #eee; margin: 20px 0;">
                <p style="color: #95a5a6; font-size: 12px; text-align: center;">
                    This is an automated message, please do not reply to this email.
                </p>
            </div>
        `;

        return this.sendEmail({
            to: email,
            subject: 'Password Reset Request',
            html
        });
    }

    static async sendIssueReport({ title, description, user, metadata }) {
        try {
            // Validate required fields
            if (!title || !description || !user || !metadata) {
                throw new Error('Missing required fields for issue report email');
            }

            // Ensure metadata is properly formatted
            let parsedMetadata;
            try {
                parsedMetadata = typeof metadata === 'string' ? JSON.parse(metadata) : metadata;
            } catch (error) {
                throw new Error('Invalid metadata format for issue report');
            }

            const html = `
                <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
                    <h2 style="color: #2c3e50;">New Issue Report</h2>
                    <div style="background-color: #f8f9fa; padding: 20px; border-radius: 5px; margin: 20px 0;">
                        <p><strong>Title:</strong> ${title}</p>
                        <p><strong>Description:</strong> ${description}</p>
                        <p><strong>Reported by:</strong> ${user.contact_name} (${user.email})</p>
                        <p><strong>Role:</strong> ${user.role}</p>
                        <p><strong>Timestamp:</strong> ${new Date().toISOString()}</p>
                    </div>
                    <h3 style="color: #2c3e50;">System Information:</h3>
                    <pre style="background-color: #f8f9fa; padding: 15px; border-radius: 5px; overflow-x: auto;">
                        ${JSON.stringify(parsedMetadata, null, 2)}
                    </pre>
                    <hr style="border: none; border-top: 1px solid #eee; margin: 20px 0;">
                    <p style="color: #95a5a6; font-size: 12px; text-align: center;">
                        This is an automated message from the issue reporting system.
                    </p>
                </div>
            `;

            return this.sendEmail({
                to: '<EMAIL>',
                subject: `[Issue Report] ${title}`,
                html
            });
        } catch (error) {
            logger.error('Error preparing issue report email:', {
                error: error.message,
                stack: error.stack,
                title,
                userId: user?.id
            });
            throw error;
        }
    }

    static async sendNewsletterSubscriptionConfirmation(email) {
        const html = `
            <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
                <h1 style="color: #2c3e50;">Welcome to Our Newsletter!</h1>
                <p style="color: #34495e; font-size: 16px;">Thank you for subscribing to our institutional newsletter. You'll now receive updates about:</p>
                <ul style="color: #34495e; font-size: 16px;">
                    <li>Latest investment opportunities</li>
                    <li>Market insights and analysis</li>
                    <li>Platform updates and improvements</li>
                    <li>Industry news and trends</li>
                </ul>
                <p style="color: #34495e; font-size: 16px;">If you have any questions or would like to update your preferences, please don't hesitate to contact us.</p>
                <hr style="border: none; border-top: 1px solid #eee; margin: 20px 0;">
                <p style="color: #95a5a6; font-size: 12px; text-align: center;">
                    This is an automated message, please do not reply to this email.
                </p>
            </div>
        `;

        return this.sendEmail({
            to: email,
            subject: 'Welcome to Our Newsletter!',
            html
        });
    }
}

export default EmailService; 