const fetch = globalThis.fetch;
import pkg from 'pg';
const { Pool } = pkg;
import dotenv from 'dotenv';

dotenv.config();

const pool = new Pool({ connectionString: process.env.DATABASE_URL });

const API_URL = `https://api.coingecko.com/api/v3/simple/price?ids=bitcoin,ethereum,binancecoin,xrp,solana,tether,dogecoin,cardano,polkadot&vs_currencies=usd,eur,gbp,jpy,aud,cad,chf,sgd,inr`;

const FIAT_CURRENCIES = ['USD', 'EUR', 'GBP', 'JPY', 'AUD', 'CAD', 'CHF', 'SGD', 'INR'];
const CRYPTO_CURRENCIES = {
    bitcoin: 'BTC',
    ethereum: 'ETH',
    binancecoin: 'BNB',
    xrp: 'XRP',
    solana: 'SOL',
    tether: 'USDT',
    dogecoin: 'DOGE',
    cardano: 'ADA',
    polkadot: 'DOT'
};

async function fetchExchangeRates() {
  console.log("FETCHING EXCHANGE RATES");
  try {
        const response = await fetch(API_URL);
        if (!response.ok) throw new Error(`API request failed: ${response.statusText}`);
        const data = await response.json();

        const client = await pool.connect();
        try {
            await client.query('BEGIN');
            await client.query("DELETE FROM exchange_rates WHERE timestamp < NOW() - INTERVAL '2 days'");

            const queryText = `
                INSERT INTO exchange_rates (base_currency, target_currency, rate, timestamp)
                VALUES ($1, $2, $3, NOW())
                ON CONFLICT (base_currency, target_currency)
                DO UPDATE SET rate = EXCLUDED.rate, timestamp = NOW();
            `;

            for (const cryptoKey in CRYPTO_CURRENCIES) {
                const cryptoSymbol = CRYPTO_CURRENCIES[cryptoKey];
                for (const fiat of FIAT_CURRENCIES) {
                    if (data[cryptoKey] && data[cryptoKey][fiat.toLowerCase()]) {
                        const rate = data[cryptoKey][fiat.toLowerCase()];
                        await client.query(queryText, [cryptoSymbol, fiat, rate]);
                    }
                }
            }

            await client.query('COMMIT');
            console.log('[EXCHANGE] Crypto & Fiat exchange rates updated successfully');
        } catch (err) {
            await client.query('ROLLBACK');
            console.error('[EXCHANGE] Database error:', err);
        } finally {
            client.release();
        }
    } catch (err) {
        console.error('[EXCHANGE] Error fetching exchange rates:', err);
    }
}

export { fetchExchangeRates };