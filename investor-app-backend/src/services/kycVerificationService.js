import * as faceapi from 'face-api.js';
import { createWorker } from 'tesseract.js';
import sharp from 'sharp';
import fetch from 'node-fetch';
import { logger } from '../utils/logger.js';

// Initialize face-api models
const loadFaceDetectionModels = async () => {
  try {
    await faceapi.nets.ssdMobilenetv1.loadFromDisk('./models');
    await faceapi.nets.faceLandmark68Net.loadFromDisk('./models');
    await faceapi.nets.faceRecognitionNet.loadFromDisk('./models');
    logger.info('Face detection models loaded successfully');
  } catch (error) {
    logger.error('Error loading face detection models:', error);
    throw new Error('Failed to initialize face detection system');
  }
};

// Download image from URL
const downloadImage = async (url) => {
  try {
    const response = await fetch(url);
    if (!response.ok) {
      throw new Error(`Failed to download image: ${response.statusText}`);
    }
    const buffer = await response.buffer();
    if (buffer.length === 0) {
      throw new Error('Downloaded image is empty');
    }
    return buffer;
  } catch (error) {
    logger.error('Error downloading image:', error);
    throw new Error('Failed to download image from storage');
  }
};

// Process image for face detection
const processImage = async (buffer) => {
  try {
    // Validate image format and size
    const metadata = await sharp(buffer).metadata();
    if (!metadata.format || !['jpeg', 'jpg', 'png'].includes(metadata.format.toLowerCase())) {
      throw new Error('Invalid image format. Only JPEG and PNG are supported.');
    }
    if (metadata.width < 100 || metadata.height < 100) {
      throw new Error('Image resolution too low. Minimum size is 100x100 pixels.');
    }

    // Resize image to a reasonable size for processing
    const processedBuffer = await sharp(buffer)
      .resize(800, 600, { 
        fit: 'inside',
        withoutEnlargement: true
      })
      .normalize() // Normalize image for better face detection
      .toBuffer();
    
    return processedBuffer;
  } catch (error) {
    logger.error('Error processing image:', error);
    throw new Error('Failed to process image for verification');
  }
};

// Detect faces in image
const detectFaces = async (imageBuffer) => {
  try {
    const img = await faceapi.bufferToImage(imageBuffer);
    const detections = await faceapi.detectAllFaces(img)
      .withFaceLandmarks()
      .withFaceDescriptors();
    
    return detections;
  } catch (error) {
    logger.error('Error detecting faces:', error);
    throw new Error('Failed to detect faces in image');
  }
};

// Compare two face descriptors
const compareFaces = (descriptor1, descriptor2) => {
  try {
    const distance = faceapi.euclideanDistance(descriptor1, descriptor2);
    // Lower distance means more similar faces
    // We'll consider faces similar if distance is less than 0.6
    return {
      match: distance < 0.6,
      confidence: 1 - distance // Convert distance to confidence score
    };
  } catch (error) {
    logger.error('Error comparing faces:', error);
    throw new Error('Failed to compare faces');
  }
};

// Extract text from passport using OCR
const extractPassportText = async (imageBuffer) => {
  try {
    const worker = await createWorker();
    await worker.loadLanguage('eng');
    await worker.initialize('eng');
    
    const { data: { text } } = await worker.recognize(imageBuffer);
    await worker.terminate();
    
    return text;
  } catch (error) {
    logger.error('Error extracting passport text:', error);
    throw new Error('Failed to extract text from passport');
  }
};

// Validate passport text
const validatePassportText = (text) => {
  const requiredFields = [
    'passport',
    'date of birth',
    'nationality',
    'sex',
    'expiry date',
    'document number'
  ];

  const foundFields = requiredFields.filter(field => 
    text.toLowerCase().includes(field)
  );

  return {
    isValid: foundFields.length >= 4, // Require at least 4 fields to be present
    foundFields,
    missingFields: requiredFields.filter(field => !foundFields.includes(field))
  };
};

// Verify KYC documents
export const verifyKYC = async (selfieUrl, passportUrl) => {
  try {
    // Load face detection models if not already loaded
    await loadFaceDetectionModels();

    // Download and process images
    const [selfieBuffer, passportBuffer] = await Promise.all([
      downloadImage(selfieUrl),
      downloadImage(passportUrl)
    ]);

    const [processedSelfie, processedPassport] = await Promise.all([
      processImage(selfieBuffer),
      processImage(passportBuffer)
    ]);

    // Detect faces in both images
    const [selfieFaces, passportFaces] = await Promise.all([
      detectFaces(processedSelfie),
      detectFaces(processedPassport)
    ]);

    // Check if exactly one face is detected in each image
    if (selfieFaces.length !== 1 || passportFaces.length !== 1) {
      return {
        success: false,
        message: 'Invalid number of faces detected. Please ensure clear, single face images.',
        details: {
          selfieFaces: selfieFaces.length,
          passportFaces: passportFaces.length,
          error: 'Multiple or no faces detected'
        }
      };
    }

    // Compare faces
    const faceComparison = compareFaces(
      selfieFaces[0].descriptor,
      passportFaces[0].descriptor
    );

    if (!faceComparison.match) {
      return {
        success: false,
        message: 'Face verification failed. The selfie does not match the passport photo.',
        details: {
          faceMatch: false,
          confidence: faceComparison.confidence,
          error: 'Face mismatch'
        }
      };
    }

    // Extract text from passport
    const passportText = await extractPassportText(processedPassport);

    // Validate passport text
    const passportValidation = validatePassportText(passportText);

    if (!passportValidation.isValid) {
      return {
        success: false,
        message: 'Invalid passport image. Please ensure the passport is clearly visible and contains required information.',
        details: {
          hasRequiredFields: false,
          foundFields: passportValidation.foundFields,
          missingFields: passportValidation.missingFields,
          error: 'Missing required passport fields'
        }
      };
    }

    // All checks passed
    return {
      success: true,
      message: 'KYC verification successful',
      details: {
        faceMatch: true,
        faceConfidence: faceComparison.confidence,
        passportValidation: {
          foundFields: passportValidation.foundFields,
          missingFields: passportValidation.missingFields
        },
        passportText: passportText.substring(0, 200) + '...' // Include first 200 chars for reference
      }
    };

  } catch (error) {
    logger.error('Error in KYC verification:', error);
    return {
      success: false,
      message: 'Error during KYC verification',
      details: {
        error: error.message,
        stack: process.env.NODE_ENV === 'development' ? error.stack : undefined
      }
    };
  }
}; 