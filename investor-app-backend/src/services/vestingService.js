import { pool } from '../setup/databases.js';
import { logger } from '../utils/logger.js';

const vestingService = {
  async getUserVestingRecords(userId) {
    try {
      const query = `
        SELECT
          vr.*,
          ta.name as asset_name,
          ta.type as asset_type,
          ta.value as asset_value
        FROM vesting_records vr
        JOIN tokenised_assets ta ON vr.asset_id = ta.id
        CROSS JOIN LATERAL jsonb_array_elements(vr.beneficiaries) AS b
        WHERE (b->>'beneficiary_id')::integer = $1
        ORDER BY vr.created_at DESC
      `;

      const result = await pool.query(query, [userId]);
      console.log('Retrieved user vesting records:', result.rows);
      return result.rows;
    } catch (error) {
      logger.error('Error in getUserVestingRecords:', error);
      throw error;
    }
  },

  async getManagerVestingRecords(managerId) {
    try {
      const query = `
        SELECT DISTINCT
          vr.*,
          ta.name as asset_name,
          ta.type as asset_type,
          ta.value as asset_value,
          beneficiary_ii.company_name as client_company,
          beneficiary_ii.contact_name as client_contact
        FROM vesting_records vr
        JOIN tokenised_assets ta ON vr.asset_id = ta.id
        CROSS JOIN LATERAL jsonb_array_elements(vr.beneficiaries) AS b
        JOIN institutional_investors beneficiary_ii ON (b->>'beneficiary_id')::integer = beneficiary_ii.id
        WHERE beneficiary_ii.manager_id = $1
        ORDER BY vr.created_at DESC
      `;

      const result = await pool.query(query, [managerId]);
      console.log('Retrieved manager vesting records:', result.rows);
      return result.rows;
    } catch (error) {
      logger.error('Error in getManagerVestingRecords:', error);
      throw error;
    }
  },

  async createVestingRecord(assetId, contractAddress, vestingSchedule, beneficiaries, blockchainTxHash) {
    try {
      console.log('Creating vesting record with data:', {
        assetId,
        contractAddress,
        vestingSchedule,
        beneficiaries,
        blockchainTxHash
      });

      // Ensure beneficiaries is properly formatted as JSONB
      const formattedBeneficiaries = Array.isArray(beneficiaries)
        ? beneficiaries
        : JSON.parse(beneficiaries);

      // Ensure vestingSchedule is properly formatted as JSONB
      const formattedSchedule = typeof vestingSchedule === 'string'
        ? JSON.parse(vestingSchedule)
        : vestingSchedule;

      const query = `
        INSERT INTO vesting_records (
          asset_id,
          contract_address,
          vesting_schedule,
          beneficiaries,
          blockchain_tx_hash,
          status
        )
        VALUES ($1, $2, $3, $4, $5, 'active')
        RETURNING *
      `;

      const result = await pool.query(query, [
        assetId,
        contractAddress,
        formattedSchedule,
        formattedBeneficiaries,
        blockchainTxHash
      ]);

      console.log('Created vesting record:', result.rows[0]);

      return result.rows[0];
    } catch (error) {
      logger.error('Error in createVestingRecord:', error);
      throw error;
    }
  },

  async getVestingRecordById(recordId) {
    try {
      const query = `
        SELECT
          vr.*,
          ta.name as asset_name,
          ta.type as asset_type,
          ta.value as asset_value,
          ii.company_name as client_company,
          ii.contact_name as client_contact
        FROM vesting_records vr
        JOIN tokenised_assets ta ON vr.asset_id = ta.id
        JOIN institutional_investors ii ON ta.user_id = ii.id
        WHERE vr.id = $1
      `;

      const result = await pool.query(query, [recordId]);
      console.log('Retrieved vesting record:', result.rows[0]);
      return result.rows[0];
    } catch (error) {
      logger.error('Error in getVestingRecordById:', error);
      throw error;
    }
  },

  async updateVestingRecordStatus(recordId, status) {
    try {
      const query = `
        UPDATE vesting_records
        SET status = $1,
            updated_at = CURRENT_TIMESTAMP
        WHERE id = $2
        RETURNING *
      `;

      const result = await pool.query(query, [status, recordId]);
      return result.rows[0];
    } catch (error) {
      logger.error('Error in updateVestingRecordStatus:', error);
      throw error;
    }
  }
};

export default vestingService;