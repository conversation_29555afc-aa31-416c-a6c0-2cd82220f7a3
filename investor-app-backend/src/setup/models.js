import { Sequelize, DataTypes } from 'sequelize';
import dotenv from 'dotenv';

dotenv.config();

// Initialize Sequelize
const sequelize = new Sequelize(process.env.DATABASE_URL, {
  dialect: 'postgres',
  logging: console.log, // Enable SQL query logging
  dialectOptions: {
    ssl: {
      require: true,
      rejectUnauthorized: false
    }
  }
});

// Test database connection
sequelize.authenticate()
  .then(() => {
    console.log('Database connection established successfully.');
  })
  .catch(err => {
    console.error('Unable to connect to the database:', err);
  });

// User Model (matching institutional_investors table)
const User = sequelize.define('institutional_investors', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  role: {
    type: DataTypes.STRING(50),
    allowNull: false,
    defaultValue: 'client'
  },
  manager_id: {
    type: DataTypes.INTEGER,
    allowNull: true
  },
  company_name: {
    type: DataTypes.STRING(255),
    allowNull: false
  },
  contact_name: {
    type: DataTypes.STRING(255),
    allowNull: false
  },
  email: {
    type: DataTypes.STRING(255),
    allowNull: false,
    unique: true
  },
  password: {
    type: DataTypes.STRING(255),
    allowNull: false
  },
  tax_id: {
    type: DataTypes.STRING(50),
    allowNull: true
  },
  address: {
    type: DataTypes.TEXT,
    allowNull: false
  },
  phone: {
    type: DataTypes.STRING(20),
    allowNull: false
  },
  profile_image: {
    type: DataTypes.STRING(255),
    allowNull: true
  }
}, {
  timestamps: false, // Disable timestamps since we're not using them
  tableName: 'institutional_investors'
});

// Asset Model
const Asset = sequelize.define('tokenised_assets', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  name: {
    type: DataTypes.STRING(255),
    allowNull: false
  },
  type: {
    type: DataTypes.STRING(100),
    allowNull: false
  },
  value: {
    type: DataTypes.DECIMAL(15, 2),
    allowNull: false
  },
  metadata: {
    type: DataTypes.JSONB,
    defaultValue: {}
  },
  supporting_documents: {
    type: DataTypes.ARRAY(DataTypes.STRING),
    defaultValue: []
  },
  blockchain_tx_hash: {
    type: DataTypes.STRING(255),
    allowNull: false,
    unique: true
  }
}, {
  timestamps: false, // Disable timestamps since we're not using them
  tableName: 'tokenised_assets'
});

// Activity Model
const Activity = sequelize.define('Activity', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  user_id: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: 'institutional_investors',
      key: 'id'
    }
  },
  type: {
    type: DataTypes.STRING(50),
    allowNull: false
  },
  message: {
    type: DataTypes.TEXT,
    allowNull: false
  },
  status: {
    type: DataTypes.STRING(20),
    defaultValue: 'pending'
  },
  metadata: {
    type: DataTypes.JSONB,
    defaultValue: {},
    get() {
      const raw = this.getDataValue('metadata');
      return raw || {};
    }
  }
}, {
  timestamps: true, // Enable timestamps
  underscored: true, // Use snake_case for column names
  tableName: 'activities',  // Use the existing notifications table
  createdAt: 'created_at', // Map createdAt to created_at
  updatedAt: 'updated_at', // Map updatedAt to updated_at
  getterMethods: {
    metadata() {
      const raw = this.getDataValue('metadata');
      return raw || {};
    }
  }
});

// Define associations
User.hasMany(Asset, { foreignKey: 'user_id' });
Asset.belongsTo(User, { foreignKey: 'user_id' });

User.hasMany(Activity, { foreignKey: 'user_id' });
Activity.belongsTo(User, { foreignKey: 'user_id' });

// Sync models with database
async function syncModels() {
  try {
    console.log('Starting model synchronization...');
    await sequelize.sync();
    console.log('Database models synchronized successfully');
    
    // Test the Activity model
    const testActivity = await Activity.findOne();
    console.log('Activity model test query result:', testActivity ? 'Found activities' : 'No activities found');
  } catch (error) {
    console.error('Error synchronizing database models:', error);
    console.error('Error details:', {
      message: error.message,
      stack: error.stack,
      name: error.name,
      code: error.code
    });
  }
}

// Export models and database connection
export {
  sequelize,
  User,
  Asset,
  Activity,
  syncModels
}; 