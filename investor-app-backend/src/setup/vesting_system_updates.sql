-- First, create the vesting_records table if it doesn't exist
CREATE TABLE IF NOT EXISTS vesting_records (
    id SERIAL PRIMARY KEY,
    asset_id INTEGER NOT NULL REFERENCES tokenised_assets(id) ON DELETE CASCADE,
    contract_address VARCHAR(255) NOT NULL,
    blockchain_tx_hash VARCHAR(255) NOT NULL,
    vesting_schedule JSONB NOT NULL,
    beneficiaries JSONB NOT NULL,
    status VARCHAR(20) NOT NULL DEFAULT 'active',
    created_at TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT valid_status CHECK (status IN ('active', 'completed', 'cancelled'))
);

-- Add vesting-related fields to tokenised_assets table
ALTER TABLE tokenised_assets
ADD COLUMN IF NOT EXISTS is_vested BOOLEAN DEFAULT FALSE,
ADD COLUMN IF NOT EXISTS vesting_type VARCHAR(50),
ADD COLUMN IF NOT EXISTS vesting_record_id INTEGER REFERENCES vesting_records(id);

-- Create an index for faster vesting-related queries
CREATE INDEX IF NOT EXISTS idx_tokenised_assets_vesting ON tokenised_assets(is_vested, vesting_type);

-- Create indexes for vesting_records table
CREATE INDEX IF NOT EXISTS idx_vesting_records_asset_id ON vesting_records(asset_id);
CREATE INDEX IF NOT EXISTS idx_vesting_records_status ON vesting_records(status);
CREATE INDEX IF NOT EXISTS idx_vesting_records_beneficiaries ON vesting_records USING gin (beneficiaries);

-- Create other necessary indexes
CREATE INDEX IF NOT EXISTS idx_tokenised_assets_user_id ON tokenised_assets(user_id);
CREATE INDEX IF NOT EXISTS idx_tokenised_assets_created_at ON tokenised_assets(created_at);
CREATE INDEX IF NOT EXISTS idx_asset_approvals_asset_id ON asset_approvals(asset_id);
CREATE INDEX IF NOT EXISTS idx_asset_approvals_status ON asset_approvals(status);

-- Drop existing views if they exist
DROP VIEW IF EXISTS beneficiary_vesting_details;
DROP VIEW IF EXISTS vested_assets_view;

-- Create a view for vested assets with all relevant information
CREATE OR REPLACE VIEW vested_assets_view AS
SELECT 
    ta.id AS asset_id,
    ta.name AS asset_name,
    ta.type AS asset_type,
    ta.value AS asset_value,
    ta.metadata AS asset_metadata,
    ta.supporting_documents,
    ta.blockchain_tx_hash AS asset_tx_hash,
    ta.created_at AS asset_created_at,
    ta.is_vested,
    ta.vesting_type,
    vr.id AS vesting_record_id,
    vr.contract_address AS vesting_contract_address,
    vr.vesting_schedule,
    vr.beneficiaries,
    vr.status AS vesting_status,
    vr.blockchain_tx_hash AS vesting_tx_hash,
    vr.created_at AS vesting_created_at,
    vr.updated_at AS vesting_updated_at,
    aa.status AS approval_status,
    aa.blockchain_tx_hash AS approval_tx_hash,
    aa.created_at AS approval_created_at,
    ta.user_id AS manager_id,
    ii.company_name AS manager_company,
    ii.contact_name AS manager_name
FROM tokenised_assets ta
LEFT JOIN vesting_records vr ON ta.vesting_record_id = vr.id
LEFT JOIN asset_approvals aa ON ta.id = aa.asset_id
LEFT JOIN institutional_investors ii ON ta.user_id = ii.id
WHERE ta.is_vested = TRUE;

-- Update existing vested assets in the database
UPDATE tokenised_assets ta
SET 
    is_vested = TRUE,
    vesting_type = 'time_based',
    vesting_record_id = vr.id
FROM vesting_records vr
WHERE ta.id = vr.asset_id;

-- Create a function to calculate vested amounts
CREATE OR REPLACE FUNCTION calculate_vested_amount(
    p_asset_id INTEGER,
    p_beneficiary_id INTEGER
) RETURNS NUMERIC AS $$
DECLARE
    v_asset_value NUMERIC;
    v_vesting_schedule JSONB;
    v_beneficiary_percentage NUMERIC;
    v_start_date DATE;
    v_duration INTEGER;
    v_cliff_period INTEGER;
    v_elapsed_days INTEGER;
    v_vested_percentage NUMERIC;
BEGIN
    -- Get asset value and vesting details
    SELECT 
        ta.value,
        vr.vesting_schedule,
        (b->>'percentage')::NUMERIC
    INTO 
        v_asset_value,
        v_vesting_schedule,
        v_beneficiary_percentage
    FROM tokenised_assets ta
    JOIN vesting_records vr ON ta.vesting_record_id = vr.id,
    jsonb_array_elements(vr.beneficiaries) AS b
    WHERE ta.id = p_asset_id
    AND (b->>'beneficiary_id')::INTEGER = p_beneficiary_id;

    -- Extract schedule details
    v_start_date := (v_vesting_schedule->>'start_date')::DATE;
    v_duration := (v_vesting_schedule->>'duration')::INTEGER;
    v_cliff_period := (v_vesting_schedule->>'cliff_period')::INTEGER;

    -- Calculate elapsed days
    v_elapsed_days := EXTRACT(EPOCH FROM (CURRENT_DATE - v_start_date))/86400;

    -- Calculate vested percentage
    IF v_elapsed_days < v_cliff_period THEN
        v_vested_percentage := 0;
    ELSIF v_elapsed_days >= v_duration THEN
        v_vested_percentage := 100;
    ELSE
        v_vested_percentage := ((v_elapsed_days - v_cliff_period)::NUMERIC / (v_duration - v_cliff_period)::NUMERIC) * 100;
    END IF;

    -- Calculate and return vested amount
    RETURN (v_asset_value * v_beneficiary_percentage / 100) * (v_vested_percentage / 100);
END;
$$ LANGUAGE plpgsql;

-- Create a view for beneficiary vesting details
CREATE OR REPLACE VIEW beneficiary_vesting_details AS
SELECT 
    va.asset_id,
    va.asset_name,
    va.asset_type,
    va.asset_value,
    va.vesting_record_id,
    va.vesting_contract_address,
    va.vesting_schedule,
    va.vesting_status,
    va.approval_status,
    b->>'beneficiary_id' AS beneficiary_id,
    b->>'percentage' AS beneficiary_percentage,
    b->>'name' AS beneficiary_name,
    calculate_vested_amount(va.asset_id, (b->>'beneficiary_id')::INTEGER) AS vested_amount
FROM vested_assets_view va,
jsonb_array_elements(va.beneficiaries) AS b; 