import xss from 'xss';

// Sanitize a single value
const sanitizeValue = (value) => {
  if (typeof value === 'string') {
    return xss(value.trim());
  }
  if (Array.isArray(value)) {
    return value.map(sanitizeValue);
  }
  if (typeof value === 'object' && value !== null) {
    return sanitizeInput(value);
  }
  return value;
};

// Sanitize an object recursively
export const sanitizeInput = (obj) => {
  if (!obj) return obj;
  
  const sanitized = {};
  for (const key in obj) {
    if (Object.prototype.hasOwnProperty.call(obj, key)) {
      sanitized[key] = sanitizeValue(obj[key]);
    }
  }
  return sanitized;
};

// Sanitize SQL query parameters
export const sanitizeSqlParams = (params) => {
  if (!Array.isArray(params)) {
    return [];
  }
  return params.map(param => {
    if (typeof param === 'string') {
      return param.replace(/[^a-zA-Z0-9\s\-_.,]/g, '');
    }
    return param;
  });
}; 