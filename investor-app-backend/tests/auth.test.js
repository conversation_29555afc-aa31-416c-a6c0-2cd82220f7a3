import request from 'supertest';
import app from '../src/server.js'; // Import your Express app
import { pool } from '../tests/setup.js';

describe('Auth Routes', () => {
  afterAll(async () => {
    await pool.end();
  });

  test('Should register a new client user', async () => {
    const response = await request(app)
      .post('/auth/register')
      .send({
        role: 'client',
        contactName: 'Test User',
        email: '<EMAIL>',
        password: 'securepassword',
        taxId: '*********',
        phone: '*********0'
      });

    expect(response.statusCode).toBe(201);
    expect(response.body).toHaveProperty('token');
    expect(response.body.user.role).toBe('client');
  });

  test('Should not register a user with an existing email', async () => {
    const response = await request(app)
      .post('/auth/register')
      .send({
        role: 'client',
        contactName: 'Test User',
        email: '<EMAIL>',
        password: 'securepassword',
        taxId: '*********',
        phone: '*********0'
      });

    expect(response.statusCode).toBe(400);
    expect(response.body.error).toBe('An account with this email already exists.');
  });
});

test('Should login with valid credentials', async () => {
  const response = await request(app)
    .post('/auth/login')
    .send({
      email: '<EMAIL>',
      password: 'securepassword',
    });

  expect(response.statusCode).toBe(200);
  expect(response.body).toHaveProperty('token');
});

test('Should fail login with incorrect password', async () => {
  const response = await request(app)
    .post('/auth/login')
    .send({
      email: '<EMAIL>',
      password: 'wrongpassword',
    });

  expect(response.statusCode).toBe(400);
  expect(response.body.error).toBe('Invalid credentials');
});