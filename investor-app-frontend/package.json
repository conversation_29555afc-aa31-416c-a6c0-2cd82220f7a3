{"name": "archimedes-financial", "version": "0.0.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "test:unit": "vitest", "test:e2e": "start-server-and-test preview http://localhost:4173 'cypress run --e2e'", "test:e2e:dev": "start-server-and-test 'vite dev --port 4173' http://localhost:4173 'cypress open --e2e'", "lint": "eslint . --fix", "format": "prettier --write src/", "download-models": "node scripts/download-models.js"}, "dependencies": {"@popperjs/core": "^2.11.8", "@stripe/stripe-js": "^5.5.0", "apexcharts": "^4.5.0", "bootstrap": "^5.3.3", "bootstrap-icons": "^1.11.3", "chart.js": "^4.4.8", "chartjs-adapter-date-fns": "^3.0.0", "dotenv": "^16.4.7", "face-api.js": "^0.22.2", "file-saver": "^2.0.5", "html2canvas": "^1.4.1", "papaparse": "^5.5.2", "pinia": "^2.3.0", "tesseract.js": "^5.0.4", "tippy.js": "^6.3.7", "vue": "^3.5.13", "vue-router": "^4.5.0", "vue-toastification": "^2.0.0-rc.5", "vue-web-cam": "^1.9.0", "vue3-apexcharts": "^1.8.0", "xlsx": "^0.18.5"}, "devDependencies": {"@eslint/js": "^9.14.0", "@vitejs/plugin-vue": "^5.2.1", "@vitejs/plugin-vue-jsx": "^4.1.1", "@vitest/eslint-plugin": "1.1.20", "@vue/eslint-config-prettier": "^10.1.0", "@vue/test-utils": "^2.4.6", "cypress": "^13.17.0", "eslint": "^9.14.0", "eslint-plugin-cypress": "^4.1.0", "eslint-plugin-vue": "^9.30.0", "jsdom": "^25.0.1", "prettier": "^3.3.3", "sass": "^1.83.1", "sass-loader": "^16.0.4", "start-server-and-test": "^2.0.9", "vite": "^6.0.5", "vite-plugin-vue-devtools": "^7.6.8", "vitest": "^2.1.8"}}