<script setup>
import { onMounted, computed, ref, provide, watch, onBeforeUnmount } from 'vue';
import { RouterView } from 'vue-router';
import NavBar from './components/NavBar.vue';
import { useProfileStore } from '@/stores/profile';
import { useAuthStore } from '@/stores/auth';
import { useToast } from 'vue-toastification';
import { useRouter } from 'vue-router';
import { useNotificationsStore } from '@/stores/notifications';
import { setupGlobalModalCleanup, removeGlobalModalCleanup, addModalStyles } from '@/utils/modalUtils';

// Store instances
const profileStore = useProfileStore();
const authStore = useAuthStore();
const notificationsStore = useNotificationsStore();
const toast = useToast();
const router = useRouter();

// App state
const theme = ref(localStorage.getItem('theme') || 'light');
const isLoading = ref(true);
const error = ref(null);

// Provide theme context to child components
provide('theme', {
  current: theme,
  toggle: toggleTheme
});

// Theme toggle function
function toggleTheme() {
  theme.value = theme.value === 'dark' ? 'light' : 'dark';
  applyTheme();
}

// Apply theme to document
function applyTheme() {
  document.documentElement.setAttribute('data-bs-theme', theme.value);
  localStorage.setItem('theme', theme.value);
}

// Initialize notifications
const initializeNotifications = async () => {
  if (authStore.isLoggedIn) {
    await notificationsStore.fetchNotifications();
  }
};

// Initialize WebSocket connection
const initializeWebSocket = async () => {
  // TODO: Implement WebSocket initialization
  // This is a placeholder for future WebSocket implementation
};

// Initialize app
const initializeApp = async () => {
  try {
    isLoading.value = true;
    error.value = null;

    if (authStore.isLoggedIn) {
      await Promise.all([
        authStore.fetchUserProfile(),
        profileStore.fetchUserProfile()
      ]);
      authStore.startSessionCheck();
    }

    // Apply theme
    applyTheme();

    // Initialize other app-wide features
    await initializeNotifications();
    await initializeWebSocket();

    isLoading.value = false;
  } catch (error) {
    error.value = error.message || 'Failed to initialize application. Please refresh the page.';
    toast.error(error.value);
    console.error('App initialization error:', error);
  } finally {
    isLoading.value = false;
  }
};

// Watch for authentication state changes
watch(() => authStore.isLoggedIn, async (newValue) => {
  if (newValue) {
    await initializeApp();
  }
});

// Lifecycle hooks
onMounted(() => {
  initializeApp();

  // Setup global modal cleanup and add modal styles
  setupGlobalModalCleanup();
  addModalStyles();
});

onBeforeUnmount(() => {
  if (authStore.isLoggedIn) {
    authStore.stopSessionCheck();
  }

  // Remove global modal cleanup
  removeGlobalModalCleanup();
});

// Computed properties
const showNavBar = computed(() => {
  const route = router.currentRoute.value;
  return authStore.isLoggedIn && route.meta.layout !== 'public';
});
</script>

<template>
  <div id="app-container" :class="{ 'app-loading': isLoading }" :data-layout="$route.meta.layout || 'default'">
    <!-- Loading Overlay -->
    <div v-if="isLoading" class="loading-overlay">
      <div class="spinner-border text-primary" role="status">
        <span class="visually-hidden">Loading...</span>
      </div>
    </div>

    <!-- Error Alert -->
    <div v-if="error" class="error-alert alert alert-danger" role="alert">
      {{ error }}
      <button type="button" class="btn-close" @click="error = null"><i class="bi bi-x"></i></button>
    </div>

    <!-- Show NavBar only for authenticated users and non-public layouts -->
    <NavBar v-if="showNavBar" />

    <!-- Main content area -->
    <main :class="authStore.isLoggedIn ? 'logged-in' : 'not-logged-in'">
      <RouterView v-slot="{ Component }">
        <Transition name="fade" mode="out-in">
          <Suspense>
            <component :is="Component" />
            <template #fallback>
              <div class="loading-content">
                <div class="spinner-border text-primary" role="status">
                  <span class="visually-hidden">Loading content...</span>
                </div>
              </div>
            </template>
          </Suspense>
        </Transition>
      </RouterView>
    </main>
  </div>
</template>

<style lang="scss">
@import '@/assets/styles/modal.scss';

// Global walkthrough styles
.walkthrough-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1051;
  pointer-events: auto;
}

.walkthrough-step {
  z-index: 1060;
}

#app-container {
  min-height: 100vh;
  display: grid;
  grid-template-columns: 1fr;
  display: grid;
  grid-template-columns: auto 1fr;

  &[data-layout="default"] {
    grid-template-columns: auto 1fr;
  }

  &.app-loading {
    opacity: 0.6;
    pointer-events: none;
  }
}

main {
  min-height: 100vh;
  background: var(--bs-body-bg);
  overflow-x: hidden;
  width: 100%;
  &.not-logged-in{width: 100vw;}
}

.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  backdrop-filter: blur(5px);
}

.error-alert {
  position: fixed;
  top: 1rem;
  left: 50%;
  transform: translateX(-50%);
  z-index: 9999;
  min-width: 300px;
  max-width: 600px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.loading-content {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 200px;
}

// Transitions
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

// Theme-specific styles
.theme-dark {
  --bs-body-bg: #1a1a1a;
  --bs-body-color: #f8f9fa;

  .loading-overlay {
    background: rgba(0, 0, 0, 0.8);
  }
}

.theme-light {
  --bs-body-bg: #f8f9fa;
  --bs-body-color: #212529;

  .loading-overlay {
    background: rgba(255, 255, 255, 0.8);
  }
}
</style>
