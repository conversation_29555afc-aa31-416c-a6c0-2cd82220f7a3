<template>
  <div v-if="isOpen" class="modal-wrapper">
    <div class="modal fade show d-block" tabindex="-1">
      <div class="modal-dialog modal-lg modal-dialog-centered">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title">Asset Details</h5>
            <button type="button" class="modal-close-btn" @click="closeModal">
              <i class="bi bi-x-lg"></i>
            </button>
          </div>
          <div class="modal-body">
            <!-- Asset Information -->
            <div class="asset-info mb-4">
              <div class="row g-3">
                <div class="col-md-6">
                  <div class="info-card">
                    <label class="info-label">Asset Name</label>
                    <p class="info-value">{{ asset.name }}</p>
                  </div>
                </div>
                <div class="col-md-6">
                  <div class="info-card">
                    <label class="info-label">Asset Type</label>
                    <p class="info-value">
                      <span :class="`badge badge-${getTypeClass(asset.type)}`">{{ asset.type }}</span>
                    </p>
                  </div>
                </div>
                <div class="col-md-6">
                  <div class="info-card">
                    <label class="info-label">Total Value</label>
                    <p class="info-value">${{ asset.value.toLocaleString() }}</p>
                  </div>
                </div>
                <div class="col-md-6">
                  <div class="info-card">
                    <label class="info-label">Status</label>
                    <p class="info-value">
                      <span :class="`status-badge ${getStatusClass(asset.approval_status)}`">
                        {{ asset.approval_status }}
                      </span>
                    </p>
                  </div>
                </div>
                <div class="col-12">
                  <div class="info-card">
                    <label class="info-label">Blockchain Transaction</label>
                    <p class="info-value">
                      <a :href="`https://etherscan.io/tx/${asset.blockchain_tx_hash}`" target="_blank" class="hash-link">
                        {{ shortenHash(asset.blockchain_tx_hash) }}
                      </a>
                    </p>
                  </div>
                </div>
              </div>
            </div>

            <!-- Vesting Contract Information -->
            <div v-if="vestingContract" class="mb-4">
              <VestingDetails
                :contract="vestingContract"
                :isManager="isManager"
                @status-updated="handleVestingStatusUpdate"
              />
            </div>

            <!-- Supporting Documents -->
            <div v-if="asset.supporting_documents && asset.supporting_documents.length" class="mb-4">
              <h6 class="section-title">Supporting Documents</h6>
              <div class="documents-list">
                <a v-for="(doc, index) in asset.supporting_documents"
                   :key="index"
                   :href="doc"
                   target="_blank"
                   class="document-link">
                  <i class="bi bi-file-earmark-text"></i>
                  Document {{ index + 1 }}
                </a>
              </div>
            </div>

            <!-- Ownership Records -->
            <div class="ownership-records">
              <div class="d-flex justify-content-between align-items-center mb-3">
                <h6 class="section-title mb-0">Ownership Records</h6>
                <button v-if="isManager"
                        class="btn btn-primary btn-sm"
                        @click="openUpdateOwnershipModal">
                  <i class="bi bi-pencil-square me-1"></i> Update Ownership
                </button>
              </div>

              <div class="table-responsive">
                <table class="table">
                  <thead>
                    <tr>
                      <th>Owner</th>
                      <th>Percentage</th>
                      <th>Transaction Hash</th>
                      <th>Date</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr v-for="record in asset.ownership_records" :key="record.id">
                      <td>{{ record.owner_name }}</td>
                      <td>{{ record.ownership_percentage }}%</td>
                      <td>
                        <a :href="`https://etherscan.io/tx/${record.blockchain_tx_hash}`"
                           target="_blank"
                           class="hash-link">
                          {{ shortenHash(record.blockchain_tx_hash) }}
                        </a>
                      </td>
                      <td>{{ formatDate(record.created_at) }}</td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="modal-backdrop fade show"></div>

    <!-- Update Ownership Modal -->
    <div v-if="showUpdateOwnershipModal" class="modal-wrapper">
      <div class="modal fade show d-block" tabindex="-1">
        <div class="modal-dialog modal-lg modal-dialog-centered">
          <div class="modal-content">
            <div class="modal-header">
              <h5 class="modal-title">Update Ownership</h5>
              <button type="button" class="modal-close-btn" @click="closeUpdateOwnershipModal">
                <i class="bi bi-x-lg"></i>
              </button>
            </div>
            <div class="modal-body">
              <form @submit.prevent="updateOwnership">
                <div v-for="(record, index) in asset.ownership_records" :key="record.id" class="mb-3">
                  <div class="d-flex align-items-center gap-3">
                    <div class="flex-grow-1">
                      <label class="form-label">{{ record.owner_name }}</label>
                      <input
                        type="number"
                        v-model="ownershipUpdates[index]"
                        class="form-control"
                        min="0"
                        max="100"
                        step="0.01"
                        required
                      />
                    </div>
                    <div class="text-muted">%</div>
                  </div>
                </div>
                <div class="alert alert-info">
                  <i class="bi bi-info-circle me-2"></i>
                  Total ownership must equal 100%
                </div>
                <div class="d-flex justify-content-end gap-2">
                  <button type="button" class="btn btn-secondary" @click="closeUpdateOwnershipModal">
                    Cancel
                  </button>
                  <button type="submit" class="btn btn-primary" :disabled="!isValidOwnership">
                    Update Ownership
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      </div>
      <div class="modal-backdrop fade show"></div>
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted, onBeforeUnmount } from 'vue';
import { useToast } from 'vue-toastification';
import { apiClient } from '@/stores/auth';
import VestingDetails from './VestingDetails.vue';

export default {
  components: { VestingDetails },
  props: {
    isOpen: Boolean,
    asset: Object,
    isManager: Boolean
  },
  emits: ['close', 'update-ownership'],
  setup(props, { emit }) {
    const toast = useToast();
    const showUpdateOwnershipModal = ref(false);
    const ownershipUpdates = ref([]);
    const vestingContract = ref(null);

    const getTypeClass = (type) => ({
      real_estate: "primary",
      equities: "success",
      bonds: "warning"
    }[type] || "secondary");

    const getStatusClass = (status) => ({
      pending: "text-warning",
      approved: "text-success",
      verified: "text-success",
      rejected: "text-danger"
    }[status] || "text-muted");

    const shortenHash = (hash) => {
      if (!hash) return '';
      return `${hash.substring(0, 5)}...${hash.substring(hash.length - 5)}`;
    };

    const formatDate = (date) => {
      return new Date(date).toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      });
    };

    const openUpdateOwnershipModal = () => {
      ownershipUpdates.value = props.asset.ownership_records.map(record => record.ownership_percentage);
      showUpdateOwnershipModal.value = true;
    };

    const closeUpdateOwnershipModal = () => {
      showUpdateOwnershipModal.value = false;
      ownershipUpdates.value = [];
    };

    const isValidOwnership = computed(() => {
      const total = ownershipUpdates.value.reduce((sum, value) => sum + Number(value), 0);
      return Math.abs(total - 100) < 0.01; // Allow for small floating point differences
    });

    const updateOwnership = () => {
      if (!isValidOwnership.value) {
        toast.error('Total ownership must equal 100%');
        return;
      }

      const updates = props.asset.ownership_records.map((record, index) => ({
        owner_id: record.owner_id,
        ownership_percentage: Number(ownershipUpdates.value[index])
      }));

      emit('update-ownership', updates);
      closeUpdateOwnershipModal();
    };

    const closeModal = () => {
      emit('close');
    };

    const fetchVestingContract = async () => {
      if (!props.asset?.id) return;
      try {
        const response = await apiClient.get(`/vesting/${props.asset.id}`);
        vestingContract.value = response.data.vestingRecord;
      } catch (error) {
        if (error.response?.status !== 404) {
          console.error('Error fetching vesting contract:', error);
        }
      }
    };

    const handleVestingStatusUpdate = (newStatus) => {
      if (vestingContract.value) {
        vestingContract.value.status = newStatus;
      }
    };

    onMounted(() => {
      if (props.asset?.id) {
        fetchVestingContract();
      }

      // Add event listener to clean up modal backdrop when modal is closed
      document.addEventListener('hidden.bs.modal', () => {
        const backdrops = document.querySelectorAll('.modal-backdrop');
        backdrops.forEach(backdrop => backdrop.remove());
      });
    });

    onBeforeUnmount(() => {
      // Remove event listener when component is unmounted
      document.removeEventListener('hidden.bs.modal', () => {});
    });

    return {
      showUpdateOwnershipModal,
      ownershipUpdates,
      getTypeClass,
      getStatusClass,
      shortenHash,
      formatDate,
      openUpdateOwnershipModal,
      closeUpdateOwnershipModal,
      isValidOwnership,
      updateOwnership,
      closeModal,
      vestingContract,
      handleVestingStatusUpdate
    };
  }
};
</script>

<style scoped lang="scss">
.modal-wrapper {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  z-index: 1050;
  overflow-x: hidden;
  overflow-y: auto;
}

.modal {
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(5px);
  -webkit-backdrop-filter: blur(5px);
  animation: modalFadeIn 0.3s ease forwards;

  &:not(.show) {
    pointer-events: none;
    display: none;
  }
}

.modal-dialog {
  max-width: 800px;
  margin: 1.75rem auto;
  transform: translateY(-50px);
  opacity: 0;
  animation: modalContentSlideIn 0.3s ease 0.1s forwards;
}

.modal-content {
  border: none;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  background: var(--bs-body-bg);
}

.modal-header {
  border-bottom: 1px solid var(--bs-border-color);
  padding: 1rem 1.5rem;

  .modal-title {
    font-size: 1.25rem;
    font-weight: 600;
  }
}

.modal-body {
  padding: 1.5rem;
}

.info-card {
  background: rgba(var(--bs-primary-rgb), 0.05);
  border-radius: 8px;
  padding: 1rem;
  height: 100%;

  .info-label {
    font-size: 0.875rem;
    color: var(--bs-muted);
    margin-bottom: 0.25rem;
  }

  .info-value {
    font-size: 1rem;
    font-weight: 500;
    margin: 0;
  }
}

.section-title {
  font-size: 1rem;
  font-weight: 600;
  color: var(--bs-primary);
  margin-bottom: 1rem;
}

.documents-list {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;

  .document-link {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    background: rgba(var(--bs-primary-rgb), 0.1);
    border-radius: 6px;
    color: var(--bs-primary);
    text-decoration: none;
    transition: all 0.2s ease;

    &:hover {
      background: rgba(var(--bs-primary-rgb), 0.2);
      transform: translateY(-1px);
    }

    i {
      font-size: 1.1rem;
    }
  }
}

.table {
  margin: 0;

  th {
    font-size: 0.875rem;
    font-weight: 600;
    color: var(--bs-muted);
    border-bottom-width: 1px;
  }

  td {
    vertical-align: middle;
  }
}

.hash-link {
  color: var(--bs-primary);
  text-decoration: none;
  font-family: monospace;
  font-size: 0.875rem;

  &:hover {
    text-decoration: underline;
  }
}

.modal-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.5);
  z-index: 1040;
  opacity: 0;
  animation: backdropFadeIn 0.3s ease forwards;

  &:not(.show) {
    display: none;
  }
}

@keyframes modalFadeIn {
  from {
    background: rgba(0, 0, 0, 0);
    backdrop-filter: blur(0);
    -webkit-backdrop-filter: blur(0);
  }
  to {
    background: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(5px);
    -webkit-backdrop-filter: blur(5px);
  }
}

@keyframes modalContentSlideIn {
  from {
    transform: translateY(-50px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes backdropFadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}
</style>
