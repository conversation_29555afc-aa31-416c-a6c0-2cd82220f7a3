<template>
  <div class="create-vesting-form">
    <!-- Step Indicator -->
    <div class="step-indicator mb-4">
      <div class="d-flex justify-content-between">
        <div v-for="(step, index) in steps" :key="index"
             :class="['step', { active: currentStep >= index, completed: currentStep > index }]">
          <div class="step-number">{{ index + 1 }}</div>
          <div class="step-label">{{ step }}</div>
        </div>
      </div>
    </div>

    <!-- Step 1: Asset Selection -->
    <div v-if="currentStep === 0" class="step-content">
      <h3 class="mb-4">Select Asset</h3>
      <div class="row">
        <div class="col-md-6">
          <div class="form-group mb-3">
            <label class="form-label">Select Existing Asset</label>
            <select v-model="selectedAssetId" class="form-select" @change="handleAssetSelect">
              <option value="">Choose an asset...</option>
              <option v-for="asset in availableAssets" :key="asset.id" :value="asset.id">
                {{ asset.name }} ({{ asset.type }})
              </option>
            </select>
          </div>
        </div>
        <div class="col-md-6">
          <div class="form-group mb-3">
            <label class="form-label">Or Create New Asset</label>
            <button class="btn btn-outline-primary w-100" @click="showNewAssetForm = true">
              <i class="bi bi-plus-circle me-2"></i>Create New Asset
            </button>
          </div>
        </div>
      </div>

      <!-- New Asset Form -->
      <div v-if="showNewAssetForm" class="new-asset-form mt-4">
        <h4 class="mb-3">New Asset Details</h4>
        <div class="row g-3">
          <div class="col-md-6">
            <div class="form-group">
              <label class="form-label">Asset Name</label>
              <input v-model="newAsset.name" type="text" class="form-control" required>
            </div>
          </div>
          <div class="col-md-6">
            <div class="form-group">
              <label class="form-label">Asset Type</label>
              <select v-model="newAsset.type" class="form-select" required>
                <option value="">Select type...</option>
                <option value="equities">Equities</option>
                <option value="real_estate">Real Estate</option>
                <option value="bonds">Bonds</option>
              </select>
            </div>
          </div>
          <div class="col-md-6">
            <div class="form-group">
              <label class="form-label">Value ($)</label>
              <input v-model="newAsset.value" type="number" class="form-control" required>
            </div>
          </div>
          <div class="col-md-6">
            <div class="form-group">
              <label class="form-label">Client</label>
              <select v-model="newAsset.client_id" class="form-select" required>
                <option value="">Select client...</option>
                <option v-for="client in clients" :key="client.id" :value="client.id">
                  {{ client.name }}
                </option>
              </select>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Step 2: Vesting Details -->
    <div v-if="currentStep === 1" class="step-content">
      <h3 class="mb-4">Vesting Schedule</h3>
      <div class="row g-3">
        <div class="col-md-6">
          <div class="form-group">
            <label class="form-label">Duration (days)</label>
            <input v-model="vestingSchedule.duration" type="number" class="form-control" required>
          </div>
        </div>
        <div class="col-md-6">
          <div class="form-group">
            <label class="form-label">Cliff Period (days)</label>
            <input v-model="vestingSchedule.cliff_period" type="number" class="form-control" required>
          </div>
        </div>
        <div class="col-md-6">
          <div class="form-group">
            <label class="form-label">Release Frequency</label>
            <select v-model="vestingSchedule.release_frequency" class="form-select" required>
              <option value="daily">Daily</option>
              <option value="weekly">Weekly</option>
              <option value="monthly">Monthly</option>
              <option value="quarterly">Quarterly</option>
            </select>
          </div>
        </div>
        <div class="col-md-6">
          <div class="form-group">
            <label class="form-label">Start Date</label>
            <input v-model="vestingSchedule.start_date" type="date" class="form-control" required>
          </div>
        </div>
      </div>

      <!-- Beneficiaries Section -->
      <div class="beneficiaries-section mt-4">
        <h4 class="mb-3">Beneficiaries</h4>
        <div v-for="(beneficiary, index) in vestingSchedule.beneficiaries" :key="index" class="beneficiary-item mb-3">
          <div class="row g-3">
            <div class="col-md-5">
              <div class="form-group">
                <label class="form-label">Beneficiary</label>
                <select v-model="beneficiary.beneficiary_id" class="form-select" required>
                  <option value="">Select beneficiary...</option>
                  <option v-for="client in clients" :key="client.id" :value="client.id">
                    {{ client.name }}
                  </option>
                </select>
              </div>
            </div>
            <div class="col-md-5">
              <div class="form-group">
                <label class="form-label">Percentage</label>
                <input v-model="beneficiary.percentage" type="number" class="form-control" required>
              </div>
            </div>
            <div class="col-md-2">
              <div class="form-group">
                <label class="form-label">&nbsp;</label>
                <button class="btn btn-danger w-100" @click="removeBeneficiary(index)">
                  <i class="bi bi-trash"></i>
                </button>
              </div>
            </div>
          </div>
        </div>
        <button class="btn btn-outline-primary" @click="addBeneficiary">
          <i class="bi bi-plus-circle me-2"></i>Add Beneficiary
        </button>
      </div>
    </div>

    <!-- Step 3: Review -->
    <div v-if="currentStep === 2" class="step-content">
      <h3 class="mb-4">Review & Confirm</h3>

      <!-- Asset Details -->
      <div class="review-section mb-4">
        <h5>Asset Details</h5>
        <div class="row g-3">
          <div class="col-md-4">
            <div class="info-card">
              <label class="info-label">Asset Name</label>
              <p class="info-value">{{ selectedAsset?.name || newAsset.name }}</p>
            </div>
          </div>
          <div class="col-md-4">
            <div class="info-card">
              <label class="info-label">Asset Type</label>
              <p class="info-value">{{ selectedAsset?.type || newAsset.type }}</p>
            </div>
          </div>
          <div class="col-md-4">
            <div class="info-card">
              <label class="info-label">Value</label>
              <p class="info-value">${{ (selectedAsset?.value || newAsset.value).toLocaleString() }}</p>
            </div>
          </div>
        </div>
      </div>

      <!-- Vesting Schedule -->
      <div class="review-section mb-4">
        <h5>Vesting Schedule</h5>
        <div class="row g-3">
          <div class="col-md-3">
            <div class="info-card">
              <label class="info-label">Duration</label>
              <p class="info-value">{{ vestingSchedule.duration }} days</p>
            </div>
          </div>
          <div class="col-md-3">
            <div class="info-card">
              <label class="info-label">Cliff Period</label>
              <p class="info-value">{{ vestingSchedule.cliff_period }} days</p>
            </div>
          </div>
          <div class="col-md-3">
            <div class="info-card">
              <label class="info-label">Release Frequency</label>
              <p class="info-value">{{ vestingSchedule.release_frequency }}</p>
            </div>
          </div>
          <div class="col-md-3">
            <div class="info-card">
              <label class="info-label">Start Date</label>
              <p class="info-value">{{ formatDate(vestingSchedule.start_date) }}</p>
            </div>
          </div>
        </div>
      </div>

      <!-- Beneficiaries -->
      <div class="review-section">
        <h5>Beneficiaries</h5>
        <div class="table-responsive">
          <table class="table">
            <thead>
              <tr>
                <th>Beneficiary</th>
                <th>Percentage</th>
                <th>Amount</th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="beneficiary in vestingSchedule.beneficiaries" :key="beneficiary.beneficiary_id">
                <td>{{ getClientName(beneficiary.beneficiary_id) }}</td>
                <td>{{ beneficiary.percentage }}%</td>
                <td>${{ ((selectedAsset?.value || newAsset.value) * beneficiary.percentage / 100).toLocaleString() }}</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>

      <!-- Warning -->
      <div class="alert alert-warning mt-4">
        <i class="bi bi-exclamation-triangle me-2"></i>
        Creating a new vesting contract will invalidate any existing vesting contracts for this asset.
      </div>
    </div>

    <!-- Navigation Buttons -->
    <div class="form-navigation mt-4">
      <div class="d-flex justify-content-between">
        <button v-if="currentStep > 0"
                class="btn btn-secondary"
                @click="previousStep">
          <i class="bi bi-arrow-left me-2"></i>Previous
        </button>
        <button v-if="currentStep < steps.length - 1"
                class="btn btn-primary"
                @click="nextStep"
                :disabled="!canProceed">
          Next<i class="bi bi-arrow-right ms-2"></i>
        </button>
        <button v-else
                class="btn btn-success"
                @click="submitForm"
                :disabled="!canProceed">
          <i class="bi bi-check-lg me-2"></i>Create Contract
        </button>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted } from 'vue';
import { useToast } from 'vue-toastification';
import { apiClient } from '@/stores/auth';

export default {
  name: 'CreateVestingContract',
  emits: ['success'],
  setup(props, { emit }) {
    const toast = useToast();
    const steps = ['Select Asset', 'Vesting Details', 'Review & Confirm'];
    const currentStep = ref(0);
    const showNewAssetForm = ref(false);
    const availableAssets = ref([]);
    const clients = ref([]);
    const selectedAssetId = ref('');
    const selectedAsset = ref(null);
    const newAsset = ref({
      name: '',
      type: '',
      value: '',
      client_id: ''
    });
    const vestingSchedule = ref({
      duration: 365,
      cliff_period: 90,
      release_frequency: 'monthly',
      start_date: new Date().toISOString().split('T')[0],
      beneficiaries: []
    });

    const canProceed = computed(() => {
      switch (currentStep.value) {
        case 0:
          return selectedAssetId.value || (showNewAssetForm.value && newAsset.value.name && newAsset.value.type && newAsset.value.value && newAsset.value.client_id);
        case 1:
          return vestingSchedule.value.duration > 0 &&
                 vestingSchedule.value.cliff_period >= 0 &&
                 vestingSchedule.value.release_frequency &&
                 vestingSchedule.value.start_date &&
                 vestingSchedule.value.beneficiaries.length > 0 &&
                 vestingSchedule.value.beneficiaries.every(b => b.beneficiary_id && b.percentage > 0);
        case 2:
          return true; // All validation done in previous steps
        default:
          return false;
      }
    });

    const fetchAssets = async () => {
      try {
        const response = await apiClient.get('/tokenisation');
        availableAssets.value = response.data.assets;
      } catch (error) {
        console.error('Error fetching assets:', error);
        toast.error('Failed to fetch available assets');
      }
    };

    const fetchClients = async () => {
      try {
        const response = await apiClient.get('/clients');
        if (response.data && response.data.clients) {
          clients.value = response.data.clients.map(client => ({
            id: client.id,
            name: client.name,
            type: client.role
          }));
        } else {
          console.error('Invalid response format:', response.data);
          toast.error('Failed to load clients');
        }
      } catch (error) {
        console.error('Error fetching clients:', error);
        toast.error('Failed to load clients');
      }
    };

    const handleAssetSelect = () => {
      if (selectedAssetId.value) {
        selectedAsset.value = availableAssets.value.find(a => a.id === selectedAssetId.value);
        showNewAssetForm.value = false;
      }
    };

    const addBeneficiary = () => {
      vestingSchedule.value.beneficiaries.push({
        beneficiary_id: '',
        percentage: 0
      });
    };

    const removeBeneficiary = (index) => {
      vestingSchedule.value.beneficiaries.splice(index, 1);
    };

    const getClientName = (clientId) => {
      const client = clients.value.find(c => c.id === clientId);
      return client ? client.contact_name : 'Unknown Client';
    };

    const formatDate = (date) => {
      return new Date(date).toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      });
    };

    const nextStep = () => {
      if (canProceed.value) {
        currentStep.value++;
      }
    };

    const previousStep = () => {
      currentStep.value--;
    };

    const submitForm = async () => {
      try {
        let assetId;
        if (showNewAssetForm.value) {
          // Create new asset with required fields
          const assetResponse = await apiClient.post('/tokenisation/tokenise', {
            ...newAsset.value,
            ownership_percentage: 100, // Default to 100% ownership for new assets
            client_id: newAsset.value.client_id
          });
          assetId = assetResponse.data.asset.id;
        } else {
          assetId = selectedAssetId.value;
        }

        // Create vesting contract with required fields
        const { beneficiaries, ...scheduleWithoutBeneficiaries } = vestingSchedule.value;
        const formattedBeneficiaries = beneficiaries.map(b => ({
          beneficiary_id: parseInt(b.beneficiary_id),
          name: getClientName(b.beneficiary_id),
          percentage: parseFloat(b.percentage)
        }));

        const vestingData = {
          assetId: parseInt(assetId),
          vestingSchedule: {
            ...scheduleWithoutBeneficiaries,
            duration: parseInt(scheduleWithoutBeneficiaries.duration),
            cliff_period: parseInt(scheduleWithoutBeneficiaries.cliff_period),
            start_date: scheduleWithoutBeneficiaries.start_date
          },
          beneficiaries: formattedBeneficiaries,
          contractAddress: `0x${Array(40).fill('0123456789abcdef').map(x => x[Math.floor(Math.random() * x.length)]).join('')}`,
          blockchainTxHash: `0x${Array(64).fill('0123456789abcdef').map(x => x[Math.floor(Math.random() * x.length)]).join('')}`
        };

        console.log('Sending vesting data:', JSON.stringify(vestingData, null, 2));
        const response = await apiClient.post('/vesting', vestingData);
        console.log('Vesting record created:', response.data);

        toast.success('Vesting contract created successfully');
        emit('success');
      } catch (error) {
        console.error('Error creating vesting contract:', error);
        toast.error(error.response?.data?.error || 'Failed to create vesting contract');
      }
    };

    onMounted(() => {
      fetchAssets();
      fetchClients();
    });

    return {
      steps,
      currentStep,
      showNewAssetForm,
      availableAssets,
      clients,
      selectedAssetId,
      selectedAsset,
      newAsset,
      vestingSchedule,
      canProceed,
      handleAssetSelect,
      addBeneficiary,
      removeBeneficiary,
      getClientName,
      formatDate,
      nextStep,
      previousStep,
      submitForm
    };
  }
};
</script>

<style scoped lang="scss">
.create-vesting-form {
  max-width: 800px;
  margin: 0 auto;
  padding: 2rem;
  background: var(--bs-body-bg);
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);

  .step-indicator {
    .step {
      display: flex;
      flex-direction: column;
      align-items: center;
      position: relative;
      flex: 1;

      &:not(:last-child)::after {
        content: '';
        position: absolute;
        top: 20px;
        left: 50%;
        width: 100%;
        height: 2px;
        background: var(--bs-border-color);
        z-index: 1;
      }

      .step-number {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        background: var(--bs-light);
        border: 2px solid var(--bs-border-color);
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: 600;
        margin-bottom: 0.5rem;
        position: relative;
        z-index: 2;
      }

      .step-label {
        font-size: 0.875rem;
        color: var(--bs-muted);
      }

      &.active {
        .step-number {
          background: var(--bs-primary);
          border-color: var(--bs-primary);
          color: white;
        }

        .step-label {
          color: var(--bs-primary);
          font-weight: 500;
        }
      }

      &.completed {
        .step-number {
          background: var(--bs-success);
          border-color: var(--bs-success);
          color: white;
        }

        .step-label {
          color: var(--bs-success);
        }
      }
    }
  }

  .step-content {
    padding: 2rem 0;
  }

  .info-card {
    background: rgba(var(--bs-primary-rgb), 0.05);
    border-radius: 8px;
    padding: 1rem;
    height: 100%;

    .info-label {
      font-size: 0.875rem;
      color: var(--bs-muted);
      margin-bottom: 0.25rem;
    }

    .info-value {
      font-size: 1rem;
      font-weight: 500;
      margin: 0;
    }
  }

  .beneficiary-item {
    background: rgba(var(--bs-light-rgb), 0.1);
    border-radius: 8px;
    padding: 1rem;
  }

  .review-section {
    h5 {
      color: var(--bs-primary);
      margin-bottom: 1rem;
    }
  }

  .form-navigation {
    border-top: 1px solid var(--bs-border-color);
    padding-top: 1.5rem;
  }
}
</style>
