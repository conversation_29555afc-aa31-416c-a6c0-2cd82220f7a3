<template>
  <div class="line-chart">
    <canvas ref="chartRef"></canvas>
  </div>
</template>

<script>
import { ref, onMounted, watch } from 'vue';
import Chart from 'chart.js/auto';

export default {
  name: 'LineChart',
  props: {
    data: {
      type: Array,
      required: true
    }
  },
  setup(props) {
    const chartRef = ref(null);
    let chart = null;

    const createChart = () => {
      if (chart) {
        chart.destroy();
      }

      const ctx = chartRef.value.getContext('2d');
      chart = new Chart(ctx, {
        type: 'line',
        data: {
          labels: props.data.map(d => d.date),
          datasets: [
            {
              label: 'Vested',
              data: props.data.map(d => d.vested),
              borderColor: 'rgb(var(--bs-primary-rgb))',
              backgroundColor: 'rgba(var(--bs-primary-rgb), 0.1)',
              fill: true,
              tension: 0.4
            },
            {
              label: 'Unvested',
              data: props.data.map(d => d.unvested),
              borderColor: 'rgba(var(--bs-primary-rgb), 0.3)',
              backgroundColor: 'rgba(var(--bs-primary-rgb), 0.05)',
              fill: true,
              tension: 0.4
            }
          ]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          plugins: {
            legend: {
              position: 'top',
              labels: {
                usePointStyle: true,
                padding: 20
              }
            },
            tooltip: {
              mode: 'index',
              intersect: false,
              callbacks: {
                label: function(context) {
                  return `${context.dataset.label}: ${context.parsed.y.toFixed(1)}%`;
                }
              }
            }
          },
          scales: {
            y: {
              beginAtZero: true,
              max: 100,
              ticks: {
                callback: function(value) {
                  return value + '%';
                }
              },
              grid: {
                color: 'rgba(var(--bs-primary-rgb), 0.1)'
              }
            },
            x: {
              grid: {
                display: false
              }
            }
          }
        }
      });
    };

    onMounted(() => {
      createChart();
    });

    watch(() => props.data, () => {
      createChart();
    }, { deep: true });

    return {
      chartRef
    };
  }
};
</script>

<style scoped>
.line-chart {
  width: 100%;
  height: 100%;
  min-height: 300px;
}
</style>
