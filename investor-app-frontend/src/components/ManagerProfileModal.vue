<template>
  <div class="modal-wrapper">
    <div
      class="modal fade"
      id="managerProfileModal"
      tabindex="-1"
      aria-labelledby="managerProfileModalLabel"
      aria-hidden="true"
    >
      <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title" id="managerProfileModalLabel">Manager Profile</h5>
            <button type="button" class="modal-close-btn" data-bs-dismiss="modal" aria-label="Close">
              <i class="bi bi-x-lg"></i>
            </button>
          </div>
          <div class="modal-body">
            <div v-if="manager" class="manager-profile">
              <div class="profile-image">
                <img
                  :src="manager.manager_profile_image || defaultProfileImage"
                  alt="Manager Profile"
                  class="img-fluid rounded-circle"
                />
              </div>
              <div class="profile-info">
                <div class="info-item">
                  <label>Company Name</label>
                  <p>{{ manager.manager_company_name }}</p>
                </div>
                <div class="info-item">
                  <label>Contact Name</label>
                  <p>{{ manager.manager_contact_name }}</p>
                </div>
                <div class="info-item">
                  <label>Email</label>
                  <p>{{ manager.manager_email }}</p>
                </div>
              </div>
            </div>
            <div v-else class="no-manager">
              <i class="bi bi-person-x"></i>
              <p>No manager information available.</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed, onMounted } from 'vue';
import { useProfileStore } from '@/stores/profile';
import defaultProfileImage from '@/assets/images/default-profile.png';

const profileStore = useProfileStore();

// Compute manager details from the user's profile (returned via the GET profile request)
const manager = computed(() => {
  if (profileStore.user && profileStore.user.manager_contact_name) {
    return {
      manager_company_name: profileStore.user.manager_company_name,
      manager_contact_name: profileStore.user.manager_contact_name,
      manager_email: profileStore.user.manager_email,
      manager_profile_image: profileStore.user.manager_profile_image,
    };
  }
  return null;
});

onMounted(() => {
  // Clean up leftover modal backdrop when the modal is hidden
  const modalElement = document.getElementById("managerProfileModal");
  if (modalElement) {
    modalElement.addEventListener("hidden.bs.modal", () => {
      const backdrops = document.querySelectorAll('.modal-backdrop');
      backdrops.forEach(backdrop => backdrop.remove());
    });
  }
});
</script>

<style scoped lang="scss">
.modal-wrapper {
  position: relative;
}

.modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(5px);
  -webkit-backdrop-filter: blur(5px);
  z-index: 1055;
  display: none;

  &.fade {
    transition: opacity 0.15s linear;
  }

  &.show {
    display: block;
  }

  &:not(.show) {
    pointer-events: none;
  }
}

.modal-dialog {
  max-width: 500px;
  margin: 1.75rem auto;
  transform: translateY(-50px);
  transition: transform 0.3s ease-out;

  .modal.show & {
    transform: none;
  }
}

.modal-content {
  border: none;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  background: var(--bs-body-bg);
  position: relative;
  z-index: 2;
}

.modal-header {
  border-bottom: 1px solid var(--bs-border-color);
  padding: 1rem 1.5rem;

  .modal-title {
    font-size: 1.25rem;
    font-weight: 600;
  }
}

.modal-body {
  padding: 1.5rem;
}

.manager-profile {
  text-align: center;

  .profile-image {
    margin-bottom: 1.5rem;

    img {
      width: 120px;
      height: 120px;
      object-fit: cover;
      border: 3px solid var(--bs-primary);
      padding: 3px;
    }
  }

  .profile-info {
    .info-item {
      margin-bottom: 1rem;
      text-align: left;

      label {
        font-size: 0.875rem;
        color: var(--bs-secondary);
        margin-bottom: 0.25rem;
        display: block;
      }

      p {
        font-size: 1rem;
        margin: 0;
        color: var(--bs-body-color);
        font-weight: 500;
      }
    }
  }
}

.no-manager {
  text-align: center;
  padding: 2rem;
  color: var(--bs-secondary);

  i {
    font-size: 3rem;
    margin-bottom: 1rem;
    display: block;
  }

  p {
    margin: 0;
    font-size: 1rem;
  }
}

// Ensure proper modal display
:deep(.modal-backdrop) {
  z-index: 1054;
}

// Add fade transition for backdrop
:deep(.modal-backdrop) {
  opacity: 0;
  transition: opacity 0.15s linear;

  &.show {
    opacity: 0.5;
  }

  &:not(.show) {
    display: none;
  }
}
</style>
