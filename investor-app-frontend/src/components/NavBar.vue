<template>
  <div>
    <!-- Sidebar -->
    <div class="sidebar" :class="{ 'collapsed': isCollapsed, 'mobile-open': isMobileOpen }" ref="sidebarElement">
      <!-- Sidebar Header -->
      <div class="sidebar-header d-flex flex-row align-items-center p-3">
        <img src="@/assets/images/alchemy finance logo.svg" alt="Archimedes Finance Logo" class="sidebar-logo" />
        <a class="sidebar-brand" href="/" v-if="!isCollapsed">Archimedes Finance</a>
      </div>

      <!-- Role Badge -->
      <span v-if="authStore.user?.role && !isCollapsed"
        :class="['role-badge', authStore.user.role === 'manager' ? 'badge-manager' : 'badge-client']"
        v-tooltip="`Your account type is: ${authStore.user.role}`">
        <i :class="authStore.user.role === 'manager' ? 'bi bi-person-badge' : 'bi bi-person'"></i>
        {{ authStore.user.role === 'manager' ? 'Manager' : 'Client' }}
      </span>

      <!-- Navigation Menu -->
      <div class="sidebar-menu">
        <ul class="nav flex-column" v-if="authStore.isLoggedIn">
          <li class="nav-item collapse-toggle-target">
            <!-- Toggle Sidebar Button -->
            <a class="nav-link" @click="toggleSidebar"
              v-tooltip="{ content: `${isCollapsed ? 'Expand' : 'Collapse'} Sidebar`, placement: 'right' }">
              <i :class="isCollapsed ? 'bi bi-arrow-right-square' : 'bi bi-arrow-left-square'"></i>
              <span v-if="!isCollapsed">Collapse</span>
            </a>
          </li>
          <li class="nav-item">
            <RouterLink to="/dashboard" class="nav-link" v-tooltip="{ content: 'Dashboard', placement: 'right' }">
              <i class="bi bi-speedometer2"></i>
              <span v-if="!isCollapsed">Dashboard</span>
            </RouterLink>
          </li>
          <li class="nav-item">
            <RouterLink to="/assets" class="nav-link" v-tooltip="{ content: 'Tokenised Assets', placement: 'right' }">
              <i class="bi bi-box"></i>
              <span v-if="!isCollapsed">Tokenised Assets</span>
            </RouterLink>
          </li>
          <li class="nav-item">
            <RouterLink to="/reports" class="nav-link" v-tooltip="{ content: 'Reports', placement: 'right' }">
              <i class="bi bi-graph-up"></i>
              <span v-if="!isCollapsed">Reports</span>
            </RouterLink>
          </li>
          <li class="nav-item">
            <RouterLink to="/vesting" class="nav-link" v-tooltip="{ content: 'Vesting Management', placement: 'right' }">
              <i class="bi bi-calendar-check"></i>
              <span v-if="!isCollapsed">Vesting</span>
            </RouterLink>
          </li>
          <li class="nav-item">
            <RouterLink to="/messages" class="nav-link" v-tooltip="{ content: 'Messages', placement: 'right' }">
              <i class="bi bi-envelope"></i>
              <span v-if="!isCollapsed">Messages</span>
              <span v-if="!isCollapsed && unreadMessageCount > 0" class="badge bg-danger ms-auto">{{ unreadMessageCount }}</span>
              <span v-else-if="isCollapsed && unreadMessageCount > 0" class="badge bg-danger position-absolute top-0 end-0 translate-middle-y">{{ unreadMessageCount }}</span>
            </RouterLink>
          </li>
          <li class="nav-item">
            <RouterLink to="/settings/profile" class="nav-link" v-tooltip="{ content: 'Profile', placement: 'right' }">
              <i class="bi bi-person"></i>
              <span v-if="!isCollapsed">Profile</span>
            </RouterLink>
          </li>
          <li class="nav-item">
            <RouterLink to="/settings/subscriptions" class="nav-link"
              v-tooltip="{ content: 'Manage Subscription', placement: 'right' }">
              <i class="bi bi-credit-card"></i>
              <span v-if="!isCollapsed">Manage Subscription</span>
            </RouterLink>
          </li>
          <li v-if="authStore.user?.role === 'manager'" class="nav-item"
            v-tooltip="{ content: 'Manage Clients', placement: 'right' }">
            <RouterLink to="/clients" class="nav-link">
              <i class="bi bi-people"></i>
              <span v-if="!isCollapsed">Manage Clients</span>
            </RouterLink>
          </li>
          <li class="nav-item">
            <RouterLink to="/help" class="nav-link" v-tooltip="{ content: 'Help & Support', placement: 'right' }">
              <i class="bi bi-question-circle"></i>
              <span v-if="!isCollapsed">Help & Support</span>
            </RouterLink>
          </li>
        </ul>
      </div>

      <!-- Sidebar Footer -->
      <div class="sidebar-footer">
        <template v-if="authStore.isLoggedIn">
          <!-- Session Extension Button (only shows when session is about to expire) -->
          <SessionExtensionButton :is-collapsed="isCollapsed" />
          <NotificationsDropdown />
          <ProfileTile />
        </template>
        <template v-else>
          <RouterLink to="/auth/login" class="btn btn-outline-primary w-100">
            <i class="bi bi-box-arrow-in-right"></i>
            <span v-if="!isCollapsed">Login</span>
          </RouterLink>
        </template>
        <small class="version" v-tooltip="'Frontend application version'">Version: v-0.1.{{ version }}-beta</small>
      </div>
    </div>

    <!-- Overlay for mobile sidebar -->
    <div v-if="isMobileOpen" class="overlay" @click="toggleSidebar"></div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, watch, computed } from 'vue';
import { useAuthStore } from '@/stores/auth';
import { useMessagesStore } from '@/stores/messages';
import { RouterLink } from 'vue-router';
import ProfileTile from '@/components/ProfileTile.vue';
import NotificationsDropdown from '@/components/NotificationsDropdown.vue';
import SessionExtensionButton from '@/components/SessionExtensionButton.vue';

const version = ref(import.meta.env.VITE_APP_VERSION || '1.0.0');
const authStore = useAuthStore();
const messagesStore = useMessagesStore();
const isCollapsed = ref(localStorage.getItem('sidebarCollapsed') === 'true');
const isMobileOpen = ref(false);
const mql = window.matchMedia('(max-width: 1024px)');

// Get unread message count
const unreadMessageCount = computed(() => messagesStore.unreadCount);

// Fetch messages when component is mounted
onMounted(async () => {
  if (authStore.isLoggedIn) {
    await messagesStore.fetchMessages();
  }
});

// Watch for auth changes to fetch messages
watch(() => authStore.isLoggedIn, (isLoggedIn) => {
  if (isLoggedIn) {
    messagesStore.fetchMessages();
  }
});

// Toggle Sidebar based on screen size
const toggleSidebar = () => {
  if (mql.matches) {
    isMobileOpen.value = !isMobileOpen.value;
  } else {
    isCollapsed.value = !isCollapsed.value;
    localStorage.setItem('sidebarCollapsed', isCollapsed.value);
  }
};

// Touch gesture handlers for mobile sidebar
const handleTouchStart = (event) => {
  const touch = event.touches[0];
  if (mql.matches && touch.clientX < 50) {
    isMobileOpen.value = true;
  }
};

const handleTouchEnd = (event) => {
  const touch = event.changedTouches[0];
  if (mql.matches && isMobileOpen.value && touch.clientX > 200) {
    isMobileOpen.value = false;
  }
};

onMounted(() => {
  if (mql.matches) {
    window.addEventListener('touchstart', handleTouchStart);
    window.addEventListener('touchend', handleTouchEnd);
  }
});

onUnmounted(() => {
  if (mql.matches) {
    window.removeEventListener('touchstart', handleTouchStart);
    window.removeEventListener('touchend', handleTouchEnd);
  }
});

watch(isCollapsed, () => {
  document.body.classList.toggle('sidebar-collapsed', isCollapsed.value);
});
</script>

<style lang="scss">
.sidebar {
  position: sticky;
  left: 0;
  top: 0;
  display: flex;
  flex-direction: column;
  background-color: var(--bs-card2-bg);
  z-index: 99;
  max-width: 280px;
  width: 280px;
  height: 100vh;
  border-right: 1px solid var(--bs-border-color);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 0 20px rgba(var(--bs-dark-rgb), 0.05);

  // Sidebar Header
  .sidebar-header {
    padding: 1.5rem;
    display: flex;
    align-items: center;
    gap: 1rem;
    border-bottom: 1px solid var(--bs-border-color);

    .sidebar-logo {
      height: 24px;
      width: auto;
      transition: all 0.3s ease;
    }

    .sidebar-brand {
      font-size: 1rem;
      font-weight: 700;
      color: var(--bs-primary);
      text-decoration: none;
      white-space: nowrap;
      opacity: 1;
      transition: opacity 0.3s ease;
      letter-spacing: -0.01em;
    }
  }

  // Role Badge
  .role-badge {
    margin: 0 1.5rem;
    padding: 0.5rem;
    font-size: 0.75rem;
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    margin-top: 1rem;
    transition: all 0.3s ease;

    &.badge-manager {
      background: rgba(46, 204, 113, 0.1);
      color: #2ecc71;
    }

    &.badge-client {
      background: rgba(76, 130, 247, 0.1);
      color: #4c82f7;
    }

    i {
      font-size: 1rem;
    }
  }

  // Navigation Menu
  .sidebar-menu {
    flex: 1;
    padding: 1.5rem 1.25rem;
    overflow-y: auto;

    .nav-item {
      margin-bottom: 0.5rem;

      .nav-link {
        display: flex;
        align-items: center;
        padding: 0.875rem 1.25rem;
        color: var(--bs-body-color);
        border-radius: 10px;
        transition: all 0.3s ease;
        gap: 1rem;
        opacity: 0.75;

        i {
          font-size: 1.2rem;
          transition: transform 0.3s ease;
        }

        span {
          font-size: 0.95rem;
          font-weight: 500;
          white-space: nowrap;
          opacity: 1;
          transition: opacity 0.3s ease;
        }

        &:hover {
          background: rgba(var(--bs-primary-rgb), 0.08);
          color: var(--bs-primary);
          opacity: 1;

          i {
            transform: translateX(3px);
          }
        }

        &.router-link-active {
          background: var(--bs-primary);
          color: white;
          opacity: 1;
          box-shadow: 0 4px 12px rgba(var(--bs-primary-rgb), 0.2);

          &:hover {
            background: var(--bs-primary);
            opacity: 0.95;
          }
        }
      }

      // Divider
      &.nav-divider {
        height: 1px;
        background-color: var(--bs-border-color);
        opacity: 0.5;
        margin: 1rem 0;
      }
    }
  }

  // Sidebar Footer
  .sidebar-footer {
    padding: 1.25rem 1.5rem;
    display: flex;
    flex-direction: column;
    gap: 1rem;
    border-top: 1px solid var(--bs-border-color);

    .version {
      font-size: 0.75rem;
      color: var(--bs-body-color);
      opacity: 0.5;
      text-align: center;
      margin-top: 0.5rem;
    }
  }

  // Profile Tile Component
  .profile-tile {
    background: rgba(var(--bs-light-rgb), 0.5);
    border-radius: 12px;
    padding: 0.75rem;
    display: grid;
    grid-template-columns: 1fr auto;
    gap: 0.5rem;
    transition: all 0.3s ease;

    &:hover {
      background: rgba(var(--bs-light-rgb), 1);
    }

    .profile-info {
      display: flex;
      align-items: center;
      gap: 0.75rem;

      .profile-avatar {
        width: 38px;
        height: 38px;
        border-radius: 10px;
        object-fit: cover;
        border: 2px solid rgba(255, 255, 255, 0.1);
        transition: all 0.3s ease;

        &:hover {
          transform: scale(1.05);
          border-color: var(--bs-primary);
        }
      }

      .profile-details {
        .profile-name {
          font-size: 0.9rem;
          font-weight: 500;
          margin: 0;
          color: var(--bs-body-color);
          line-height: 1.2;
        }

        .profile-role {
          font-size: 0.75rem;
          color: var(--bs-muted);
          margin: 0;
          line-height: 1.2;
        }
      }
    }

    .menu-container {
      position: relative;

      .settings-btn {
        width: 32px;
        height: 32px;
        padding: 0;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 8px;
        background: transparent;
        border: none;
        color: var(--bs-muted);
        transition: all 0.3s ease;

        &:hover,
        &.active {
          background: rgba(255, 255, 255, 0.05);
          color: var(--bs-primary);
        }

        i {
          font-size: 1.2rem;
        }
      }

      .dropdown-menu {
        position: absolute;
        bottom: 100%;
        right: -4rem;
        left: auto;
        margin-bottom: 0.5rem;
        min-width: 200px;
        border: 1px solid rgba(255, 255, 255, 0.1);
        border-radius: 12px;
        padding: 0.5rem;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
        backdrop-filter: blur(10px);
        -webkit-backdrop-filter: blur(10px);

        .dropdown-item {
          display: flex;
          align-items: center;
          gap: 0.75rem;
          padding: 0.75rem 1rem;
          color: var(--bs-body-color);
          border-radius: 8px;
          transition: all 0.3s ease;

          i {
            font-size: 1.1rem;
            color: var(--bs-muted);
            transition: all 0.3s ease;
          }

          &:hover {
            background: rgba(255, 255, 255, 0.05);
            color: var(--bs-primary);

            i {
              color: var(--bs-primary);
              transform: translateX(3px);
            }
          }
        }
      }
    }
  }

  // Collapsed State
  &.collapsed {
    width: 80px;

    .sidebar-header {
      justify-content: center;
      padding: 1.5rem 0.75rem;

      .sidebar-logo {
        height: 32px;
      }
    }

    .sidebar-menu {
      padding: 1.5rem 0.75rem;

      .nav-link {
        justify-content: center;
        padding: 0.875rem;

        i {
          margin: 0;
        }
      }
    }

    .sidebar-footer {
      padding: 1.25rem 0.75rem;
      align-items: center;
    }

    .sidebar-brand,
    .nav-link span,
    .role-badge span {
      opacity: 0;
      width: 0;
      display: none;
    }

    .role-badge {
      margin: 1rem auto;
      width: 40px;
      height: 40px;
      padding: 0;
      border-radius: 50%;

      i {
        margin: 0;
      }
    }

    .profile-tile {
      grid-template-columns: 1fr;
      padding: 0.75rem;

      .profile-info {
        justify-content: center;
        align-items: center;

        .profile-avatar {
          width: 42px;
          height: 42px;
        }

        .profile-details {
          display: none;
        }
      }

      .menu-container {
        display: none;
      }
    }

    .notifications-container {
      .notification-btn {
        width: 100%;
        height: 42px;
        padding: 0;
        justify-content: center;
        margin: 0 auto;

        .label {
          display: none;
        }

        .badge {
          position: absolute;
          top: -5px;
          right: -5px;
          margin: 0;
          padding: 0.25rem 0.5rem;
          font-size: 0.7rem;
          min-width: 20px;
          height: 20px;
          display: flex;
          align-items: center;
          justify-content: center;
          border-radius: 10px;
        }
      }
    }
  }

  // Mobile Styles
  @media (max-width: 1024px) {
    position: fixed;
    transform: translateX(-100%);
    box-shadow: 0 0 30px rgba(var(--bs-dark-rgb), 0.15);
    z-index: 1050;
    width: 100%;
    max-width: 300px;

    &.mobile-open {
      transform: translateX(0);
    }
  }
}

// Overlay for mobile sidebar
.overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--bs-secondary-bg);
  z-index: 1040;
  backdrop-filter: blur(4px);
  -webkit-backdrop-filter: blur(4px);
}

// Mobile toggle button
.mobile-toggle {
  position: fixed;
  top: 1rem;
  left: 1rem;
  z-index: 1030;
  width: 40px;
  height: 40px;
  border-radius: 8px;
  background-color: var(--bs-primary);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  border: none;
  box-shadow: 0 4px 12px rgba(var(--bs-primary-rgb), 0.2);
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 16px rgba(var(--bs-primary-rgb), 0.3);
  }

  i {
    font-size: 1.25rem;
  }

  @media (min-width: 1025px) {
    display: none;
  }
}

// Modal z-index fix
.modal-open {
  .sidebar {
    z-index: 1030;
  }
}
</style>
