<template>
  <div :class="['dropdown notifications-container', { collapsed: isCollapsed }]"
    v-tooltip="`You have ${unreadCount} unread notifications`">
    <!-- Expanded View -->
    <button v-if="!isCollapsed" class="btn btn-outline-primary dropdown-toggle notification-btn" type="button"
      @click="openModal" :disabled="isLoading">
      <i class="bi bi-bell icon"></i>
      <span class="label"> Notifications</span>
      <span v-if="unreadCount > 0" class="badge bg-danger">{{ unreadCount }}</span>
      <i v-if="isLoading" class="bi bi-arrow-repeat ms-2 spinner-icon"></i>
    </button>

    <!-- Collapsed View -->
    <button v-else class="btn btn-icon notification-btn-collapsed" @click="openModal" data-bs-toggle="tooltip"
      title="Notifications" :disabled="isLoading">
      <i class="bi bi-bell icon"></i>
      <span v-if="unreadCount > 0" class="badge bg-danger small-badge">{{ unreadCount }}</span>
      <i v-if="isLoading" class="bi bi-arrow-repeat spinner-icon"></i>
    </button>

    <NotificationsModal ref="notificationsModalRef" :isLoading="isLoading" />
  </div>
</template>

<script setup>
import { computed, ref, watch } from "vue";
import { useNotificationsStore } from "@/stores/notifications";
import NotificationsModal from "@/components/NotificationsModal.vue";
import { Modal } from "bootstrap";

const notificationsStore = useNotificationsStore();
const notificationsModalRef = ref(null);
const isCollapsed = ref(false);
const isLoading = ref(false);

// Opens the notifications modal after fetching notifications
const openModal = async () => {
  try {
    isLoading.value = true;
    await notificationsStore.fetchNotifications();
    const modalElement = document.getElementById("notificationsModal");
    if (modalElement) {
      const bootstrapModal = new Modal(modalElement);
      bootstrapModal.show();

      // The actual marking of notifications as read is handled by the event listener in NotificationsModal.vue
      // when the modal is shown (via the 'shown.bs.modal' event)
    }
  } catch (_) {
    // Silently handle errors
  } finally {
    isLoading.value = false;
  }
};

// Computes unread notifications count
const unreadCount = computed(() =>
  notificationsStore.notifications.filter((n) => n.status !== "read" && n.status !== "accepted").length
);

// Watch for sidebar collapse state
watch(
  () => notificationsStore.isSidebarCollapsed,
  (newVal) => {
    isCollapsed.value = newVal;
  }
);
</script>

<style scoped lang="scss">
.notifications-container {
  position: relative;
  width: 100%;

  .notification-btn {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    gap: 0.75rem;
    padding: 0.75rem 1rem;
    background: rgba(var(--bs-light-rgb), 1);
    border: none;
    border-radius: 12px;
    color: var(--bs-body-color);
    transition: all 0.3s ease;

    &:hover {
      background: rgba(var(--bs-light-rgb), .5);
      color: var(--bs-primary);

      i {
        transform: translateY(-2px);
      }
    }

    i {
      font-size: 1.2rem;
      transition: transform 0.3s ease;
    }

    .label {
      font-size: 0.9rem;
      font-weight: 500;
    }

    .badge {
      margin-left: auto;
      background: var(--bs-primary);
      font-size: 0.75rem;
      padding: 0.25rem 0.5rem;
      border-radius: 6px;
    }

    &:disabled {
      opacity: 0.7;
      cursor: not-allowed;
    }

    .spinner-icon {
      font-size: 1.1rem;
      animation: spin 1s linear infinite;
    }
  }

  // Collapsed notification button
  &.collapsed {
    .notification-btn {
      width: 42px;
      height: 42px;
      padding: 0;
      justify-content: center;
      margin: 0 auto;

      .label {
        display: none;
      }

      .badge {
        position: absolute;
        top: -5px;
        right: -5px;
        margin: 0;
        padding: 0.25rem 0.5rem;
        font-size: 0.7rem;
        min-width: 20px;
        height: 20px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 10px;
      }
    }
  }
}

.notification-btn-collapsed {
  &:disabled {
    opacity: 0.7;
    cursor: not-allowed;
  }

  .spinner-icon {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 1.2rem;
    animation: spin 1s linear infinite;
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

// Notification Modal Styles
::v-deep(.modal-content) {
  background: var(--bs-dark);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);

  .modal-header {
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    padding: 1.25rem 1.5rem;

    .modal-title {
      font-size: 1.1rem;
      font-weight: 600;
      color: var(--bs-body-color);
    }

    .btn-close {
      color: var(--bs-body-color);
      opacity: 0.5;
      transition: opacity 0.3s ease;

      &:hover {
        opacity: 1;
      }
    }
  }

  .modal-body {
    padding: 1.5rem;

    .notification-item {
      background: rgba(255, 255, 255, 0.02);
      border: 1px solid rgba(255, 255, 255, 0.05);
      border-radius: 12px;
      padding: 1rem;
      margin-bottom: 0.75rem;
      transition: all 0.3s ease;

      &:hover {
        background: rgba(255, 255, 255, 0.05);
        transform: translateY(-2px);
      }

      &.highlight {
        background: rgba(var(--bs-primary-rgb), 0.1);
        border-color: rgba(var(--bs-primary-rgb), 0.2);
      }

      .notification-text {
        p {
          font-size: 0.9rem;
          color: var(--bs-body-color);
          margin-bottom: 0.25rem;
        }

        small {
          font-size: 0.75rem;
          color: var(--bs-muted);
        }
      }

      .notification-actions {
        .btn {
          width: 32px;
          height: 32px;
          padding: 0;
          display: flex;
          align-items: center;
          justify-content: center;
          border-radius: 8px;
          background: rgba(255, 255, 255, 0.05);
          border: none;
          color: var(--bs-muted);
          transition: all 0.3s ease;

          &:hover {
            background: rgba(255, 255, 255, 0.1);
            color: var(--bs-primary);
            transform: scale(1.05);
          }

          i {
            font-size: 1rem;
          }
        }
      }
    }
  }
}

// Fix modal z-index and interaction issues
::v-deep(.modal) {
  z-index: 1050;

  &:not(.show) {
    pointer-events: none;
    display: none;
  }
}

::v-deep(.modal-backdrop) {
  z-index: 1040;

  &:not(.show) {
    display: none;
  }
}

::v-deep(.modal-dialog) {
  z-index: 1055;
}
</style>
