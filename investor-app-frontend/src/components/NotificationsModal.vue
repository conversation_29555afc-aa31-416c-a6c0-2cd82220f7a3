<template>
  <div>
    <div class="modal fade" id="notificationsModal" tabindex="-1" aria-hidden="true">
      <div class="modal-dialog modal-lg modal-dialog-centered">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title">Notifications</h5>
            <button type="button" class="modal-close-btn" data-bs-dismiss="modal" aria-label="Close">
              <i class="bi bi-x-lg"></i>
            </button>
          </div>
          <div class="modal-body">
            <!-- Loading State -->
            <div v-if="isLoading" class="loading-container">
              <i class="bi bi-arrow-repeat spinner-icon"></i>
              <p class="mt-3 text-muted">Loading notifications...</p>
            </div>

            <!-- Content when not loading -->
            <template v-else>
              <!-- Action Required Section -->
              <div v-if="actionNotifications.length > 0" class="mb-4">
                <h6 class="mb-3">Action Required</h6>
                <div class="notifications-list">
                  <div v-for="notification in actionNotifications" :key="notification.id"
                    class="notification-item d-flex align-items-center justify-content-between p-3 mb-2 rounded border highlight">
                    <div class="notification-text">
                      <p class="mb-1 fw-bold">{{ notification.message }}</p>
                      <small class="text-muted">{{ formatDate(notification.created_at) }}</small>
                    </div>
                    <div class="notification-actions d-flex gap-2">
                      <button v-if="notification.type === 'link_request'" class="btn btn-success btn-sm"
                        @click="acceptLinkRequest(notification)">
                        <i class="bi bi-check-circle"></i>
                      </button>
                      <button v-if="notification.type === 'link_request'" class="btn btn-danger btn-sm"
                        @click="ignoreRequest(notification.id)">
                        <i class="bi bi-x-circle"></i>
                      </button>
                      <button v-if="notification.type === 'new_message'" class="btn btn-primary btn-sm"
                        @click="viewMessage(notification)">
                        <i class="bi bi-envelope-open"></i>
                      </button>
                    </div>
                  </div>
                </div>
              </div>

              <!-- History / Records Section -->
              <div v-if="recordNotifications.length > 0">
                <h6 class="mb-3">Notification History</h6>
                <div class="notifications-list">
                  <div v-for="notification in recordNotifications" :key="notification.id"
                    class="notification-item d-flex align-items-center justify-content-between p-3 mb-2 rounded">
                    <div class="notification-text">
                      <p class="mb-1">{{ notification.message }}</p>
                      <small class="text-muted">{{ formatDate(notification.created_at) }}</small>
                    </div>
                    <div class="notification-actions">
                      <button class="btn btn-outline-secondary btn-sm" @click="markAsRead(notification)">
                        <i class="bi bi-eye"></i>
                      </button>
                    </div>
                  </div>
                </div>
              </div>

              <!-- No Notifications -->
              <div v-if="notifications.length === 0" class="text-center text-muted py-4">
                No notifications available.
              </div>
            </template>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed, onMounted } from "vue";
import axios from "axios";
import { useNotificationsStore } from "@/stores/notifications";
import { Modal } from "bootstrap";
import { useAuthStore } from "@/stores/auth";
import { useToast } from "vue-toastification";
import { useRouter } from "vue-router";

const props = defineProps({
  isLoading: {
    type: Boolean,
    default: false
  }
});

const toast = useToast();
const notificationsStore = useNotificationsStore();
const authStore = useAuthStore();
const router = useRouter();

// Get notifications from the store
const notifications = computed(() => notificationsStore.notifications || []);

// Computed properties for grouping notifications
const actionNotifications = computed(() => {
  // Only notifications that are pending action. Adjust this logic as needed.
  return notifications.value.filter(n => n.status === "pending");
});
const recordNotifications = computed(() => {
  // All notifications that have been acted upon (accepted, read, ignored)
  return notifications.value.filter(n => n.status !== "pending");
});

// Format the date for display
const formatDate = (dateStr) => {
  const options = { year: 'numeric', month: 'short', day: 'numeric', hour: '2-digit', minute: '2-digit' };
  return new Date(dateStr).toLocaleDateString(undefined, options);
};

// Accept link request function
const acceptLinkRequest = async (notification) => {
  try {
    const token = authStore.token;
    const userId = authStore.user?.id;
    if (!token || !userId) {
      return;
    }
    const response = await axios.post(
      `${import.meta.env.VITE_API_URL}/clients/accept-link`,
      { clientId: userId, notificationId: notification.id },
      { headers: { Authorization: `Bearer ${token}` } }
    );
    if (response.status === 200 && response.data?.message) {
      // Update notification to mark it as read (or another allowed status)
      await axios.put(
        `${import.meta.env.VITE_API_URL}/notifications/${notification.id}`,
        { status: "read" },
        { headers: { Authorization: `Bearer ${token}` } }
      );
      await notificationsStore.fetchNotifications();
      toast.success("Link request accepted successfully!", { position: "top-right" });
    } else {
      throw new Error(`Unexpected response format: ${JSON.stringify(response.data)}`);
    }
  } catch (error) {
    toast.error("Failed to accept the link request. Please try again.", { position: "top-right" });
  }
};

// Ignore request function
const ignoreRequest = async (notificationId) => {
  try {
    const token = authStore.token;
    if (!token) {
      return;
    }
    await axios.post(
      `${import.meta.env.VITE_API_URL}/notifications/ignore-link`,
      { notificationId },
      { headers: { Authorization: `Bearer ${token}` } }
    );
    // Remove the ignored notification from the local store
    notificationsStore.notifications = notificationsStore.notifications.filter(n => n.id !== notificationId);
    toast.success("Notification ignored.", { position: "top-right" });
  } catch (error) {
    toast.error("Failed to ignore notification.", { position: "top-right" });
  }
};

// Mark as read function
const markAsRead = async (notification) => {
  try {
    const token = authStore.token;
    if (!token) {
      return;
    }
    await axios.put(
      `${import.meta.env.VITE_API_URL}/notifications/${notification.id}`,
      { status: "read" },
      { headers: { Authorization: `Bearer ${token}` } }
    );
    await notificationsStore.fetchNotifications();
  } catch (error) {
  }
};

// View message function
const viewMessage = async (notification) => {
  try {
    // Close the notifications modal
    const modalElement = document.getElementById("notificationsModal");
    if (modalElement) {
      const modal = Modal.getInstance(modalElement);
      if (modal) {
        modal.hide();
      }
    }

    // Mark the notification as read
    await markAsRead(notification);

    // Extract message ID from notification metadata
    const metadata = typeof notification.metadata === 'string'
      ? JSON.parse(notification.metadata)
      : notification.metadata;

    if (metadata && metadata.messageId) {
      // Navigate to messages page
      router.push('/messages');

      // Open the message detail modal (with a slight delay to ensure the page loads)
      setTimeout(() => {
        // We'll use a custom event to communicate with the MessagesView component
        const event = new CustomEvent('open-message-detail', {
          detail: { messageId: metadata.messageId }
        });
        window.dispatchEvent(event);
      }, 500);
    }
  } catch (error) {
    toast.error("Failed to open message. Please try again.");
  }
};

// Function to mark non-action notifications as read when modal is opened
const markNonActionNotificationsAsRead = async () => {
  try {
    // Get notifications that don't have actions (not pending) and are not already read
    const nonActionNotifications = notifications.value.filter(n =>
      n.status !== "pending" && n.status !== "read" && n.status !== "approved" && n.status !== "ignored"
    );

    if (nonActionNotifications.length > 0) {

      // Extract IDs of notifications to mark as read
      const notificationIds = nonActionNotifications.map(notification => notification.id);

      // Use the batch endpoint to mark all notifications as read at once
      await notificationsStore.markMultipleAsRead(notificationIds);
    }
  } catch (error) {
  }
};

onMounted(() => {
  notificationsStore.fetchNotifications();
  // Clean up leftover modal backdrop when the modal is hidden.
  const modalElement = document.getElementById("notificationsModal");
  if (modalElement) {
    modalElement.addEventListener("hidden.bs.modal", () => {
      const backdrops = document.querySelectorAll('.modal-backdrop');
      backdrops.forEach(backdrop => backdrop.remove());
    });

    // Add event listener for when modal is shown
    modalElement.addEventListener("shown.bs.modal", markNonActionNotificationsAsRead);
  }
});
</script>

<style scoped lang="scss">
#notificationsModal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  max-width: 100vw;
  backdrop-filter: blur(10px);
}
.modal-header {
  border-bottom: 1px solid var(--bs-border-color);
}

.modal-title {
  font-size: 1.25rem;
  font-weight: 600;
}

.modal-body {
  padding: 1.5rem;
}

.notifications-list {
  max-height: 60vh;
  overflow-y: auto;
}

.notification-item {
  background-color: var(--bs-card2-bg);

  &.highlight {
    background-color: var(--bs-container-bg);
  }
}

.notification-text p {
  margin: 0;
  font-size: 0.95rem;
}

.notification-actions button {
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 36px;
  min-height: 36px;
  border-radius: 0.375rem;
  transition: background-color 0.3s ease;
}

.notification-actions button:hover {
  opacity: 0.9;
}

// Ensure modal is properly layered
::v-deep(.modal) {
  z-index: 1050;
}

::v-deep(.modal-backdrop) {
  z-index: 1040;
}

::v-deep(.modal-dialog) {
  z-index: 1055;
}

// Remove pointer-events from modal when not visible
::v-deep(.modal:not(.show)) {
  pointer-events: none;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 200px;
  padding: 2rem;

  .spinner-icon {
    font-size: 2.5rem;
    color: var(--bs-primary);
    animation: spin 1s linear infinite;
  }

  p {
    font-size: 1rem;
    margin: 0;
    color: var(--bs-muted);
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
</style>
