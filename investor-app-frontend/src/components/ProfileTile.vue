<template>
  <div class="profile-tile">
    <div class="profile-info">
      <!-- Clicking the avatar navigates to the profile page -->
      <RouterLink to="/settings/profile">
        <img :src="profileStore.profilePicture || profileStore.defaultProfilePicture" alt="User Avatar"
          class="profile-avatar" />
      </RouterLink>
      <!-- Only show details when sidebar is not collapsed -->
      <div class="profile-details" v-if="!isCollapsed">
        <p class="profile-name">{{ profileStore.user?.contact_name || 'User Name' }}</p>
        <p class="profile-role">
          {{ profileStore.user?.role === 'manager' ? 'Investment Manager' : 'Investor' }}
        </p>
      </div>
    </div>
    <!-- Settings / Menu Button -->
    <div v-if="!isCollapsed" class="menu-container">
      <button :class="`btn settings-btn ${menuOpen ? 'active' : ''}`" @click.stop="toggleMenu">
        <i class="bi bi-three-dots-vertical"></i>
      </button>
      <div v-if="menuOpen" class="dropdown-menu show">
        <RouterLink to="/settings/profile" class="dropdown-item" @click.stop="closeMenu"><i class="bi bi-person"></i>Profile
        </RouterLink>
        <RouterLink to="/documentation" class="dropdown-item" @click.stop="closeMenu"><i
            class="bi bi-file-binary"></i>Documentation</RouterLink>
        <!-- Theme Switcher Menu Item -->
        <button type="button" class="dropdown-item theme-switcher-item" @click.stop="toggleTheme($event)">
          <i :class="theme === 'light' ? 'bi bi-moon-stars' : 'bi bi-sun'"></i>
          {{ theme === 'dark' ? 'Light' : 'Dark' }} Theme
        </button>
        <!-- Dashboard Walkthrough Menu Item -->
        <button type="button" class="dropdown-item" @click.stop="restartWalkthrough($event)">
          <i class="bi bi-info-circle"></i>
          Dashboard Walkthrough
        </button>
        <button class="dropdown-item" @click.stop="logout">
          <i class="bi bi-box-arrow-right"></i>Logout
        </button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, watch } from 'vue';
import { useProfileStore } from '@/stores/profile';
import { useAuthStore } from '@/stores/auth';
import { useWalkthroughStore } from '@/stores/walkthrough';
import { useRouter } from 'vue-router';

const profileStore = useProfileStore();
const authStore = useAuthStore();
const walkthroughStore = useWalkthroughStore();
const router = useRouter();

// Sidebar collapse state; might be managed globally in your app
const isCollapsed = ref(false);
const menuOpen = ref(false);

// Theme: initialise from localStorage or default to 'light'
const theme = ref(localStorage.getItem('theme') || 'light');

const toggleTheme = (event) => {
  // Prevent the click event from bubbling up to the parent
  event.stopPropagation();
  // Toggle theme between 'light' and 'dark'
  theme.value = (theme.value === 'dark') ? 'light' : 'dark';
  document.documentElement.setAttribute('data-bs-theme', theme.value);
  localStorage.setItem('theme', theme.value);
  closeMenu();
};

const restartWalkthrough = async (event) => {
  // Prevent the click event from bubbling up to the parent
  event.stopPropagation();
  // Reset walkthrough state
  walkthroughStore.resetWalkthrough();
  closeMenu();

  // Navigate to dashboard if not already there
  if (router.currentRoute.value.name !== 'dashboard') {
    await router.push('/dashboard');
    // Wait for the dashboard to load before starting the walkthrough
    setTimeout(() => {
      walkthroughStore.startWalkthrough();
    }, 500);
  } else {
    // If already on dashboard, start the walkthrough immediately
    walkthroughStore.startWalkthrough();
  }
};

const toggleMenu = () => {
  menuOpen.value = !menuOpen.value;
};

const closeMenu = () => {
  menuOpen.value = false;
};

const logout = () => {
  authStore.logout();
  closeMenu();
  router.push('/auth/login');
};

watch(() => profileStore.user, (newUser) => {
  console.log('Profile updated:', newUser);
});
</script>

<style scoped lang="scss">
.profile-tile {
  background-color: rgba(var(--bs-primary-rgb), 0.05);
  border-radius: 12px;
  padding: 0.75rem;
  display: grid;
  grid-template-columns: 1fr auto;
  gap: 0.5rem;
  transition: all 0.3s ease;
  border: 1px solid rgba(var(--bs-primary-rgb), 0.1);

  &:hover {
    background-color: rgba(var(--bs-primary-rgb), 0.08);
  }

  .profile-info {
    display: flex;
    align-items: center;
    gap: 0.75rem;

    .profile-avatar {
      width: 40px;
      height: 40px;
      border-radius: 10px;
      object-fit: cover;
      border: 2px solid rgba(var(--bs-primary-rgb), 0.2);
      transition: all 0.3s ease;
      box-shadow: 0 4px 8px rgba(var(--bs-dark-rgb), 0.1);

      &:hover {
        transform: scale(1.05);
        border-color: var(--bs-primary);
        box-shadow: 0 6px 12px rgba(var(--bs-primary-rgb), 0.2);
      }
    }

    .profile-details {
      .profile-name {
        font-size: 0.95rem;
        font-weight: 600;
        margin: 0;
        color: var(--bs-body-color);
        line-height: 1.2;
      }

      .profile-role {
        font-size: 0.8rem;
        color: var(--bs-body-color);
        opacity: 0.6;
        margin: 0;
        line-height: 1.2;
      }
    }
  }

  .menu-container {
    position: relative;

    .settings-btn {
      width: 34px;
      height: 34px;
      padding: 0;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 8px;
      background: transparent;
      border: none;
      color: var(--bs-body-color);
      opacity: 0.7;
      transition: all 0.2s ease;

      &:hover,
      &.active {
        background-color: rgba(var(--bs-primary-rgb), 0.1);
        color: var(--bs-primary);
        opacity: 1;
      }

      i {
        font-size: 1.2rem;
      }
    }

    .dropdown-menu {
      position: absolute;
      bottom: 100%;

      margin-bottom: 0.5rem;
      min-width: 220px;
      border: 1px solid var(--bs-border-color);
      border-radius: 12px;
      padding: 0.75rem;
      box-shadow: 0 8px 24px rgba(var(--bs-dark-rgb), 0.1);
      background-color: var(--bs-card2-bg);
      z-index: 1000;

      .dropdown-item {
        display: flex;
        align-items: center;
        gap: 0.75rem;
        padding: 0.75rem 1rem;
        color: var(--bs-body-color);
        border-radius: 8px;
        transition: all 0.2s ease;
        text-decoration: none;
        border: none;
        background: transparent;
        width: 100%;
        text-align: left;
        cursor: pointer;

        i {
          font-size: 1.1rem;
          color: var(--bs-body-color);
          opacity: 0.7;
          transition: all 0.2s ease;
        }

        &:hover {
          background-color: rgba(var(--bs-primary-rgb), 0.08);
          color: var(--bs-primary);

          i {
            color: var(--bs-primary);
            opacity: 1;
            transform: translateX(3px);
          }
        }
      }
    }
  }
}

// Collapsed state adjustments
.sidebar.collapsed {
  .profile-tile {
    padding: 0.5rem;
    background-color: transparent;
    border: none;

    .profile-info {
      justify-content: center;

      .profile-avatar {
        width: 44px;
        height: 44px;
        border-radius: 12px;
      }

      .profile-details {
        display: none;
      }
    }

    .menu-container {
      display: none;
    }
  }
}
</style>
