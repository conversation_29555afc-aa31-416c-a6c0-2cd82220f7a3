<template>
  <div v-if="isOpen" class="modal-overlay" @click="close">
    <div class="modal-content" @click.stop>
      <div class="modal-header">
        <h2>Export Reports</h2>
        <button class="close-button" @click="close">&times;</button>
      </div>

      <div class="modal-body">
        <div class="export-options">
          <div class="format-selection">
            <h3>Export Format</h3>
            <div class="format-grid">
              <div
                v-for="format in exportFormats"
                :key="format.id"
                class="format-card"
                :class="{ 'selected': selectedFormat === format.id }"
                @click="selectedFormat = format.id"
              >
                <div class="format-icon">{{ format.icon }}</div>
                <div class="format-info">
                  <h4>{{ format.name }}</h4>
                  <p>{{ format.description }}</p>
                </div>
              </div>
            </div>
          </div>

          <div class="data-selection">
            <h3>Data to Include</h3>
            <div class="checkbox-group">
              <label v-for="option in dataOptions" :key="option.id">
                <input
                  type="checkbox"
                  v-model="selectedData"
                  :value="option.id"
                >
                {{ option.label }}
              </label>
            </div>
          </div>

          <div class="date-range">
            <h3>Date Range</h3>
            <div class="date-inputs">
              <div class="input-group">
                <label>Start Date</label>
                <input type="date" v-model="dateRange.start" />
              </div>
              <div class="input-group">
                <label>End Date</label>
                <input type="date" v-model="dateRange.end" />
              </div>
            </div>
          </div>
        </div>

        <div class="export-preview" v-if="previewData">
          <h3>Preview</h3>
          <div class="preview-content">
            <pre>{{ previewData }}</pre>
          </div>
        </div>
      </div>

      <div class="modal-footer">
        <button class="btn btn-secondary" @click="close">Cancel</button>
        <button
          class="btn btn-primary"
          @click="exportReport"
          :disabled="!selectedFormat || selectedData.length === 0"
        >
          Export
        </button>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, watch } from 'vue';
import { useAuthStore } from '@/stores/auth';
import axios from 'axios';
import * as XLSX from 'xlsx';
import { saveAs } from 'file-saver';
import Papa from 'papaparse';

export default {
  name: 'ReportExportModal',
  props: {
    isOpen: Boolean,
    filters: Object,
    assets: Array,
    statistics: Object
  },
  emits: ['close'],
  setup(props, { emit }) {
    const authStore = useAuthStore();
    const selectedFormat = ref('xlsx');
    const selectedData = ref(['assets', 'statistics']);
    const dateRange = ref({
      start: props.filters?.startDate || '',
      end: props.filters?.endDate || ''
    });

    const exportFormats = [
      {
        id: 'xlsx',
        name: 'Excel (XLSX)',
        description: 'Microsoft Excel format, compatible with most financial software',
        icon: '📊',
        mimeType: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
      },
      {
        id: 'csv',
        name: 'CSV',
        description: 'Comma-separated values, widely supported by accounting systems',
        icon: '📝',
        mimeType: 'text/csv'
      },
      {
        id: 'pdf',
        name: 'PDF',
        description: 'Portable Document Format, suitable for sharing and printing',
        icon: '📄',
        mimeType: 'application/pdf'
      },
      {
        id: 'qbo',
        name: 'QuickBooks',
        description: 'QuickBooks Online format for direct import',
        icon: '💼',
        mimeType: 'application/x-qbo'
      },
      {
        id: 'ofx',
        name: 'OFX',
        description: 'Open Financial Exchange format for banking systems',
        icon: '🏦',
        mimeType: 'application/x-ofx'
      }
    ];

    const dataOptions = [
      { id: 'assets', label: 'Asset Details' },
      { id: 'statistics', label: 'Statistics' },
      { id: 'ownership', label: 'Ownership Records' },
      { id: 'transactions', label: 'Transaction History' },
      { id: 'approvals', label: 'Approval History' }
    ];

    const previewData = computed(() => {
      if (!props.assets || !props.statistics) return null;

      const data = {
        assets: props.assets.map(asset => ({
          id: asset.id,
          name: asset.name,
          type: asset.type,
          value: asset.value,
          status: asset.approval_status,
          created_at: asset.created_at
        })),
        statistics: props.statistics
      };

      return JSON.stringify(data, null, 2);
    });

    const exportReport = async () => {
      try {
        const response = await axios.post(
          `${import.meta.env.VITE_API_URL}/reports/export`,
          {
            format: selectedFormat.value,
            data: selectedData.value,
            dateRange: dateRange.value,
            filters: props.filters
          },
          {
            headers: { Authorization: `Bearer ${authStore.token}` },
            responseType: 'blob'
          }
        );

        const format = exportFormats.find(f => f.id === selectedFormat.value);
        const filename = `financial_report_${new Date().toISOString().split('T')[0]}.${selectedFormat.value}`;

        saveAs(new Blob([response.data], { type: format.mimeType }), filename);
        close();
      } catch (error) {
        console.error('Error exporting report:', error);
        // Handle error appropriately
      }
    };

    const close = () => {
      emit('close');
    };

    return {
      selectedFormat,
      selectedData,
      dateRange,
      exportFormats,
      dataOptions,
      previewData,
      exportReport,
      close
    };
  }
};
</script>

<style scoped>
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  background: rgba(0, 0, 0, 0.5);

}

.modal-content {
  background: var(--bs-body-bg);
  border-radius: 12px;
  width: 90%;
  max-width: 800px;
  max-height: 90vh;
  display: flex;
  flex-direction: column;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.modal-header {
  padding: 1.5rem;
  border-bottom: 1px solid var(--bs-border-color);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.modal-header h2 {
  margin: 0;
  font-size: 1.5rem;
  color: var(--bs-body-color);
}

.close-button {
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: var(--bs-body-color);
}

.modal-body {
  padding: 1.5rem;
  overflow-y: auto;
}

.export-options {
  display: grid;
  gap: 2rem;
}

.format-selection h3,
.data-selection h3,
.date-range h3 {
  margin-bottom: 1rem;
  color: var(--bs-body-color);
}

.format-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
}

.format-card {
  border: 1px solid var(--bs-border-color);
  border-radius: 8px;
  padding: 1rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.format-card:hover {
  border-color: var(--bs-primary);
  background-color: rgba(var(--bs-primary-rgb), 0.05);
}

.format-card.selected {
  border-color: var(--bs-primary);
  background-color: rgba(var(--bs-primary-rgb), 0.1);
}

.format-icon {
  font-size: 2rem;
  margin-bottom: 0.5rem;
}

.format-info h4 {
  margin: 0 0 0.5rem;
  color: var(--bs-body-color);
}

.format-info p {
  margin: 0;
  font-size: 0.875rem;
  color: var(--bs-gray-600);
}

.checkbox-group {
  display: grid;
  gap: 0.5rem;
}

.checkbox-group label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  cursor: pointer;
}

.date-inputs {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
}

.input-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.input-group label {
  font-size: 0.875rem;
  color: var(--bs-gray-600);
}

.input-group input {
  padding: 0.5rem;
  border: 1px solid var(--bs-border-color);
  border-radius: 4px;
  background: var(--bs-body-bg);
  color: var(--bs-body-color);
}

.export-preview {
  margin-top: 2rem;
  padding-top: 2rem;
  border-top: 1px solid var(--bs-border-color);
}

.preview-content {
  background: var(--bs-secondary-bg);
  padding: 1rem;
  border-radius: 4px;
  max-height: 200px;
  overflow-y: auto;
}

.preview-content pre {
  margin: 0;
  font-size: 0.875rem;
  color: var(--bs-body-color);
}

.modal-footer {
  padding: 1.5rem;
  border-top: 1px solid var(--bs-border-color);
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
}

.btn {
  padding: 0.5rem 1rem;
  border-radius: 4px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.btn-primary {
  background: var(--bs-primary);
  color: white;
  border: none;
}

.btn-primary:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.btn-secondary {
  background: var(--bs-secondary);
  color: var(--bs-body-color);
  border: 1px solid var(--bs-border-color);
}

@media (max-width: 768px) {
  .modal-content {
    width: 95%;
  }

  .format-grid {
    grid-template-columns: 1fr;
  }

  .date-inputs {
    grid-template-columns: 1fr;
  }
}
</style>
