<template>
  <div class="modal fade" id="reportIssueModal" tabindex="-1" ref="reportModal" data-bs-backdrop="static">
    <div class="modal-dialog modal-lg">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title">Report an Issue</h5>
          <button type="button" class="modal-close-btn" @click="closeModal" aria-label="Close">
            <i class="bi bi-x-lg"></i>
          </button>
        </div>
        <div class="modal-body">
          <div class="alert alert-info">
            <i class="bi bi-info-circle me-2"></i>
            This report may include some personally identifiable information such as your IP address and application environment details to help us diagnose the issue.
          </div>
          <form @submit.prevent="submitReport">
            <div class="mb-3">
              <label for="issueTitle" class="form-label">Issue Title</label>
              <input
                type="text"
                class="form-control"
                :class="{ 'is-invalid': formErrors.title }"
                id="issueTitle"
                v-model="report.title"
                required
              >
              <div class="invalid-feedback" v-if="formErrors.title">
                {{ formErrors.title }}
              </div>
            </div>
            <div class="mb-3">
              <label for="issueDescription" class="form-label">Description</label>
              <textarea
                class="form-control"
                :class="{ 'is-invalid': formErrors.description }"
                id="issueDescription"
                rows="4"
                v-model="report.description"
                required
              ></textarea>
              <div class="invalid-feedback" v-if="formErrors.description">
                {{ formErrors.description }}
              </div>
            </div>
            <div class="mb-3">
              <label class="form-label">Current View Screenshot</label>
              <div class="d-flex flex-wrap gap-2">
                <div v-for="(screenshot, index) in report.screenshots" :key="index" class="position-relative">
                  <img :src="screenshot.preview" class="img-thumbnail" style="max-width: 150px;">
                  <button type="button" class="btn btn-sm btn-danger position-absolute top-0 end-0 m-1" @click="removeScreenshot(index)">
                    <i class="bi bi-x"></i>
                  </button>
                </div>
              </div>
            </div>
            <div class="mb-3">
              <label for="issueScreenshots" class="form-label">Additional Screenshots (Optional)</label>
              <input type="file" class="form-control" id="issueScreenshots" multiple accept="image/*" @change="handleFileUpload">
            </div>
          </form>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" @click="closeModal">Cancel</button>
          <button type="button" class="btn btn-primary" @click="submitReport" :disabled="isSubmitting">
            <span v-if="isSubmitting" class="spinner-border spinner-border-sm me-2"></span>
            Submit Report
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, watch } from 'vue';
import { Modal } from 'bootstrap';
import axios from 'axios';
import html2canvas from 'html2canvas';
import { useToast } from 'vue-toastification';

const props = defineProps({
  show: {
    type: Boolean,
    default: false
  }
});

const emit = defineEmits(['update:show', 'submitted', 'loading']);

const toast = useToast();
const reportModal = ref(null);
const modal = ref(null);
const isSubmitting = ref(false);
const formErrors = ref({
  title: '',
  description: ''
});

const report = ref({
  title: '',
  description: '',
  screenshots: [],
  metadata: {
    userAgent: navigator.userAgent,
    platform: navigator.platform,
    language: navigator.language,
    screenResolution: `${window.screen.width}x${window.screen.height}`,
    timestamp: new Date().toISOString(),
    consoleLogs: [],
    currentUrl: window.location.href
  }
});

const validateForm = () => {
  let isValid = true;
  formErrors.value = {
    title: '',
    description: ''
  };

  if (!report.value.title.trim()) {
    formErrors.value.title = 'Title is required';
    isValid = false;
  }

  if (!report.value.description.trim()) {
    formErrors.value.description = 'Description is required';
    isValid = false;
  }

  return isValid;
};

const captureConsoleLogs = () => {
  const logs = [];
  const originalConsole = { ...console };

  ['log', 'info', 'warn', 'error'].forEach(method => {
    console[method] = (...args) => {
      logs.push({
        type: method,
        timestamp: new Date().toISOString(),
        message: args.map(arg =>
          typeof arg === 'object' ? JSON.stringify(arg) : arg
        ).join(' ')
      });
      originalConsole[method](...args);
    };
  });

  return logs;
};

const captureScreenshot = async () => {
  try {
    const dataUrl = await html2canvas(document.body, {
      useCORS: true,
      allowTaint: true,
      backgroundColor: '#ffffff'
    }).then(canvas => canvas.toDataURL('image/png'));

    const response = await fetch(dataUrl);
    const blob = await response.blob();
    const file = new File([blob], 'current-view.png', { type: 'image/png' });

    return {
      file,
      preview: dataUrl
    };
  } catch (error) {
    console.error('Error capturing screenshot:', error);
    return null;
  }
};

const showModal = async () => {
  emit('loading', true);
  if (!modal.value) {
    modal.value = new Modal(reportModal.value);
  }

  // Reset form
  report.value = {
    title: '',
    description: '',
    screenshots: [],
    metadata: {
      userAgent: navigator.userAgent,
      platform: navigator.platform,
      language: navigator.language,
      screenResolution: `${window.screen.width}x${window.screen.height}`,
      timestamp: new Date().toISOString(),
      consoleLogs: captureConsoleLogs(),
      currentUrl: window.location.href
    }
  };

  // Capture current view
  const screenshot = await captureScreenshot();
  if (screenshot) {
    report.value.screenshots.push(screenshot);
  }

  modal.value.show();
  emit('loading', false);
};

const closeModal = () => {
  if (modal.value) {
    modal.value.hide();
    emit('update:show', false);
  }
};

const handleFileUpload = (event) => {
  const files = event.target.files;
  for (let i = 0; i < files.length; i++) {
    const file = files[i];
    const reader = new FileReader();
    reader.onload = (e) => {
      report.value.screenshots.push({
        file,
        preview: e.target.result
      });
    };
    reader.readAsDataURL(file);
  }
};

const removeScreenshot = (index) => {
  report.value.screenshots.splice(index, 1);
};

const submitReport = async () => {
  if (!validateForm()) {
    return;
  }

  try {
    isSubmitting.value = true;

    const formData = new FormData();
    formData.append('title', report.value.title);
    formData.append('description', report.value.description);
    formData.append('metadata', JSON.stringify(report.value.metadata));

    report.value.screenshots.forEach((screenshot) => {
      formData.append('screenshots', screenshot.file);
    });

    await axios.post(`${import.meta.env.VITE_API_URL}/help/report-issue`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
        'Authorization': `Bearer ${localStorage.getItem('token')}`
      }
    });

    closeModal();
    emit('submitted');

    toast.success('Issue report submitted successfully! Our team will review it shortly.', {
      position: "top-right",
      timeout: 5000,
      closeOnClick: true,
      pauseOnFocusLoss: true,
      pauseOnHover: true,
      draggable: true,
      draggablePercent: 0.6,
      showCloseButtonOnHover: false,
      hideProgressBar: false,
      closeButton: "button",
      icon: true,
      rtl: false
    });
  } catch (error) {
    console.error('Error submitting report:', error);
    toast.error('Failed to submit report. Please try again later.', {
      position: "top-right",
      timeout: 5000,
      closeOnClick: true,
      pauseOnFocusLoss: true,
      pauseOnHover: true,
      draggable: true,
      draggablePercent: 0.6,
      showCloseButtonOnHover: false,
      hideProgressBar: false,
      closeButton: "button",
      icon: true,
      rtl: false
    });
  } finally {
    isSubmitting.value = false;
  }
};

onMounted(() => {
  modal.value = new Modal(reportModal.value);

  // Add event listener for modal hidden event
  reportModal.value.addEventListener('hidden.bs.modal', () => {
    emit('update:show', false);
  });
});

// Watch for show prop changes
watch(() => props.show, (newValue) => {
  if (newValue) {
    showModal();
  } else if (modal.value) {
    modal.value.hide();
  }
}, { immediate: true });
</script>

<style lang="scss" scoped>
.modal {
  backdrop-filter: blur(10px);
  &:not(.show) {
    pointer-events: none;
    display: none;
  }
}

.modal-content {
  border: none;
  border-radius: 12px;
}

.modal-header {
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.modal-footer {
  border-top: 1px solid rgba(0, 0, 0, 0.1);
}

.img-thumbnail {
  border-radius: 8px;
  object-fit: cover;
}

:deep(.modal-backdrop) {
  z-index: 1040;
}

:deep(.modal) {
  z-index: 1050;
}
</style>
