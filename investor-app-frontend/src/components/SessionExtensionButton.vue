<template>
  <div class="session-extension-wrapper">
    <!-- Session Extension Button -->
    <div v-if="shouldShowExtensionButton" class="session-extension-container" :class="{ 'collapsed': isCollapsed }">
      <button
        class="session-extension-button"
        @click="showExtensionModal"
        v-tooltip="{ content: `Session expires in ${formattedRemainingTime}`, placement: 'left' }"
      >
        <i class="bi bi-clock-history"></i>
        <span v-if="!isCollapsed" class="extend-session-label">Extend Session</span>
        <span class="time-badge" :class="timeRemainingClass">{{ shortRemainingTime }}</span>
      </button>
    </div>

    <!-- Session Extension Modal (using teleport) -->
    <teleport to="body">
    <div class="modal fade" id="sessionExtensionModal" tabindex="-1" aria-labelledby="sessionExtensionModalLabel" aria-hidden="true">
      <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title" id="sessionExtensionModalLabel">
              <i class="bi bi-shield-lock me-2"></i>
              Extend Your Session
            </h5>
            <button type="button" class="modal-close-btn" data-bs-dismiss="modal" aria-label="Close">
              <i class="bi bi-x-lg"></i>
            </button>
          </div>
          <div class="modal-body">
            <div class="session-info-container">
              <div class="session-timer-display">
                <div class="timer-circle" :class="timeRemainingClass">
                  <span class="time-remaining">{{ formattedRemainingTime }}</span>
                  <span class="time-label">remaining</span>
                </div>
              </div>

              <p class="session-message">
                Your trusted device session will expire soon. Would you like to extend it for another 24 hours?
              </p>

              <div class="session-details">
                <p class="detail-item">
                  <i class="bi bi-calendar-event"></i>
                  Current expiration: {{ formattedExpirationTime }}
                </p>
                <p class="detail-item">
                  <i class="bi bi-calendar-plus"></i>
                  New expiration: {{ formattedNewExpirationTime }}
                </p>
              </div>
            </div>
          </div>
          <div class="modal-footer">
            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
              <i class="bi bi-x-circle me-1"></i>
              Cancel
            </button>
            <button
              type="button"
              class="btn btn-primary"
              @click="extendSession"
              :disabled="isExtending"
            >
              <i class="bi bi-clock-history me-1"></i>
              <span v-if="isExtending">
                <span class="spinner-border spinner-border-sm me-1" role="status" aria-hidden="true"></span>
                Extending...
              </span>
              <span v-else>Extend Session</span>
            </button>
          </div>
        </div>
      </div>
    </div>
    </teleport>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue';
import { useAuthStore } from '@/stores/auth';
import { useToast } from 'vue-toastification';
import { Modal } from 'bootstrap';

const props = defineProps({
  isCollapsed: {
    type: Boolean,
    default: false
  }
});

const authStore = useAuthStore();
const refreshInterval = ref(null);
const currentTime = ref(Date.now());
const isExtending = ref(false);
const modal = ref(null);

// Refresh the current time every 30 seconds to update the display
onMounted(() => {
  // Set initial time
  currentTime.value = Date.now();

  // Update time every 30 seconds
  refreshInterval.value = setInterval(() => {
    currentTime.value = Date.now();
  }, 30000); // Update every 30 seconds

  // Initialize the modal with a slight delay to ensure teleport has completed
  setTimeout(() => {
    const modalElement = document.getElementById('sessionExtensionModal');
    if (modalElement) {
      modal.value = new Modal(modalElement);

      // Clean up leftover modal backdrop when the modal is hidden
      modalElement.addEventListener("hidden.bs.modal", () => {
        const backdrops = document.querySelectorAll('.modal-backdrop');
        backdrops.forEach(backdrop => backdrop.remove());
      });


    } else {
      // Modal element not found, will be initialized on next render
    }
  }, 100); // Small delay to ensure DOM is updated
});

onUnmounted(() => {
  // Clear the refresh interval
  if (refreshInterval.value) {
    clearInterval(refreshInterval.value);
  }

  // Dispose of the modal if it exists
  if (modal.value) {
    try {
      modal.value.dispose();
    } catch (e) {
      console.warn('Error disposing modal:', e);
    }
  }

  // Clean up any leftover backdrops
  const backdrops = document.querySelectorAll('.modal-backdrop');
  backdrops.forEach(backdrop => backdrop.remove());
});

// Get token expiration information and refresh it when currentTime changes
const tokenExpiration = computed(() => {
  // Use currentTime.value to make the computed property reactive to time changes
  if (currentTime.value) {
    return authStore.getTokenExpirationInfo();
  }
  return null;
});

// Show the extension button when:
// 1. User has rememberMe enabled
// 2. Session is nearing expiration (less than 1 hour remaining)
const shouldShowExtensionButton = computed(() => {
  if (!tokenExpiration.value) return false;

  const isRememberMe = tokenExpiration.value.isRememberMe;
  const remainingHours = tokenExpiration.value.remainingTime / (60 * 60 * 1000);


  return isRememberMe && remainingHours < 1; // Show when less than 1 hour remaining
});

// Format the remaining time for display
const formattedRemainingTime = computed(() => {
  if (!tokenExpiration.value) return '';
  return tokenExpiration.value.formattedRemainingTime;
});

// Short format for the badge (e.g., "45m" or "5m")
const shortRemainingTime = computed(() => {
  if (!tokenExpiration.value) return '';

  const minutes = Math.floor(tokenExpiration.value.remainingTime / (60 * 1000));
  if (minutes < 60) {
    return `${minutes}m`;
  } else {
    const hours = Math.floor(minutes / 60);
    const remainingMinutes = minutes % 60;
    return `${hours}h${remainingMinutes > 0 ? ` ${remainingMinutes}m` : ''}`;
  }
});

// CSS class based on remaining time
const timeRemainingClass = computed(() => {
  if (!tokenExpiration.value) return '';

  const remainingMinutes = tokenExpiration.value.remainingTime / (60 * 1000);

  if (remainingMinutes < 10) return 'critical';
  if (remainingMinutes < 30) return 'warning';
  return 'normal';
});

// Format the current expiration time
const formattedExpirationTime = computed(() => {
  if (!tokenExpiration.value) return '';

  const date = tokenExpiration.value.expirationDate;
  return date.toLocaleString(undefined, {
    weekday: 'short',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  });
});

// Format the new expiration time (current time + 24 hours)
const formattedNewExpirationTime = computed(() => {
  const newExpirationDate = new Date(Date.now() + 24 * 60 * 60 * 1000);
  return newExpirationDate.toLocaleString(undefined, {
    weekday: 'short',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  });
});

// Show the extension modal
const showExtensionModal = () => {
  if (modal.value) {
    modal.value.show();
  }
};

// Get toast instance
const toast = useToast();

// Extend the session for another 24 hours
const extendSession = async () => {
  try {
    isExtending.value = true;

    // Call the backend to extend the session
    await authStore.extendSession();

    // Hide the modal
    if (modal.value) {
      modal.value.hide();
    }

    // Show success message
    toast.success('Session extended for 24 hours', {
      timeout: 3000,
      position: 'top-right'
    });

  } catch (_) {
    // Silently catch errors and show generic error message
    toast.error('Failed to extend session. Please try again.', {
      timeout: 5000,
      position: 'top-right'
    });
  } finally {
    isExtending.value = false;
  }
};
</script>

<style scoped lang="scss">
.session-extension-wrapper {
  display: contents; // This makes the wrapper not affect layout
}

.session-extension-container {
  margin-bottom: 0.75rem;
}

.modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(5px);
  -webkit-backdrop-filter: blur(5px);
  z-index: 1055;
  display: none;

  &.fade {
    transition: opacity 0.15s linear;
  }

  &.show {
    display: block;
  }

  &:not(.show) {
    pointer-events: none;
  }
}

.modal-dialog {
  max-width: 500px;
  margin: 1.75rem auto;
  transform: translateY(-50px);
  transition: transform 0.3s ease-out;

  .modal.show & {
    transform: none;
  }
}

.modal-content {
  border: none;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  background: var(--bs-body-bg);
  position: relative;
  z-index: 2;
}

.modal-header {
  border-bottom: 1px solid var(--bs-border-color);
  padding: 1rem 1.5rem;

  .modal-title {
    font-size: 1.25rem;
    font-weight: 600;
  }

  .modal-close-btn {
    background: transparent;
    border: none;
    color: var(--bs-secondary);
    font-size: 1.25rem;
    line-height: 1;
    padding: 0;
    transition: color 0.2s ease;

    &:hover {
      color: var(--bs-primary);
    }
  }
}

.modal-body {
  padding: 1.5rem;
}

// Ensure proper modal display
:deep(.modal-backdrop) {
  z-index: 1054;
}

// Add fade transition for backdrop
:deep(.modal-backdrop) {
  opacity: 0;
  transition: opacity 0.15s linear;

  &.show {
    opacity: 0.5;
  }

  &:not(.show) {
    display: none;
  }
}
.session-extension-button {
  display: flex;
  align-items: center;
  justify-content: left;
  width: 100%;
  padding: 0.75rem 1rem;
  background-color: rgba(var(--bs-primary-rgb), 0.08);
  border: 1px solid rgba(var(--bs-primary-rgb), 0.15);
  border-radius: 10px;
  color: var(--bs-primary);
  font-weight: 500;
  transition: all 0.3s ease;
  position: relative;
  gap: 0.75rem;
  &:hover {
    background-color: rgba(var(--bs-primary-rgb), 0.12);
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(var(--bs-primary-rgb), 0.15);
  }

  i {
    font-size: 1.2rem;
    margin-right: 0.5rem;
  }

  .extend-session-label {
    line-height: 1.2;
    text-align: left;
  }
  .time-badge {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    min-width: 40px;
    height: 24px;
    padding: 1rem 0.75rem;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 700;
    margin-left: 0.5rem;
    line-height: 1;
    &.normal {
      background-color: rgba(var(--bs-success-rgb), 0.15);
      color: var(--bs-success);
    }

    &.warning {
      background-color: rgba(var(--bs-warning-rgb), 0.15);
      color: var(--bs-warning);
    }

    &.critical {
      background-color: rgba(var(--bs-danger-rgb), 0.15);
      color: var(--bs-danger);
      animation: pulse 1.5s infinite;
    }
  }
}

// Modal styling
.session-info-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 1rem 0;

  .session-timer-display {
    margin-bottom: 1.5rem;

    .timer-circle {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      width: 120px;
      height: 120px;
      border-radius: 50%;
      margin: 0 auto;
      text-align: center;

      &.normal {
        background: linear-gradient(135deg, rgba(var(--bs-success-rgb), 0.05), rgba(var(--bs-success-rgb), 0.15));
        border: 2px solid rgba(var(--bs-success-rgb), 0.2);
        color: var(--bs-success);
        box-shadow: 0 4px 12px rgba(var(--bs-success-rgb), 0.1);
      }

      &.warning {
        background: linear-gradient(135deg, rgba(var(--bs-warning-rgb), 0.05), rgba(var(--bs-warning-rgb), 0.15));
        border: 2px solid rgba(var(--bs-warning-rgb), 0.2);
        color: var(--bs-warning);
        box-shadow: 0 4px 12px rgba(var(--bs-warning-rgb), 0.1);
      }

      &.critical {
        background: linear-gradient(135deg, rgba(var(--bs-danger-rgb), 0.05), rgba(var(--bs-danger-rgb), 0.15));
        border: 2px solid rgba(var(--bs-danger-rgb), 0.2);
        color: var(--bs-danger);
        animation: pulse-border 2s infinite;
        box-shadow: 0 4px 12px rgba(var(--bs-danger-rgb), 0.15);
      }

      .time-remaining {
        font-size: 1.75rem;
        font-weight: 700;
      line-height: 1;
    }

      .time-label {
        font-size: 0.85rem;
        opacity: 0.8;
      }
    }
  }

  .session-message {
    text-align: center;
    margin-bottom: 1.5rem;
    max-width: 400px;
  }

  .session-details {
    width: 100%;
    background-color: var(--bs-body-bg);
    border-radius: 10px;
    padding: 1rem;

    .detail-item {
      display: flex;
      align-items: center;
      margin-bottom: 0.5rem;

      &:last-child {
        margin-bottom: 0;
      }

      i {
        margin-right: 0.75rem;
        color: var(--bs-primary);
      }
    }
  }
}

// Animations
@keyframes pulse {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.6;
  }
  100% {
    opacity: 1;
  }
}

@keyframes pulse-border {
  0% {
    box-shadow: 0 0 0 0 rgba(var(--bs-danger-rgb), 0.4);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(var(--bs-danger-rgb), 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(var(--bs-danger-rgb), 0);
  }
}

// Collapsed sidebar styles
.session-extension-container.collapsed {
  .session-extension-button {
    padding: 0.75rem;
    justify-content: center;

    i {
      margin-right: 0;
    }

    span:not(.time-badge) {
      display: none;
    }

    .time-badge {
      position: absolute;
      top: -8px;
      right: -8px;
      margin-left: 0;
      height: 20px;
      min-width: 20px;
      font-size: 0.7rem;
    }
  }
}
</style>
