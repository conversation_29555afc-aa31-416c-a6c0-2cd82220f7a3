<template>
  <div v-if="authStore.rememberMe && tokenExpiration" class="card session-info-card mb-4">
    <div class="card-body">
      <h5 class="card-title">
        <i class="bi bi-shield-lock me-2"></i>
        Extended Session
      </h5>

      <div class="session-info">
        <p class="mb-2">
          You selected "Trust this device for 24 hours" at login. Your session will remain active for:
        </p>

        <div class="session-timer">
          <div class="timer-display">
            <span class="time-remaining">{{ tokenExpiration.formattedRemainingTime }}</span>
          </div>
          <div class="progress mt-2 mb-3">
            <div
              class="progress-bar"
              role="progressbar"
              :style="{ width: `${progressPercentage}%` }"
              :class="progressColorClass"
              aria-valuenow="25"
              aria-valuemin="0"
              aria-valuemax="100">
            </div>
          </div>
        </div>

        <p class="text-muted small mb-0">
          Session expires: {{ formattedExpirationTime }}
        </p>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed, ref, onMounted, onUnmounted } from 'vue';
import { useAuthStore } from '@/stores/auth';

const authStore = useAuthStore();
const refreshInterval = ref(null);
const currentTime = ref(Date.now());

// Refresh the current time every 30 seconds to update the display
onMounted(() => {
  // Set initial time
  currentTime.value = Date.now();

  // Update time every 30 seconds
  refreshInterval.value = setInterval(() => {
    currentTime.value = Date.now();
  }, 30000); // Update every 30 seconds
});

onUnmounted(() => {
  if (refreshInterval.value) {
    clearInterval(refreshInterval.value);
  }
});

// Get token expiration information and refresh it when currentTime changes
const tokenExpiration = computed(() => {
  // Use currentTime.value to make the computed property reactive to time changes
  if (currentTime.value) {
    return authStore.getTokenExpirationInfo();
  }
  return null;
});

// Calculate progress percentage (how much time has elapsed)
const progressPercentage = computed(() => {
  if (!tokenExpiration.value) return 0;

  // For a 24-hour session, the total duration would be 24 * 60 * 60 * 1000 milliseconds
  const totalDuration = 24 * 60 * 60 * 1000;
  const elapsed = totalDuration - tokenExpiration.value.remainingTime;

  // Calculate percentage with bounds checking
  const percentage = Math.max(0, Math.min(100, (elapsed / totalDuration) * 100));
  return percentage;
});

// Determine progress bar color based on remaining time
const progressColorClass = computed(() => {
  if (!tokenExpiration.value) return 'bg-secondary';

  const remainingHours = tokenExpiration.value.remainingTime / (60 * 60 * 1000);

  if (remainingHours < 1) return 'bg-danger';
  if (remainingHours < 6) return 'bg-warning';
  return 'bg-success';
});

// Format the expiration time as a readable date/time
const formattedExpirationTime = computed(() => {
  if (!tokenExpiration.value) return '';

  const date = tokenExpiration.value.expirationDate;
  return date.toLocaleString(undefined, {
    weekday: 'short',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  });
});
</script>

<style scoped lang="scss">
.session-info-card {
  border-radius: 12px;
  border: 1px solid var(--bs-border-color);
  background-color: var(--bs-card2-bg);
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.04);
  transition: all 0.3s ease;

  &:hover {
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.06);
    transform: translateY(-2px);
  }

  .card-title {
    font-size: 1.15rem;
    font-weight: 600;
    color: var(--bs-primary);
    margin-bottom: 1.25rem;
    display: flex;
    align-items: center;

    i {
      color: var(--bs-primary);
      margin-right: 0.5rem;
    }
  }

  .session-info {
    p {
      color: var(--bs-body-color);
    }
  }

  .session-timer {
    margin: 1.25rem 0;

    .timer-display {
      text-align: center;
      margin-bottom: 0.75rem;

      .time-remaining {
        font-size: 2rem;
        font-weight: 700;
        color: var(--bs-primary);
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
        letter-spacing: 0.5px;
      }
    }
  }

  .progress {
    height: 8px;
    border-radius: 6px;
    background-color: var(--bs-border-color);
    overflow: hidden;
    box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.05);

    .progress-bar {
      border-radius: 6px;
      transition: width 0.5s ease;

      &.bg-success {
        background: linear-gradient(90deg, rgba(40, 167, 69, 0.9), rgba(32, 201, 151, 0.9));
        box-shadow: 0 0 10px rgba(40, 167, 69, 0.3);
      }

      &.bg-warning {
        background: linear-gradient(90deg, rgba(255, 193, 7, 0.9), rgba(253, 126, 20, 0.9));
        box-shadow: 0 0 10px rgba(255, 193, 7, 0.3);
      }

      &.bg-danger {
        background: linear-gradient(90deg, rgba(220, 53, 69, 0.9), rgba(200, 35, 51, 0.9));
        box-shadow: 0 0 10px rgba(220, 53, 69, 0.3);
      }
    }
  }

  .text-muted {
    font-size: 0.85rem;
    text-align: center;
    margin-top: 0.5rem;
  }
}
</style>
