<template>
  <div v-if="isOpen" class="modal-wrapper">
    <div class="modal fade show d-block" tabindex="-1">
      <div class="modal-dialog modal-lg modal-dialog-centered">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title">
              {{ isManager ? "Tokenise New Asset for Client" : "Propose New Asset for Tokenisation" }}
            </h5>
            <button type="button" class="modal-close-btn" @click="closeModal">
              <i class="bi bi-x-lg"></i>
            </button>
          </div>
          <div class="modal-body">
            <form @submit.prevent="tokeniseAsset">
              <!-- Manager View: Select Client -->
              <div v-if="isManager" class="mb-4">
                <label class="form-label fw-bold">Assign Client</label>
                <div class="position-relative">
                  <input
                    type="text"
                    v-model="clientSearch"
                    class="form-control"
                    placeholder="Search client by name or email..."
                    @input="searchClients"
                  />
                  <ul
                    v-if="clientSearch.trim() && searchResults.length > 0"
                    class="list-group position-absolute w-100"
                    style="z-index: 1000; top: 100%;"
                  >
                    <li
                      v-for="client in searchResults"
                      :key="client.id"
                      class="list-group-item list-group-item-action"
                      @click="selectClient(client)"
                    >
                      {{ client.name }} ({{ client.email }})
                    </li>
                  </ul>
                </div>
                <select v-model="selectedClient" class="form-select mt-2">
                  <option disabled value="">Select a Client</option>
                  <option v-for="client in allClients" :key="client.id" :value="client.id">
                    {{ client.name }}
                  </option>
                </select>
              </div>

              <!-- Client View: Informational Message -->
              <div v-else class="alert alert-info mb-4">
                You are proposing this asset for review by your manager. Once submitted, they will verify and approve it.
              </div>

              <!-- Asset Details Section -->
              <div class="card mb-4">
                <div class="card-body">
                  <h6 class="card-title mb-3">Asset Details</h6>
                  <div class="row g-3">
                    <div class="col-md-6">
                      <label class="form-label">Asset Name</label>
                      <input v-model="newAsset.name" type="text" class="form-control" placeholder="Enter asset name" required />
                    </div>
                    <div class="col-md-6">
                      <label class="form-label">Asset Type</label>
                      <select v-model="newAsset.type" class="form-select" required>
                        <option disabled value="">Select asset type</option>
                        <option value="real_estate">Real Estate</option>
                        <option value="equities">Equities</option>
                        <option value="bonds">Bonds</option>
                      </select>
                    </div>
                    <div class="col-md-6">
                      <label class="form-label">Asset Value (USD)</label>
                      <input v-model="newAsset.value" type="number" class="form-control" placeholder="Enter asset value" required />
                    </div>
                    <div class="col-md-6">
                      <label class="form-label">Ownership (%)</label>
                      <input v-model="newAsset.ownership_percentage" type="number" class="form-control" placeholder="Enter ownership percentage" required />
                    </div>
                  </div>
                </div>
              </div>

              <!-- Ownership Assignment Type -->
              <div class="mb-3">
                <label class="form-label">Ownership Assignment</label>
                <div class="form-check">
                  <input type="radio" v-model="newAsset.assignmentType" value="direct" class="form-check-input" id="directAssignment" />
                  <label class="form-check-label" for="directAssignment">Direct Assignment</label>
                </div>
                <div class="form-check">
                  <input type="radio" v-model="newAsset.assignmentType" value="vesting" class="form-check-input" id="vestingAssignment" />
                  <label class="form-check-label" for="vestingAssignment">Vesting Contract</label>
                </div>
              </div>

              <!-- Direct Assignment Fields -->
              <div v-if="newAsset.assignmentType === 'direct'" class="mb-3">
                <label class="form-label">Client</label>
                <select v-model="newAsset.client_id" class="form-control" required>
                  <option value="">Select Client</option>
                  <option value="self">Self</option>
                  <option v-for="client in allClients" :key="client.id" :value="client.id">
                    {{ client.name }}
                  </option>
                </select>
              </div>

              <!-- Vesting Contract Fields -->
              <div v-if="newAsset.assignmentType === 'vesting'" class="mb-4">
                <h6 class="section-title">Vesting Contract Details</h6>

                <!-- Contract Address -->
                <div class="mb-3">
                  <label class="form-label">Contract Address</label>
                  <input type="text" v-model="newAsset.contract_address" class="form-control" required
                         placeholder="0x..." pattern="0x[a-fA-F0-9]{40}" />
                  <div class="form-text">Enter the deployed vesting contract address</div>
                </div>

                <!-- Vesting Schedule -->
                <div class="mb-3">
                  <label class="form-label">Start Date</label>
                  <input type="date" v-model="newAsset.vesting_schedule.start_date" class="form-control" required />
                </div>

                <div class="mb-3">
                  <label class="form-label">Duration (days)</label>
                  <input type="number" v-model="newAsset.vesting_schedule.duration" class="form-control" required min="1" />
                </div>

                <div class="mb-3">
                  <label class="form-label">Cliff Period (days)</label>
                  <input type="number" v-model="newAsset.vesting_schedule.cliff_period" class="form-control" min="0" />
                </div>

                <div class="mb-3">
                  <label class="form-label">Release Frequency</label>
                  <select v-model="newAsset.vesting_schedule.release_frequency" class="form-control">
                    <option value="daily">Daily</option>
                    <option value="weekly">Weekly</option>
                    <option value="monthly" selected>Monthly</option>
                    <option value="quarterly">Quarterly</option>
                  </select>
                </div>

                <!-- Beneficiaries -->
                <div class="mb-3">
                  <label class="form-label">Beneficiaries</label>
                  <div v-for="(beneficiary, index) in newAsset.beneficiaries" :key="index" class="beneficiary-item mb-2">
                    <div class="d-flex gap-2">
                      <div class="flex-grow-1">
                        <select v-model="beneficiary.beneficiary_id" class="form-control" required>
                          <option value="">Select Beneficiary</option>
                          <option v-for="client in allClients" :key="client.id" :value="client.id">
                            {{ client.name }}
                          </option>
                        </select>
                      </div>
                      <div class="input-group" style="width: 150px;">
                        <input type="number" v-model="beneficiary.percentage" class="form-control" required
                               min="0" max="100" step="0.01" placeholder="Percentage" />
                        <span class="input-group-text">%</span>
                      </div>
                      <button type="button" class="btn btn-outline-danger" @click="removeBeneficiary(index)">
                        <i class="bi bi-trash"></i>
                      </button>
                    </div>
                  </div>
                  <button type="button" class="btn btn-outline-primary btn-sm" @click="addBeneficiary">
                    <i class="bi bi-plus-circle me-1"></i> Add Beneficiary
                  </button>
                  <div class="form-text">Total percentage must equal 100%</div>
                </div>
              </div>

              <!-- Supporting Documents Section -->
              <div class="card mb-4">
                <div class="card-body">
                  <h6 class="card-title mb-3">Supporting Documents</h6>
                  <label class="form-label">Document URLs (comma-separated)</label>
                  <input
                    v-model="newAsset.supporting_documents"
                    type="text"
                    class="form-control"
                    placeholder="https://example.com/doc1.pdf, https://example.com/doc2.pdf"
                  />
                </div>
              </div>

              <!-- Submit Button -->
              <button type="submit" class="btn btn-primary w-100" :disabled="!isFormComplete">
                {{ isManager ? "Tokenise Asset" : "Propose Asset for Review" }}
              </button>
            </form>
          </div>
        </div>
      </div>
    </div>
    <div class="modal-backdrop fade show"></div>
  </div>
</template>

<script>
import { ref, computed, watch, onMounted, onBeforeUnmount } from "vue";
import { useAuthStore } from "@/stores/auth";
import { useToast } from 'vue-toastification';

export default {
  props: {
    isOpen: Boolean,
    isManager: Boolean,
    allClients: Array,
  },
  emits: ["close", "tokenise"],
  setup(props, { emit }) {
    const authStore = useAuthStore();
    const toast = useToast();
    const clientSearch = ref("");
    const selectedClient = ref(null);
    const searchResults = ref([]);
    const newAsset = ref({
      name: "",
      type: "",
      value: null,
      ownership_percentage: null,
      assignmentType: "direct",
      client_id: null, // will be set either by selection or by current user's id
      contract_address: "",
      vesting_schedule: {
        start_date: "",
        duration: 365,
        cliff_period: 0,
        release_frequency: "monthly"
      },
      beneficiaries: [],
      supporting_documents: "",
    });

    // Automatically assign client_id if not manager
    watch(
      () => authStore.user,
      (user) => {
        if (user && !props.isManager) {
          newAsset.value.client_id = user.id;
        }
      },
      { immediate: true }
    );

    const searchClients = () => {
      if (!clientSearch.value.trim()) {
        searchResults.value = [];
        return;
      }
      searchResults.value = props.allClients.filter(client =>
        client.name.toLowerCase().includes(clientSearch.value.toLowerCase()) ||
        client.email.toLowerCase().includes(clientSearch.value.toLowerCase())
      );
    };

    const selectClient = (client) => {
      selectedClient.value = client.id;
      clientSearch.value = client.name;
      newAsset.value.client_id = client.id;
      searchResults.value = [];
    };

    const addBeneficiary = () => {
      newAsset.value.beneficiaries.push({
        beneficiary_id: "",
        percentage: 0
      });
    };

    const removeBeneficiary = (index) => {
      newAsset.value.beneficiaries.splice(index, 1);
    };

    const validateForm = () => {
      if (!newAsset.value.name || !newAsset.value.type || !newAsset.value.value) {
        toast.error('Please fill in all required fields');
        return false;
      }

      if (newAsset.value.assignmentType === 'direct' && !newAsset.value.client_id) {
        toast.error('Please select a client');
        return false;
      }

      if (newAsset.value.assignmentType === 'vesting') {
        if (!newAsset.value.contract_address) {
          toast.error('Please enter the vesting contract address');
          return false;
        }

        if (!newAsset.value.vesting_schedule.start_date || !newAsset.value.vesting_schedule.duration) {
          toast.error('Please fill in all required vesting schedule fields');
          return false;
        }

        if (newAsset.value.beneficiaries.length === 0) {
          toast.error('Please add at least one beneficiary');
          return false;
        }

        const totalPercentage = newAsset.value.beneficiaries.reduce((sum, b) => sum + b.percentage, 0);
        if (Math.abs(totalPercentage - 100) > 0.01) {
          toast.error('Beneficiary percentages must sum to 100%');
          return false;
        }
      }

      return true;
    };

    const tokeniseAsset = async () => {
      if (!validateForm()) return;

      try {
        const assetData = {
          name: newAsset.value.name,
          type: newAsset.value.type,
          value: newAsset.value.value,
          supporting_documents: newAsset.value.supporting_documents
        };

        if (newAsset.value.assignmentType === 'direct') {
          assetData.client_id = newAsset.value.client_id;
        } else {
          assetData.vesting = {
            contract_address: newAsset.value.contract_address,
            vesting_schedule: newAsset.value.vesting_schedule,
            beneficiaries: newAsset.value.beneficiaries
          };
        }

        emit("tokenise", assetData);
      } catch (error) {
        toast.error(error.response?.data?.error || 'Failed to tokenise asset');
      }
    };

    const closeModal = () => {
      newAsset.value = {
        name: "",
        type: "",
        value: null,
        ownership_percentage: null,
        assignmentType: "direct",
        client_id: null,
        contract_address: "",
        vesting_schedule: {
          start_date: "",
          duration: 365,
          cliff_period: 0,
          release_frequency: "monthly"
        },
        beneficiaries: [],
        supporting_documents: "",
      };
      emit("close");
    };

    const isFormComplete = computed(() => {
      return newAsset.value.name &&
             newAsset.value.type &&
             newAsset.value.value &&
             newAsset.value.ownership_percentage &&
             newAsset.value.client_id &&
             newAsset.value.assignmentType &&
             newAsset.value.contract_address &&
             newAsset.value.vesting_schedule.start_date &&
             newAsset.value.vesting_schedule.duration &&
             newAsset.value.beneficiaries.length > 0;
    });

    // Add event listeners for modal cleanup
    onMounted(() => {
      // Add event listener to clean up modal backdrop when modal is closed
      document.addEventListener('hidden.bs.modal', () => {
        const backdrops = document.querySelectorAll('.modal-backdrop');
        backdrops.forEach(backdrop => backdrop.remove());
      });
    });

    onBeforeUnmount(() => {
      // Remove event listener when component is unmounted
      document.removeEventListener('hidden.bs.modal', () => {});
    });

    return {
      clientSearch,
      searchResults,
      selectedClient,
      searchClients,
      selectClient,
      newAsset,
      addBeneficiary,
      removeBeneficiary,
      tokeniseAsset,
      closeModal,
      isFormComplete,
    };
  },
};
</script>

<style scoped lang="scss">
.modal-wrapper {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  z-index: 1050;
  overflow-x: hidden;
  overflow-y: auto;
}

.modal {
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(5px);
  -webkit-backdrop-filter: blur(5px);

  &:not(.show) {
    pointer-events: none;
    display: none;
  }
}

.modal-dialog {
  max-width: 800px;
  margin: 1.75rem auto;
}

.modal-content {
  border: none;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  background: var(--bs-body-bg);
}

.modal-header {
  border-bottom: 1px solid var(--bs-border-color);
  padding: 1rem 1.5rem;

  .modal-title {
    font-size: 1.25rem;
    font-weight: 600;
  }
}

.modal-body {
  padding: 1.5rem;
}

.modal-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.5);
  z-index: 1040;

  &:not(.show) {
    display: none;
  }
}

// Form styles
.form-label {
  font-weight: 500;
  margin-bottom: 0.5rem;
}

.form-control, .form-select {
  border-radius: 8px;
  border: 1px solid var(--bs-border-color);
  padding: 0.625rem 1rem;

  &:focus {
    box-shadow: 0 0 0 3px rgba(var(--bs-primary-rgb), 0.25);
  }
}

.alert {
  border-radius: 8px;
  padding: 1rem;
}

// Client search results
.list-group {
  margin-top: 0.25rem;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  max-height: 200px;
  overflow-y: auto;

  .list-group-item {
    cursor: pointer;
    transition: all 0.2s ease;

    &:hover {
      background: var(--bs-primary);
      color: white;
    }
  }
}

.beneficiary-item {
  background: rgba(var(--bs-primary-rgb), 0.05);
  border-radius: 8px;
  padding: 0.75rem;
}

.section-title {
  font-size: 1rem;
  font-weight: 600;
  color: var(--bs-primary);
  margin-bottom: 1rem;
}
</style>
