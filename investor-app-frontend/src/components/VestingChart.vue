<template>
  <!-- No changes to template section -->
</template>

<script setup>
import { ref, watch, onMounted } from 'vue';

const props = defineProps({
  vestingData: {
    type: Object,
    required: true
  }
});

const chartRef = ref(null);
const chartInstance = ref(null);

const updateChart = () => {
  if (!props.vestingData || !chartRef.value) return;

  if (chartInstance.value) {
    chartInstance.value.destroy();
  }

  const ctx = chartRef.value.getContext('2d');
  const chartData = props.vestingData.vesting.chart_data;

  chartInstance.value = new Chart(ctx, {
    type: 'line',
    data: {
      labels: chartData.map(point => formatDate(new Date(point.date))),
      datasets: [
        {
          label: 'Vested Amount ($)',
          data: chartData.map(point => point.vested_amount),
          borderColor: '#4CAF50',
          backgroundColor: 'rgba(76, 175, 80, 0.1)',
          fill: true,
          yAxisID: 'y'
        },
        {
          label: 'Vested Percentage (%)',
          data: chartData.map(point => point.vested_percentage),
          borderColor: '#2196F3',
          backgroundColor: 'rgba(33, 150, 243, 0.1)',
          fill: true,
          yAxisID: 'y1'
        }
      ]
    },
    options: {
      responsive: true,
      interaction: {
        mode: 'index',
        intersect: false,
      },
      scales: {
        y: {
          type: 'linear',
          display: true,
          position: 'left',
          title: {
            display: true,
            text: 'Vested Amount ($)'
          }
        },
        y1: {
          type: 'linear',
          display: true,
          position: 'right',
          title: {
            display: true,
            text: 'Vested Percentage (%)'
          },
          grid: {
            drawOnChartArea: false
          }
        }
      },
      plugins: {
        tooltip: {
          callbacks: {
            label: function(context) {
              let label = context.dataset.label || '';
              if (label) {
                label += ': ';
              }
              if (context.datasetIndex === 0) {
                label += '$' + context.parsed.y.toLocaleString();
              } else {
                label += context.parsed.y.toFixed(1) + '%';
              }
              return label;
            }
          }
        }
      }
    }
  });
};

watch(() => props.vestingData, () => {
  updateChart();
}, { deep: true });

onMounted(() => {
  updateChart();
});
</script>

<style>
  /* No changes to style section */
</style>
