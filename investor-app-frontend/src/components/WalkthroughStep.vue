<template>
  <div class="walkthrough-step" :class="[`position-${position}`, { 'with-arrow': showArrow }, customClass]" :style="stepStyle">
    <div class="step-header">
      <h5 class="step-title">{{ title }}</h5>
      <button class="close-btn" @click="$emit('skip')">
        <i class="bi bi-x-lg"></i>
      </button>
    </div>
    <div class="step-content">
      <p>{{ content }}</p>
    </div>
    <div class="step-footer">
      <div class="step-progress">
        <span>{{ currentStep + 1 }} of {{ totalSteps }}</span>
      </div>
      <div class="step-actions">
        <button v-if="currentStep > 0" class="btn btn-sm btn-outline-primary" @click="$emit('previous')">
          <i class="bi bi-arrow-left me-1"></i> Previous
        </button>
        <button v-if="currentStep < totalSteps - 1" class="btn btn-sm btn-primary" @click="$emit('next')">
          Next <i class="bi bi-arrow-right ms-1"></i>
        </button>
        <button v-else class="btn btn-sm btn-success" @click="$emit('complete')">
          Finish <i class="bi bi-check-lg ms-1"></i>
        </button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue';

const props = defineProps({
  title: {
    type: String,
    required: true
  },
  content: {
    type: String,
    required: true
  },
  position: {
    type: String,
    default: 'bottom',
    validator: (value) => ['top', 'bottom', 'left', 'right'].includes(value)
  },
  targetRect: {
    type: Object,
    default: () => ({})
  },
  currentStep: {
    type: Number,
    required: true
  },
  totalSteps: {
    type: Number,
    required: true
  },
  showArrow: {
    type: Boolean,
    default: true
  },
  customClass: {
    type: String,
    default: ''
  }
});

const emit = defineEmits(['next', 'previous', 'skip', 'complete']);

// Calculate position based on target element and ensure it's within viewport
const stepStyle = computed(() => {
  if (!props.targetRect || !props.targetRect.width) {
    return {};
  }

  const { left, top, width, height } = props.targetRect;
  let margin = 40; // Increased space between target and tooltip
  const stepWidth = 350; // Width of the walkthrough step box
  const stepHeight = 200; // Increased approximate height of the walkthrough step box

  // Get viewport dimensions
  const viewportWidth = window.innerWidth;
  const viewportHeight = window.innerHeight;

  // Identify specific elements by their position and size for special handling
  // Use a more reliable method to detect the profile tile
  const isProfileTile = document.querySelector('.profile-tile') && (
    // Check if the target element is the profile tile itself
    (Math.abs(document.querySelector('.profile-tile').getBoundingClientRect().left - left) < 10 &&
     Math.abs(document.querySelector('.profile-tile').getBoundingClientRect().top - top) < 10) ||
    // Or check if the target has the class 'profile-step' from the walkthrough store
    props.customClass === 'profile-step'
  );

  const isNotifications = document.querySelector('.notifications-container') &&
                         Math.abs(document.querySelector('.notifications-container').getBoundingClientRect().left - left) < 10 &&
                         Math.abs(document.querySelector('.notifications-container').getBoundingClientRect().top - top) < 10;

  const isSidebar = document.querySelector('.sidebar-menu') &&
                   Math.abs(document.querySelector('.sidebar-menu').getBoundingClientRect().left - left) < 10 &&
                   Math.abs(document.querySelector('.sidebar-menu').getBoundingClientRect().top - top) < 10;

  // Calculate available space in each direction
  const spaceAbove = top;
  const spaceBelow = viewportHeight - (top + height);
  const spaceLeft = left;
  const spaceRight = viewportWidth - (left + width);

  // Determine the best position based on available space and element type
  let bestPosition;

  // Special case handling for specific elements
  if (isProfileTile) {
    // Position above the profile tile with margin equal to its height plus spacing
    bestPosition = 'top';
    margin = height + 16; // exclude the offset height and add spacing
  } else if (isNotifications) {
    // Position above notifications with margin
    bestPosition = 'top';
    margin = height + 16;
  } else if (isSidebar) {
    // Always position to the right of sidebar
    bestPosition = 'right';
  } else {
    // For other elements, find the direction with the most space
    const spaces = [
      { direction: 'top', space: spaceAbove, minRequired: stepHeight + margin },
      { direction: 'bottom', space: spaceBelow, minRequired: stepHeight + margin },
      { direction: 'left', space: spaceLeft, minRequired: stepWidth + margin },
      { direction: 'right', space: spaceRight, minRequired: stepWidth + margin }
    ];

    // Filter spaces that have enough room for the step
    const viableSpaces = spaces.filter(s => s.space >= s.minRequired);

    if (viableSpaces.length > 0) {
      // Sort viable spaces from largest to smallest
      viableSpaces.sort((a, b) => b.space - a.space);
      bestPosition = viableSpaces[0].direction;
    } else {
      // If no direction has enough space, use the one with the most space
      spaces.sort((a, b) => b.space - a.space);
      bestPosition = spaces[0].direction;
    }
  }

  // Calculate position based on the determined best position
  let initialPosition;

  // Position based on the determined best position with extra margin to avoid overlap
  switch (bestPosition) {
    case 'top':
      initialPosition = {
        left: left + width / 2,
        top: top - margin,
        transform: 'translate(-50%, -100%)'
      };
      break;
    case 'bottom':
      initialPosition = {
        left: left + width / 2,
        top: top + height + margin,
        transform: 'translateX(-50%)'
      };
      break;
    case 'left':
      initialPosition = {
        left: left - margin,
        top: top + height / 2,
        transform: 'translate(-100%, -50%)'
      };
      break;
    case 'right':
      initialPosition = {
        left: left + width + margin,
        top: top + height / 2,
        transform: 'translateY(-50%)'
      };
      break;
    default:
      initialPosition = {
        left: left + width / 2,
        top: top + height + margin,
        transform: 'translateX(-50%)'
      };
  }

  // Calculate actual coordinates after transform to check viewport boundaries
  let actualLeft = initialPosition.left;
  let actualTop = initialPosition.top;

  // Adjust for transform to get the actual top-left corner of the step
  if (initialPosition.transform.includes('translate(-50%, -100%)')) {
    actualLeft -= stepWidth / 2;
    actualTop -= stepHeight;
  } else if (initialPosition.transform.includes('translateX(-50%)')) {
    actualLeft -= stepWidth / 2;
  } else if (initialPosition.transform.includes('translate(-100%, -50%)')) {
    actualLeft -= stepWidth;
    actualTop -= stepHeight / 2;
  } else if (initialPosition.transform.includes('translateY(-50%)')) {
    actualTop -= stepHeight / 2;
  }

  // Ensure the step stays within viewport boundaries
  // Add padding from viewport edges
  const edgePadding = 20;

  // Adjust horizontal position if needed
  if (actualLeft + stepWidth > viewportWidth - edgePadding) {
    // Too far right - move it left
    actualLeft = viewportWidth - stepWidth - edgePadding;
  }
  if (actualLeft < edgePadding) {
    // Too far left - move it right
    actualLeft = edgePadding;
  }

  // Adjust vertical position if needed
  if (actualTop + stepHeight > viewportHeight - edgePadding) {
    // Too far down - move it up
    actualTop = viewportHeight - stepHeight - edgePadding;
  }
  if (actualTop < edgePadding) {
    // Too far up - move it down
    actualTop = edgePadding;
  }

  // Reset transform since we're now using absolute positioning
  // This ensures the step is exactly where we want it regardless of the original transform
  return {
    left: `${actualLeft}px`,
    top: `${actualTop}px`,
    transform: 'none',
    width: `${stepWidth}px`,
    maxWidth: `${stepWidth}px`
  };
});
</script>

<style scoped lang="scss">
.walkthrough-step {
  position: fixed;
  z-index: 1060;
  background-color: var(--bs-body-bg);
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  width: 350px;
  max-width: calc(100vw - 40px); /* Ensure it doesn't overflow viewport width */
  padding: 1rem;
  border: 1px solid rgba(var(--bs-primary-rgb), 0.2);
  pointer-events: auto; /* Ensure the step is clickable */
  height: max-content;
  max-height: calc(100vh - 40px); /* Ensure it doesn't overflow viewport height */
  // overflow-y: auto; /* Add scrolling if content is too tall */

  /* Add a subtle glow effect to make the step stand out */
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15), 0 0 0 1px rgba(var(--bs-primary-rgb), 0.1), 0 0 15px rgba(var(--bs-primary-rgb), 0.1);

  &.with-arrow {
    &::before {
      content: '';
      position: absolute;
      width: 12px;
      height: 12px;
      background-color: var(--bs-body-bg);
      border: 1px solid rgba(var(--bs-primary-rgb), 0.2);
      transform: rotate(45deg);
    }
  }

  &.position-top.with-arrow::before {
    bottom: -6px;
    left: 50%;
    transform: translateX(-50%) rotate(45deg);
    border-top: none;
    border-left: none;
  }

  &.position-bottom.with-arrow::before {
    top: -6px;
    left: 50%;
    transform: translateX(-50%) rotate(45deg);
    border-bottom: none;
    border-right: none;
  }

  &.position-left.with-arrow::before {
    right: -6px;
    top: 50%;
    transform: translateY(-50%) rotate(45deg);
    border-left: none;
    border-bottom: none;
  }

  &.position-right.with-arrow::before {
    left: -6px;
    top: 50%;
    transform: translateY(-50%) rotate(45deg);
    border-right: none;
    border-top: none;
  }

  .step-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.75rem;

    .step-title {
      margin: 0;
      font-weight: 600;
      color: var(--bs-primary);
    }

    .close-btn {
      background: none;
      border: none;
      color: var(--bs-secondary);
      cursor: pointer;
      padding: 0.25rem;
      border-radius: 4px;
      display: flex;
      align-items: center;
      justify-content: center;
      position: relative;
      z-index: 1065; /* Higher z-index to ensure clickability */
      pointer-events: auto;

      &:hover {
        color: var(--bs-danger);
        background-color: rgba(var(--bs-danger-rgb), 0.1);
      }
    }
  }

  .step-content {
    margin-bottom: 1rem;

    p {
      margin: 0;
      font-size: 0.95rem;
      line-height: 1.5;
      color: var(--bs-body-color);
    }
  }

  .step-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
gap: 1rem;
    .step-progress {
      font-size: 0.85rem;
      color: var(--bs-secondary);
    }

    .step-actions {
      display: flex;
      gap: 0.5rem;

      button {
        position: relative;
        z-index: 1065; /* Higher z-index to ensure clickability */
        pointer-events: auto;
      }
    }
  }

  // Special styling for profile step
  &.profile-step {
    // Remove forced left positioning
    &.position-left {
      margin-left: 0;
    }

    // Add styling to ensure it fits better above the profile tile
    &.position-top {
      margin-top: 0;
    }

    border: 2px solid var(--bs-primary);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2), 0 0 0 1px rgba(var(--bs-primary-rgb), 0.2), 0 0 15px rgba(var(--bs-primary-rgb), 0.2);
  }

  // Special styling for notifications step
  &.notifications-step {
    &.position-top {
      margin-top: 0;
    }

    border: 2px solid var(--bs-primary);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2), 0 0 0 1px rgba(var(--bs-primary-rgb), 0.2), 0 0 15px rgba(var(--bs-primary-rgb), 0.2);
  }
}
</style>
