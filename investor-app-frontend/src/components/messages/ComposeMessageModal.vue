<template>
  <div class="modal fade" id="composeMessageModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-lg modal-dialog-centered">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title">Compose Message</h5>
          <button type="button" class="modal-close-btn" data-bs-dismiss="modal" aria-label="Close">
            <i class="bi bi-x-lg"></i>
          </button>
        </div>

        <div class="modal-body">
          <form @submit.prevent="sendMessage">
            <!-- Recipients -->
            <div class="mb-3">
              <label for="recipients" class="form-label">To:</label>

              <!-- For Clients (single recipient - their manager) -->
              <div v-if="isClient && recipients.length > 0" class="manager-recipient">
                <div class="recipient-chip">
                  {{ recipients[0].contact_name }}
                  <input type="hidden" name="recipientIds" :value="recipients[0].id">
                </div>
                <div class="manager-details mt-2">
                  <small class="text-muted d-block">Your account manager: {{ recipients[0].email }}</small>
                </div>
              </div>

              <!-- For Managers (multiple recipients possible) -->
              <div v-else-if="isManager">
                <div v-if="recipients.length === 0" class="alert alert-warning">
                  <i class="bi bi-exclamation-triangle me-2"></i>
                  You don't have any clients assigned to your account yet.
                </div>
                <div v-else>
                  <select
                    v-if="!isBroadcast"
                    v-model="selectedRecipients"
                    class="form-select"
                    id="recipients"
                    multiple
                    required
                  >
                    <option v-for="recipient in recipients" :key="recipient.id" :value="recipient.id">
                      {{ recipient.contact_name }} ({{ recipient.email }})
                    </option>
                  </select>

                  <div v-else class="alert alert-info">
                    <i class="bi bi-megaphone me-2"></i>
                    This message will be sent to all your clients ({{ recipients.length }})
                  </div>

                  <small class="text-muted mt-1 d-block" v-if="!isBroadcast && recipients.length > 0">
                    Hold Ctrl/Cmd to select multiple clients
                  </small>
                </div>
              </div>

              <!-- Loading State -->
              <div v-else-if="loading" class="text-center py-2">
                <i class="bi bi-arrow-repeat spinner-icon"></i>
                <span class="ms-2">Loading recipients...</span>
              </div>

              <!-- No Recipients Available -->
              <div v-else class="alert alert-warning">
                <i class="bi bi-exclamation-triangle me-2"></i>
                No recipients available. Managers need linked clients, and clients need an assigned manager.
              </div>
            </div>

            <!-- Broadcast Option (Managers only) -->
            <div v-if="isManager && recipients.length > 1" class="mb-3 form-check">
              <input
                type="checkbox"
                class="form-check-input"
                id="broadcastCheck"
                v-model="isBroadcast"
              >
              <label class="form-check-label" for="broadcastCheck">
                Send as broadcast message to all clients
              </label>
            </div>

            <!-- Subject -->
            <div class="mb-3">
              <label for="subject" class="form-label">Subject:</label>
              <input
                type="text"
                class="form-control"
                id="subject"
                v-model="subject"
                required
                maxlength="255"
              >
            </div>

            <!-- Technical Issue Alert for Clients -->
            <div v-if="isClient" class="alert alert-info mb-3">
              <i class="bi bi-info-circle me-2"></i>
              <strong>Need technical help?</strong> If your message is about a technical issue or bug, please use the
              <router-link to="/help" class="alert-link">Help & Support</router-link>
              section to report it instead for faster resolution.
            </div>

            <!-- Message Body -->
            <div class="mb-3">
              <label for="messageBody" class="form-label">Message:</label>
              <textarea
                class="form-control"
                id="messageBody"
                v-model="body"
                rows="8"
                required
                @input="checkSensitiveInfo"
              ></textarea>

              <!-- Sensitive Info Warning -->
              <div
                v-if="sensitiveInfoCheck.containsSensitiveInfo"
                class="alert alert-warning mt-2"
              >
                <i class="bi bi-shield-exclamation me-2"></i>
                <strong>Sensitive information detected:</strong>
                <ul class="mb-0 mt-1">
                  <li v-for="(warning, index) in sensitiveInfoCheck.warnings" :key="index">
                    {{ warning }}
                  </li>
                </ul>
              </div>

              <div v-if="sensitiveInfoCheck.isChecking" class="text-muted mt-2">
                <i class="bi bi-arrow-repeat spinner-icon"></i>
                <span class="ms-2">Checking for sensitive information...</span>
              </div>
            </div>

            <!-- Attachments -->
            <div class="mb-3">
              <label for="attachments" class="form-label">Attachments:</label>
              <div class="input-group">
                <input
                  type="file"
                  class="form-control"
                  id="attachments"
                  multiple
                  @change="handleFileUpload"
                >
                <button
                  type="button"
                  class="btn btn-outline-secondary"
                  @click="clearAttachments"
                  :disabled="!attachments.length"
                >
                  Clear
                </button>
              </div>

              <!-- Attachment Warning -->
              <div v-if="attachments.length > 0" class="alert alert-info mt-2">
                <i class="bi bi-info-circle me-2"></i>
                Attachments cannot be guaranteed safe for both sender and recipient.
              </div>

              <!-- Selected Files List -->
              <div v-if="attachments.length > 0" class="selected-files mt-3">
                <div v-for="(file, index) in attachments" :key="index" class="selected-file">
                  <i class="bi" :class="getFileIcon(file.name)"></i>
                  <span class="ms-2">{{ file.name }}</span>
                  <span class="file-size ms-2">({{ formatFileSize(file.size) }})</span>
                  <button
                    type="button"
                    class="btn btn-sm text-danger ms-2"
                    @click="removeAttachment(index)"
                  >
                    <i class="bi bi-x"></i>
                  </button>
                </div>
              </div>
            </div>
          </form>
        </div>

        <div class="modal-footer">
          <button
            type="button"
            class="btn btn-primary"
            @click="sendMessage"
            :disabled="!canSendMessage || sending"
          >
            <i v-if="sending" class="bi bi-arrow-repeat spinner-icon me-2"></i>
            <i v-else class="bi bi-send me-2"></i>
            Send Message
          </button>
          <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue';
import { useMessagesStore } from '@/stores/messages';
import { useAuthStore } from '@/stores/auth';
import { Modal } from 'bootstrap';
import { useToast } from 'vue-toastification';
import { debounce } from 'lodash';

// Props
const props = defineProps({
  initialRecipientId: {
    type: [Number, String],
    default: null
  },
  initialSubject: {
    type: String,
    default: ''
  },
  replyToMessage: {
    type: Object,
    default: null
  }
});

// Emits
const emit = defineEmits(['message-sent']);

// Store
const messagesStore = useMessagesStore();
const authStore = useAuthStore();
const toast = useToast();

// State
const subject = ref(props.initialSubject || '');
const body = ref('');
const selectedRecipients = ref([]);
const recipients = ref([]);
const attachments = ref([]);
const loading = ref(false);
const sending = ref(false);
const modalInstance = ref(null);
const isBroadcast = ref(false);
const sensitiveInfoCheck = computed(() => messagesStore.sensitiveInfoCheck);

// Computed
const isClient = computed(() => authStore.user?.role === 'client');
const isManager = computed(() => authStore.user?.role === 'manager');

const canSendMessage = computed(() => {
  if (loading.value || !recipients.value.length) return false;

  if (isClient.value) {
    // Clients must have a manager
    return recipients.value.length > 0 && subject.value.trim() && body.value.trim();
  } else if (isManager.value) {
    // Managers must select at least one recipient or use broadcast
    return (
      ((selectedRecipients.value.length > 0 && !isBroadcast.value) ||
       (isBroadcast.value && recipients.value.length > 0)) &&
      subject.value.trim() &&
      body.value.trim()
    );
  }

  return false;
});

// Methods
const loadRecipients = async () => {
  loading.value = true;

  try {
    await messagesStore.fetchRecipients();
    recipients.value = messagesStore.recipients;

    // For clients, auto-select their manager
    if (isClient.value && recipients.value.length > 0) {
      selectedRecipients.value = [recipients.value[0].id];
    }

    // If initialRecipientId is provided, select it
    if (props.initialRecipientId && recipients.value.some(r => r.id === parseInt(props.initialRecipientId))) {
      selectedRecipients.value = [parseInt(props.initialRecipientId)];
    }
  } catch (error) {
    console.error('Error loading recipients:', error);
    toast.error('Failed to load recipients');
  } finally {
    loading.value = false;
  }
};

const handleFileUpload = (event) => {
  const files = event.target.files;
  if (!files || !files.length) return;

  // Check file size limit (10MB per file)
  const maxSize = 10 * 1024 * 1024; // 10MB
  const oversizedFiles = [];

  // Add new files
  for (let i = 0; i < files.length; i++) {
    const file = files[i];

    if (file.size > maxSize) {
      oversizedFiles.push(file.name);
    } else if (attachments.value.length < 5) { // Max 5 attachments
      attachments.value.push(file);
    } else {
      toast.warning('Maximum 5 attachments allowed');
      break;
    }
  }

  // Reset the input to allow selecting the same file again
  event.target.value = '';

  // Show warning for oversized files
  if (oversizedFiles.length > 0) {
    toast.error(`Some files exceed the 10MB limit: ${oversizedFiles.join(', ')}`);
  }
};

const removeAttachment = (index) => {
  attachments.value.splice(index, 1);
};

const clearAttachments = () => {
  attachments.value = [];
};

const getFileIcon = (fileName) => {
  const extension = fileName.split('.').pop().toLowerCase();

  const iconMap = {
    pdf: 'bi-file-earmark-pdf',
    doc: 'bi-file-earmark-word',
    docx: 'bi-file-earmark-word',
    xls: 'bi-file-earmark-excel',
    xlsx: 'bi-file-earmark-excel',
    ppt: 'bi-file-earmark-ppt',
    pptx: 'bi-file-earmark-ppt',
    jpg: 'bi-file-earmark-image',
    jpeg: 'bi-file-earmark-image',
    png: 'bi-file-earmark-image',
    gif: 'bi-file-earmark-image',
    txt: 'bi-file-earmark-text',
    zip: 'bi-file-earmark-zip',
    rar: 'bi-file-earmark-zip'
  };

  return iconMap[extension] || 'bi-file-earmark';
};

const formatFileSize = (bytes) => {
  if (!bytes) return 'Unknown size';

  const units = ['B', 'KB', 'MB', 'GB'];
  let size = bytes;
  let unitIndex = 0;

  while (size >= 1024 && unitIndex < units.length - 1) {
    size /= 1024;
    unitIndex++;
  }

  return `${size.toFixed(1)} ${units[unitIndex]}`;
};

// Debounced function to check for sensitive information
const checkSensitiveInfo = debounce(async () => {
  if (body.value.length > 10) {
    await messagesStore.checkSensitiveInfo(body.value);
  } else {
    messagesStore.resetSensitiveInfoCheck();
  }
}, 500);

const sendMessage = async () => {
  if (!canSendMessage.value) return;

  sending.value = true;

  try {
    const messageData = {
      subject: subject.value,
      body: body.value,
      recipientIds: isClient.value
        ? [recipients.value[0].id]
        : (isBroadcast.value ? recipients.value.map(r => r.id) : selectedRecipients.value),
      isBroadcast: isBroadcast.value
    };

    const result = await messagesStore.sendMessage(messageData, attachments.value);

    if (result) {
      // Reset form
      subject.value = '';
      body.value = '';
      selectedRecipients.value = [];
      attachments.value = [];
      isBroadcast.value = false;
      messagesStore.resetSensitiveInfoCheck();

      // Close modal
      if (modalInstance.value) {
        modalInstance.value.hide();
      }

      // Emit event
      emit('message-sent');
    }
  } catch (error) {
    console.error('Error sending message:', error);
    toast.error('Failed to send message');
  } finally {
    sending.value = false;
  }
};

const resetForm = () => {
  subject.value = props.initialSubject || '';
  body.value = '';
  selectedRecipients.value = [];
  attachments.value = [];
  isBroadcast.value = false;
  messagesStore.resetSensitiveInfoCheck();

  // If initialRecipientId is provided, select it
  if (props.initialRecipientId && recipients.value.some(r => r.id === parseInt(props.initialRecipientId))) {
    selectedRecipients.value = [parseInt(props.initialRecipientId)];
  }

  // For clients, auto-select their manager
  if (isClient.value && recipients.value.length > 0) {
    selectedRecipients.value = [recipients.value[0].id];
  }

  // If replying to a message
  if (props.replyToMessage) {
    subject.value = `Re: ${props.replyToMessage.subject}`;
    selectedRecipients.value = [props.replyToMessage.sender_id];
  }
};

// Lifecycle hooks
onMounted(async () => {
  await loadRecipients();

  const modalElement = document.getElementById('composeMessageModal');
  if (modalElement) {
    modalInstance.value = new Modal(modalElement);

    // Add event listener for when modal is shown
    modalElement.addEventListener('shown.bs.modal', () => {
      resetForm();
    });
  }
});

// Watch for changes to replyToMessage
watch(() => props.replyToMessage, (newVal) => {
  if (newVal) {
    subject.value = `Re: ${newVal.subject}`;
    selectedRecipients.value = [newVal.sender_id];
  }
});
</script>

<style scoped lang="scss">
.recipient-chip {
  display: inline-block;
  background-color: var(--bs-primary);
  color: white;
  padding: 0.25rem 0.75rem;
  border-radius: 1rem;
  font-size: 0.875rem;
  margin-right: 0.5rem;
  margin-bottom: 0.5rem;
}

.manager-recipient {
  border-radius: 0.375rem;
  padding: 0.5rem;
  background-color: rgba(var(--bs-primary-rgb), 0.05);
  border: 1px solid rgba(var(--bs-primary-rgb), 0.2);
}

.selected-file {
  display: flex;
  align-items: center;
  padding: 0.5rem;
  border-radius: 0.25rem;
  background-color: var(--bs-tertiary-bg);
  margin-bottom: 0.5rem;

  .file-size {
    color: var(--bs-secondary-color);
    font-size: 0.875rem;
  }
}

.spinner-icon {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}
</style>
