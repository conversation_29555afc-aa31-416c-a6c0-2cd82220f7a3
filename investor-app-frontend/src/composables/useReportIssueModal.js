import { ref } from 'vue';

export function useReportIssueModal() {
  const isReportModalVisible = ref(false);
  const isReportModalLoading = ref(false);

  const showReportModal = () => {
    isReportModalVisible.value = true;
  };

  const handleReportSubmitted = () => {
    // Handle any additional logic after report submission if needed
  };

  const handleReportModalLoading = (loading) => {
    isReportModalLoading.value = loading;
  };

  return {
    isReportModalVisible,
    isReportModalLoading,
    showReportModal,
    handleReportSubmitted,
    handleReportModalLoading,
  };
}
