// src/directives/tooltip.js
import tippy from 'tippy.js';
import 'tippy.js/dist/tippy.css';

export default {
  mounted(el, binding) {
    let content = '';
    let placement = 'top'; // default placement

    // If the binding value is an object, extract content and placement
    if (typeof binding.value === 'object' && binding.value !== null) {
      content = binding.value.content;
      if (binding.value.placement) {
        placement = binding.value.placement;
      }
    } else {
      // Otherwise, binding.value is the content string
      content = binding.value || el.getAttribute('title');
    }

    if (content) {
      tippy(el, {
        content: content,
        allowHTML: true,
        placement: placement,
        animation: 'shift-away',
        duration: [500, 20],
        theme: 'light-border',
      });
      // Remove native title attribute to prevent default tooltip
      el.removeAttribute('title');
    }
  },
  updated(el, binding) {
    let newContent = '';
    let newPlacement = 'right';
    if (typeof binding.value === 'object' && binding.value !== null) {
      newContent = binding.value.content;
      if (binding.value.placement) {
        newPlacement = binding.value.placement;
      }
    } else {
      newContent = binding.value || el.getAttribute('title');
    }

    if (el._tippy) {
      el._tippy.setContent(newContent);
      // If tippy supports updating placement dynamically (it doesn't out-of-the-box), you may need to reinitialize.
      // For now, this updates only the content.
    }
  },
};