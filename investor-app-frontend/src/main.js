import { createApp } from 'vue';
import { createPinia } from 'pinia';
import App from './App.vue';
import router from './router';
import VueApexCharts from 'vue3-apexcharts';
import ToastPlugin from 'vue-toastification';
import tooltipDirective from './directives/tooltip.js';

// Styles
import './assets/styles/custom.scss';
import 'bootstrap-icons/font/bootstrap-icons.css';
import 'vue-toastification/dist/index.css';

// Bootstrap JS
import 'bootstrap/dist/js/bootstrap.bundle.min.js';

// Create Vue app instance
const app = createApp(App);

// Configure error handling
app.config.errorHandler = (err, vm, info) => {
  console.error('Global error:', err);
  console.error('Error info:', info);

  // Log to error tracking service in production
  if (import.meta.env.PROD) {
    // TODO: Implement error tracking service
    // errorTrackingService.logError(err, info);
  }
};

// Configure performance monitoring
if (import.meta.env.PROD) {
  app.config.performance = true;
}

// Configure warning handler
app.config.warnHandler = (msg, vm, trace) => {
  console.warn('Vue warning:', msg);
  console.warn('Warning trace:', trace);
};

// Create and configure Pinia
const pinia = createPinia();

// Plugin configurations
const toastConfig = {
  position: 'top-right',
  timeout: 5000,
  closeOnClick: true,
  pauseOnHover: true,
  draggable: true,
  draggablePercent: 0.6,
  showCloseButtonOnHover: false,
  hideProgressBar: true,
  closeButton: 'button',
  icon: true,
  rtl: false
};

// Register plugins
app.use(router);
app.use(pinia);
app.use(VueApexCharts);
app.use(ToastPlugin, toastConfig);

// Register directives
app.directive('tooltip', tooltipDirective);

// Mount app when document is ready
document.addEventListener('DOMContentLoaded', () => {
  app.mount('#app');
});

// Export app instance for testing
export { app, pinia };
