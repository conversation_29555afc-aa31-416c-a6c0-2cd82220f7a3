import { defineStore } from 'pinia';
import axios from 'axios';
import { useToast } from 'vue-toastification';
import { useNotificationsStore } from "@/stores/notifications";

const toast = useToast();
import router from '@/router'; // Import Vue Router instance

// Helper function to format remaining time
function formatRemainingTime(milliseconds) {
  if (milliseconds <= 0) return 'Expired';

  const seconds = Math.floor(milliseconds / 1000);
  const minutes = Math.floor(seconds / 60);
  const hours = Math.floor(minutes / 60);

  const remainingMinutes = minutes % 60;

  if (hours > 0) {
    return `${hours}h ${remainingMinutes}m`;
  } else {
    return `${minutes}m`;
  }
}

export const useAuthStore = defineStore('auth', {
  state: () => ({
    token: localStorage.getItem('token') || null,
    userId: localStorage.getItem('userId') || null,
    user: null,
    lastActivity: Date.now(),
    sessionCheckInterval: null, // Store the interval reference
    rememberMe: localStorage.getItem('rememberMe') === 'true',
  }),

  getters: {
    isLoggedIn: (state) => !!state.token,

    // Get token expiration information
    tokenExpiration: (state) => {
      if (!state.token) return null;

      try {
        // Decode the JWT token
        const tokenParts = state.token.split('.');
        if (tokenParts.length !== 3) return null;

        const payload = JSON.parse(atob(tokenParts[1]));

        // Calculate expiration time and remaining time
        const expirationTime = payload.exp * 1000; // Convert to milliseconds
        const remainingTime = expirationTime - Date.now();
        const isRememberMe = payload.rememberMe === true;

        return {
          expirationTime,
          remainingTime,
          isRememberMe,
          expirationDate: new Date(expirationTime),
          // Format remaining time as hours and minutes
          formattedRemainingTime: formatRemainingTime(remainingTime)
        };
      } catch (e) {
        console.warn('Error decoding token:', e);
        return null;
      }
    },
  },

  actions: {
    async login(email, password, rememberMe = false) {
      try {
        // Ensure rememberMe is properly passed as a boolean
        const rememberMeBool = !!rememberMe;

        const response = await axios.post(`${import.meta.env.VITE_API_URL}/auth/login`, {
          email,
          password,
          rememberMe: rememberMeBool,
        });

        const { token, user, expiresIn } = response.data;

        if (!token || !user || !user.id) {
          throw new Error("Invalid login response: Missing user ID or token.");
        }

        // Store token & user data
        localStorage.setItem('token', token);
        localStorage.setItem('userId', user.id);
        localStorage.setItem('user', JSON.stringify(user));
        localStorage.setItem('rememberMe', rememberMeBool ? 'true' : 'false');

        this.token = token;
        this.userId = user.id;
        this.user = user;
        this.lastActivity = Date.now();
        this.rememberMe = rememberMeBool;

        // Silently decode token to verify rememberMe flag is set correctly
        try {
          const tokenParts = token.split('.');
          if (tokenParts.length === 3) {
            const payload = JSON.parse(atob(tokenParts[1]));
            // Store expiration time for reference
            const expTime = new Date(payload.exp * 1000);
          }
        } catch (e) {
          // Silently handle token decode errors
        }

        // Fetch user profile immediately after login
        await this.fetchUserProfile();

        this.startSessionCheck();

        // Fetch notifications immediately after login
        const notificationsStore = useNotificationsStore();
        await notificationsStore.fetchNotifications();
        return { success: true };
      } catch (error) {
        console.error('Login error:', error.response?.data || error.message);
        throw new Error(error.response?.data?.error || 'Login failed. Please try again.');
      }
    },

    setAuthData(token, user) {
      if (!token || !user || !user.id) {
        throw new Error("Invalid authentication data");
      }

      localStorage.setItem('token', token);
      localStorage.setItem('userId', user.id);
      localStorage.setItem('user', JSON.stringify(user));

      // Preserve the rememberMe setting
      const rememberMe = localStorage.getItem('rememberMe') === 'true';

      this.token = token;
      this.userId = user.id;
      this.user = user;
      this.lastActivity = Date.now();
      this.rememberMe = rememberMe;

      this.startSessionCheck();
    },

    async fetchUserProfile() {
      try {
        if (!this.token) {
          // Skip user profile fetch when no token is available
          return;
        }

        const response = await axios.get(`${import.meta.env.VITE_API_URL}/profile`, {
          headers: { Authorization: `Bearer ${this.token}` },
        });

        if (!response.data.user || !response.data.user.id) {
          throw new Error("Invalid user profile response.");
        }

        this.user = response.data.user;
        localStorage.setItem('user', JSON.stringify(this.user)); // Store user details locally
        this.lastActivity = Date.now(); // ✅ Prevents immediate session expiry

      } catch (error) {
        console.error('Error fetching user profile:', error.response?.data || error.message);

        // ✅ Do NOT log out immediately unless we confirm the token is actually invalid
        if (error.response?.status === 401) {
          this.logoutAndRedirect();
        }
      }
    },

    logout() {

      // Explicitly clear stored authentication data
      this.token = null;
      this.userId = null;
      this.user = null;
      this.lastActivity = null;
      this.sessionCheckInterval = null;
      this.rememberMe = false;

      // Completely remove from localStorage
      localStorage.removeItem('token');
      localStorage.removeItem('userId');
      localStorage.removeItem('user');
      localStorage.removeItem('notifications');
      localStorage.removeItem('rememberMe');

      // Clear session interval checks
      this.cleanupSessionCheck();
    },

    async logoutAndRedirect() {
      await this.logout();
      router.push('/auth/login'); // Ensure logout process completes before redirect
    },

    async checkSessionValidity() {
      try {
        if (!this.token) {
          this.handleAuthError('Your session has expired. Please log in again.');
          return false;
        }

        // Check if we have rememberMe set
        const rememberMe = localStorage.getItem('rememberMe') === 'true';

        const response = await axios.get(`${import.meta.env.VITE_API_URL}/auth/check-session`, {
          headers: { Authorization: `Bearer ${this.token}` },
        });

        // Check for new token in response header (case-insensitive)
        const newToken = response.headers['x-new-token'] || response.headers['X-New-Token'];
        if (newToken) {
          this.setAuthData(newToken, this.user || response.data.user);
        }

        if (!response.data.valid) {
          this.handleAuthError('Your session has expired. Please log in again.');
          return false;
        }

        // Update user data if it's returned from the check-session endpoint
        if (response.data.user) {
          this.user = response.data.user;
          localStorage.setItem('user', JSON.stringify(this.user));
        }

        this.lastActivity = Date.now();
        return true;
      } catch (error) {
        if (error.response?.status === 401 || error.response?.status === 403) {
          this.handleAuthError('Your session has expired. Please log in again.');
        } else {
          console.error('Session check error:', error);
        }
        return false;
      }
    },

    handleAuthError(message) {
      const toast = useToast();
      toast.error(message, {
        timeout: 5000,
        position: "top-right",
        closeButton: true,
        pauseOnHover: true,
        draggable: true,
      });
      this.logoutAndRedirect();
    },

    updateActivity() {
      this.lastActivity = Date.now();
    },

    // Get the latest token expiration information
    getTokenExpirationInfo() {
      if (!this.token) return null;

      try {
        // Decode the JWT token
        const tokenParts = this.token.split('.');
        if (tokenParts.length !== 3) return null;

        const payload = JSON.parse(atob(tokenParts[1]));

        // Calculate expiration time and remaining time
        const expirationTime = payload.exp * 1000; // Convert to milliseconds
        const remainingTime = expirationTime - Date.now();
        const isRememberMe = payload.rememberMe === true;

        return {
          expirationTime,
          remainingTime,
          isRememberMe,
          expirationDate: new Date(expirationTime),
          formattedRemainingTime: formatRemainingTime(remainingTime)
        };
      } catch (e) {
        console.warn('Error decoding token:', e);
        return null;
      }
    },

    // Extend the session for another 24 hours
    async extendSession() {
      try {

        // Call the backend to extend the session
        const response = await axios.post(`${import.meta.env.VITE_API_URL}/auth/extend-session`, {}, {
          headers: { Authorization: `Bearer ${this.token}` },
        });

        // If the backend returns a new token, update it
        if (response.data.token) {
          // Store the new token
          localStorage.setItem('token', response.data.token);
          localStorage.setItem('rememberMe', 'true');

          this.token = response.data.token;
          this.rememberMe = true;
          this.lastActivity = Date.now();

          // Silently decode token to verify expiration
          try {
            const tokenParts = response.data.token.split('.');
            if (tokenParts.length === 3) {
              const payload = JSON.parse(atob(tokenParts[1]));
              // Store expiration time for reference
              const expTime = new Date(payload.exp * 1000);
            }
          } catch (e) {
            // Silently handle token decode errors
          }

          return true;
        } else {
          throw new Error('No token received from server');
        }
      } catch (error) {
        console.error('Failed to extend session:', error);
        throw error;
      }
    },

    startSessionCheck() {
      this.cleanupSessionCheck(); // Ensure previous checks are cleared before starting a new one

      window.addEventListener('focus', this.checkSessionValidity);
      window.addEventListener('mousemove', this.updateActivity);

      // Set different check intervals based on rememberMe
      // For trusted devices (rememberMe=true), check much less frequently (30 minutes)
      // For regular sessions, check every 5 minutes
      const checkInterval = this.rememberMe ? 30 * 60 * 1000 : 5 * 60 * 1000;

      this.sessionCheckInterval = setInterval(() => {
        this.checkSessionValidity();
      }, checkInterval);
    },

    cleanupSessionCheck() {
      if (this.sessionCheckInterval) {
        clearInterval(this.sessionCheckInterval);
        this.sessionCheckInterval = null;
      }

      window.removeEventListener('focus', this.checkSessionValidity);
      window.removeEventListener('mousemove', this.updateActivity);
    },
  },
});

// Create an Axios instance with global error handling
const apiClient = axios.create({
  baseURL: import.meta.env.VITE_API_URL,
});

// Global Response Interceptor
apiClient.interceptors.response.use(
  (response) => {
    // Check for new token in response header (case-insensitive)
    const newToken = response.headers['x-new-token'] || response.headers['X-New-Token'];
    if (newToken) {
      const authStore = useAuthStore();
      authStore.setAuthData(newToken, authStore.user);
    }
    return response;
  },
  (error) => {
    const authStore = useAuthStore();

    if (error.response) {
      const { status, data } = error.response;

      // If the backend reports an authentication issue, clear the session
      if (status === 401 || data.error?.toLowerCase().includes("invalid token")) {

        authStore.logoutAndRedirect(); // Clear session from the frontend
        router.push("/login"); // Redirect to login page
      }
    }

    return Promise.reject(error);
  }
);

// Automatically attach token to every request
apiClient.interceptors.request.use((config) => {
  const token = localStorage.getItem("token");
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  } else {
  }
  return config;
}, (error) => Promise.reject(error));

// Global response interceptor for handling errors
apiClient.interceptors.response.use(
  (response) => response,
  (error) => {

    // Extract token from request
    const status = error.response?.status;
    let backendErrorMessage = error.response?.data?.error || error.response?.data?.message;

    backendErrorMessage = backendErrorMessage
      ? `Server: ${backendErrorMessage}`
      : '';

    if (status === 401) {
      toast.error(`🪫 Session expired. Please log in again. ${backendErrorMessage}`, { position: "top-right" });

      const authStore = useAuthStore();  // Ensure we access the auth store correctly
      authStore.logoutAndRedirect();
    } else if (status === 403) {
      toast.warning(`✋ Some actions are only available to Manager roles. ${backendErrorMessage}`, { position: "top-right" });
    } else if (status === 500) {
      toast.error(`🤖 Server error. Please try again later. ${backendErrorMessage}`, { position: "top-right" });
    } else {
      toast.error(`⚠️ Notice: ${backendErrorMessage}`, { position: "top-right" });
    }

    return Promise.reject(error);
  }
);

// Export the Axios instance for reuse
export { apiClient };
