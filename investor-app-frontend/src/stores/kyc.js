import { defineStore } from 'pinia';
import axios from 'axios';
import { useToast } from 'vue-toastification';

export const useKycStore = defineStore('kyc', {
  state: () => ({
    status: null,
    loading: false,
    error: null,
    verificationHistory: [],
  }),

  getters: {
    isVerified: (state) => state.status === 'verified',
    isPending: (state) => state.status === 'pending',
    isFailed: (state) => state.status === 'failed',
    hasNotStarted: (state) => !state.status || state.status === 'not_started',
  },

  actions: {
    async fetchKycStatus() {
      this.loading = true;
      this.error = null;
      try {
        const response = await axios.get('/kyc/status');
        this.status = response.data.status;
        return response.data;
      } catch (error) {
        this.error = error.response?.data?.error || 'Failed to fetch KYC status';
        throw error;
      } finally {
        this.loading = false;
      }
    },

    async submitKycVerification(formData) {
      this.loading = true;
      this.error = null;
      try {
        const response = await axios.post('/kyc/verify', formData, {
          headers: {
            'Content-Type': 'multipart/form-data',
          },
        });
        this.status = response.data.status;
        return response.data;
      } catch (error) {
        this.error = error.response?.data?.error || 'Failed to submit KYC verification';
        throw error;
      } finally {
        this.loading = false;
      }
    },

    async fetchVerificationHistory() {
      this.loading = true;
      this.error = null;
      try {
        const response = await axios.get('/kyc/history');
        this.verificationHistory = response.data;
        return response.data;
      } catch (error) {
        this.error = error.response?.data?.error || 'Failed to fetch verification history';
        throw error;
      } finally {
        this.loading = false;
      }
    },

    reset() {
      this.status = null;
      this.loading = false;
      this.error = null;
      this.verificationHistory = [];
    },
  },
});
