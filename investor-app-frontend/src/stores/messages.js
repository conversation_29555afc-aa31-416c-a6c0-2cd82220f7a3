import { defineStore } from 'pinia';
import axios from 'axios';
import { useAuthStore } from './auth';
import { useToast } from 'vue-toastification';

export const useMessagesStore = defineStore('messages', {
  state: () => ({
    messages: [],
    currentMessage: null,
    loading: false,
    error: null,
    pagination: {
      total: 0,
      page: 1,
      limit: 20,
      pages: 0
    },
    currentFolder: 'inbox',
    recipients: [],
    sensitiveInfoCheck: {
      containsSensitiveInfo: false,
      warnings: [],
      isChecking: false
    }
  }),

  getters: {
    unreadCount: (state) => {
      return state.messages.filter(msg =>
        msg.status === 'unread' && state.currentFolder === 'inbox'
      ).length;
    },

    hasMessages: (state) => {
      return state.messages.length > 0;
    }
  },

  actions: {
    async fetchMessages(folder = 'inbox', page = 1, limit = 20) {
      this.loading = true;
      this.error = null;
      this.currentFolder = folder;

      try {
        const authStore = useAuthStore();
        const response = await axios.get(
          `${import.meta.env.VITE_API_URL}/messages`,
          {
            params: { folder, page, limit },
            headers: { Authorization: `Bearer ${authStore.token}` }
          }
        );

        this.messages = response.data.messages;
        this.pagination = response.data.pagination;
      } catch (error) {
        console.error('Error fetching messages:', error);
        this.error = error.response?.data?.error || 'Failed to load messages';
      } finally {
        this.loading = false;
      }
    },

    async fetchMessageDetails(messageId) {
      this.loading = true;
      this.error = null;

      try {
        const authStore = useAuthStore();
        const response = await axios.get(
          `${import.meta.env.VITE_API_URL}/messages/action/${messageId}`,
          {
            headers: { Authorization: `Bearer ${authStore.token}` }
          }
        );

        this.currentMessage = response.data;
        return response.data;
      } catch (error) {
        console.error('Error fetching message details:', error);
        this.error = error.response?.data?.error || 'Failed to load message details';
        return null;
      } finally {
        this.loading = false;
      }
    },

    async sendMessage(messageData, attachments = []) {
      this.loading = true;
      this.error = null;
      const toast = useToast();

      try {
        const authStore = useAuthStore();

        // Create form data for file uploads
        const formData = new FormData();
        formData.append('subject', messageData.subject);
        formData.append('body', messageData.body);
        formData.append('recipientIds', JSON.stringify(messageData.recipientIds));
        formData.append('isBroadcast', messageData.isBroadcast || false);

        // Add attachments if any
        if (attachments && attachments.length > 0) {
          attachments.forEach(file => {
            formData.append('attachments', file);
          });
        }

        const response = await axios.post(
          `${import.meta.env.VITE_API_URL}/messages`,
          formData,
          {
            headers: {
              Authorization: `Bearer ${authStore.token}`,
              'Content-Type': 'multipart/form-data'
            }
          }
        );

        // Show success message
        toast.success('Message sent successfully');

        // If message contains sensitive info, show a warning
        if (response.data.containsSensitiveInfo) {
          toast.warning('Your message was sent but contains potentially sensitive information');
        }

        return response.data;
      } catch (error) {
        console.error('Error sending message:', error);
        this.error = error.response?.data?.error || 'Failed to send message';
        toast.error(this.error);
        return null;
      } finally {
        this.loading = false;
      }
    },

    async markAsRead(messageId) {
      try {
        const authStore = useAuthStore();
        await axios.put(
          `${import.meta.env.VITE_API_URL}/messages/action/${messageId}/read`,
          {},
          {
            headers: { Authorization: `Bearer ${authStore.token}` }
          }
        );

        // Update local state
        const index = this.messages.findIndex(msg => msg.id === parseInt(messageId));
        if (index !== -1) {
          this.messages[index].status = 'read';
        }
      } catch (error) {
        console.error('Error marking message as read:', error);
      }
    },

    async archiveMessage(messageId) {
      this.loading = true;
      const toast = useToast();

      try {
        const authStore = useAuthStore();
        await axios.delete(
          `${import.meta.env.VITE_API_URL}/messages/action/${messageId}`,
          {
            headers: { Authorization: `Bearer ${authStore.token}` }
          }
        );

        // Remove from local state
        this.messages = this.messages.filter(msg => msg.id !== parseInt(messageId));
        toast.success('Message archived');
      } catch (error) {
        console.error('Error archiving message:', error);
        toast.error(error.response?.data?.error || 'Failed to archive message');
      } finally {
        this.loading = false;
      }
    },

    async fetchRecipients() {
      this.loading = true;

      try {
        const authStore = useAuthStore();
        const userRole = authStore.user?.role;

        let endpoint = '/messages/clients'; // Default for managers

        if (userRole === 'client') {
          endpoint = '/messages/manager';
        }

        const response = await axios.get(
          `${import.meta.env.VITE_API_URL}${endpoint}`,
          {
            headers: { Authorization: `Bearer ${authStore.token}` }
          }
        );

        if (userRole === 'client') {
          // For clients, we get a single manager
          this.recipients = response.data ? [response.data] : [];
          console.log('Client recipients (manager):', this.recipients);
        } else {
          // For managers, we get an array of clients
          this.recipients = response.data || [];
          console.log('Manager recipients (clients):', this.recipients);
        }

        return this.recipients;
      } catch (error) {
        console.error('Error fetching recipients:', error);
        const toast = useToast();
        toast.error('Failed to load message recipients. Please try again.');
        return [];
      } finally {
        this.loading = false;
      }
    },

    async checkSensitiveInfo(text) {
      if (!text || text.trim().length < 10) {
        this.sensitiveInfoCheck = {
          containsSensitiveInfo: false,
          warnings: [],
          isChecking: false
        };
        return this.sensitiveInfoCheck;
      }

      this.sensitiveInfoCheck.isChecking = true;

      try {
        const authStore = useAuthStore();
        const response = await axios.post(
          `${import.meta.env.VITE_API_URL}/messages/check-sensitive`,
          { text },
          {
            headers: { Authorization: `Bearer ${authStore.token}` }
          }
        );

        this.sensitiveInfoCheck = {
          ...response.data,
          isChecking: false
        };

        return this.sensitiveInfoCheck;
      } catch (error) {
        console.error('Error checking sensitive info:', error);
        this.sensitiveInfoCheck = {
          containsSensitiveInfo: false,
          warnings: [],
          isChecking: false
        };
        return this.sensitiveInfoCheck;
      }
    },

    resetSensitiveInfoCheck() {
      this.sensitiveInfoCheck = {
        containsSensitiveInfo: false,
        warnings: [],
        isChecking: false
      };
    }
  }
});
