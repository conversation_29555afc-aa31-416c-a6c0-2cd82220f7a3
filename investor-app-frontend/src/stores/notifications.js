import { define<PERSON><PERSON> } from 'pinia';
import axios from 'axios';

export const useNotificationsStore = defineStore('notifications', {
    state: () => ({
        notifications: [],
    }),

    actions: {
        async fetchNotifications() {
            try {
                const token = localStorage.getItem('token');
                if (!token) {
                    return;
                }

                const response = await axios.get(`${import.meta.env.VITE_API_URL}/notifications`, {
                    headers: { Authorization: `Bearer ${token}` },
                });

                this.notifications = response.data.notifications || [];
            } catch (error) {
            }
        },

        async sendNotification(recipientId, type, message) {
            try {
                await axios.post(
                    `${import.meta.env.VITE_API_URL}/notifications`,
                    { recipientId, type, message },
                    { headers: { Authorization: `Bearer ${localStorage.getItem('token')}` } }
                );
                await this.fetchNotifications(); // Refresh notifications after sending
            } catch (error) {
            }
        },

        async updateNotification(id, status) {
            try {
                await axios.put(
                    `${import.meta.env.VITE_API_URL}/notifications/${id}`,
                    { status },
                    { headers: { Authorization: `Bearer ${localStorage.getItem('token')}` } }
                );
                await this.fetchNotifications(); // Refresh notifications after updating
            } catch (error) {
            }
        },

        async deleteNotification(id) {
            try {
                await axios.delete(`${import.meta.env.VITE_API_URL}/notifications/${id}`, {
                    headers: { Authorization: `Bearer ${localStorage.getItem('token')}` },
                });
                await this.fetchNotifications(); // Refresh notifications after deleting
            } catch (error) {
            }
        },

        async markAsRead(id) {
            try {
                await this.updateNotification(id, "read");
            } catch (error) {
            }
        },

        async markMultipleAsRead(ids) {
            try {
                const token = localStorage.getItem('token');
                if (!token) {
                    return;
                }

                if (!ids || ids.length === 0) {
                    return; // Nothing to update
                }

                await axios.put(
                    `${import.meta.env.VITE_API_URL}/notifications/batch/mark-as-read`,
                    { ids },
                    { headers: { Authorization: `Bearer ${token}` } }
                );
                await this.fetchNotifications(); // Refresh notifications after updating
            } catch (error) {
            }
        },

        async ignoreNotification(id) {
            try {
                await this.updateNotification(id, "ignored");
            } catch (error) {
            }
        }
    }
});
