import { defineStore } from 'pinia';
import axios from 'axios';
import { useAuthStore } from '@/stores/auth';
import defaultProfileImage from '@/assets/images/default-profile.png'; // import local default image

export const useProfileStore = defineStore('profile', {
  state: () => ({
    user: {
      contact_name: '',
      email: '',
      phone: '',
      address: '',
      company_name: '',
      role: '',
      membership: '',
      profile_image: '',
    },
    profilePicture: null,
    // Use the imported local image as the fallback
    defaultProfilePicture: defaultProfileImage,
    passwords: {
      currentPassword: '',
      newPassword: '',
    },
    uploading: false,
    uploadFeedback: null,
    loading: false,
    error: null,
    successMessage: '',
    emailError: null,
  }),

  actions: {
    async fetchUserProfile() {
      try {
        this.loading = true;
        this.error = null;

        const authStore = useAuthStore();
        const response = await axios.get(
          `${import.meta.env.VITE_API_URL}/profile`,
          {
            headers: {
              'Authorization': `Bearer ${authStore.token}`
            }
          }
        );

        if (response.data.user) {
          // Include KYC status in user data
          this.user = {
            ...response.data.user,
            kyc_status: response.data.user.kyc_status || 'unverified'
          };
        } else {
          throw new Error('Failed to fetch profile');
        }
      } catch (error) {
        this.error = error.message || 'An error occurred while fetching profile';
        throw error;
      } finally {
        this.loading = false;
      }
    },

    async updateProfile() {
      const authStore = useAuthStore();
      try {
        await axios.put(`${import.meta.env.VITE_API_URL}/profile`, this.user, {
          headers: { Authorization: `Bearer ${authStore.token}` },
        });
        this.successMessage = 'Profile updated successfully!';
      } catch (error) {
        if (error.response?.status === 409) {
          this.emailError = 'This email is already in use.';
        } else {
          this.error = 'Error updating profile';
        }
        console.error('Error updating profile:', error);
      }
    },

    async changePassword() {
      const authStore = useAuthStore();
      try {
        await axios.put(`${import.meta.env.VITE_API_URL}/profile/password`, this.passwords, {
          headers: { Authorization: `Bearer ${authStore.token}` },
        });
        this.successMessage = 'Password changed successfully!';
        this.passwords = { currentPassword: '', newPassword: '' };
      } catch (error) {
        this.error = 'Error changing password';
        console.error('Error changing password:', error);
      }
    },

    async uploadProfilePicture(event) {
      const authStore = useAuthStore();
      const file = event.target.files[0];

      // Ensure a file is selected and is a valid Blob
      if (!file || !(file instanceof Blob)) {
          this.uploadFeedback = { success: false, message: 'Invalid file. Please select an image file.' };
          return;
      }

      const maxSizeInBytes = 5 * 1024 * 1024;
      if (file.size > maxSizeInBytes) {
          this.uploadFeedback = { success: false, message: 'The file size must be less than 5MB.' };
          return;
      }

      const image = new Image();
      const reader = new FileReader();

      reader.onload = (e) => {
          image.src = e.target.result;
          image.onload = async () => {
              if (image.width > 1000 || image.height > 1000) {
                  this.uploadFeedback = { success: false, message: 'Image dimensions must not exceed 1000x1000 pixels.' };
                  return;
              }

              const formData = new FormData();
              formData.append('profileImage', file);

              this.uploading = true;
              this.uploadFeedback = null;

              try {
                  const response = await axios.post(`${import.meta.env.VITE_API_URL}/profile/upload`, formData, {
                      headers: {
                          Authorization: `Bearer ${authStore.token}`,
                          'Content-Type': 'multipart/form-data',
                      },
                  });

                  // Assuming the backend now returns the new image URL in response.data.imageUrl
                  this.profilePicture = response.data.imageUrl || this.defaultProfilePicture;
                  this.uploadFeedback = { success: true, message: 'Profile image uploaded successfully!' };
                  this.fetchUserProfile();
              } catch (error) {
                  console.error('Error uploading profile image:', error.response?.data || error.message);
                  this.uploadFeedback = { success: false, message: error.response?.data?.error || 'Image upload failed.' };
              } finally {
                  this.uploading = false;
              }
          };
      };

      try {
          reader.readAsDataURL(file);
      } catch (err) {
          console.error("FileReader error:", err);
          this.uploadFeedback = { success: false, message: 'Could not process the selected file.' };
      }
    },

    logout() {
      this.user = {
        contact_name: '',
        email: '',
        phone: '',
        address: '',
        company_name: '',
        role: '',
        membership: '',
        profile_image: '',
      };
      this.profilePicture = this.defaultProfilePicture;
    },
  },

  getters: {
    isAuthenticated: (state) => !!state.user.email,
  },
});
