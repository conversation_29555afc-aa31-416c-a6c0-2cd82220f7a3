<template>
  <div class="create-vesting-page">
    <div class="page-header">
      <h2>Create Vesting Contract</h2>
      <p>Set up a new vesting schedule for an asset</p>
    </div>

    <div v-if="isManager" class="page-content">
      <CreateVestingContract @success="handleSuccess" />
    </div>
    <div v-else class="access-denied">
      <i class="bi bi-shield-lock"></i>
      <h3>Access Denied</h3>
      <p>Only managers can create vesting contracts.</p>
      <router-link to="/" class="btn btn-primary">
        Return to Dashboard
      </router-link>
    </div>
  </div>
</template>

<script>
import { computed } from 'vue';
import { useAuthStore } from '@/stores/auth';
import CreateVestingContract from '@/components/CreateVestingContract.vue';
import { useRouter } from 'vue-router';
import { useToast } from 'vue-toastification';

export default {
  name: 'CreateVestingContractView',
  components: {
    CreateVestingContract
  },
  setup() {
    const authStore = useAuthStore();
    const router = useRouter();
    const toast = useToast();

    const isManager = computed(() => authStore.user?.role === 'manager');

    const handleSuccess = () => {
      toast.success('Vesting contract created successfully');
      router.push('/vesting');
    };

    return {
      isManager,
      handleSuccess
    };
  }
};
</script>

<style scoped lang="scss">
.create-vesting-page {
  padding: 2rem;

  .page-header {
    margin-bottom: 2rem;

    h2 {
      font-size: 2rem;
      font-weight: 600;
      color: var(--bs-primary);
      margin-bottom: 0.5rem;
    }

    p {
      color: var(--bs-muted);
      margin: 0;
    }
  }

  .access-denied {
    text-align: center;
    padding: 4rem 2rem;
    background: rgba(var(--bs-danger-rgb), 0.1);
    border-radius: 12px;

    i {
      font-size: 3rem;
      color: var(--bs-danger);
      margin-bottom: 1rem;
    }

    h3 {
      font-size: 1.5rem;
      font-weight: 600;
      color: var(--bs-danger);
      margin-bottom: 0.5rem;
    }

    p {
      color: var(--bs-muted);
      margin-bottom: 1.5rem;
    }
  }
}
</style>
