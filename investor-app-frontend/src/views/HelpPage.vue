<template>
  <div class="help-page container py-4">
    <h1 class="mb-4">Help & Support</h1>

    <!-- Help Topics Section -->
    <div class="help-topics mb-5">
      <h2 class="h4 mb-3">Help Topics</h2>
      <p class="lead mb-4">Welcome to our comprehensive help center. Select a topic below to learn more about our platform's features and capabilities.</p>

      <!-- Frontend Features Section -->
      <div class="feature-category mb-5">
        <h3 class="h5 mb-3 category-header">
          <i class="bi bi-laptop me-2"></i>
          Frontend Features
        </h3>
        <div class="row g-4">
          <!-- Dashboard Overview -->
          <div class="col-md-6 col-lg-4">
            <div class="card h-100">
              <div class="card-body">
                <h3 class="h5 mb-3">
                  <i class="bi bi-speedometer2 me-2"></i>
                  Dashboard Overview
                </h3>
                <p class="card-text">Learn how to navigate your dashboard, view your portfolio performance, and access key metrics.</p>
                <ul class="list-unstyled feature-list">
                  <li><i class="bi bi-check-circle-fill text-success me-2"></i>Portfolio overview</li>
                  <li><i class="bi bi-check-circle-fill text-success me-2"></i>Performance metrics</li>
                  <li><i class="bi bi-check-circle-fill text-success me-2"></i>Recent activity</li>
                </ul>
                <RouterLink :to="{ name: 'help-dashboard' }" class="btn btn-sm btn-outline-primary mt-3">
                  <i class="bi bi-book me-1"></i> Read Documentation
                </RouterLink>
              </div>
            </div>
          </div>

          <!-- Tokenised Assets -->
          <div class="col-md-6 col-lg-4">
            <div class="card h-100">
              <div class="card-body">
                <h3 class="h5 mb-3">
                  <i class="bi bi-box me-2"></i>
                  Tokenised Assets
                </h3>
                <p class="card-text">Understanding how to view and manage your tokenised assets on the platform.</p>
                <ul class="list-unstyled feature-list">
                  <li><i class="bi bi-check-circle-fill text-success me-2"></i>Asset listing</li>
                  <li><i class="bi bi-check-circle-fill text-success me-2"></i>Asset details</li>
                  <li><i class="bi bi-check-circle-fill text-success me-2"></i>Transaction history</li>
                </ul>
                <RouterLink :to="{ name: 'help-assets' }" class="btn btn-sm btn-outline-primary mt-3">
                  <i class="bi bi-book me-1"></i> Read Documentation
                </RouterLink>
              </div>
            </div>
          </div>

          <!-- Vesting System -->
          <div class="col-md-6 col-lg-4">
            <div class="card h-100">
              <div class="card-body">
                <h3 class="h5 mb-3">
                  <i class="bi bi-clock-history me-2"></i>
                  Vesting System
                </h3>
                <p class="card-text">Understand how the vesting system works for your tokenised assets, including schedules and release conditions.</p>
                <ul class="list-unstyled feature-list">
                  <li><i class="bi bi-check-circle-fill text-success me-2"></i>Vesting schedules</li>
                  <li><i class="bi bi-check-circle-fill text-success me-2"></i>Lock-up periods</li>
                  <li><i class="bi bi-check-circle-fill text-success me-2"></i>Early release conditions</li>
                </ul>
                <RouterLink :to="{ name: 'help-vesting' }" class="btn btn-sm btn-outline-primary mt-3">
                  <i class="bi bi-book me-1"></i> Read Documentation
                </RouterLink>
              </div>
            </div>
          </div>

          <!-- Profile Management -->
          <div class="col-md-6 col-lg-4">
            <div class="card h-100">
              <div class="card-body">
                <h3 class="h5 mb-3">
                  <i class="bi bi-person me-2"></i>
                  Profile Management
                </h3>
                <p class="card-text">Update your profile information and manage your account preferences.</p>
                <ul class="list-unstyled feature-list">
                  <li><i class="bi bi-check-circle-fill text-success me-2"></i>Profile settings</li>
                  <li><i class="bi bi-check-circle-fill text-success me-2"></i>Security settings</li>
                  <li><i class="bi bi-check-circle-fill text-success me-2"></i>Notification preferences</li>
                </ul>
                <RouterLink :to="{ name: 'help-profile' }" class="btn btn-sm btn-outline-primary mt-3">
                  <i class="bi bi-book me-1"></i> Read Documentation
                </RouterLink>
              </div>
            </div>
          </div>

          <!-- KYC Verification -->
          <div class="col-md-6 col-lg-4">
            <div class="card h-100">
              <div class="card-body">
                <h3 class="h5 mb-3">
                  <i class="bi bi-shield-check me-2"></i>
                  KYC Verification
                </h3>
                <p class="card-text">Complete your identity verification process to access advanced platform features.</p>
                <ul class="list-unstyled feature-list">
                  <li><i class="bi bi-check-circle-fill text-success me-2"></i>Document upload</li>
                  <li><i class="bi bi-check-circle-fill text-success me-2"></i>Selfie verification</li>
                  <li><i class="bi bi-check-circle-fill text-success me-2"></i>Verification status</li>
                </ul>
                <RouterLink :to="{ name: 'help-kyc' }" class="btn btn-sm btn-outline-primary mt-3">
                  <i class="bi bi-book me-1"></i> Read Documentation
                </RouterLink>
              </div>
            </div>
          </div>

          <!-- Reports & Analytics -->
          <div class="col-md-6 col-lg-4">
            <div class="card h-100">
              <div class="card-body">
                <h3 class="h5 mb-3">
                  <i class="bi bi-graph-up me-2"></i>
                  Reports & Analytics
                </h3>
                <p class="card-text">Access detailed reports and analytics about your investments and portfolio performance.</p>
                <ul class="list-unstyled feature-list">
                  <li><i class="bi bi-check-circle-fill text-success me-2"></i>Balance history</li>
                  <li><i class="bi bi-check-circle-fill text-success me-2"></i>Asset performance</li>
                  <li><i class="bi bi-check-circle-fill text-success me-2"></i>Export capabilities</li>
                </ul>
                <RouterLink :to="{ name: 'help-reports' }" class="btn btn-sm btn-outline-primary mt-3">
                  <i class="bi bi-book me-1"></i> Read Documentation
                </RouterLink>
              </div>
            </div>
          </div>

          <!-- Client Management -->
          <div class="col-md-6 col-lg-4">
            <div class="card h-100">
              <div class="card-body">
                <h3 class="h5 mb-3">
                  <i class="bi bi-people me-2"></i>
                  Client Management
                </h3>
                <p class="card-text">For managers: Learn how to manage client relationships, link clients, and oversee their assets.</p>
                <ul class="list-unstyled feature-list">
                  <li><i class="bi bi-check-circle-fill text-success me-2"></i>Client linking</li>
                  <li><i class="bi bi-check-circle-fill text-success me-2"></i>Client overview</li>
                  <li><i class="bi bi-check-circle-fill text-success me-2"></i>Client dashboard</li>
                  <li><i class="bi bi-check-circle-fill text-success me-2"></i>Asset approval</li>
                </ul>
                <RouterLink :to="{ name: 'help-clients' }" class="btn btn-sm btn-outline-primary mt-3">
                  <i class="bi bi-book me-1"></i> Read Documentation
                </RouterLink>
              </div>
            </div>
          </div>

          <!-- Notifications -->
          <div class="col-md-6 col-lg-4">
            <div class="card h-100">
              <div class="card-body">
                <h3 class="h5 mb-3">
                  <i class="bi bi-bell me-2"></i>
                  Notifications
                </h3>
                <p class="card-text">Stay informed with real-time notifications about your account, assets, and platform updates.</p>
                <ul class="list-unstyled feature-list">
                  <li><i class="bi bi-check-circle-fill text-success me-2"></i>Real-time alerts</li>
                  <li><i class="bi bi-check-circle-fill text-success me-2"></i>Notification preferences</li>
                  <li><i class="bi bi-check-circle-fill text-success me-2"></i>Action notifications</li>
                </ul>
                <RouterLink :to="{ name: 'help-notifications' }" class="btn btn-sm btn-outline-primary mt-3">
                  <i class="bi bi-book me-1"></i> Read Documentation
                </RouterLink>
              </div>
            </div>
          </div>

          <!-- Subscription Management -->
          <div class="col-md-6 col-lg-4">
            <div class="card h-100">
              <div class="card-body">
                <h3 class="h5 mb-3">
                  <i class="bi bi-credit-card me-2"></i>
                  Subscription Management
                </h3>
                <p class="card-text">Manage your subscription, payment methods, and billing information.</p>
                <ul class="list-unstyled feature-list">
                  <li><i class="bi bi-check-circle-fill text-success me-2"></i>Plan details</li>
                  <li><i class="bi bi-check-circle-fill text-success me-2"></i>Payment methods</li>
                  <li><i class="bi bi-check-circle-fill text-success me-2"></i>Billing history</li>
                </ul>
                <RouterLink :to="{ name: 'help-subscriptions' }" class="btn btn-sm btn-outline-primary mt-3">
                  <i class="bi bi-book me-1"></i> Read Documentation
                </RouterLink>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Backend Features Section -->
      <div class="feature-category mb-5">
        <h3 class="h5 mb-3 category-header">
          <i class="bi bi-server me-2"></i>
          Backend & Technical Features
        </h3>
        <div class="row g-4">
          <!-- Authentication & Security -->
          <div class="col-md-6 col-lg-4">
            <div class="card h-100">
              <div class="card-body">
                <h3 class="h5 mb-3">
                  <i class="bi bi-shield-lock me-2"></i>
                  Authentication & Security
                </h3>
                <p class="card-text">Learn about our security measures, authentication methods, and how to keep your account secure.</p>
                <ul class="list-unstyled feature-list">
                  <li><i class="bi bi-check-circle-fill text-success me-2"></i>JWT authentication</li>
                  <li><i class="bi bi-check-circle-fill text-success me-2"></i>Role-based access</li>
                  <li><i class="bi bi-check-circle-fill text-success me-2"></i>Session management</li>
                </ul>
                <RouterLink :to="{ name: 'help-authentication' }" class="btn btn-sm btn-outline-primary mt-3">
                  <i class="bi bi-book me-1"></i> Read Documentation
                </RouterLink>
              </div>
            </div>
          </div>

          <!-- API Integration -->
          <div class="col-md-6 col-lg-4">
            <div class="card h-100">
              <div class="card-body">
                <h3 class="h5 mb-3">
                  <i class="bi bi-code-slash me-2"></i>
                  API Integration
                </h3>
                <p class="card-text">Technical documentation for developers integrating with our platform's API endpoints.</p>
                <ul class="list-unstyled feature-list">
                  <li><i class="bi bi-check-circle-fill text-success me-2"></i>API endpoints</li>
                  <li><i class="bi bi-check-circle-fill text-success me-2"></i>Authentication</li>
                  <li><i class="bi bi-check-circle-fill text-success me-2"></i>Rate limiting</li>
                </ul>
                <RouterLink :to="{ name: 'help-api' }" class="btn btn-sm btn-outline-primary mt-3">
                  <i class="bi bi-book me-1"></i> Read Documentation
                </RouterLink>
              </div>
            </div>
          </div>

          <!-- Blockchain Integration -->
          <div class="col-md-6 col-lg-4">
            <div class="card h-100">
              <div class="card-body">
                <h3 class="h5 mb-3">
                  <i class="bi bi-link-45deg me-2"></i>
                  Blockchain Integration
                </h3>
                <p class="card-text">Understand how our platform integrates with blockchain technology for asset tokenization.</p>
                <ul class="list-unstyled feature-list">
                  <li><i class="bi bi-check-circle-fill text-success me-2"></i>Transaction verification</li>
                  <li><i class="bi bi-check-circle-fill text-success me-2"></i>Smart contracts</li>
                  <li><i class="bi bi-check-circle-fill text-success me-2"></i>Blockchain security</li>
                </ul>
                <RouterLink :to="{ name: 'help-blockchain' }" class="btn btn-sm btn-outline-primary mt-3">
                  <i class="bi bi-book me-1"></i> Read Documentation
                </RouterLink>
              </div>
            </div>
          </div>

          <!-- Exchange Rates -->
          <div class="col-md-6 col-lg-4">
            <div class="card h-100">
              <div class="card-body">
                <h3 class="h5 mb-3">
                  <i class="bi bi-currency-exchange me-2"></i>
                  Exchange Rates
                </h3>
                <p class="card-text">Learn about our multi-currency support and real-time exchange rate functionality.</p>
                <ul class="list-unstyled feature-list">
                  <li><i class="bi bi-check-circle-fill text-success me-2"></i>Currency conversion</li>
                  <li><i class="bi bi-check-circle-fill text-success me-2"></i>Historical rates</li>
                  <li><i class="bi bi-check-circle-fill text-success me-2"></i>Rate updates</li>
                </ul>
                <RouterLink :to="{ name: 'help-exchange' }" class="btn btn-sm btn-outline-primary mt-3">
                  <i class="bi bi-book me-1"></i> Read Documentation
                </RouterLink>
              </div>
            </div>
          </div>

          <!-- Data Management -->
          <div class="col-md-6 col-lg-4">
            <div class="card h-100">
              <div class="card-body">
                <h3 class="h5 mb-3">
                  <i class="bi bi-database me-2"></i>
                  Data Management
                </h3>
                <p class="card-text">Information about how we store, process, and protect your data on our platform.</p>
                <ul class="list-unstyled feature-list">
                  <li><i class="bi bi-check-circle-fill text-success me-2"></i>Data storage</li>
                  <li><i class="bi bi-check-circle-fill text-success me-2"></i>Privacy measures</li>
                  <li><i class="bi bi-check-circle-fill text-success me-2"></i>Data export</li>
                </ul>
                <RouterLink :to="{ name: 'help-data' }" class="btn btn-sm btn-outline-primary mt-3">
                  <i class="bi bi-book me-1"></i> Read Documentation
                </RouterLink>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Report Issue Section -->
    <div class="report-issue">
      <h2 class="h4 mb-3">Report an Issue</h2>
      <div class="card">
        <div class="card-body">
          <p class="text-muted mb-4">
            If you're experiencing any issues with the platform, please use the form below to report them.
            Our technical team will review your report and get back to you as soon as possible.
          </p>
          <button
            class="btn btn-primary"
            @click="showReportModal"
            :disabled="isReportModalLoading"
          >
            <i v-if="!isReportModalLoading" class="bi bi-bug me-2"></i>
            <i v-else class="bi bi-arrow-repeat me-2"></i>
            {{ isReportModalLoading ? 'Loading...' : 'Report an Issue' }}
          </button>
        </div>
      </div>
    </div>

    <!-- Report Issue Modal -->
    <ReportIssueModal
      v-model:show="isReportModalVisible"
      @submitted="handleReportSubmitted"
      @loading="handleReportModalLoading"
    />
  </div>
</template>

<script setup>
import ReportIssueModal from '@/components/ReportIssueModal.vue';
import { useReportIssueModal } from '@/composables/useReportIssueModal';
import { useRouter } from 'vue-router';

const router = useRouter();

const {
  isReportModalVisible,
  isReportModalLoading,
  showReportModal,
  handleReportSubmitted,
  handleReportModalLoading,
} = useReportIssueModal();
</script>

<style lang="scss" scoped>
.help-page {
  max-width: 1200px;
  margin: 0 auto;
}

.feature-category {
  background-color: var(--bs-body-bg);
  border-radius: 8px;
  padding: 1.5rem;
  margin-bottom: 2rem;

  .category-header {
    border-bottom: 1px solid var(--bs-border-color);
    padding-bottom: 0.75rem;
    margin-bottom: 1.5rem;
    color: var(--bs-primary);
  }
}

.card {
  border: none;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  height: 100%;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  }

  .card-body {
    padding: 1.5rem;
    display: flex;
    flex-direction: column;
  }

  h3 {
    color: var(--bs-primary);
  }

  .bi {
    font-size: 1.2rem;
  }

  .feature-list {
    margin-bottom: auto;
  }

  .btn-outline-primary {
    margin-top: auto;
    align-self: flex-start;
  }
}

/* Loading Button Styles */
.btn:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

.bi-arrow-repeat {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
</style>
