<template>
  <div class="container mt-5">
    <div class="row justify-content-center">
      <div class="col-lg-6 col-md-8 col-sm-10">
        <div class="card ">
          <div class="card-body">
            <h3 class="card-title text-center mb-4">Set New Password</h3>

            <!-- Feedback Alert -->
            <div v-if="successMessage" class="alert alert-success" role="alert">
              {{ successMessage }}
            </div>
            <div v-if="errorMessage" class="alert alert-danger" role="alert">
              {{ errorMessage }}
            </div>

            <!-- Reset Confirmation Form -->
            <form @submit.prevent="resetPassword">
              <div class="mb-3">
                <label for="password" class="form-label">New Password</label>
                <input id="password" v-model="password" type="password" class="form-control"
                  placeholder="Enter your new password" required />
              </div>

              <div class="mb-3">
                <label for="confirmPassword" class="form-label">Confirm Password</label>
                <input id="confirmPassword" v-model="confirmPassword" type="password" class="form-control"
                  placeholder="Confirm your new password" required />
              </div>

              <button type="submit" class="btn btn-primary w-100" :disabled="isLoading">
                <span v-if="isLoading" class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                <span v-if="!isLoading">Reset Password</span>
              </button>
            </form>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import axios from 'axios';

export default {
  data() {
    return {
      token: '', // Will be fetched from the route parameter
      password: '',
      confirmPassword: '',
      isLoading: false,
      successMessage: null,
      errorMessage: null,
    };
  },
  async created() {
    // Fetch the token from the URL
    this.token = this.$route.params.token;
  },
  methods: {
    async resetPassword() {
      if (this.password !== this.confirmPassword) {
        this.errorMessage = 'Passwords do not match.';
        return;
      }

      this.isLoading = true;
      this.successMessage = null;
      this.errorMessage = null;

      try {
        await axios.post(`${import.meta.env.VITE_API_URL}/auth/password/reset/confirm`, {
          token: this.token,
          newPassword: this.password,
        });
        this.successMessage = 'Password reset successfully. You can now log in with your new password.';
      } catch (error) {
        this.errorMessage = error.response?.data?.error || 'Failed to reset password.';
      } finally {
        this.isLoading = false;
      }
    },
  },
};
</script>
