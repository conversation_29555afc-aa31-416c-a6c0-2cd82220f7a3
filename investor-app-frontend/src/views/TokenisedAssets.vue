<template>
  <div class="container mt-5">
    <h2 class="text-center mb-4">Asset Tokenisation System</h2>

    <!-- 🔹 Open Tokenisation Form -->
    <button class="btn btn-primary mb-4" @click="openModal">
      <i class="bi bi-plus-circle me-2"></i> Tokenise New Asset
    </button>

    <!-- 🔹 Tokenisation Modal -->
    <TokeniseAssetModal :isOpen="isModalOpen" :isManager="isManager" :allClients="allClients" @close="closeModal"
      @tokenise="tokeniseAsset" />

    <!-- 🔹 Asset Details Modal -->
    <AssetDetailsModal :isOpen="isDetailsModalOpen" :asset="selectedAsset" :isManager="isManager"
      @close="closeDetailsModal" @update-ownership="updateAssetOwnership" />

    <!-- 🔹 Tokenised Assets List -->
    <div class="card">
      <div class="card-body">
        <h5 class="card-title">Tokenised Assets</h5>

        <!-- 🔹 Filters Section -->
        <div class="filters-container">
          <!-- 🔹 Filter by Client (Only for Managers) -->
          <div class="filter-item" v-if="isManager">
            <label class="form-label">Client</label>
            <div class="input-group">
              <span class="input-group-text"><i class="bi bi-person"></i></span>
              <select v-model="selectedClientFilter" class="form-control">
                <option value="">All Clients</option>
                <option v-for="client in allClients" :key="client.id" :value="client.id">
                  {{ client.name }}
                </option>
              </select>
            </div>
          </div>

          <!-- 🔹 Filter by Asset Type -->
          <div class="filter-item">
            <label class="form-label">Asset Type</label>
            <div class="input-group">
              <span class="input-group-text"><i class="bi bi-box"></i></span>
              <select v-model="selectedTypeFilter" class="form-control">
                <option value="">All Types</option>
                <option value="real_estate">Real Estate</option>
                <option value="equities">Equities</option>
                <option value="bonds">Bonds</option>
              </select>
            </div>
          </div>

          <!-- 🔹 Filter by Status -->
          <div class="filter-item">
            <label class="form-label">Status</label>
            <div class="input-group">
              <span class="input-group-text"><i class="bi bi-check-circle"></i></span>
              <select v-model="selectedStatusFilter" class="form-control">
                <option value="">All Statuses</option>
                <option value="pending">Pending</option>
                <option value="approved">Approved</option>
                <option value="rejected">Rejected</option>
              </select>
            </div>
          </div>

          <!-- 🔹 Sort by Asset Value -->
          <div class="filter-item">
            <label class="form-label">Sort by Value</label>
            <div class="input-group">
              <span class="input-group-text"><i class="bi bi-sort-numeric-down"></i></span>
              <select v-model="sortByValue" class="form-control">
                <option value="asc">Ascending</option>
                <option value="desc">Descending</option>
              </select>
            </div>
          </div>
        </div>

        <!-- 🔹 Search Input -->
        <div class="mb-4">
          <div class="input-group">
            <span class="input-group-text"><i class="bi bi-search"></i></span>
            <input v-model="searchQuery" type="text" class="form-control"
              placeholder="Search assets by name, type, or metadata..." />
          </div>
        </div>

        <!-- Loading State -->
        <div v-if="isLoading" class="loading-container">
          <i class="bi bi-arrow-repeat spinner-icon"></i>
          <p class="mt-3 text-muted">Loading assets...</p>
        </div>

        <!-- Content when not loading -->
        <template v-else>
          <div class="table-responsive">
            <table class="table modern-table">
              <thead>
                <tr>
                  <th>Asset Name</th>
                  <th v-if="isManager">Owner</th>
                  <th>Type</th>
                  <th>Value (USD)</th>
                  <th v-if="!isManager">Ownership (%)</th>
                  <th>Status</th>
                  <th>Actions</th>
                </tr>
              </thead>
              <tbody>
                <tr v-for="asset in filteredAssets" :key="asset.id">
                  <td>{{ asset.name }}</td>
                  <td>{{ listAssetOwners(asset.ownership_records) }}</td>
                  <td>
                    <span :class="`badge badge-${getTypeClass(asset.type)}`">{{ asset.type }}</span>
                  </td>
                  <td class="text-end">${{ asset.value.toLocaleString() }}</td>
                  <td v-if="!isManager" class="text-center">
                    {{ getCurrentOwnershipPercentage(asset) }}%
                  </td>
                  <td>
                    <span :class="`status-badge ${getStatusClass(asset.approval_status)}`">
                      {{ asset.approval_status }}
                    </span>
                  </td>
                  <td>
                    <div class="btn-group">
                      <button @click="openDetailsModal(asset)" class="btn btn-sm btn-outline-primary">
                        <i class="bi bi-eye"></i>
                      </button>
                      <button v-if="isManager && asset.approval_status === 'pending'" @click="verifyAsset(asset.id)"
                        class="btn btn-sm btn-outline-success">
                        <i class="bi bi-check-lg"></i>
                      </button>
                    </div>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
          <p v-if="!filteredAssets.length" class="text-muted text-center mt-3">
            No tokenised assets found.
          </p>
        </template>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted } from "vue";
import TokeniseAssetModal from "@/components/TokeniseAssetModal.vue";
import AssetDetailsModal from "@/components/AssetDetailsModal.vue";
import { useAuthStore, apiClient } from "@/stores/auth";
import { useToast } from "vue-toastification";

export default {
  components: { TokeniseAssetModal, AssetDetailsModal },
  setup() {
    const toast = useToast();
    const authStore = useAuthStore();
    const tokenisedAssets = ref([]);
    const isModalOpen = ref(false);
    const isDetailsModalOpen = ref(false);
    const selectedAsset = ref(null);
    const allClients = ref([]);
    const selectedClientFilter = ref("");
    const selectedTypeFilter = ref("");
    const selectedStatusFilter = ref("");
    const sortByValue = ref("asc");
    const searchQuery = ref("");
    const isLoading = ref(true);

    const isManager = computed(() => authStore.user?.role === "manager");

    const fetchTokenisedAssets = async () => {
      try {
        if (!authStore.user) {
          console.warn("User data is not available yet.");
          return;
        }
        isLoading.value = true;
        const response = await apiClient.get("/tokenisation/");
        tokenisedAssets.value = response.data.assets || [];
      } catch (error) {
        toast.error(error.response?.data?.error || "Something went wrong while fetching assets.");
      } finally {
        isLoading.value = false;
      }
    };

    const fetchClients = async () => {
      if (!isManager.value) return;
      try {
        const response = await apiClient.get("/clients");
        allClients.value = response.data.clients || [];
      } catch (error) {
        console.error("Error fetching clients:", error.response?.data?.error || error.message);
      }
    };

    const openModal = () => {
      isModalOpen.value = true;
    };

    const closeModal = () => {
      isModalOpen.value = false;
    };

    const openDetailsModal = (asset) => {
      selectedAsset.value = asset;
      isDetailsModalOpen.value = true;
    };

    const closeDetailsModal = () => {
      selectedAsset.value = null;
      isDetailsModalOpen.value = false;
    };

    const updateAssetOwnership = async (updates) => {
      try {
        await apiClient.post(`/tokenisation/${selectedAsset.value.id}/ownership`, { updates });
        toast.success("Ownership updated successfully!");
        fetchTokenisedAssets();
      } catch (error) {
        toast.error(error.response?.data?.error || "Failed to update ownership.");
      }
    };

    const tokeniseAsset = async (assetData) => {
      try {
        await apiClient.post("/tokenisation/tokenise", assetData);
        toast.success("Asset successfully tokenised!");
        fetchTokenisedAssets();
      } catch (error) {
        toast.error(error.response?.data?.error || "Failed to tokenise asset.");
      } finally {
        closeModal();
      }
    };

    const getCurrentOwnershipPercentage = (asset) => {
      if (!asset.ownership_records || !asset.ownership_records.length) return 'N/A';
      const currentUserId = authStore.user.id;
      const record = asset.ownership_records.find(r => r.owner_id === currentUserId);
      return record ? record.ownership_percentage : 'N/A';
    };

    const getStatusClass = (status) => ({
      pending: "text-warning",
      approved: "text-success",
      verified: "text-success",
      rejected: "text-danger"
    }[status] || "text-muted");

    const getTypeClass = (type) => ({
      real_estate: "primary",
      equities: "success",
      bonds: "warning"
    }[type] || "secondary");

    const verifyAsset = async (assetId) => {
      try {
        const response = await apiClient.patch(`/tokenisation/approve/${assetId}`);
        toast.success(response.data.message || "Asset approved successfully!");
        fetchTokenisedAssets();
      } catch (error) {
        toast.error(error.response?.data?.error || "Failed to approve asset.");
      }
    };

    const filteredAssets = computed(() => {
      let filtered = tokenisedAssets.value;

      // Filter by client (for managers)
      if (isManager.value && selectedClientFilter.value) {
        filtered = filtered.filter(asset =>
          asset.ownership_records.some(record => record.owner_id === Number(selectedClientFilter.value))
        );
      }

      // Filter by type
      if (selectedTypeFilter.value) {
        filtered = filtered.filter(asset => asset.type === selectedTypeFilter.value);
      }

      // Filter by status
      if (selectedStatusFilter.value) {
        filtered = filtered.filter(asset => asset.approval_status === selectedStatusFilter.value);
      }

      // Filter by search query
      if (searchQuery.value.trim()) {
        const query = searchQuery.value.toLowerCase();
        filtered = filtered.filter(asset =>
          asset.name.toLowerCase().includes(query) ||
          asset.type.toLowerCase().includes(query) ||
          (asset.metadata && JSON.stringify(asset.metadata).toLowerCase().includes(query))
        );
      }

      // Sort by value
      return filtered.sort((a, b) =>
        sortByValue.value === "asc" ? a.value - b.value : b.value - a.value
      );
    });

    onMounted(() => {
      if (!authStore.user) {
        console.warn("User is not logged in, skipping API calls.");
        return;
      }
      fetchTokenisedAssets();
      if (isManager.value) {
        fetchClients();
      }
    });
    const listAssetOwners = (ownershipRecords) => {
      if (!ownershipRecords || !ownershipRecords.length) return 'N/A';
      return ownershipRecords.map(record => record.owner_name).join(', ');
    };
    return {
      isManager,
      isModalOpen,
      isDetailsModalOpen,
      selectedAsset,
      allClients,
      selectedClientFilter,
      selectedTypeFilter,
      selectedStatusFilter,
      sortByValue,
      searchQuery,
      isLoading,
      openModal,
      closeModal,
      openDetailsModal,
      closeDetailsModal,
      updateAssetOwnership,
      tokeniseAsset,
      filteredAssets,
      getStatusClass,
      getTypeClass,
      getCurrentOwnershipPercentage,
      verifyAsset,
      listAssetOwners
    };
  },
};
</script>

<style scoped>
.filters-container {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
  gap: 16px;
  margin-bottom: 20px;
}

.filter-item {
  display: flex;
  flex-direction: column;
}

.input-group-text {
  /* background: var(--bs-gray-900); */
  /* color: var(--bs-light); */
}

.table.modern-table {
  border-collapse: separate;
  border-spacing: 0 8px;
}

.table.modern-table th {
  text-transform: uppercase;
  font-weight: 600;
}

.table.modern-table tbody tr {
  background: var(--bs-dark);
  transition: all 0.2s ease-in-out;
}

.table.modern-table tbody tr:hover {
  background: var(--bs-gray-800);
}

.status-badge {
  padding: 6px 12px;
  border-radius: 12px;
  font-weight: bold;
}

.status-badge.text-warning {
  background: rgba(255, 193, 7, 0.2);
  color: #ffc107;
}

.status-badge.text-success {
  background: rgba(40, 167, 69, 0.2);
  color: #28a745;
}

.status-badge.text-danger {
  background: rgba(220, 53, 69, 0.2);
  color: #dc3545;
}

.btn-group {
  .btn {
    padding: 0.25rem 0.5rem;
    font-size: 0.875rem;
  }
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 200px;
  padding: 2rem;

  .spinner-icon {
    font-size: 2.5rem;
    color: var(--bs-primary);
    animation: spin 1s linear infinite;
  }

  p {
    font-size: 1rem;
    margin: 0;
    color: var(--bs-muted);
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
</style>
