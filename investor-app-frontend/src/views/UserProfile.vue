<template>
  <div class="container profile-container">
    <h2 class="text-center mb-4">Manage Your Profile</h2>
    <div v-if="profileStore.user" class="profile-layout">
      <!-- Profile Card -->
      <div class="profile-card">

        <div class="card">
          <div class="card-body text-center">
            <!-- Profile Image with Tooltip -->
            <div class="profile-img-container" :title="userTooltip">
              <img :src="userAvatar" alt="Profile" class="profile-img" />
              <button class="edit-icon" @click="triggerUpload">
                <i class="bi bi-camera"></i>
              </button>
              <input ref="fileInput" type="file" class="d-none" accept="image/*" @change="uploadProfilePicture" />
            </div>

            <!-- Upload Feedback -->
            <div v-if="profileStore.uploading" class="spinner-border text-primary mt-2" role="status">
              <span class="visually-hidden">Uploading...</span>
            </div>

            <div v-if="profileStore.uploadFeedback"
              :class="['alert', profileStore.uploadFeedback.success ? 'alert-success' : 'alert-danger', 'mt-2']">
              {{ profileStore.uploadFeedback.message }}
            </div>

            <!-- Profile Info -->
            <h4 class="mt-3">{{ profileStore.user.contact_name || "Your Name" }}</h4>
            <p class="text-muted">{{ profileStore.user.email || "<EMAIL>" }}</p>

            <!-- User Role & Membership -->
            <div class="profile-badges">
              <span :class="`badge badge-secondary badge-${profileStore.user.role.toLowerCase()}`">
                {{ profileStore.user.role || "Investor" }}
              </span>
              <span :class="`badge badge-primary badge-${profileStore.user.membership.toLowerCase()}`">
                {{ profileStore.user.membership || "Standard" }}
              </span>
            </div>

            <!-- Client-Specific: Link Status -->
            <div v-if="profileStore.user.role === 'client'" class="mt-3 link-status p-3">
              <h5 class="fw-bold text-primary">Link Status</h5>
              <p v-if="profileStore.user.manager_contact_name" class="text-success mb-0">
                ✅ Linked to
                <button class="manager-link-btn" @click="openManagerModal">
                  {{ profileStore.user.manager_contact_name }} from {{ profileStore.user.manager_company_name }}
                </button>
              </p>
              <p v-else class="text-danger mb-0">
                ❌ Not linked to a manager
              </p>
            </div>

            <!-- Notification Badge (if available) -->
            <div v-if="profileStore.notificationCount" class="notification-badge">
              <span class="badge bg-danger">{{ profileStore.notificationCount }}</span>
            </div>
          </div>
        </div>
        <!-- Session Info Card (only shown when rememberMe is true) -->
        <SessionInfoCard />
      </div>

      <!-- Profile & Settings Form -->
      <div class="profile-details">
        <!-- Existing profile update form code -->
        <div class="card">
          <div class="card-body">
            <h5 class="card-title">Profile Information</h5>
            <form @submit.prevent="updateProfile">
              <div class="mb-3">
                <label for="name" class="form-label">Full Name</label>
                <input id="name" v-model="profileStore.user.contact_name" type="text" class="form-control" required />
              </div>
              <div class="mb-3">
                <label for="email" class="form-label">Email</label>
                <input id="email" v-model="profileStore.user.email" type="email" class="form-control" required />
                <p v-if="profileStore.emailError" class="text-danger small">{{ profileStore.emailError }}</p>
              </div>
              <div class="mb-3">
                <label for="phone" class="form-label">Phone Number</label>
                <input id="phone" v-model="profileStore.user.phone" type="tel" class="form-control" />
              </div>
              <button type="submit" class="btn btn-primary w-100">Update Profile</button>
            </form>
          </div>
        </div>

        <!-- Application Preferences Section -->
        <div class="card mt-4">
          <div class="card-body">
            <h5 class="card-title">Application Preferences</h5>
            <div class="d-grid gap-3">
              <!-- Theme Preference -->
              <div class="d-flex justify-content-between align-items-center">
                <div>
                  <h6 class="mb-0">Theme</h6>
                  <p class="text-muted small mb-0">Switch between light and dark mode</p>
                </div>
                <div class="form-check form-switch">
                  <input class="form-check-input" type="checkbox" id="themeSwitch"
                    :checked="theme === 'dark'" @change="toggleTheme" />
                  <label class="form-check-label" for="themeSwitch">
                    {{ theme === 'dark' ? 'Dark' : 'Light' }}
                  </label>
                </div>
              </div>

              <!-- Dashboard Walkthrough -->
              <div class="d-flex justify-content-between align-items-center">
                <div>
                  <h6 class="mb-0">Dashboard Walkthrough</h6>
                  <p class="text-muted small mb-0">Restart the interactive dashboard guide</p>
                </div>
                <button class="btn btn-sm btn-outline-primary" @click="restartWalkthrough">
                  <i class="bi bi-info-circle me-1"></i> Start Walkthrough
                </button>
              </div>
            </div>
          </div>
        </div>

        <!-- KYC Verification Section -->
        <div class="card mt-4">
          <div class="accordion" id="profileAccordion">
            <!-- KYC Section -->
            <div class="accordion-item">
              <h2 class="accordion-header" id="headingKyc">
                <button
                  class="accordion-button collapsed"
                  type="button"
                  data-bs-toggle="collapse"
                  data-bs-target="#collapseKyc"
                  aria-expanded="false"
                  aria-controls="collapseKyc"
                >
                  <div class="d-flex align-items-center">
                    <i class="bi bi-shield-check me-2"></i>
                    <span>Identity Verification</span>
                    <span :class="['badge ms-2', getKycBadgeClass(profileStore.user.kyc_status)]">
                      {{ getKycStatusText(profileStore.user.kyc_status) }}
                    </span>
                  </div>
                </button>
              </h2>
              <div
                id="collapseKyc"
                class="accordion-collapse collapse"
                aria-labelledby="headingKyc"
                data-bs-parent="#profileAccordion"
              >
                <div class="accordion-body">
                  <div class="kyc-info mb-4">
                    <p class="text-muted">
                      Complete your identity verification to access additional features and ensure secure transactions.
                    </p>
                    <div class="kyc-status p-3 rounded" :class="getKycStatusClass(profileStore.user.kyc_status)">
                      <div class="d-flex align-items-center">
                        <i :class="getKycStatusIcon(profileStore.user.kyc_status)" class="me-2"></i>
                        <span>{{ getKycStatusMessage(profileStore.user.kyc_status) }}</span>
                      </div>
                    </div>
                  </div>

                  <router-link
                    to="/kyc"
                    class="btn btn-primary w-100"
                    :class="{ 'btn-success': profileStore.user?.kyc_status === 'verified' }">
                    <i class="bi bi-person-check me-2"></i>
                    {{ getKycButtonText(profileStore.user?.kyc_status) }}
                  </router-link>
                </div>
              </div>
            </div>

            <!-- Password Change Section -->
            <div class="accordion-item">
              <h2 class="accordion-header" id="headingPassword">
                <button
                  class="accordion-button collapsed"
                  type="button"
                  data-bs-toggle="collapse"
                  data-bs-target="#collapsePassword"
                  aria-expanded="false"
                  aria-controls="collapsePassword"
                >
                  Change Password
                </button>
              </h2>
              <div
                id="collapsePassword"
                class="accordion-collapse collapse"
                aria-labelledby="headingPassword"
                data-bs-parent="#profileAccordion"
              >
                <div class="accordion-body">
                  <form @submit.prevent="changePassword">
                    <div class="mb-3">
                      <label for="currentPassword" class="form-label">Current Password</label>
                      <input
                        id="currentPassword"
                        v-model="passwords.currentPassword"
                        type="password"
                        class="form-control"
                        required
                      />
                    </div>
                    <div class="mb-3">
                      <label for="newPassword" class="form-label">New Password</label>
                      <input
                        id="newPassword"
                        v-model="passwords.newPassword"
                        type="password"
                        class="form-control"
                        required
                      />
                    </div>
                    <button type="submit" class="btn btn-danger w-100">Change Password</button>
                  </form>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div v-else class="text-center">
      <p>Loading profile...</p>
    </div>

    <!-- Include the Manager Profile Modal -->
    <ManagerProfileModal />
  </div>
</template>

<script setup>
import { computed, ref, onMounted, onUnmounted, defineAsyncComponent } from 'vue';
import { useProfileStore } from '@/stores/profile';
import { useAuthStore } from '@/stores/auth';
import { useWalkthroughStore } from '@/stores/walkthrough';
import { useRouter } from 'vue-router';
import ManagerProfileModal from '@/components/ManagerProfileModal.vue';
import { Modal } from "bootstrap";
import userProfileTileCard from "@/components/userProfileTileCard.vue";
import SessionInfoCard from "@/components/SessionInfoCard.vue";

const profileStore = useProfileStore();
const authStore = useAuthStore();
const walkthroughStore = useWalkthroughStore();
const router = useRouter();
const passwords = ref({ currentPassword: '', newPassword: '' });
const fileInput = ref(null);
const theme = ref(localStorage.getItem('theme') || 'light');

// Lazy load the KYC component
const KycVerification = defineAsyncComponent({
  loader: () => import('@/components/kyc/KycVerification.vue'),
  loadingComponent: () => import('@/components/kyc/KycLoading.vue'),
  errorComponent: () => import('@/components/kyc/KycError.vue'),
  delay: 200,
  timeout: 3000
});

onMounted(() => {
  profileStore.fetchUserProfile();
});

// Computed property for user avatar: Use profilePicture if available, else defaultProfilePicture
const userAvatar = computed(() =>
  profileStore.profilePicture || profileStore.defaultProfilePicture
);

// Computed property for a tooltip summarising user info
const userTooltip = computed(() => {
  let tooltip = `Email: ${profileStore.user.email || 'N/A'}`;
  if (profileStore.user.membership) {
    tooltip += `\nMembership: ${profileStore.user.membership}`;
  }
  if (profileStore.user.lastLogin) {
    tooltip += `\nLast Login: ${profileStore.user.lastLogin}`;
  }
  return tooltip;
});

const updateProfile = async () => {
  await profileStore.updateProfile();
};

const changePassword = async () => {
  await profileStore.changePassword();
};

const triggerUpload = () => {
  fileInput.value.click();
};

const uploadProfilePicture = (event) => {
  profileStore.uploadProfilePicture(event);
};

const openManagerModal = () => {
  // Use Bootstrap's Modal API to open the manager modal
  const modalElement = document.getElementById("managerProfileModal");
  if (modalElement) {
    const modal = new Modal(modalElement, {
      backdrop: 'static',
      keyboard: false
    });
    modal.show();
  }
};

const toggleTheme = (event) => {
  // Toggle theme between 'light' and 'dark'
  theme.value = (theme.value === 'dark') ? 'light' : 'dark';
  document.documentElement.setAttribute('data-bs-theme', theme.value);
  localStorage.setItem('theme', theme.value);
};

const restartWalkthrough = async () => {
  // Reset walkthrough state
  walkthroughStore.resetWalkthrough();

  // Navigate to dashboard if not already there
  if (router.currentRoute.value.name !== 'dashboard') {
    await router.push('/dashboard');
    // Wait for the dashboard to load before starting the walkthrough
    setTimeout(() => {
      walkthroughStore.startWalkthrough();
    }, 500);
  } else {
    // If already on dashboard, start the walkthrough immediately
    walkthroughStore.startWalkthrough();
  }
};

// This function is kept for future use if KYC verification is implemented
const handleVerificationSubmitted = async () => {
  await profileStore.fetchUserProfile();
  // Note: showVerification variable is not defined yet
  // Will be implemented when KYC verification is added
};

// Clean up modal when component is unmounted
onUnmounted(() => {
  const modalElement = document.getElementById("managerProfileModal");
  if (modalElement) {
    const modal = Modal.getInstance(modalElement);
    if (modal) {
      modal.dispose();
    }
  }
});

// Add these new helper functions
const getKycBadgeClass = (status) => {
  switch (status) {
    case 'verified':
      return 'bg-success';
    case 'pending':
      return 'bg-warning';
    case 'failed':
      return 'bg-danger';
    default:
      return 'bg-secondary';
  }
};

const getKycStatusText = (status) => {
  switch (status) {
    case 'verified':
      return 'Verified';
    case 'pending':
      return 'Pending';
    case 'failed':
      return 'Failed';
    default:
      return 'Not Started';
  }
};

const getKycStatusClass = (status) => {
  switch (status) {
    case 'verified':
      return 'bg-success bg-opacity-10 text-success';
    case 'pending':
      return 'bg-warning bg-opacity-10 text-warning';
    case 'failed':
      return 'bg-danger bg-opacity-10 text-danger';
    default:
      return 'bg-secondary bg-opacity-10 text-secondary';
  }
};

const getKycStatusIcon = (status) => {
  switch (status) {
    case 'verified':
      return 'bi bi-check-circle-fill text-success';
    case 'pending':
      return 'bi bi-clock-fill text-warning';
    case 'failed':
      return 'bi bi-x-circle-fill text-danger';
    default:
      return 'bi bi-person-fill text-secondary';
  }
};

const getKycStatusMessage = (status) => {
  switch (status) {
    case 'verified':
      return 'Your identity has been verified successfully';
    case 'pending':
      return 'Your verification is being reviewed';
    case 'failed':
      return 'Verification failed. Please try again';
    default:
      return 'Complete your identity verification';
  }
};

const getKycButtonText = (status) => {
  switch (status) {
    case 'verified':
      return 'View Verification Status';
    case 'pending':
      return 'Check Verification Status';
    case 'failed':
      return 'Retry Verification';
    default:
      return 'Start Verification';
  }
};

// Add this to the script setup section
const isKycExpanded = ref(false);

const handleKycExpand = (event) => {
  const isExpanded = event.target.getAttribute('aria-expanded') === 'true';
  isKycExpanded.value = isExpanded;
};
</script>

<style scoped lang="scss">
.profile-container {
  padding: 1rem;
}

.profile-layout {
  display: flex;
  flex-wrap: wrap;
  gap: 2rem;
  justify-content: center;
}

.profile-card {
  flex: 1 1 300px;
  max-width: 450px;
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.profile-details {
  flex: 2 1 400px;
  max-width: 600px;
}

.profile-img-container {
  position: relative;
  display: inline-block;
}

.profile-img {
  width: 120px;
  height: 120px;
  object-fit: cover;
  border-radius: 50%;
  border: 2px solid var(--bs-card-border-color);
}

.edit-icon {
  position: absolute;
  bottom: 10px;
  right: 10px;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  border: none;
  border-radius: 50%;
  padding: 8px;
  font-size: 16px;
  cursor: pointer;
  opacity: 0.8;
  transition: background 0.3s ease;
}

.edit-icon:hover {
  background: rgba(0, 0, 0, 0.9);
}

/* Optional: Style for the notification badge on the avatar */
.notification-badge {
  position: absolute;
  top: 0;
  right: 0;
  transform: translate(50%, -50%);
  z-index: 1;
}

.manager-link-btn {
  background: none;
  border: none;
  padding: 0;
  color: inherit;
  font-weight: bold;
  text-decoration: underline;
  cursor: pointer;
  transition: color 0.2s ease, text-decoration 0.2s ease;
}

.manager-link-btn:hover {
  color: var(--bs-primary);
  text-decoration: none;
}

.link-status {
  border: 2px solid var(--bs-card-border-color);
  border-radius: 0.5rem;
  background-color: var(--bs-light);
}

.accordion-button {
  .badge {
    font-size: 0.8rem;
    padding: 0.35em 0.65em;
  }
}

.accordion-button:not(.collapsed) {
  .badge {
    background-color: rgba(255, 255, 255, 0.2) !important;
  }
}
</style>
