<template>
  <div class="vesting-page">
    <div class="page-header">
      <h2>Vesting Contracts</h2>
      <div class="header-actions">
        <button
          v-if="isManager"
          class="btn btn-primary"
          @click="router.push('/vesting/create')"
        >
          <i class="bi bi-plus-circle me-2"></i>Create Vesting Contract
        </button>
      </div>
    </div>

    <!-- ... rest of the existing template ... -->
  </div>
</template>

<script>
// ... existing imports ...

export default {
  name: 'VestingView',
  setup() {
    const authStore = useAuthStore();
    const router = useRouter();

    const isManager = computed(() => authStore.user?.role === 'manager');

    // ... rest of the existing setup code ...

    return {
      // ... existing returns ...
      isManager,
      router
    };
  }
};
</script>

<style scoped lang="scss">
.vesting-page {
  // ... existing styles ...

  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;

    h2 {
      font-size: 2rem;
      font-weight: 600;
      color: var(--bs-primary);
      margin: 0;
    }

    .header-actions {
      display: flex;
      gap: 1rem;
    }
  }

  // ... rest of the existing styles ...
}
</style>
