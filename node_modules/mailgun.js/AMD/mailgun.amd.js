define((function(){"use strict";var t=function(e,n){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=e[n])},t(e,n)};function e(e,n){if("function"!=typeof n&&null!==n)throw new TypeError("Class extends value "+String(n)+" is not a constructor or null");function r(){this.constructor=e}t(e,n),e.prototype=null===n?Object.create(n):(r.prototype=n.prototype,new r)}var n=function(){return n=Object.assign||function(t){for(var e,n=1,r=arguments.length;n<r;n++)for(var o in e=arguments[n])Object.prototype.hasOwnProperty.call(e,o)&&(t[o]=e[o]);return t},n.apply(this,arguments)};function r(t,e,n,r){return new(n||(n=Promise))((function(o,s){function i(t){try{u(r.next(t))}catch(t){s(t)}}function a(t){try{u(r.throw(t))}catch(t){s(t)}}function u(t){var e;t.done?o(t.value):(e=t.value,e instanceof n?e:new n((function(t){t(e)}))).then(i,a)}u((r=r.apply(t,e||[])).next())}))}function o(t,e){var n,r,o,s={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]},i=Object.create(("function"==typeof Iterator?Iterator:Object).prototype);return i.next=a(0),i.throw=a(1),i.return=a(2),"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function a(a){return function(u){return function(a){if(n)throw new TypeError("Generator is already executing.");for(;i&&(i=0,a[0]&&(s=0)),s;)try{if(n=1,r&&(o=2&a[0]?r.return:a[0]?r.throw||((o=r.return)&&o.call(r),0):r.next)&&!(o=o.call(r,a[1])).done)return o;switch(r=0,o&&(a=[2&a[0],o.value]),a[0]){case 0:case 1:o=a;break;case 4:return s.label++,{value:a[1],done:!1};case 5:s.label++,r=a[1],a=[0];continue;case 7:a=s.ops.pop(),s.trys.pop();continue;default:if(!(o=s.trys,(o=o.length>0&&o[o.length-1])||6!==a[0]&&2!==a[0])){s=0;continue}if(3===a[0]&&(!o||a[1]>o[0]&&a[1]<o[3])){s.label=a[1];break}if(6===a[0]&&s.label<o[1]){s.label=o[1],o=a;break}if(o&&s.label<o[2]){s.label=o[2],s.ops.push(a);break}o[2]&&s.ops.pop(),s.trys.pop();continue}a=e.call(t,s)}catch(t){a=[6,t],r=0}finally{n=o=0}if(5&a[0])throw a[1];return{value:a[0]?a[1]:void 0,done:!0}}([a,u])}}}function s(t,e,n){if(n||2===arguments.length)for(var r,o=0,s=e.length;o<s;o++)!r&&o in e||(r||(r=Array.prototype.slice.call(e,0,o)),r[o]=e[o]);return t.concat(r||Array.prototype.slice.call(e))}"function"==typeof SuppressedError&&SuppressedError;var i="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{};function a(t){return t&&t.__esModule&&Object.prototype.hasOwnProperty.call(t,"default")?t.default:t}var u,c={exports:{}};
/*! https://mths.be/base64 v1.0.0 by @mathias | MIT license */var p,l,d,h=(u||(u=1,p=c,l=c.exports,function(t){var e=l,n=p&&p.exports==e&&p,r="object"==typeof i&&i;r.global!==r&&r.window!==r||(t=r);var o=function(t){this.message=t};(o.prototype=new Error).name="InvalidCharacterError";var s=function(t){throw new o(t)},a="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",u=/[\t\n\f\r ]/g,c={encode:function(t){t=String(t),/[^\0-\xFF]/.test(t)&&s("The string to be encoded contains characters outside of the Latin1 range.");for(var e,n,r,o,i=t.length%3,u="",c=-1,p=t.length-i;++c<p;)e=t.charCodeAt(c)<<16,n=t.charCodeAt(++c)<<8,r=t.charCodeAt(++c),u+=a.charAt((o=e+n+r)>>18&63)+a.charAt(o>>12&63)+a.charAt(o>>6&63)+a.charAt(63&o);return 2==i?(e=t.charCodeAt(c)<<8,n=t.charCodeAt(++c),u+=a.charAt((o=e+n)>>10)+a.charAt(o>>4&63)+a.charAt(o<<2&63)+"="):1==i&&(o=t.charCodeAt(c),u+=a.charAt(o>>2)+a.charAt(o<<4&63)+"=="),u},decode:function(t){var e=(t=String(t).replace(u,"")).length;e%4==0&&(e=(t=t.replace(/==?$/,"")).length),(e%4==1||/[^+a-zA-Z0-9/]/.test(t))&&s("Invalid character: the string to be decoded is not correctly encoded.");for(var n,r,o=0,i="",c=-1;++c<e;)r=a.indexOf(t.charAt(c)),n=o%4?64*n+r:r,o++%4&&(i+=String.fromCharCode(255&n>>(-2*o&6)));return i},version:"1.0.0"};if(e&&!e.nodeType)if(n)n.exports=c;else for(var d in c)c.hasOwnProperty(d)&&(e[d]=c[d]);else t.base64=c}(c.exports)),c.exports),f={exports:{}},y=f.exports;var m=(d||(d=1,function(t){var e,n;e=y,n=function(){return function(){return function(t){var e=[];if(0===t.length)return"";if("string"!=typeof t[0])throw new TypeError("Url must be a string. Received "+t[0]);if(t[0].match(/^[^/:]+:\/*$/)&&t.length>1){var n=t.shift();t[0]=n+t[0]}t[0].match(/^file:\/\/\//)?t[0]=t[0].replace(/^([^/:]+):\/*/,"$1:///"):t[0]=t[0].replace(/^([^/:]+):\/*/,"$1://");for(var r=0;r<t.length;r++){var o=t[r];if("string"!=typeof o)throw new TypeError("Url must be a string. Received "+o);""!==o&&(r>0&&(o=o.replace(/^[\/]+/,"")),o=r<t.length-1?o.replace(/[\/]+$/,""):o.replace(/[\/]+$/,"/"),e.push(o))}var s=e.join("/"),i=(s=s.replace(/\/(\?|&|#[^!])/g,"$1")).split("?");return i.shift()+(i.length>0?"?":"")+i.join("&")}("object"==typeof arguments[0]?arguments[0]:[].slice.call(arguments))}},t.exports?t.exports=n():e.urljoin=n()}(f)),f.exports),b=a(m);function g(t,e){return function(){return t.apply(e,arguments)}}const{toString:v}=Object.prototype,{getPrototypeOf:w}=Object,R=(_=Object.create(null),t=>{const e=v.call(t);return _[e]||(_[e]=e.slice(8,-1).toLowerCase())});var _;const S=t=>(t=t.toLowerCase(),e=>R(e)===t),T=t=>e=>typeof e===t,{isArray:q}=Array,E=T("undefined");const D=S("ArrayBuffer");const O=T("string"),A=T("function"),k=T("number"),x=t=>null!==t&&"object"==typeof t,C=t=>{if("object"!==R(t))return!1;const e=w(t);return!(null!==e&&e!==Object.prototype&&null!==Object.getPrototypeOf(e)||Symbol.toStringTag in t||Symbol.iterator in t)},P=S("Date"),F=S("File"),B=S("Blob"),L=S("FileList"),j=S("URLSearchParams"),[U,N,I,M]=["ReadableStream","Request","Response","Headers"].map(S);function W(t,e,{allOwnKeys:n=!1}={}){if(null==t)return;let r,o;if("object"!=typeof t&&(t=[t]),q(t))for(r=0,o=t.length;r<o;r++)e.call(null,t[r],r,t);else{const o=n?Object.getOwnPropertyNames(t):Object.keys(t),s=o.length;let i;for(r=0;r<s;r++)i=o[r],e.call(null,t[i],i,t)}}function H(t,e){e=e.toLowerCase();const n=Object.keys(t);let r,o=n.length;for(;o-- >0;)if(r=n[o],e===r.toLowerCase())return r;return null}const V="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:"undefined"!=typeof window?window:global,z=t=>!E(t)&&t!==V;const J=(K="undefined"!=typeof Uint8Array&&w(Uint8Array),t=>K&&t instanceof K);var K;const $=S("HTMLFormElement"),Q=(({hasOwnProperty:t})=>(e,n)=>t.call(e,n))(Object.prototype),G=S("RegExp"),X=(t,e)=>{const n=Object.getOwnPropertyDescriptors(t),r={};W(n,((n,o)=>{let s;!1!==(s=e(n,o,t))&&(r[o]=s||n)})),Object.defineProperties(t,r)},Y="abcdefghijklmnopqrstuvwxyz",Z="0123456789",tt={DIGIT:Z,ALPHA:Y,ALPHA_DIGIT:Y+Y.toUpperCase()+Z};const et=S("AsyncFunction"),nt=(rt="function"==typeof setImmediate,ot=A(V.postMessage),rt?setImmediate:ot?(st=`axios@${Math.random()}`,it=[],V.addEventListener("message",(({source:t,data:e})=>{t===V&&e===st&&it.length&&it.shift()()}),!1),t=>{it.push(t),V.postMessage(st,"*")}):t=>setTimeout(t));var rt,ot,st,it;const at="undefined"!=typeof queueMicrotask?queueMicrotask.bind(V):"undefined"!=typeof process&&process.nextTick||nt;var ut={isArray:q,isArrayBuffer:D,isBuffer:function(t){return null!==t&&!E(t)&&null!==t.constructor&&!E(t.constructor)&&A(t.constructor.isBuffer)&&t.constructor.isBuffer(t)},isFormData:t=>{let e;return t&&("function"==typeof FormData&&t instanceof FormData||A(t.append)&&("formdata"===(e=R(t))||"object"===e&&A(t.toString)&&"[object FormData]"===t.toString()))},isArrayBufferView:function(t){let e;return e="undefined"!=typeof ArrayBuffer&&ArrayBuffer.isView?ArrayBuffer.isView(t):t&&t.buffer&&D(t.buffer),e},isString:O,isNumber:k,isBoolean:t=>!0===t||!1===t,isObject:x,isPlainObject:C,isReadableStream:U,isRequest:N,isResponse:I,isHeaders:M,isUndefined:E,isDate:P,isFile:F,isBlob:B,isRegExp:G,isFunction:A,isStream:t=>x(t)&&A(t.pipe),isURLSearchParams:j,isTypedArray:J,isFileList:L,forEach:W,merge:function t(){const{caseless:e}=z(this)&&this||{},n={},r=(r,o)=>{const s=e&&H(n,o)||o;C(n[s])&&C(r)?n[s]=t(n[s],r):C(r)?n[s]=t({},r):q(r)?n[s]=r.slice():n[s]=r};for(let t=0,e=arguments.length;t<e;t++)arguments[t]&&W(arguments[t],r);return n},extend:(t,e,n,{allOwnKeys:r}={})=>(W(e,((e,r)=>{n&&A(e)?t[r]=g(e,n):t[r]=e}),{allOwnKeys:r}),t),trim:t=>t.trim?t.trim():t.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,""),stripBOM:t=>(65279===t.charCodeAt(0)&&(t=t.slice(1)),t),inherits:(t,e,n,r)=>{t.prototype=Object.create(e.prototype,r),t.prototype.constructor=t,Object.defineProperty(t,"super",{value:e.prototype}),n&&Object.assign(t.prototype,n)},toFlatObject:(t,e,n,r)=>{let o,s,i;const a={};if(e=e||{},null==t)return e;do{for(o=Object.getOwnPropertyNames(t),s=o.length;s-- >0;)i=o[s],r&&!r(i,t,e)||a[i]||(e[i]=t[i],a[i]=!0);t=!1!==n&&w(t)}while(t&&(!n||n(t,e))&&t!==Object.prototype);return e},kindOf:R,kindOfTest:S,endsWith:(t,e,n)=>{t=String(t),(void 0===n||n>t.length)&&(n=t.length),n-=e.length;const r=t.indexOf(e,n);return-1!==r&&r===n},toArray:t=>{if(!t)return null;if(q(t))return t;let e=t.length;if(!k(e))return null;const n=new Array(e);for(;e-- >0;)n[e]=t[e];return n},forEachEntry:(t,e)=>{const n=(t&&t[Symbol.iterator]).call(t);let r;for(;(r=n.next())&&!r.done;){const n=r.value;e.call(t,n[0],n[1])}},matchAll:(t,e)=>{let n;const r=[];for(;null!==(n=t.exec(e));)r.push(n);return r},isHTMLForm:$,hasOwnProperty:Q,hasOwnProp:Q,reduceDescriptors:X,freezeMethods:t=>{X(t,((e,n)=>{if(A(t)&&-1!==["arguments","caller","callee"].indexOf(n))return!1;const r=t[n];A(r)&&(e.enumerable=!1,"writable"in e?e.writable=!1:e.set||(e.set=()=>{throw Error("Can not rewrite read-only method '"+n+"'")}))}))},toObjectSet:(t,e)=>{const n={},r=t=>{t.forEach((t=>{n[t]=!0}))};return q(t)?r(t):r(String(t).split(e)),n},toCamelCase:t=>t.toLowerCase().replace(/[-_\s]([a-z\d])(\w*)/g,(function(t,e,n){return e.toUpperCase()+n})),noop:()=>{},toFiniteNumber:(t,e)=>null!=t&&Number.isFinite(t=+t)?t:e,findKey:H,global:V,isContextDefined:z,ALPHABET:tt,generateString:(t=16,e=tt.ALPHA_DIGIT)=>{let n="";const{length:r}=e;for(;t--;)n+=e[Math.random()*r|0];return n},isSpecCompliantForm:function(t){return!!(t&&A(t.append)&&"FormData"===t[Symbol.toStringTag]&&t[Symbol.iterator])},toJSONObject:t=>{const e=new Array(10),n=(t,r)=>{if(x(t)){if(e.indexOf(t)>=0)return;if(!("toJSON"in t)){e[r]=t;const o=q(t)?[]:{};return W(t,((t,e)=>{const s=n(t,r+1);!E(s)&&(o[e]=s)})),e[r]=void 0,o}}return t};return n(t,0)},isAsyncFn:et,isThenable:t=>t&&(x(t)||A(t))&&A(t.then)&&A(t.catch),setImmediate:nt,asap:at};function ct(t,e,n,r,o){Error.call(this),Error.captureStackTrace?Error.captureStackTrace(this,this.constructor):this.stack=(new Error).stack,this.message=t,this.name="AxiosError",e&&(this.code=e),n&&(this.config=n),r&&(this.request=r),o&&(this.response=o,this.status=o.status?o.status:null)}ut.inherits(ct,Error,{toJSON:function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:ut.toJSONObject(this.config),code:this.code,status:this.status}}});const pt=ct.prototype,lt={};["ERR_BAD_OPTION_VALUE","ERR_BAD_OPTION","ECONNABORTED","ETIMEDOUT","ERR_NETWORK","ERR_FR_TOO_MANY_REDIRECTS","ERR_DEPRECATED","ERR_BAD_RESPONSE","ERR_BAD_REQUEST","ERR_CANCELED","ERR_NOT_SUPPORT","ERR_INVALID_URL"].forEach((t=>{lt[t]={value:t}})),Object.defineProperties(ct,lt),Object.defineProperty(pt,"isAxiosError",{value:!0}),ct.from=(t,e,n,r,o,s)=>{const i=Object.create(pt);return ut.toFlatObject(t,i,(function(t){return t!==Error.prototype}),(t=>"isAxiosError"!==t)),ct.call(i,t.message,e,n,r,o),i.cause=t,i.name=t.name,s&&Object.assign(i,s),i};function dt(t){return ut.isPlainObject(t)||ut.isArray(t)}function ht(t){return ut.endsWith(t,"[]")?t.slice(0,-2):t}function ft(t,e,n){return t?t.concat(e).map((function(t,e){return t=ht(t),!n&&e?"["+t+"]":t})).join(n?".":""):e}const yt=ut.toFlatObject(ut,{},null,(function(t){return/^is[A-Z]/.test(t)}));function mt(t,e,n){if(!ut.isObject(t))throw new TypeError("target must be an object");e=e||new FormData;const r=(n=ut.toFlatObject(n,{metaTokens:!0,dots:!1,indexes:!1},!1,(function(t,e){return!ut.isUndefined(e[t])}))).metaTokens,o=n.visitor||c,s=n.dots,i=n.indexes,a=(n.Blob||"undefined"!=typeof Blob&&Blob)&&ut.isSpecCompliantForm(e);if(!ut.isFunction(o))throw new TypeError("visitor must be a function");function u(t){if(null===t)return"";if(ut.isDate(t))return t.toISOString();if(!a&&ut.isBlob(t))throw new ct("Blob is not supported. Use a Buffer instead.");return ut.isArrayBuffer(t)||ut.isTypedArray(t)?a&&"function"==typeof Blob?new Blob([t]):Buffer.from(t):t}function c(t,n,o){let a=t;if(t&&!o&&"object"==typeof t)if(ut.endsWith(n,"{}"))n=r?n:n.slice(0,-2),t=JSON.stringify(t);else if(ut.isArray(t)&&function(t){return ut.isArray(t)&&!t.some(dt)}(t)||(ut.isFileList(t)||ut.endsWith(n,"[]"))&&(a=ut.toArray(t)))return n=ht(n),a.forEach((function(t,r){!ut.isUndefined(t)&&null!==t&&e.append(!0===i?ft([n],r,s):null===i?n:n+"[]",u(t))})),!1;return!!dt(t)||(e.append(ft(o,n,s),u(t)),!1)}const p=[],l=Object.assign(yt,{defaultVisitor:c,convertValue:u,isVisitable:dt});if(!ut.isObject(t))throw new TypeError("data must be an object");return function t(n,r){if(!ut.isUndefined(n)){if(-1!==p.indexOf(n))throw Error("Circular reference detected in "+r.join("."));p.push(n),ut.forEach(n,(function(n,s){!0===(!(ut.isUndefined(n)||null===n)&&o.call(e,n,ut.isString(s)?s.trim():s,r,l))&&t(n,r?r.concat(s):[s])})),p.pop()}}(t),e}function bt(t){const e={"!":"%21","'":"%27","(":"%28",")":"%29","~":"%7E","%20":"+","%00":"\0"};return encodeURIComponent(t).replace(/[!'()~]|%20|%00/g,(function(t){return e[t]}))}function gt(t,e){this._pairs=[],t&&mt(t,this,e)}const vt=gt.prototype;function wt(t){return encodeURIComponent(t).replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}function Rt(t,e,n){if(!e)return t;const r=n&&n.encode||wt;ut.isFunction(n)&&(n={serialize:n});const o=n&&n.serialize;let s;if(s=o?o(e,n):ut.isURLSearchParams(e)?e.toString():new gt(e,n).toString(r),s){const e=t.indexOf("#");-1!==e&&(t=t.slice(0,e)),t+=(-1===t.indexOf("?")?"?":"&")+s}return t}vt.append=function(t,e){this._pairs.push([t,e])},vt.toString=function(t){const e=t?function(e){return t.call(this,e,bt)}:bt;return this._pairs.map((function(t){return e(t[0])+"="+e(t[1])}),"").join("&")};class _t{constructor(){this.handlers=[]}use(t,e,n){return this.handlers.push({fulfilled:t,rejected:e,synchronous:!!n&&n.synchronous,runWhen:n?n.runWhen:null}),this.handlers.length-1}eject(t){this.handlers[t]&&(this.handlers[t]=null)}clear(){this.handlers&&(this.handlers=[])}forEach(t){ut.forEach(this.handlers,(function(e){null!==e&&t(e)}))}}var St={silentJSONParsing:!0,forcedJSONParsing:!0,clarifyTimeoutError:!1},Tt={isBrowser:!0,classes:{URLSearchParams:"undefined"!=typeof URLSearchParams?URLSearchParams:gt,FormData:"undefined"!=typeof FormData?FormData:null,Blob:"undefined"!=typeof Blob?Blob:null},protocols:["http","https","file","blob","url","data"]};const qt="undefined"!=typeof window&&"undefined"!=typeof document,Et="object"==typeof navigator&&navigator||void 0,Dt=qt&&(!Et||["ReactNative","NativeScript","NS"].indexOf(Et.product)<0),Ot="undefined"!=typeof WorkerGlobalScope&&self instanceof WorkerGlobalScope&&"function"==typeof self.importScripts,At=qt&&window.location.href||"http://localhost";var kt={...Object.freeze({__proto__:null,hasBrowserEnv:qt,hasStandardBrowserEnv:Dt,hasStandardBrowserWebWorkerEnv:Ot,navigator:Et,origin:At}),...Tt};function xt(t){function e(t,n,r,o){let s=t[o++];if("__proto__"===s)return!0;const i=Number.isFinite(+s),a=o>=t.length;if(s=!s&&ut.isArray(r)?r.length:s,a)return ut.hasOwnProp(r,s)?r[s]=[r[s],n]:r[s]=n,!i;r[s]&&ut.isObject(r[s])||(r[s]=[]);return e(t,n,r[s],o)&&ut.isArray(r[s])&&(r[s]=function(t){const e={},n=Object.keys(t);let r;const o=n.length;let s;for(r=0;r<o;r++)s=n[r],e[s]=t[s];return e}(r[s])),!i}if(ut.isFormData(t)&&ut.isFunction(t.entries)){const n={};return ut.forEachEntry(t,((t,r)=>{e(function(t){return ut.matchAll(/\w+|\[(\w*)]/g,t).map((t=>"[]"===t[0]?"":t[1]||t[0]))}(t),r,n,0)})),n}return null}const Ct={transitional:St,adapter:["xhr","http","fetch"],transformRequest:[function(t,e){const n=e.getContentType()||"",r=n.indexOf("application/json")>-1,o=ut.isObject(t);o&&ut.isHTMLForm(t)&&(t=new FormData(t));if(ut.isFormData(t))return r?JSON.stringify(xt(t)):t;if(ut.isArrayBuffer(t)||ut.isBuffer(t)||ut.isStream(t)||ut.isFile(t)||ut.isBlob(t)||ut.isReadableStream(t))return t;if(ut.isArrayBufferView(t))return t.buffer;if(ut.isURLSearchParams(t))return e.setContentType("application/x-www-form-urlencoded;charset=utf-8",!1),t.toString();let s;if(o){if(n.indexOf("application/x-www-form-urlencoded")>-1)return function(t,e){return mt(t,new kt.classes.URLSearchParams,Object.assign({visitor:function(t,e,n,r){return kt.isNode&&ut.isBuffer(t)?(this.append(e,t.toString("base64")),!1):r.defaultVisitor.apply(this,arguments)}},e))}(t,this.formSerializer).toString();if((s=ut.isFileList(t))||n.indexOf("multipart/form-data")>-1){const e=this.env&&this.env.FormData;return mt(s?{"files[]":t}:t,e&&new e,this.formSerializer)}}return o||r?(e.setContentType("application/json",!1),function(t,e,n){if(ut.isString(t))try{return(e||JSON.parse)(t),ut.trim(t)}catch(t){if("SyntaxError"!==t.name)throw t}return(n||JSON.stringify)(t)}(t)):t}],transformResponse:[function(t){const e=this.transitional||Ct.transitional,n=e&&e.forcedJSONParsing,r="json"===this.responseType;if(ut.isResponse(t)||ut.isReadableStream(t))return t;if(t&&ut.isString(t)&&(n&&!this.responseType||r)){const n=!(e&&e.silentJSONParsing)&&r;try{return JSON.parse(t)}catch(t){if(n){if("SyntaxError"===t.name)throw ct.from(t,ct.ERR_BAD_RESPONSE,this,null,this.response);throw t}}}return t}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,maxBodyLength:-1,env:{FormData:kt.classes.FormData,Blob:kt.classes.Blob},validateStatus:function(t){return t>=200&&t<300},headers:{common:{Accept:"application/json, text/plain, */*","Content-Type":void 0}}};ut.forEach(["delete","get","head","post","put","patch"],(t=>{Ct.headers[t]={}}));const Pt=ut.toObjectSet(["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"]);const Ft=Symbol("internals");function Bt(t){return t&&String(t).trim().toLowerCase()}function Lt(t){return!1===t||null==t?t:ut.isArray(t)?t.map(Lt):String(t)}function jt(t,e,n,r,o){return ut.isFunction(r)?r.call(this,e,n):(o&&(e=n),ut.isString(e)?ut.isString(r)?-1!==e.indexOf(r):ut.isRegExp(r)?r.test(e):void 0:void 0)}let Ut=class{constructor(t){t&&this.set(t)}set(t,e,n){const r=this;function o(t,e,n){const o=Bt(e);if(!o)throw new Error("header name must be a non-empty string");const s=ut.findKey(r,o);(!s||void 0===r[s]||!0===n||void 0===n&&!1!==r[s])&&(r[s||e]=Lt(t))}const s=(t,e)=>ut.forEach(t,((t,n)=>o(t,n,e)));if(ut.isPlainObject(t)||t instanceof this.constructor)s(t,e);else if(ut.isString(t)&&(t=t.trim())&&!/^[-_a-zA-Z0-9^`|~,!#$%&'*+.]+$/.test(t.trim()))s((t=>{const e={};let n,r,o;return t&&t.split("\n").forEach((function(t){o=t.indexOf(":"),n=t.substring(0,o).trim().toLowerCase(),r=t.substring(o+1).trim(),!n||e[n]&&Pt[n]||("set-cookie"===n?e[n]?e[n].push(r):e[n]=[r]:e[n]=e[n]?e[n]+", "+r:r)})),e})(t),e);else if(ut.isHeaders(t))for(const[e,r]of t.entries())o(r,e,n);else null!=t&&o(e,t,n);return this}get(t,e){if(t=Bt(t)){const n=ut.findKey(this,t);if(n){const t=this[n];if(!e)return t;if(!0===e)return function(t){const e=Object.create(null),n=/([^\s,;=]+)\s*(?:=\s*([^,;]+))?/g;let r;for(;r=n.exec(t);)e[r[1]]=r[2];return e}(t);if(ut.isFunction(e))return e.call(this,t,n);if(ut.isRegExp(e))return e.exec(t);throw new TypeError("parser must be boolean|regexp|function")}}}has(t,e){if(t=Bt(t)){const n=ut.findKey(this,t);return!(!n||void 0===this[n]||e&&!jt(0,this[n],n,e))}return!1}delete(t,e){const n=this;let r=!1;function o(t){if(t=Bt(t)){const o=ut.findKey(n,t);!o||e&&!jt(0,n[o],o,e)||(delete n[o],r=!0)}}return ut.isArray(t)?t.forEach(o):o(t),r}clear(t){const e=Object.keys(this);let n=e.length,r=!1;for(;n--;){const o=e[n];t&&!jt(0,this[o],o,t,!0)||(delete this[o],r=!0)}return r}normalize(t){const e=this,n={};return ut.forEach(this,((r,o)=>{const s=ut.findKey(n,o);if(s)return e[s]=Lt(r),void delete e[o];const i=t?function(t){return t.trim().toLowerCase().replace(/([a-z\d])(\w*)/g,((t,e,n)=>e.toUpperCase()+n))}(o):String(o).trim();i!==o&&delete e[o],e[i]=Lt(r),n[i]=!0})),this}concat(...t){return this.constructor.concat(this,...t)}toJSON(t){const e=Object.create(null);return ut.forEach(this,((n,r)=>{null!=n&&!1!==n&&(e[r]=t&&ut.isArray(n)?n.join(", "):n)})),e}[Symbol.iterator](){return Object.entries(this.toJSON())[Symbol.iterator]()}toString(){return Object.entries(this.toJSON()).map((([t,e])=>t+": "+e)).join("\n")}get[Symbol.toStringTag](){return"AxiosHeaders"}static from(t){return t instanceof this?t:new this(t)}static concat(t,...e){const n=new this(t);return e.forEach((t=>n.set(t))),n}static accessor(t){const e=(this[Ft]=this[Ft]={accessors:{}}).accessors,n=this.prototype;function r(t){const r=Bt(t);e[r]||(!function(t,e){const n=ut.toCamelCase(" "+e);["get","set","has"].forEach((r=>{Object.defineProperty(t,r+n,{value:function(t,n,o){return this[r].call(this,e,t,n,o)},configurable:!0})}))}(n,t),e[r]=!0)}return ut.isArray(t)?t.forEach(r):r(t),this}};function Nt(t,e){const n=this||Ct,r=e||n,o=Ut.from(r.headers);let s=r.data;return ut.forEach(t,(function(t){s=t.call(n,s,o.normalize(),e?e.status:void 0)})),o.normalize(),s}function It(t){return!(!t||!t.__CANCEL__)}function Mt(t,e,n){ct.call(this,null==t?"canceled":t,ct.ERR_CANCELED,e,n),this.name="CanceledError"}function Wt(t,e,n){const r=n.config.validateStatus;n.status&&r&&!r(n.status)?e(new ct("Request failed with status code "+n.status,[ct.ERR_BAD_REQUEST,ct.ERR_BAD_RESPONSE][Math.floor(n.status/100)-4],n.config,n.request,n)):t(n)}Ut.accessor(["Content-Type","Content-Length","Accept","Accept-Encoding","User-Agent","Authorization"]),ut.reduceDescriptors(Ut.prototype,(({value:t},e)=>{let n=e[0].toUpperCase()+e.slice(1);return{get:()=>t,set(t){this[n]=t}}})),ut.freezeMethods(Ut),ut.inherits(Mt,ct,{__CANCEL__:!0});const Ht=(t,e,n=3)=>{let r=0;const o=function(t,e){t=t||10;const n=new Array(t),r=new Array(t);let o,s=0,i=0;return e=void 0!==e?e:1e3,function(a){const u=Date.now(),c=r[i];o||(o=u),n[s]=a,r[s]=u;let p=i,l=0;for(;p!==s;)l+=n[p++],p%=t;if(s=(s+1)%t,s===i&&(i=(i+1)%t),u-o<e)return;const d=c&&u-c;return d?Math.round(1e3*l/d):void 0}}(50,250);return function(t,e){let n,r,o=0,s=1e3/e;const i=(e,s=Date.now())=>{o=s,n=null,r&&(clearTimeout(r),r=null),t.apply(null,e)};return[(...t)=>{const e=Date.now(),a=e-o;a>=s?i(t,e):(n=t,r||(r=setTimeout((()=>{r=null,i(n)}),s-a)))},()=>n&&i(n)]}((n=>{const s=n.loaded,i=n.lengthComputable?n.total:void 0,a=s-r,u=o(a);r=s;t({loaded:s,total:i,progress:i?s/i:void 0,bytes:a,rate:u||void 0,estimated:u&&i&&s<=i?(i-s)/u:void 0,event:n,lengthComputable:null!=i,[e?"download":"upload"]:!0})}),n)},Vt=(t,e)=>{const n=null!=t;return[r=>e[0]({lengthComputable:n,total:t,loaded:r}),e[1]]},zt=t=>(...e)=>ut.asap((()=>t(...e)));var Jt=kt.hasStandardBrowserEnv?((t,e)=>n=>(n=new URL(n,kt.origin),t.protocol===n.protocol&&t.host===n.host&&(e||t.port===n.port)))(new URL(kt.origin),kt.navigator&&/(msie|trident)/i.test(kt.navigator.userAgent)):()=>!0,Kt=kt.hasStandardBrowserEnv?{write(t,e,n,r,o,s){const i=[t+"="+encodeURIComponent(e)];ut.isNumber(n)&&i.push("expires="+new Date(n).toGMTString()),ut.isString(r)&&i.push("path="+r),ut.isString(o)&&i.push("domain="+o),!0===s&&i.push("secure"),document.cookie=i.join("; ")},read(t){const e=document.cookie.match(new RegExp("(^|;\\s*)("+t+")=([^;]*)"));return e?decodeURIComponent(e[3]):null},remove(t){this.write(t,"",Date.now()-864e5)}}:{write(){},read:()=>null,remove(){}};function $t(t,e){return t&&!/^([a-z][a-z\d+\-.]*:)?\/\//i.test(e)?function(t,e){return e?t.replace(/\/?\/$/,"")+"/"+e.replace(/^\/+/,""):t}(t,e):e}const Qt=t=>t instanceof Ut?{...t}:t;function Gt(t,e){e=e||{};const n={};function r(t,e,n,r){return ut.isPlainObject(t)&&ut.isPlainObject(e)?ut.merge.call({caseless:r},t,e):ut.isPlainObject(e)?ut.merge({},e):ut.isArray(e)?e.slice():e}function o(t,e,n,o){return ut.isUndefined(e)?ut.isUndefined(t)?void 0:r(void 0,t,0,o):r(t,e,0,o)}function s(t,e){if(!ut.isUndefined(e))return r(void 0,e)}function i(t,e){return ut.isUndefined(e)?ut.isUndefined(t)?void 0:r(void 0,t):r(void 0,e)}function a(n,o,s){return s in e?r(n,o):s in t?r(void 0,n):void 0}const u={url:s,method:s,data:s,baseURL:i,transformRequest:i,transformResponse:i,paramsSerializer:i,timeout:i,timeoutMessage:i,withCredentials:i,withXSRFToken:i,adapter:i,responseType:i,xsrfCookieName:i,xsrfHeaderName:i,onUploadProgress:i,onDownloadProgress:i,decompress:i,maxContentLength:i,maxBodyLength:i,beforeRedirect:i,transport:i,httpAgent:i,httpsAgent:i,cancelToken:i,socketPath:i,responseEncoding:i,validateStatus:a,headers:(t,e,n)=>o(Qt(t),Qt(e),0,!0)};return ut.forEach(Object.keys(Object.assign({},t,e)),(function(r){const s=u[r]||o,i=s(t[r],e[r],r);ut.isUndefined(i)&&s!==a||(n[r]=i)})),n}var Xt=t=>{const e=Gt({},t);let n,{data:r,withXSRFToken:o,xsrfHeaderName:s,xsrfCookieName:i,headers:a,auth:u}=e;if(e.headers=a=Ut.from(a),e.url=Rt($t(e.baseURL,e.url),t.params,t.paramsSerializer),u&&a.set("Authorization","Basic "+btoa((u.username||"")+":"+(u.password?unescape(encodeURIComponent(u.password)):""))),ut.isFormData(r))if(kt.hasStandardBrowserEnv||kt.hasStandardBrowserWebWorkerEnv)a.setContentType(void 0);else if(!1!==(n=a.getContentType())){const[t,...e]=n?n.split(";").map((t=>t.trim())).filter(Boolean):[];a.setContentType([t||"multipart/form-data",...e].join("; "))}if(kt.hasStandardBrowserEnv&&(o&&ut.isFunction(o)&&(o=o(e)),o||!1!==o&&Jt(e.url))){const t=s&&i&&Kt.read(i);t&&a.set(s,t)}return e};var Yt="undefined"!=typeof XMLHttpRequest&&function(t){return new Promise((function(e,n){const r=Xt(t);let o=r.data;const s=Ut.from(r.headers).normalize();let i,a,u,c,p,{responseType:l,onUploadProgress:d,onDownloadProgress:h}=r;function f(){c&&c(),p&&p(),r.cancelToken&&r.cancelToken.unsubscribe(i),r.signal&&r.signal.removeEventListener("abort",i)}let y=new XMLHttpRequest;function m(){if(!y)return;const r=Ut.from("getAllResponseHeaders"in y&&y.getAllResponseHeaders());Wt((function(t){e(t),f()}),(function(t){n(t),f()}),{data:l&&"text"!==l&&"json"!==l?y.response:y.responseText,status:y.status,statusText:y.statusText,headers:r,config:t,request:y}),y=null}y.open(r.method.toUpperCase(),r.url,!0),y.timeout=r.timeout,"onloadend"in y?y.onloadend=m:y.onreadystatechange=function(){y&&4===y.readyState&&(0!==y.status||y.responseURL&&0===y.responseURL.indexOf("file:"))&&setTimeout(m)},y.onabort=function(){y&&(n(new ct("Request aborted",ct.ECONNABORTED,t,y)),y=null)},y.onerror=function(){n(new ct("Network Error",ct.ERR_NETWORK,t,y)),y=null},y.ontimeout=function(){let e=r.timeout?"timeout of "+r.timeout+"ms exceeded":"timeout exceeded";const o=r.transitional||St;r.timeoutErrorMessage&&(e=r.timeoutErrorMessage),n(new ct(e,o.clarifyTimeoutError?ct.ETIMEDOUT:ct.ECONNABORTED,t,y)),y=null},void 0===o&&s.setContentType(null),"setRequestHeader"in y&&ut.forEach(s.toJSON(),(function(t,e){y.setRequestHeader(e,t)})),ut.isUndefined(r.withCredentials)||(y.withCredentials=!!r.withCredentials),l&&"json"!==l&&(y.responseType=r.responseType),h&&([u,p]=Ht(h,!0),y.addEventListener("progress",u)),d&&y.upload&&([a,c]=Ht(d),y.upload.addEventListener("progress",a),y.upload.addEventListener("loadend",c)),(r.cancelToken||r.signal)&&(i=e=>{y&&(n(!e||e.type?new Mt(null,t,y):e),y.abort(),y=null)},r.cancelToken&&r.cancelToken.subscribe(i),r.signal&&(r.signal.aborted?i():r.signal.addEventListener("abort",i)));const b=function(t){const e=/^([-+\w]{1,25})(:?\/\/|:)/.exec(t);return e&&e[1]||""}(r.url);b&&-1===kt.protocols.indexOf(b)?n(new ct("Unsupported protocol "+b+":",ct.ERR_BAD_REQUEST,t)):y.send(o||null)}))};const Zt=(t,e)=>{const{length:n}=t=t?t.filter(Boolean):[];if(e||n){let n,r=new AbortController;const o=function(t){if(!n){n=!0,i();const e=t instanceof Error?t:this.reason;r.abort(e instanceof ct?e:new Mt(e instanceof Error?e.message:e))}};let s=e&&setTimeout((()=>{s=null,o(new ct(`timeout ${e} of ms exceeded`,ct.ETIMEDOUT))}),e);const i=()=>{t&&(s&&clearTimeout(s),s=null,t.forEach((t=>{t.unsubscribe?t.unsubscribe(o):t.removeEventListener("abort",o)})),t=null)};t.forEach((t=>t.addEventListener("abort",o)));const{signal:a}=r;return a.unsubscribe=()=>ut.asap(i),a}},te=function*(t,e){let n=t.byteLength;if(n<e)return void(yield t);let r,o=0;for(;o<n;)r=o+e,yield t.slice(o,r),o=r},ee=async function*(t){if(t[Symbol.asyncIterator])return void(yield*t);const e=t.getReader();try{for(;;){const{done:t,value:n}=await e.read();if(t)break;yield n}}finally{await e.cancel()}},ne=(t,e,n,r)=>{const o=async function*(t,e){for await(const n of ee(t))yield*te(n,e)}(t,e);let s,i=0,a=t=>{s||(s=!0,r&&r(t))};return new ReadableStream({async pull(t){try{const{done:e,value:r}=await o.next();if(e)return a(),void t.close();let s=r.byteLength;if(n){let t=i+=s;n(t)}t.enqueue(new Uint8Array(r))}catch(t){throw a(t),t}},cancel:t=>(a(t),o.return())},{highWaterMark:2})},re="function"==typeof fetch&&"function"==typeof Request&&"function"==typeof Response,oe=re&&"function"==typeof ReadableStream,se=re&&("function"==typeof TextEncoder?(ie=new TextEncoder,t=>ie.encode(t)):async t=>new Uint8Array(await new Response(t).arrayBuffer()));var ie;const ae=(t,...e)=>{try{return!!t(...e)}catch(t){return!1}},ue=oe&&ae((()=>{let t=!1;const e=new Request(kt.origin,{body:new ReadableStream,method:"POST",get duplex(){return t=!0,"half"}}).headers.has("Content-Type");return t&&!e})),ce=oe&&ae((()=>ut.isReadableStream(new Response("").body))),pe={stream:ce&&(t=>t.body)};var le;re&&(le=new Response,["text","arrayBuffer","blob","formData","stream"].forEach((t=>{!pe[t]&&(pe[t]=ut.isFunction(le[t])?e=>e[t]():(e,n)=>{throw new ct(`Response type '${t}' is not supported`,ct.ERR_NOT_SUPPORT,n)})})));const de=async(t,e)=>{const n=ut.toFiniteNumber(t.getContentLength());return null==n?(async t=>{if(null==t)return 0;if(ut.isBlob(t))return t.size;if(ut.isSpecCompliantForm(t)){const e=new Request(kt.origin,{method:"POST",body:t});return(await e.arrayBuffer()).byteLength}return ut.isArrayBufferView(t)||ut.isArrayBuffer(t)?t.byteLength:(ut.isURLSearchParams(t)&&(t+=""),ut.isString(t)?(await se(t)).byteLength:void 0)})(e):n};const he={http:null,xhr:Yt,fetch:re&&(async t=>{let{url:e,method:n,data:r,signal:o,cancelToken:s,timeout:i,onDownloadProgress:a,onUploadProgress:u,responseType:c,headers:p,withCredentials:l="same-origin",fetchOptions:d}=Xt(t);c=c?(c+"").toLowerCase():"text";let h,f=Zt([o,s&&s.toAbortSignal()],i);const y=f&&f.unsubscribe&&(()=>{f.unsubscribe()});let m;try{if(u&&ue&&"get"!==n&&"head"!==n&&0!==(m=await de(p,r))){let t,n=new Request(e,{method:"POST",body:r,duplex:"half"});if(ut.isFormData(r)&&(t=n.headers.get("content-type"))&&p.setContentType(t),n.body){const[t,e]=Vt(m,Ht(zt(u)));r=ne(n.body,65536,t,e)}}ut.isString(l)||(l=l?"include":"omit");const o="credentials"in Request.prototype;h=new Request(e,{...d,signal:f,method:n.toUpperCase(),headers:p.normalize().toJSON(),body:r,duplex:"half",credentials:o?l:void 0});let s=await fetch(h);const i=ce&&("stream"===c||"response"===c);if(ce&&(a||i&&y)){const t={};["status","statusText","headers"].forEach((e=>{t[e]=s[e]}));const e=ut.toFiniteNumber(s.headers.get("content-length")),[n,r]=a&&Vt(e,Ht(zt(a),!0))||[];s=new Response(ne(s.body,65536,n,(()=>{r&&r(),y&&y()})),t)}c=c||"text";let b=await pe[ut.findKey(pe,c)||"text"](s,t);return!i&&y&&y(),await new Promise(((e,n)=>{Wt(e,n,{data:b,headers:Ut.from(s.headers),status:s.status,statusText:s.statusText,config:t,request:h})}))}catch(e){if(y&&y(),e&&"TypeError"===e.name&&/fetch/i.test(e.message))throw Object.assign(new ct("Network Error",ct.ERR_NETWORK,t,h),{cause:e.cause||e});throw ct.from(e,e&&e.code,t,h)}})};ut.forEach(he,((t,e)=>{if(t){try{Object.defineProperty(t,"name",{value:e})}catch(t){}Object.defineProperty(t,"adapterName",{value:e})}}));const fe=t=>`- ${t}`,ye=t=>ut.isFunction(t)||null===t||!1===t;var me=t=>{t=ut.isArray(t)?t:[t];const{length:e}=t;let n,r;const o={};for(let s=0;s<e;s++){let e;if(n=t[s],r=n,!ye(n)&&(r=he[(e=String(n)).toLowerCase()],void 0===r))throw new ct(`Unknown adapter '${e}'`);if(r)break;o[e||"#"+s]=r}if(!r){const t=Object.entries(o).map((([t,e])=>`adapter ${t} `+(!1===e?"is not supported by the environment":"is not available in the build")));throw new ct("There is no suitable adapter to dispatch the request "+(e?t.length>1?"since :\n"+t.map(fe).join("\n"):" "+fe(t[0]):"as no adapter specified"),"ERR_NOT_SUPPORT")}return r};function be(t){if(t.cancelToken&&t.cancelToken.throwIfRequested(),t.signal&&t.signal.aborted)throw new Mt(null,t)}function ge(t){be(t),t.headers=Ut.from(t.headers),t.data=Nt.call(t,t.transformRequest),-1!==["post","put","patch"].indexOf(t.method)&&t.headers.setContentType("application/x-www-form-urlencoded",!1);return me(t.adapter||Ct.adapter)(t).then((function(e){return be(t),e.data=Nt.call(t,t.transformResponse,e),e.headers=Ut.from(e.headers),e}),(function(e){return It(e)||(be(t),e&&e.response&&(e.response.data=Nt.call(t,t.transformResponse,e.response),e.response.headers=Ut.from(e.response.headers))),Promise.reject(e)}))}const ve="1.7.9",we={};["object","boolean","number","function","string","symbol"].forEach(((t,e)=>{we[t]=function(n){return typeof n===t||"a"+(e<1?"n ":" ")+t}}));const Re={};we.transitional=function(t,e,n){function r(t,e){return"[Axios v1.7.9] Transitional option '"+t+"'"+e+(n?". "+n:"")}return(n,o,s)=>{if(!1===t)throw new ct(r(o," has been removed"+(e?" in "+e:"")),ct.ERR_DEPRECATED);return e&&!Re[o]&&(Re[o]=!0,console.warn(r(o," has been deprecated since v"+e+" and will be removed in the near future"))),!t||t(n,o,s)}},we.spelling=function(t){return(e,n)=>(console.warn(`${n} is likely a misspelling of ${t}`),!0)};var _e={assertOptions:function(t,e,n){if("object"!=typeof t)throw new ct("options must be an object",ct.ERR_BAD_OPTION_VALUE);const r=Object.keys(t);let o=r.length;for(;o-- >0;){const s=r[o],i=e[s];if(i){const e=t[s],n=void 0===e||i(e,s,t);if(!0!==n)throw new ct("option "+s+" must be "+n,ct.ERR_BAD_OPTION_VALUE)}else if(!0!==n)throw new ct("Unknown option "+s,ct.ERR_BAD_OPTION)}},validators:we};const Se=_e.validators;let Te=class{constructor(t){this.defaults=t,this.interceptors={request:new _t,response:new _t}}async request(t,e){try{return await this._request(t,e)}catch(t){if(t instanceof Error){let e={};Error.captureStackTrace?Error.captureStackTrace(e):e=new Error;const n=e.stack?e.stack.replace(/^.+\n/,""):"";try{t.stack?n&&!String(t.stack).endsWith(n.replace(/^.+\n.+\n/,""))&&(t.stack+="\n"+n):t.stack=n}catch(t){}}throw t}}_request(t,e){"string"==typeof t?(e=e||{}).url=t:e=t||{},e=Gt(this.defaults,e);const{transitional:n,paramsSerializer:r,headers:o}=e;void 0!==n&&_e.assertOptions(n,{silentJSONParsing:Se.transitional(Se.boolean),forcedJSONParsing:Se.transitional(Se.boolean),clarifyTimeoutError:Se.transitional(Se.boolean)},!1),null!=r&&(ut.isFunction(r)?e.paramsSerializer={serialize:r}:_e.assertOptions(r,{encode:Se.function,serialize:Se.function},!0)),_e.assertOptions(e,{baseUrl:Se.spelling("baseURL"),withXsrfToken:Se.spelling("withXSRFToken")},!0),e.method=(e.method||this.defaults.method||"get").toLowerCase();let s=o&&ut.merge(o.common,o[e.method]);o&&ut.forEach(["delete","get","head","post","put","patch","common"],(t=>{delete o[t]})),e.headers=Ut.concat(s,o);const i=[];let a=!0;this.interceptors.request.forEach((function(t){"function"==typeof t.runWhen&&!1===t.runWhen(e)||(a=a&&t.synchronous,i.unshift(t.fulfilled,t.rejected))}));const u=[];let c;this.interceptors.response.forEach((function(t){u.push(t.fulfilled,t.rejected)}));let p,l=0;if(!a){const t=[ge.bind(this),void 0];for(t.unshift.apply(t,i),t.push.apply(t,u),p=t.length,c=Promise.resolve(e);l<p;)c=c.then(t[l++],t[l++]);return c}p=i.length;let d=e;for(l=0;l<p;){const t=i[l++],e=i[l++];try{d=t(d)}catch(t){e.call(this,t);break}}try{c=ge.call(this,d)}catch(t){return Promise.reject(t)}for(l=0,p=u.length;l<p;)c=c.then(u[l++],u[l++]);return c}getUri(t){return Rt($t((t=Gt(this.defaults,t)).baseURL,t.url),t.params,t.paramsSerializer)}};ut.forEach(["delete","get","head","options"],(function(t){Te.prototype[t]=function(e,n){return this.request(Gt(n||{},{method:t,url:e,data:(n||{}).data}))}})),ut.forEach(["post","put","patch"],(function(t){function e(e){return function(n,r,o){return this.request(Gt(o||{},{method:t,headers:e?{"Content-Type":"multipart/form-data"}:{},url:n,data:r}))}}Te.prototype[t]=e(),Te.prototype[t+"Form"]=e(!0)}));const qe={Continue:100,SwitchingProtocols:101,Processing:102,EarlyHints:103,Ok:200,Created:201,Accepted:202,NonAuthoritativeInformation:203,NoContent:204,ResetContent:205,PartialContent:206,MultiStatus:207,AlreadyReported:208,ImUsed:226,MultipleChoices:300,MovedPermanently:301,Found:302,SeeOther:303,NotModified:304,UseProxy:305,Unused:306,TemporaryRedirect:307,PermanentRedirect:308,BadRequest:400,Unauthorized:401,PaymentRequired:402,Forbidden:403,NotFound:404,MethodNotAllowed:405,NotAcceptable:406,ProxyAuthenticationRequired:407,RequestTimeout:408,Conflict:409,Gone:410,LengthRequired:411,PreconditionFailed:412,PayloadTooLarge:413,UriTooLong:414,UnsupportedMediaType:415,RangeNotSatisfiable:416,ExpectationFailed:417,ImATeapot:418,MisdirectedRequest:421,UnprocessableEntity:422,Locked:423,FailedDependency:424,TooEarly:425,UpgradeRequired:426,PreconditionRequired:428,TooManyRequests:429,RequestHeaderFieldsTooLarge:431,UnavailableForLegalReasons:451,InternalServerError:500,NotImplemented:501,BadGateway:502,ServiceUnavailable:503,GatewayTimeout:504,HttpVersionNotSupported:505,VariantAlsoNegotiates:506,InsufficientStorage:507,LoopDetected:508,NotExtended:510,NetworkAuthenticationRequired:511};Object.entries(qe).forEach((([t,e])=>{qe[e]=t}));const Ee=function t(e){const n=new Te(e),r=g(Te.prototype.request,n);return ut.extend(r,Te.prototype,n,{allOwnKeys:!0}),ut.extend(r,n,null,{allOwnKeys:!0}),r.create=function(n){return t(Gt(e,n))},r}(Ct);Ee.Axios=Te,Ee.CanceledError=Mt,Ee.CancelToken=class t{constructor(t){if("function"!=typeof t)throw new TypeError("executor must be a function.");let e;this.promise=new Promise((function(t){e=t}));const n=this;this.promise.then((t=>{if(!n._listeners)return;let e=n._listeners.length;for(;e-- >0;)n._listeners[e](t);n._listeners=null})),this.promise.then=t=>{let e;const r=new Promise((t=>{n.subscribe(t),e=t})).then(t);return r.cancel=function(){n.unsubscribe(e)},r},t((function(t,r,o){n.reason||(n.reason=new Mt(t,r,o),e(n.reason))}))}throwIfRequested(){if(this.reason)throw this.reason}subscribe(t){this.reason?t(this.reason):this._listeners?this._listeners.push(t):this._listeners=[t]}unsubscribe(t){if(!this._listeners)return;const e=this._listeners.indexOf(t);-1!==e&&this._listeners.splice(e,1)}toAbortSignal(){const t=new AbortController,e=e=>{t.abort(e)};return this.subscribe(e),t.signal.unsubscribe=()=>this.unsubscribe(e),t.signal}static source(){let e;return{token:new t((function(t){e=t})),cancel:e}}},Ee.isCancel=It,Ee.VERSION=ve,Ee.toFormData=mt,Ee.AxiosError=ct,Ee.Cancel=Ee.CanceledError,Ee.all=function(t){return Promise.all(t)},Ee.spread=function(t){return function(e){return t.apply(null,e)}},Ee.isAxiosError=function(t){return ut.isObject(t)&&!0===t.isAxiosError},Ee.mergeConfig=Gt,Ee.AxiosHeaders=Ut,Ee.formToJSON=t=>xt(ut.isHTMLForm(t)?new FormData(t):t),Ee.getAdapter=me,Ee.HttpStatusCode=qe,Ee.default=Ee;const{Axios:De,AxiosError:Oe,CanceledError:Ae,isCancel:ke,CancelToken:xe,VERSION:Ce,all:Pe,Cancel:Fe,isAxiosError:Be,spread:Le,toFormData:je,AxiosHeaders:Ue,HttpStatusCode:Ne,formToJSON:Ie,getAdapter:Me,mergeConfig:We}=Ee;var He,Ve,ze,Je,Ke=function(t){function n(e){var n=e.status,r=e.statusText,o=e.message,s=e.body,i=void 0===s?{}:s,a=this,u="",c="";return"string"==typeof i?u=i:(u=(null==i?void 0:i.message)||"",c=(null==i?void 0:i.error)||""),(a=t.call(this)||this).stack="",a.status=n,a.message=o||c||r||"",a.details=u,a.type="MailgunAPIError",a}return e(n,t),n.getUserDataError=function(t,e){return new this({status:400,statusText:t,body:{message:e}})},n}(Error),$e=function(){function t(t,e){this._stream=t,this.size=e}return t.prototype.stream=function(){return this._stream},Object.defineProperty(t.prototype,Symbol.toStringTag,{get:function(){return"Blob"},enumerable:!1,configurable:!0}),t}(),Qe=function(){function t(){}return t.prototype.getAttachmentOptions=function(t){var e=t.filename,r=t.contentType,o=t.knownLength;return n(n(n({},e?{filename:e}:{filename:"file"}),r&&{contentType:r}),o&&{knownLength:o})},t.prototype.getFileInfo=function(t){var e=t.name,n=t.type,r=t.size;return this.getAttachmentOptions({filename:e,contentType:n,knownLength:r})},t.prototype.getCustomFileInfo=function(t){var e=t.filename,n=t.contentType,r=t.knownLength;return this.getAttachmentOptions({filename:e,contentType:n,knownLength:r})},t.prototype.getBufferInfo=function(t){var e=t.byteLength;return this.getAttachmentOptions({filename:"file",contentType:"",knownLength:e})},t.prototype.isStream=function(t){return"object"==typeof t&&"function"==typeof t.pipe},t.prototype.isCustomFile=function(t){return"object"==typeof t&&!!t.data},t.prototype.isBrowserFile=function(t){return"object"==typeof t&&(!!t.name||"undefined"!=typeof Blob&&t instanceof Blob)},t.prototype.isBuffer=function(t){return"undefined"!=typeof Buffer&&Buffer.isBuffer(t)},t.prototype.getAttachmentInfo=function(t){var e=this.isBrowserFile(t),n=this.isCustomFile(t);if(!("string"==typeof t)){if(e)return this.getFileInfo(t);if("undefined"!=typeof Buffer&&Buffer.isBuffer(t))return this.getBufferInfo(t);if(n)return this.getCustomFileInfo(t)}return{filename:"file",contentType:void 0,knownLength:void 0}},t.prototype.convertToFDexpectedShape=function(t){var e,n=this.isStream(t),r=this.isBrowserFile(t),o=this.isCustomFile(t);if(n||"string"==typeof t||r||this.isBuffer(t))e=t;else{if(!o)throw Ke.getUserDataError("Unknown attachment type ".concat(typeof t),'The "attachment" property expects either Buffer, Blob, or String.\n          Also, It is possible to provide an object that has the property "data" with a value that is equal to one of the types counted before.\n          Additionally, you may use an array to send more than one attachment.');e=t.data}return e},t.prototype.getBlobFromStream=function(t,e){return new $e(t,e)},t}(),Ge=function(){function t(t){this.FormDataConstructor=t,this.fileKeys=["attachment","inline","multipleValidationFile"],this.attachmentsHandler=new Qe}return t.prototype.createFormData=function(t){var e=this;if(!t)throw new Error("Please provide data object");return Object.keys(t).filter((function(e){return t[e]})).reduce((function(n,r){if(e.fileKeys.includes(r)){var o=t[r];if(e.isMessageAttachment(o))return e.addFilesToFD(r,o,n),n;throw Ke.getUserDataError("Unknown value ".concat(t[r]," with type ").concat(typeof t[r],' for property "').concat(r,'"'),'The key "'.concat(r,'" should have type of Buffer, Stream, File, or String '))}if("message"===r){var s=t[r];if(!s||!e.isMIME(s))throw Ke.getUserDataError('Unknown data type for "'.concat(r,'" property'),"The mime data should have type of Buffer, String or Blob");return e.addMimeDataToFD(r,s,n),n}return e.addCommonPropertyToFD(r,t[r],n),n}),new this.FormDataConstructor)},t.prototype.addMimeDataToFD=function(t,e,n){if("string"!=typeof e){if(this.isFormDataPackage(n))n.append(t,e,{filename:"MimeMessage"});else if(void 0!==typeof Blob){var r=n;if(e instanceof Blob)return void r.append(t,e,"MimeMessage");if(this.attachmentsHandler.isBuffer(e)){var o=new Blob([e]);r.append(t,o,"MimeMessage")}}}else n.append(t,e)},t.prototype.isMIME=function(t){return"string"==typeof t||"undefined"!=typeof Blob&&t instanceof Blob||this.attachmentsHandler.isBuffer(t)||"undefined"!=typeof ReadableStream&&t instanceof ReadableStream},t.prototype.isFormDataPackage=function(t){return"object"==typeof t&&null!==t&&"function"==typeof t.getHeaders},t.prototype.isMessageAttachment=function(t){var e=this;return this.attachmentsHandler.isCustomFile(t)||"string"==typeof t||"undefined"!=typeof File&&t instanceof File||"undefined"!=typeof Blob&&t instanceof Blob||this.attachmentsHandler.isBuffer(t)||this.attachmentsHandler.isStream(t)||Array.isArray(t)&&t.every((function(n){return e.attachmentsHandler.isCustomFile(n)||"undefined"!=typeof File&&n instanceof File||"undefined"!=typeof Blob&&t instanceof Blob||e.attachmentsHandler.isBuffer(n)||e.attachmentsHandler.isStream(n)}))},t.prototype.addFilesToFD=function(t,e,n){var r=this,o=function(t,e,o){var s="multipleValidationFile"===t?"file":t,i=r.attachmentsHandler.convertToFDexpectedShape(e),a=r.attachmentsHandler.getAttachmentInfo(e);if(r.isFormDataPackage(o)){var u=o,c="string"==typeof i?Buffer.from(i):i;u.append(s,c,a)}else if(void 0!==typeof Blob){var p=n;if("string"==typeof i||r.attachmentsHandler.isBuffer(i)){var l=new Blob([i]);return void p.append(s,l,a.filename)}if(i instanceof Blob)return void p.append(s,i,a.filename);if(r.attachmentsHandler.isStream(i)){var d=r.attachmentsHandler.getBlobFromStream(i,a.knownLength);p.set(s,d,a.filename)}}};Array.isArray(e)?e.forEach((function(e){o(t,e,n)})):o(t,e,n)},t.prototype.addCommonPropertyToFD=function(t,e,n){var r=this,o=function(t,e){if(r.isFormDataPackage(n))return"object"==typeof e?(console.warn('The received value is an object. \n"JSON.Stringify" will be used to avoid TypeError \nTo remove this warning: \nConsider switching to built-in FormData or converting the value on your own.\n'),n.append(t,JSON.stringify(e))):n.append(t,e);if("string"==typeof e)return n.append(t,e);if(void 0!==typeof Blob&&e instanceof Blob)return n.append(t,e);throw Ke.getUserDataError("Unknown value type for Form Data. String or Blob expected","Browser compliant FormData allows only string or Blob values for properties that are not attachments.")};Array.isArray(e)?e.forEach((function(e){o(t,e)})):null!=e&&o(t,e)},t}(),Xe=function(){function t(t){this.request=t}return t.prototype.list=function(t){return this.request.get("/v5/accounts/subaccounts",t).then((function(t){return t.body}))},t.prototype.get=function(t){return this.request.get("/v5/accounts/subaccounts/".concat(t)).then((function(t){return t.body}))},t.prototype.create=function(t){return this.request.postWithFD("/v5/accounts/subaccounts",{name:t}).then((function(t){return t.body}))},t.prototype.enable=function(t){return this.request.post("/v5/accounts/subaccounts/".concat(t,"/enable")).then((function(t){return t.body}))},t.prototype.disable=function(t){return this.request.post("/v5/accounts/subaccounts/".concat(t,"/disable")).then((function(t){return t.body}))},t.SUBACCOUNT_HEADER="X-Mailgun-On-Behalf-Of",t}(),Ye=function(){function t(t,e){this.username=t.username,this.key=t.key,this.url=t.url,this.timeout=t.timeout,this.headers=this.makeHeadersFromObject(t.headers),this.formDataBuilder=new Ge(e),this.maxBodyLength=********,this.proxy=null==t?void 0:t.proxy}return t.prototype.request=function(t,e,s){var i,a,u;return r(this,void 0,void 0,(function(){var r,c,p,l,d,h,f,y;return o(this,(function(o){switch(o.label){case 0:null==(r=n({},s))||delete r.headers,c=this.joinAndTransformHeaders(s),p=n({},r),(null==r?void 0:r.query)&&Object.getOwnPropertyNames(null==r?void 0:r.query).length>0&&(p.params=new URLSearchParams(r.query),delete p.query),(null==r?void 0:r.body)&&(l=null==r?void 0:r.body,p.data=l,delete p.body),h=b(this.url,e),o.label=1;case 1:return o.trys.push([1,3,,4]),[4,Ee.request(n(n({method:t.toLocaleUpperCase(),timeout:this.timeout,url:h,headers:c},p),{maxBodyLength:this.maxBodyLength,proxy:this.proxy}))];case 2:return d=o.sent(),[3,4];case 3:throw f=o.sent(),new Ke({status:(null===(i=null==(y=f)?void 0:y.response)||void 0===i?void 0:i.status)||400,statusText:(null===(a=null==y?void 0:y.response)||void 0===a?void 0:a.statusText)||y.code,body:(null===(u=null==y?void 0:y.response)||void 0===u?void 0:u.data)||y.message});case 4:return[4,this.getResponseBody(d)];case 5:return[2,o.sent()]}}))}))},t.prototype.getResponseBody=function(t){return r(this,void 0,void 0,(function(){var e;return o(this,(function(n){if(e={body:{},status:null==t?void 0:t.status},"string"==typeof t.data){if("Mailgun Magnificent API"===t.data)throw new Ke({status:400,statusText:"Incorrect url",body:t.data});e.body={message:t.data}}else e.body=t.data;return[2,e]}))}))},t.prototype.joinAndTransformHeaders=function(t){var e=new Ue,n=h.encode("".concat(this.username,":").concat(this.key));e.setAuthorization("Basic ".concat(n)),e.set(this.headers);var r=t&&t.headers,o=this.makeHeadersFromObject(r);return e.set(o),e},t.prototype.makeHeadersFromObject=function(t){void 0===t&&(t={});var e=new Ue;return e=Object.entries(t).reduce((function(t,e){var n=e[0],r=e[1];return t.set(n,r),t}),e)},t.prototype.setSubaccountHeader=function(t){var e,r=this.makeHeadersFromObject(n(n({},this.headers),((e={})[Xe.SUBACCOUNT_HEADER]=t,e)));this.headers.set(r)},t.prototype.resetSubaccountHeader=function(){this.headers.delete(Xe.SUBACCOUNT_HEADER)},t.prototype.query=function(t,e,r,o){return this.request(t,e,n({query:r},o))},t.prototype.command=function(t,e,r,o,s){void 0===s&&(s=!0);var i={};s&&(i={"Content-Type":"application/x-www-form-urlencoded"});var a=n(n(n({},i),{body:r}),o);return this.request(t,e,a)},t.prototype.get=function(t,e,n){return this.query("get",t,e,n)},t.prototype.post=function(t,e,n){return this.command("post",t,e,n)},t.prototype.postWithFD=function(t,e){var n=this.formDataBuilder.createFormData(e);return this.command("post",t,n,{headers:{"Content-Type":"multipart/form-data"}},!1)},t.prototype.putWithFD=function(t,e){var n=this.formDataBuilder.createFormData(e);return this.command("put",t,n,{headers:{"Content-Type":"multipart/form-data"}},!1)},t.prototype.patchWithFD=function(t,e){var n=this.formDataBuilder.createFormData(e);return this.command("patch",t,n,{headers:{"Content-Type":"multipart/form-data"}},!1)},t.prototype.put=function(t,e,n){return this.command("put",t,e,n)},t.prototype.delete=function(t,e){return this.command("delete",t,e)},t}(),Ze=function(t,e,n){this.name=t.name,this.require_tls=t.require_tls,this.skip_verification=t.skip_verification,this.state=t.state,this.wildcard=t.wildcard,this.spam_action=t.spam_action,this.created_at=new Date(t.created_at),this.smtp_password=t.smtp_password,this.smtp_login=t.smtp_login,this.type=t.type,this.receiving_dns_records=e||null,this.sending_dns_records=n||null,this.id=t.id,this.is_disabled=t.is_disabled,this.web_prefix=t.web_prefix,this.web_scheme=t.web_scheme,this.use_automatic_sender_security=t.use_automatic_sender_security;var r=["dkim_host","mailfrom_host"].reduce((function(e,n){return t[n]&&(e[n]=t[n]),e}),{});Object.assign(this,r)},tn=function(){function t(t,e,n,r,o,s){void 0===s&&(s=console),this.request=t,this.domainCredentials=e,this.domainTemplates=n,this.domainTags=r,this.logger=s,this.domainTracking=o}return t.prototype._handleBoolValues=function(t){var e=t,r=Object.keys(e).reduce((function(t,n){var r=n;if("boolean"==typeof e[r]){var o=e[r];t[r]="true"===o.toString()?"true":"false"}return t}),{});return n(n({},t),r)},t.prototype._parseMessage=function(t){return t.body},t.prototype.parseDomainList=function(t){return t.body&&t.body.items?t.body.items.map((function(t){return new Ze(t)})):[]},t.prototype._parseDomain=function(t){return new Ze(t.body.domain,t.body.receiving_dns_records,t.body.sending_dns_records)},t.prototype.list=function(t){var e=this;return this.request.get("/v4/domains",t).then((function(t){return e.parseDomainList(t)}))},t.prototype.get=function(t,e){var n,r,o=this,s=e?{"h:extended":null!==(n=null==e?void 0:e.extended)&&void 0!==n&&n,"h:with_dns":null===(r=null==e?void 0:e.with_dns)||void 0===r||r}:{};return this.request.get("/v4/domains/".concat(t),s).then((function(t){return o._parseDomain(t)}))},t.prototype.create=function(t){var e=this,n=this._handleBoolValues(t);return this.request.postWithFD("/v4/domains",n).then((function(t){return e._parseDomain(t)}))},t.prototype.update=function(t,e){var n=this,r=this._handleBoolValues(e);return this.request.putWithFD("/v4/domains/".concat(t),r).then((function(t){return n._parseDomain(t)}))},t.prototype.verify=function(t){var e=this;return this.request.put("/v4/domains/".concat(t,"/verify")).then((function(t){return e._parseDomain(t)}))},t.prototype.destroy=function(t){var e=this;return this.request.delete("/v3/domains/".concat(t)).then((function(t){return e._parseMessage(t)}))},t.prototype.getConnection=function(t){return this.request.get("/v3/domains/".concat(t,"/connection")).then((function(t){return t})).then((function(t){return t.body}))},t.prototype.updateConnection=function(t,e){return this.request.put("/v3/domains/".concat(t,"/connection"),e).then((function(t){return t})).then((function(t){return t.body}))},t.prototype.getTracking=function(t){return this.logger.warn("\n      'domains.getTracking' method is deprecated, and will be removed. Please use 'domains.domainTracking.getTracking' instead.\n    "),this.domainTracking.getTracking(t)},t.prototype.updateTracking=function(t,e,n){return this.logger.warn("\n      'domains.updateTracking' method is deprecated, and will be removed. Please use 'domains.domainTracking.updateTracking' instead.\n    "),this.domainTracking.updateTracking(t,e,n)},t.prototype.getIps=function(t){return this.logger.warn('"domains.getIps" method is deprecated and will be removed in the future releases.'),this.request.get(b("/v3/domains",t,"ips")).then((function(t){var e;return null===(e=null==t?void 0:t.body)||void 0===e?void 0:e.items}))},t.prototype.assignIp=function(t,e){return this.logger.warn('"domains.assignIp" method is deprecated and will be removed in the future releases.'),this.request.postWithFD(b("/v3/domains",t,"ips"),{ip:e})},t.prototype.deleteIp=function(t,e){return this.logger.warn('"domains.deleteIp" method is deprecated and will be moved into the IpsClient in the future releases.'),this.request.delete(b("/v3/domains",t,"ips",e))},t.prototype.linkIpPool=function(t,e){return this.logger.warn('"domains.linkIpPool" method is deprecated, and will be removed in the future releases.'),this.request.postWithFD(b("/v3/domains",t,"ips"),{pool_id:e})},t.prototype.unlinkIpPoll=function(t,e){this.logger.warn('"domains.unlinkIpPoll" method is deprecated, and will be moved into the IpsClient in the future releases.');var n="";if(e.pool_id&&e.ip)throw Ke.getUserDataError("Too much data for replacement","Please specify either pool_id or ip (not both)");return e.pool_id?n="?pool_id=".concat(e.pool_id):e.ip&&(n="?ip=".concat(e.ip)),this.request.delete(b("/v3/domains",t,"ips","ip_pool",n))},t.prototype.updateDKIMAuthority=function(t,e){return this.request.put("/v3/domains/".concat(t,"/dkim_authority"),{},{query:"self=".concat(e.self)}).then((function(t){return t})).then((function(t){return t.body}))},t.prototype.updateDKIMSelector=function(t,e){var n;return r(this,void 0,void 0,(function(){var r;return o(this,(function(o){switch(o.label){case 0:return[4,this.request.put("/v3/domains/".concat(t,"/dkim_selector"),{},{query:"dkim_selector=".concat(e.dkimSelector)})];case 1:return[2,{status:(r=o.sent()).status,message:null===(n=null==r?void 0:r.body)||void 0===n?void 0:n.message}]}}))}))},t.prototype.updateWebPrefix=function(t,e){return this.logger.warn('"domains.updateWebPrefix" method is deprecated, please use domains.update to set new "web_prefix". Current method will be removed in the future releases.'),this.request.put("/v3/domains/".concat(t,"/web_prefix"),{},{query:"web_prefix=".concat(e.webPrefix)}).then((function(t){return t}))},t}(),en=function(){function t(t){t&&(this.request=t)}return t.prototype.parsePage=function(t,e,n,r){var o=new URL(e).searchParams,s=e&&"string"==typeof e&&e.split(n).pop()||"",i=null;return r&&(i=o.has(r)?o.get(r):void 0),{id:t,page:"?"===n?"?".concat(s):s,iteratorPosition:i,url:e}},t.prototype.parsePageLinks=function(t,e,n){var r=this;return Object.entries(t.body.paging).reduce((function(t,o){var s=o[0],i=o[1];return t[s]=r.parsePage(s,i,e,n),t}),{})},t.prototype.updateUrlAndQuery=function(t,e){var r=t,o=n({},e);return o.page&&(r=b(t,o.page),delete o.page),{url:r,updatedQuery:o}},t.prototype.requestListWithPages=function(t,e,n){return r(this,void 0,void 0,(function(){var r,s,i,a;return o(this,(function(o){switch(o.label){case 0:return r=this.updateUrlAndQuery(t,e),s=r.url,i=r.updatedQuery,this.request?[4,this.request.get(s,i)]:[3,2];case 1:return a=o.sent(),[2,this.parseList(a,n)];case 2:throw new Ke({status:500,statusText:"Request property is empty",body:{message:""}})}}))}))},t}(),nn=function(t){function n(e){var n=t.call(this,e)||this;return n.request=e,n}return e(n,t),n.prototype.parseList=function(t){var e={};return e.items=t.body.items,e.pages=this.parsePageLinks(t,"/"),e.status=t.status,e},n.prototype.get=function(t,e){return r(this,void 0,void 0,(function(){return o(this,(function(n){return[2,this.requestListWithPages(b("/v3",t,"events"),e)]}))}))},n}(en),rn=function(t){this.start=new Date(t.start),this.end=new Date(t.end),this.resolution=t.resolution,this.stats=t.stats.map((function(t){var e=n({},t);return e.time=new Date(t.time),e}))},on=function(){function t(t,e){void 0===e&&(e=console),this.request=t,this.logger=e}return t.prototype.convertDateToUTC=function(t,e){return this.logger.warn('Date:"'.concat(e,'" was auto-converted to UTC time zone.\nValue "').concat(e.toUTCString(),'" will be used for request.\nConsider using string type for property "').concat(t,'" to avoid auto-converting')),[t,e.toUTCString()]},t.prototype.prepareSearchParams=function(t){var e=this,n=[];return"object"==typeof t&&Object.keys(t).length&&(n=Object.entries(t).reduce((function(t,n){var r=n[0],o=n[1];if(Array.isArray(o)&&o.length){var i=o.map((function(t){return[r,t]}));return s(s([],t,!0),i,!0)}return o instanceof Date?(t.push(e.convertDateToUTC(r,o)),t):("string"==typeof o&&t.push([r,o]),t)}),[])),n},t.prototype.parseStats=function(t){return new rn(t.body)},t.prototype.getDomain=function(t,e){var n=this.prepareSearchParams(e);return this.request.get(b("/v3",t,"stats/total"),n).then(this.parseStats)},t.prototype.getAccount=function(t){var e=this.prepareSearchParams(t);return this.request.get("/v3/stats/total",e).then(this.parseStats)},t}();!function(t){t.HOUR="hour",t.DAY="day",t.MONTH="month"}(He||(He={})),function(t){t.BOUNCES="bounces",t.COMPLAINTS="complaints",t.UNSUBSCRIBES="unsubscribes",t.WHITELISTS="whitelists"}(Ve||(Ve={})),function(t){t.CLICKED="clicked",t.COMPLAINED="complained",t.DELIVERED="delivered",t.OPENED="opened",t.PERMANENT_FAIL="permanent_fail",t.TEMPORARY_FAIL="temporary_fail",t.UNSUBSCRIBED="unsubscribe"}(ze||(ze={})),function(t){t.YES="yes",t.NO="no"}(Je||(Je={}));var sn=function(t){this.type=t},an=function(t){function n(e){var n=t.call(this,Ve.BOUNCES)||this;return n.address=e.address,n.code=+e.code,n.error=e.error,n.created_at=new Date(e.created_at),n}return e(n,t),n}(sn),un=function(t){function n(e){var n=t.call(this,Ve.COMPLAINTS)||this;return n.address=e.address,n.created_at=new Date(e.created_at),n}return e(n,t),n}(sn),cn=function(t){function n(e){var n=t.call(this,Ve.UNSUBSCRIBES)||this;return n.address=e.address,n.tags=e.tags,n.created_at=new Date(e.created_at),n}return e(n,t),n}(sn),pn=function(t){function n(e){var n=t.call(this,Ve.WHITELISTS)||this;return n.value=e.value,n.reason=e.reason,n.createdAt=new Date(e.createdAt),n}return e(n,t),n}(sn),ln={headers:{"Content-Type":"application/json"}},dn=function(t){function n(e){var n=t.call(this,e)||this;return n.request=e,n.models={bounces:an,complaints:un,unsubscribes:cn,whitelists:pn},n}return e(n,t),n.prototype.parseList=function(t,e){var n,r={};return r.items=(null===(n=t.body.items)||void 0===n?void 0:n.map((function(t){return new e(t)})))||[],r.pages=this.parsePageLinks(t,"?","address"),r.status=t.status,r},n.prototype._parseItem=function(t,e){return new e(t)},n.prototype.createWhiteList=function(t,e,n){if(n)throw Ke.getUserDataError("Data property should be an object","Whitelist's creation process does not support multiple creations. Data property should be an object");return this.request.postWithFD(b("v3",t,"whitelists"),e).then(this.prepareResponse)},n.prototype.createUnsubscribe=function(t,e){if(Array.isArray(e)){if(e.some((function(t){return t.tag})))throw Ke.getUserDataError("Tag property should not be used for creating multiple unsubscribes.","Tag property can be used only if one unsubscribe provided as second argument of create method. Please use tags instead.");return this.request.post(b("v3",t,"unsubscribes"),JSON.stringify(e),ln).then(this.prepareResponse)}if(null==e?void 0:e.tags)throw Ke.getUserDataError("Tags property should not be used for creating one unsubscribe.","Tags property can be used if you provides an array of unsubscribes as second argument of create method. Please use tag instead");if(Array.isArray(e.tag))throw Ke.getUserDataError("Tag property can not be an array","Please use array of unsubscribes as second argument of create method to be able to provide few tags");return this.request.postWithFD(b("v3",t,"unsubscribes"),e).then(this.prepareResponse)},n.prototype.getModel=function(t){if(t in this.models)return this.models[t];throw Ke.getUserDataError("Unknown type value","Type may be only one of [bounces, complaints, unsubscribes, whitelists]")},n.prototype.prepareResponse=function(t){return{message:t.body.message,type:t.body.type||"",value:t.body.value||"",status:t.status}},n.prototype.list=function(t,e,n){return r(this,void 0,void 0,(function(){var r;return o(this,(function(o){return r=this.getModel(e),[2,this.requestListWithPages(b("v3",t,e),n,r)]}))}))},n.prototype.get=function(t,e,n){var r=this,o=this.getModel(e);return this.request.get(b("v3",t,e,encodeURIComponent(n))).then((function(t){return r._parseItem(t.body,o)}))},n.prototype.create=function(t,e,n){var r;this.getModel(e);var o=Array.isArray(n);return"whitelists"===e?this.createWhiteList(t,n,o):"unsubscribes"===e?this.createUnsubscribe(t,n):(r=o?s([],n,!0):[n],this.request.post(b("v3",t,e),JSON.stringify(r),ln).then(this.prepareResponse))},n.prototype.destroy=function(t,e,n){return this.getModel(e),this.request.delete(b("v3",t,e,encodeURIComponent(n))).then((function(t){return{message:t.body.message,value:t.body.value||"",address:t.body.address||"",status:t.status}}))},n}(en),hn=function(t,e,n){this.id=t,this.url=e,this.urls=n},fn=function(){function t(t){this.request=t}return t.prototype._parseWebhookList=function(t){return t.body.webhooks},t.prototype._parseWebhookWithID=function(t){return function(e){var n,r=null===(n=null==e?void 0:e.body)||void 0===n?void 0:n.webhook,o=null==r?void 0:r.url,s=null==r?void 0:r.urls;return o||(o=s&&s.length?s[0]:void 0),s&&0!==s.length||!o||(s=[o]),new hn(t,o,s)}},t.prototype._parseWebhookTest=function(t){return{code:t.body.code,message:t.body.message}},t.prototype.list=function(t,e){return this.request.get(b("/v3/domains",t,"webhooks"),e).then(this._parseWebhookList)},t.prototype.get=function(t,e){return this.request.get(b("/v3/domains",t,"webhooks",e)).then(this._parseWebhookWithID(e))},t.prototype.create=function(t,e,n,r){return void 0===r&&(r=!1),r?this.request.putWithFD(b("/v3/domains",t,"webhooks",e,"test"),{url:n}).then(this._parseWebhookTest):this.request.postWithFD(b("/v3/domains",t,"webhooks"),{id:e,url:n}).then(this._parseWebhookWithID(e))},t.prototype.update=function(t,e,n){return this.request.putWithFD(b("/v3/domains",t,"webhooks",e),{url:n}).then(this._parseWebhookWithID(e))},t.prototype.destroy=function(t,e){return this.request.delete(b("/v3/domains",t,"webhooks",e)).then(this._parseWebhookWithID(e))},t}(),yn=function(){function t(t){this.request=t}return t.prototype.prepareBooleanValues=function(t){var e=new Set(["o:testmode","t:text","o:dkim","o:tracking","o:tracking-clicks","o:tracking-opens","o:require-tls","o:skip-verification"]);if(!t||0===Object.keys(t).length)throw Ke.getUserDataError("Message data object can not be empty","Message data object can not be empty");return Object.keys(t).reduce((function(n,r){return e.has(r)&&"boolean"==typeof t[r]?n[r]=t[r]?"yes":"no":n[r]=t[r],n}),{})},t.prototype._parseResponse=function(t){return n({status:t.status},t.body)},t.prototype.create=function(t,e){if(e.message)return this.request.postWithFD("/v3/".concat(t,"/messages.mime"),e).then(this._parseResponse);var n=this.prepareBooleanValues(e);return this.request.postWithFD("/v3/".concat(t,"/messages"),n).then(this._parseResponse)},t}(),mn=function(){function t(t){this.request=t}return t.prototype.list=function(t){return this.request.get("/v3/routes",t).then((function(t){return t.body.items}))},t.prototype.get=function(t){return this.request.get("/v3/routes/".concat(t)).then((function(t){return t.body.route}))},t.prototype.create=function(t){return this.request.postWithFD("/v3/routes",t).then((function(t){return t.body.route}))},t.prototype.update=function(t,e){return this.request.putWithFD("/v3/routes/".concat(t),e).then((function(t){return t.body}))},t.prototype.destroy=function(t){return this.request.delete("/v3/routes/".concat(t)).then((function(t){return t.body}))},t}(),bn=function(){function t(t,e){this.request=t,this.multipleValidation=e}return t.prototype.get=function(t){return r(this,void 0,void 0,(function(){var e;return o(this,(function(n){switch(n.label){case 0:return e={address:t},[4,this.request.get("/v4/address/validate",e)];case 1:return[2,n.sent().body]}}))}))},t}(),gn=function(){function t(t){this.request=t}return t.prototype.list=function(t){return r(this,void 0,void 0,(function(){var e;return o(this,(function(n){switch(n.label){case 0:return[4,this.request.get("/v3/ips",t)];case 1:return e=n.sent(),[2,this.parseIpsResponse(e)]}}))}))},t.prototype.get=function(t){return r(this,void 0,void 0,(function(){var e;return o(this,(function(n){switch(n.label){case 0:return[4,this.request.get("/v3/ips/".concat(t))];case 1:return e=n.sent(),[2,this.parseIpsResponse(e)]}}))}))},t.prototype.parseIpsResponse=function(t){return t.body},t}(),vn=function(){function t(t){this.request=t}return t.prototype.list=function(){var t=this;return this.request.get("/v1/ip_pools").then((function(e){return t.parseIpPoolsResponse(e)}))},t.prototype.create=function(t){return r(this,void 0,void 0,(function(){var e;return o(this,(function(r){switch(r.label){case 0:return[4,this.request.postWithFD("/v1/ip_pools",t)];case 1:return e=r.sent(),[2,n({status:e.status},e.body)]}}))}))},t.prototype.update=function(t,e){return r(this,void 0,void 0,(function(){var r;return o(this,(function(o){switch(o.label){case 0:return[4,this.request.patchWithFD("/v1/ip_pools/".concat(t),e)];case 1:return r=o.sent(),[2,n({status:r.status},r.body)]}}))}))},t.prototype.delete=function(t,e){return r(this,void 0,void 0,(function(){var r;return o(this,(function(o){switch(o.label){case 0:return[4,this.request.delete("/v1/ip_pools/".concat(t),e)];case 1:return r=o.sent(),[2,n({status:r.status},r.body)]}}))}))},t.prototype.parseIpPoolsResponse=function(t){return n({status:t.status},t.body)},t}(),wn=function(t){function s(e,n){var r=t.call(this,e)||this;return r.request=e,r.baseRoute="/v3/lists",r.members=n,r}return e(s,t),s.prototype.parseValidationResult=function(t,e){return{status:t,validationResult:n(n({},e),{created_at:new Date(1e3*e.created_at)})}},s.prototype.parseList=function(t){var e={};return e.items=t.body.items,e.pages=this.parsePageLinks(t,"?","address"),e.status=t.status,e},s.prototype.list=function(t){return r(this,void 0,void 0,(function(){return o(this,(function(e){return[2,this.requestListWithPages("".concat(this.baseRoute,"/pages"),t)]}))}))},s.prototype.get=function(t){return this.request.get("".concat(this.baseRoute,"/").concat(t)).then((function(t){return t.body.list}))},s.prototype.create=function(t){return this.request.postWithFD(this.baseRoute,t).then((function(t){return t.body.list}))},s.prototype.update=function(t,e){return this.request.putWithFD("".concat(this.baseRoute,"/").concat(t),e).then((function(t){return t.body.list}))},s.prototype.destroy=function(t){return this.request.delete("".concat(this.baseRoute,"/").concat(t)).then((function(t){return t.body}))},s.prototype.validate=function(t){return this.request.post("".concat(this.baseRoute,"/").concat(t,"/validate"),{}).then((function(t){return n({status:t.status},t.body)}))},s.prototype.validationResult=function(t){var e=this;return this.request.get("".concat(this.baseRoute,"/").concat(t,"/validate")).then((function(t){return e.parseValidationResult(t.status,t.body)}))},s.prototype.cancelValidation=function(t){return this.request.delete("".concat(this.baseRoute,"/").concat(t,"/validate")).then((function(t){return{status:t.status,message:t.body.message}}))},s}(en),Rn=function(t){function s(e){var n=t.call(this,e)||this;return n.request=e,n.baseRoute="/v3/lists",n}return e(s,t),s.prototype.checkAndUpdateData=function(t){var e=n({},t);return"object"==typeof t.vars&&(e.vars=JSON.stringify(e.vars)),"boolean"==typeof t.subscribed&&(e.subscribed=t.subscribed?"yes":"no"),e},s.prototype.parseList=function(t){var e={};return e.items=t.body.items,e.pages=this.parsePageLinks(t,"?","address"),e},s.prototype.listMembers=function(t,e){return r(this,void 0,void 0,(function(){return o(this,(function(n){return[2,this.requestListWithPages("".concat(this.baseRoute,"/").concat(t,"/members/pages"),e)]}))}))},s.prototype.getMember=function(t,e){return this.request.get("".concat(this.baseRoute,"/").concat(t,"/members/").concat(e)).then((function(t){return t.body.member}))},s.prototype.createMember=function(t,e){var n=this.checkAndUpdateData(e);return this.request.postWithFD("".concat(this.baseRoute,"/").concat(t,"/members"),n).then((function(t){return t.body.member}))},s.prototype.createMembers=function(t,e){var n={members:Array.isArray(e.members)?JSON.stringify(e.members):e.members,upsert:e.upsert};return this.request.postWithFD("".concat(this.baseRoute,"/").concat(t,"/members.json"),n).then((function(t){return t.body}))},s.prototype.updateMember=function(t,e,n){var r=this.checkAndUpdateData(n);return this.request.putWithFD("".concat(this.baseRoute,"/").concat(t,"/members/").concat(e),r).then((function(t){return t.body.member}))},s.prototype.destroyMember=function(t,e){return this.request.delete("".concat(this.baseRoute,"/").concat(t,"/members/").concat(e)).then((function(t){return t.body}))},s}(en),_n=function(){function t(t){this.request=t,this.baseRoute="/v3/domains/"}return t.prototype._parseDomainCredentialsList=function(t){return{items:t.body.items,totalCount:t.body.total_count}},t.prototype._parseMessageResponse=function(t){return{status:t.status,message:t.body.message}},t.prototype._parseDeletedResponse=function(t){return{status:t.status,message:t.body.message,spec:t.body.spec}},t.prototype.list=function(t,e){var n=this;return this.request.get(b(this.baseRoute,t,"/credentials"),e).then((function(t){return n._parseDomainCredentialsList(t)}))},t.prototype.create=function(t,e){var n=this;return this.request.postWithFD("".concat(this.baseRoute).concat(t,"/credentials"),e).then((function(t){return n._parseMessageResponse(t)}))},t.prototype.update=function(t,e,n){var r=this;return this.request.putWithFD("".concat(this.baseRoute).concat(t,"/credentials/").concat(e),n).then((function(t){return r._parseMessageResponse(t)}))},t.prototype.destroy=function(t,e){var n=this;return this.request.delete("".concat(this.baseRoute).concat(t,"/credentials/").concat(e)).then((function(t){return n._parseDeletedResponse(t)}))},t}(),Sn=function(t,e){var n,r;this.createdAt=new Date(t.created_at),this.id=t.id,this.quantity=t.quantity,this.recordsProcessed=t.records_processed,this.status=t.status,this.responseStatusCode=e,t.download_url&&(this.downloadUrl={csv:null===(n=t.download_url)||void 0===n?void 0:n.csv,json:null===(r=t.download_url)||void 0===r?void 0:r.json}),t.summary&&(this.summary={result:{catchAll:t.summary.result.catch_all,deliverable:t.summary.result.deliverable,doNotSend:t.summary.result.do_not_send,undeliverable:t.summary.result.undeliverable,unknown:t.summary.result.unknown},risk:{high:t.summary.risk.high,low:t.summary.risk.low,medium:t.summary.risk.medium,unknown:t.summary.risk.unknown}})},Tn=function(t){function s(e){var n=t.call(this)||this;return n.request=e,n.attachmentsHandler=new Qe,n}return e(s,t),s.prototype.handleResponse=function(t){return n({status:t.status},null==t?void 0:t.body)},s.prototype.parseList=function(t){var e={};return e.jobs=t.body.jobs.map((function(e){return new Sn(e,t.status)})),e.pages=this.parsePageLinks(t,"?","pivot"),e.total=t.body.total,e.status=t.status,e},s.prototype.list=function(t){return r(this,void 0,void 0,(function(){return o(this,(function(e){return[2,this.requestListWithPages("/v4/address/validate/bulk",t)]}))}))},s.prototype.get=function(t){return r(this,void 0,void 0,(function(){var e;return o(this,(function(n){switch(n.label){case 0:return[4,this.request.get("/v4/address/validate/bulk/".concat(t))];case 1:return e=n.sent(),[2,new Sn(e.body,e.status)]}}))}))},s.prototype.convertToExpectedShape=function(t){return this.attachmentsHandler.isBuffer(t.file)?{multipleValidationFile:t.file}:"string"==typeof t.file?{multipleValidationFile:{data:t.file}}:(this.attachmentsHandler.isStream(t.file),{multipleValidationFile:t.file})},s.prototype.create=function(t,e){return r(this,void 0,void 0,(function(){var n,r;return o(this,(function(o){switch(o.label){case 0:if(!e||!e.file)throw Ke.getUserDataError('"file" property expected.','Make sure second argument has "file" property.');return n=this.convertToExpectedShape(e),[4,this.request.postWithFD("/v4/address/validate/bulk/".concat(t),n)];case 1:return r=o.sent(),[2,this.handleResponse(r)]}}))}))},s.prototype.destroy=function(t){return r(this,void 0,void 0,(function(){var e;return o(this,(function(n){switch(n.label){case 0:return[4,this.request.delete("/v4/address/validate/bulk/".concat(t))];case 1:return e=n.sent(),[2,this.handleResponse(e)]}}))}))},s}(en),qn=function(t){this.name=t.name,this.description=t.description,this.createdAt=t.createdAt?new Date(t.createdAt):"",this.createdBy=t.createdBy,this.id=t.id,t.version&&(this.version=t.version,this.version&&t.version.createdAt&&(this.version.createdAt=new Date(t.version.createdAt))),t.versions&&t.versions.length&&(this.versions=t.versions.map((function(t){var e=n({},t);return e.createdAt=new Date(t.createdAt),e})))},En=function(t){function n(e){var n=t.call(this,e)||this;return n.request=e,n.baseRoute="/v3/",n}return e(n,t),n.prototype.parseCreationResponse=function(t){return new qn(t.body.template)},n.prototype.parseCreationVersionResponse=function(t){var e={};return e.status=t.status,e.message=t.body.message,t.body&&t.body.template&&(e.template=new qn(t.body.template)),e},n.prototype.parseMutationResponse=function(t){var e={};return e.status=t.status,e.message=t.body.message,t.body&&t.body.template&&(e.templateName=t.body.template.name),e},n.prototype.parseNotificationResponse=function(t){var e={};return e.status=t.status,e.message=t.body.message,e},n.prototype.parseMutateTemplateVersionResponse=function(t){var e={};return e.status=t.status,e.message=t.body.message,t.body.template&&(e.templateName=t.body.template.name,e.templateVersion={tag:t.body.template.version.tag}),e},n.prototype.parseList=function(t){var e={};return e.items=t.body.items.map((function(t){return new qn(t)})),e.pages=this.parsePageLinks(t,"?","p"),e.status=t.status,e},n.prototype.parseListTemplateVersions=function(t){var e={};return e.template=new qn(t.body.template),e.pages=this.parsePageLinks(t,"?","p"),e},n.prototype.list=function(t,e){return r(this,void 0,void 0,(function(){return o(this,(function(n){return[2,this.requestListWithPages(b(this.baseRoute,t,"/templates"),e)]}))}))},n.prototype.get=function(t,e,n){return this.request.get(b(this.baseRoute,t,"/templates/",e),n).then((function(t){return new qn(t.body.template)}))},n.prototype.create=function(t,e){var n=this;return this.request.postWithFD(b(this.baseRoute,t,"/templates"),e).then((function(t){return n.parseCreationResponse(t)}))},n.prototype.update=function(t,e,n){var r=this;return this.request.putWithFD(b(this.baseRoute,t,"/templates/",e),n).then((function(t){return r.parseMutationResponse(t)}))},n.prototype.destroy=function(t,e){var n=this;return this.request.delete(b(this.baseRoute,t,"/templates/",e)).then((function(t){return n.parseMutationResponse(t)}))},n.prototype.destroyAll=function(t){var e=this;return this.request.delete(b(this.baseRoute,t,"/templates")).then((function(t){return e.parseNotificationResponse(t)}))},n.prototype.listVersions=function(t,e,n){var r=this;return this.request.get(b(this.baseRoute,t,"/templates",e,"/versions"),n).then((function(t){return r.parseListTemplateVersions(t)}))},n.prototype.getVersion=function(t,e,n){return this.request.get(b(this.baseRoute,t,"/templates/",e,"/versions/",n)).then((function(t){return new qn(t.body.template)}))},n.prototype.createVersion=function(t,e,n){var r=this;return this.request.postWithFD(b(this.baseRoute,t,"/templates/",e,"/versions"),n).then((function(t){return r.parseCreationVersionResponse(t)}))},n.prototype.updateVersion=function(t,e,n,r){var o=this;return this.request.putWithFD(b(this.baseRoute,t,"/templates/",e,"/versions/",n),r).then((function(t){return o.parseMutateTemplateVersionResponse(t)}))},n.prototype.destroyVersion=function(t,e,n){var r=this;return this.request.delete(b(this.baseRoute,t,"/templates/",e,"/versions/",n)).then((function(t){return r.parseMutateTemplateVersionResponse(t)}))},n}(en),Dn=function(t){this.tag=t.tag,this.description=t.description,this["first-seen"]=new Date(t["first-seen"]),this["last-seen"]=new Date(t["last-seen"])},On=function(t){this.tag=t.body.tag,this.description=t.body.description,this.start=new Date(t.body.start),this.end=new Date(t.body.end),this.resolution=t.body.resolution,this.stats=t.body.stats.map((function(t){return n(n({},t),{time:new Date(t.time)})}))},An=function(t){function n(e){var n=t.call(this,e)||this;return n.request=e,n.baseRoute="/v3/",n}return e(n,t),n.prototype.parseList=function(t){var e={};return e.items=t.body.items.map((function(t){return new Dn(t)})),e.pages=this.parsePageLinks(t,"?","tag"),e.status=t.status,e},n.prototype._parseTagStatistic=function(t){return new On(t)},n.prototype.list=function(t,e){return r(this,void 0,void 0,(function(){return o(this,(function(n){return[2,this.requestListWithPages(b(this.baseRoute,t,"/tags"),e)]}))}))},n.prototype.get=function(t,e){return this.request.get(b(this.baseRoute,t,"/tags",e)).then((function(t){return new Dn(t.body)}))},n.prototype.update=function(t,e,n){return this.request.put(b(this.baseRoute,t,"/tags",e),n).then((function(t){return t.body}))},n.prototype.destroy=function(t,e){return this.request.delete("".concat(this.baseRoute).concat(t,"/tags/").concat(e)).then((function(t){return{message:t.body.message,status:t.status}}))},n.prototype.statistic=function(t,e,n){var r=this;return this.request.get(b(this.baseRoute,t,"/tags",e,"stats"),n).then((function(t){return r._parseTagStatistic(t)}))},n.prototype.countries=function(t,e){return this.request.get(b(this.baseRoute,t,"/tags",e,"stats/aggregates/countries")).then((function(t){return t.body}))},n.prototype.providers=function(t,e){return this.request.get(b(this.baseRoute,t,"/tags",e,"stats/aggregates/providers")).then((function(t){return t.body}))},n.prototype.devices=function(t,e){return this.request.get(b(this.baseRoute,t,"/tags",e,"stats/aggregates/devices")).then((function(t){return t.body}))},n}(en),kn=function(t){function s(e,n,r,o){void 0===o&&(o=console);var s=t.call(this,e)||this;return s.request=e,s.attributes=n,s.filters=r,s.logger=o,s}return e(s,t),s.prototype.convertDateToUTC=function(t,e){return this.logger.warn('Date: "'.concat(e,'" was auto-converted to UTC time zone.\nValue "').concat(e.toISOString(),'" will be used for request.\nConsider using string type for property "').concat(t,'" to avoid auto-converting')),e.toISOString()},s.prototype.prepareQueryData=function(t){var e=this,r=t,o=Object.keys(r).reduce((function(n,o){var s=o;if(r[s]&&"object"==typeof r[s]){var i=t[s];n[s]=e.convertDateToUTC(s,i)}return n}),{});return n(n({},t),o)},s.prototype.prepareResult=function(t){var e=this.prepareSeedList(t.body);return n(n({},e),{status:t.status})},s.prototype.prepareSeedList=function(t){var e,r={created_at:new Date(t.created_at),updated_at:new Date(t.updated_at),last_result_at:new Date(t.last_result_at)};e=t.Seeds?t.Seeds.map((function(t){var e={created_at:new Date(t.created_at),updated_at:new Date(t.updated_at),max_email_count_hit_at:new Date(t.max_email_count_hit_at),last_sent_to_at:new Date(t.last_sent_to_at),last_delivered_at:new Date(t.last_delivered_at)};return n(n({},t),e)})):null;var o=n(n(n({},t),{Seeds:e}),r);return delete o.Id,o},s.prototype.parseList=function(t){var e,n=this,r={items:[]};return r.items=null===(e=t.body.items)||void 0===e?void 0:e.map((function(t){return n.prepareSeedList(t)})),r.pages=this.parsePageLinks(t,"?","address"),r.status=t.status,r},s.prototype.list=function(t){return r(this,void 0,void 0,(function(){var e,r;return o(this,(function(o){switch(o.label){case 0:return e=this.prepareQueryData(t),[4,this.request.get("/v4/inbox/seedlists",e)];case 1:return r=o.sent(),[2,n(n({},this.parseList(r)),{status:200})]}}))}))},s.prototype.get=function(t){return r(this,void 0,void 0,(function(){var e,r;return o(this,(function(o){switch(o.label){case 0:return[4,this.request.get("/v4/inbox/seedlists/".concat(t))];case 1:return e=o.sent(),r=this.prepareSeedList(e.body.seedlist),[2,n(n({},r),{status:e.status})]}}))}))},s.prototype.create=function(t){return r(this,void 0,void 0,(function(){var e;return o(this,(function(n){switch(n.label){case 0:return[4,this.request.postWithFD("/v4/inbox/seedlists",t)];case 1:return e=n.sent(),[2,this.prepareResult(e)]}}))}))},s.prototype.update=function(t,e){return r(this,void 0,void 0,(function(){var n;return o(this,(function(r){switch(r.label){case 0:return[4,this.request.put("/v4/inbox/seedlists/".concat(t),e)];case 1:return n=r.sent(),[2,this.prepareResult(n)]}}))}))},s.prototype.destroy=function(t){return r(this,void 0,void 0,(function(){return o(this,(function(e){return[2,this.request.delete("/v4/inbox/seedlists/".concat(t))]}))}))},s}(en),xn=function(){function t(t,e,n,r){this.request=t,this.seedsLists=e,this.seedsLists=e,this.results=n,this.providers=r}return t.prototype.runTest=function(t){return r(this,void 0,void 0,(function(){var e;return o(this,(function(r){switch(r.label){case 0:return[4,this.request.post("/v4/inbox/tests",t)];case 1:return e=r.sent(),[2,n(n({},e.body),{status:e.status})]}}))}))},t}(),Cn=function(t){function s(e,n,r,o,s){void 0===s&&(s=console);var i=t.call(this,e)||this;return i.request=e,i.attributes=n,i.filters=r,i.sharing=o,i.logger=s,i}return e(s,t),s.prototype.convertDateToUTC=function(t,e){return this.logger.warn('Date: "'.concat(e,'" was auto-converted to UTC time zone.\nValue "').concat(e.toISOString(),'" will be used for request.\nConsider using string type for property "').concat(t,'" to avoid auto-converting')),e.toISOString()},s.prototype.prepareQueryData=function(t){var e=this,r=t,o=Object.keys(r).reduce((function(n,o){var s=o;if(r[s]&&"object"==typeof r[s]){var i=t[s];n[s]=e.convertDateToUTC(s,i)}return n}),{});return n(n({},t),o)},s.prototype.prepareInboxPlacementsResult=function(t){var e={},r={created_at:new Date(t.created_at),updated_at:new Date(t.updated_at),sharing_expires_at:new Date(t.sharing_expires_at)};t.Box&&delete(e=n(n({},t.Box),{created_at:new Date(t.Box.created_at),updated_at:new Date(t.Box.updated_at),last_result_at:new Date(t.Box.last_result_at)})).ID;var o=n(n(n(n({},t),{Box:e}),r),{id:t.Id});return delete o.ID,o},s.prototype.parseList=function(t){var e=this,n={};return n.items=t.body.items.map((function(t){return e.prepareInboxPlacementsResult(t)})),n.pages=this.parsePageLinks(t,"?","address"),n.status=t.status,n},s.prototype.list=function(t){return r(this,void 0,void 0,(function(){var e,n;return o(this,(function(r){switch(r.label){case 0:return e=this.prepareQueryData(t),[4,this.request.get("/v4/inbox/results",e)];case 1:return n=r.sent(),[2,this.parseList(n)]}}))}))},s.prototype.get=function(t){return r(this,void 0,void 0,(function(){var e,n;return o(this,(function(r){switch(r.label){case 0:return[4,this.request.get("/v4/inbox/results/".concat(t))];case 1:return e=r.sent(),n=this.prepareInboxPlacementsResult(e.body.result),[2,{status:e.status,inboxPlacementResult:n}]}}))}))},s.prototype.destroy=function(t){return r(this,void 0,void 0,(function(){var e;return o(this,(function(r){switch(r.label){case 0:return[4,this.request.delete("/v4/inbox/results/".concat(t))];case 1:return e=r.sent(),[2,n({status:e.status},e.body)]}}))}))},s.prototype.getResultByShareId=function(t){return r(this,void 0,void 0,(function(){var e,n;return o(this,(function(r){switch(r.label){case 0:return[4,this.request.get("/v4/inbox/sharing/public/".concat(t))];case 1:return e=r.sent(),n=this.prepareInboxPlacementsResult(e.body.result),[2,{status:e.status,inboxPlacementResult:n}]}}))}))},s}(en),Pn=function(){function t(t,e){this.path=e,this.request=t}return t.prototype.list=function(){return r(this,void 0,void 0,(function(){var t;return o(this,(function(e){switch(e.label){case 0:return[4,this.request.get(this.path)];case 1:return[2,{items:(t=e.sent()).body.items,status:t.status}]}}))}))},t.prototype.get=function(t){return r(this,void 0,void 0,(function(){var e;return o(this,(function(r){switch(r.label){case 0:return[4,this.request.get("".concat(this.path,"/").concat(t))];case 1:return e=r.sent(),[2,n(n({},e.body),{status:e.status})]}}))}))},t}(),Fn=function(){function t(t,e){this.request=t,this.path=e}return t.prototype.list=function(){return r(this,void 0,void 0,(function(){var t;return o(this,(function(e){switch(e.label){case 0:return[4,this.request.get(this.path)];case 1:return[2,{status:(t=e.sent()).status,supported_filters:t.body.supported_filters}]}}))}))},t}(),Bn=function(){function t(t){this.request=t}return t.prototype.prepareInboxPlacementsResultSharing=function(t){var e={expires_at:new Date(t.expires_at)};return n(n({},t),e)},t.prototype.get=function(t){return r(this,void 0,void 0,(function(){var e,r;return o(this,(function(o){switch(o.label){case 0:return[4,this.request.get("/v4/inbox/sharing/".concat(t))];case 1:return e=o.sent(),r=this.prepareInboxPlacementsResultSharing(e.body.sharing),[2,n({status:e.status},r)]}}))}))},t.prototype.update=function(t,e){return r(this,void 0,void 0,(function(){var r,s;return o(this,(function(o){switch(o.label){case 0:return[4,this.request.put("/v4/inbox/sharing/".concat(t),{},{query:"enabled=".concat(e.enabled)})];case 1:return r=o.sent(),s=this.prepareInboxPlacementsResultSharing(r.body.sharing),[2,n(n({},s),{status:r.status})]}}))}))},t}(),Ln=function(){function t(t){this.path="/v4/inbox/providers",this.request=t}return t.prototype.parseList=function(t){var e={};return e.items=t.body.items.map((function(t){var e={created_at:new Date(t.created_at),updated_at:new Date(t.updated_at)};return n(n({},t),e)})),e.status=t.status,e},t.prototype.list=function(){return r(this,void 0,void 0,(function(){var t;return o(this,(function(e){switch(e.label){case 0:return[4,this.request.get(this.path)];case 1:return t=e.sent(),[2,this.parseList(t)]}}))}))},t}(),jn=function(){function t(t,e){void 0===e&&(e=console),this.request=t,this.logger=e}return t.prototype.convertDateToUTC=function(t,e){return this.logger.warn('Date:"'.concat(e,'" was auto-converted to UTC time zone.\nValue "').concat(e.toUTCString(),'" will be used for request.\nConsider using string type for property "').concat(t,'" to avoid auto-converting')),e.toUTCString()},t.prototype.prepareQuery=function(t){var e,r;if(t){var o=null==t?void 0:t.start,s=null==t?void 0:t.end;e=o instanceof Date?this.convertDateToUTC("start",o):null!=o?o:"",r=s&&s instanceof Date?this.convertDateToUTC("end",s):null!=s?s:""}return n(n({},t),{start:e,end:r})},t.prototype.handleResponse=function(t){var e=t.body,r=Date.parse(e.start)?new Date(e.start):null,o=Date.parse(e.end)?new Date(e.end):null;return n(n({},e),{status:t.status,start:r,end:o})},t.prototype.getAccount=function(t){return r(this,void 0,void 0,(function(){var e,n;return o(this,(function(r){switch(r.label){case 0:return e=this.prepareQuery(t),[4,this.request.post("/v1/analytics/metrics",e)];case 1:return n=r.sent(),[2,this.handleResponse(n)]}}))}))},t.prototype.getAccountUsage=function(t){return r(this,void 0,void 0,(function(){var e,n;return o(this,(function(r){switch(r.label){case 0:return e=this.prepareQuery(t),[4,this.request.post("/v1/analytics/usage/metrics",e)];case 1:return n=r.sent(),[2,this.handleResponse(n)]}}))}))},t}(),Un=function(){function t(t){this.request=t}return t.prototype._parseTrackingSettings=function(t){return t.body.tracking},t.prototype._parseTrackingUpdate=function(t){return t.body},t.prototype._isOpenTrackingInfoWitPlace=function(t){return"object"==typeof t&&"place_at_the_top"in t},t.prototype.get=function(t){return r(this,void 0,void 0,(function(){var e;return o(this,(function(r){switch(r.label){case 0:return[4,this.request.get("/v2/x509/".concat(t,"/status"))];case 1:return e=r.sent(),[2,n(n({},e.body),{responseStatusCode:e.status})]}}))}))},t.prototype.generate=function(t){return r(this,void 0,void 0,(function(){var e;return o(this,(function(r){switch(r.label){case 0:return[4,this.request.post("/v2/x509/".concat(t))];case 1:return e=r.sent(),[2,n(n({},e.body),{status:e.status})]}}))}))},t.prototype.regenerate=function(t){return r(this,void 0,void 0,(function(){var e;return o(this,(function(r){switch(r.label){case 0:return[4,this.request.put("/v2/x509/".concat(t))];case 1:return e=r.sent(),[2,n(n({},e.body),{status:e.status})]}}))}))},t.prototype.getTracking=function(t){return r(this,void 0,void 0,(function(){var e;return o(this,(function(n){switch(n.label){case 0:return[4,this.request.get(b("/v3/domains",t,"tracking"))];case 1:return e=n.sent(),[2,this._parseTrackingSettings(e)]}}))}))},t.prototype.updateTracking=function(t,e,s){return r(this,void 0,void 0,(function(){var r,i;return o(this,(function(o){switch(o.label){case 0:return r=n({},s),"boolean"==typeof(null==s?void 0:s.active)&&(r.active=(null==s?void 0:s.active)?"yes":"no"),this._isOpenTrackingInfoWitPlace(s)&&"boolean"==typeof(null==s?void 0:s.place_at_the_top)&&(r.place_at_the_top=(null==s?void 0:s.place_at_the_top)?"yes":"no"),[4,this.request.putWithFD(b("/v3/domains",t,"tracking",e),r)];case 1:return i=o.sent(),[2,this._parseTrackingUpdate(i)]}}))}))},t}(),Nn=function(){function t(t,e){var r=n({},t);if(r.url||(r.url="https://api.mailgun.net"),!r.username)throw new Error('Parameter "username" is required');if(!r.key)throw new Error('Parameter "key" is required');this.request=new Ye(r,e);var o=new Rn(this.request),s=new _n(this.request),i=new En(this.request),a=new An(this.request),u=new Un(this.request),c=new Tn(this.request),p=new Bn(this.request),l=new Pn(this.request,"/v4/inbox/seedlists/a"),d=new Pn(this.request,"/v4/inbox/results/a"),h=new Fn(this.request,"/v4/inbox/seedlists/_filters"),f=new Fn(this.request,"/v4/inbox/results/_filters"),y=new kn(this.request,l,h),m=new Cn(this.request,d,f,p),b=new Ln(this.request);this.domains=new tn(this.request,s,i,a,u),this.webhooks=new fn(this.request),this.events=new nn(this.request),this.stats=new on(this.request),this.metrics=new jn(this.request),this.suppressions=new dn(this.request),this.messages=new yn(this.request),this.routes=new mn(this.request),this.ips=new gn(this.request),this.ip_pools=new vn(this.request),this.lists=new wn(this.request,o),this.validate=new bn(this.request,c),this.subaccounts=new Xe(this.request),this.inboxPlacements=new xn(this.request,y,m,b)}return t.prototype.setSubaccount=function(t){var e;null===(e=this.request)||void 0===e||e.setSubaccountHeader(t)},t.prototype.resetSubaccount=function(){var t;null===(t=this.request)||void 0===t||t.resetSubaccountHeader()},t}();return function(){function t(t){this.formData=t}return Object.defineProperty(t,"default",{get:function(){return this},enumerable:!1,configurable:!0}),t.prototype.client=function(t){return new Nn(t,this.formData)},t}()}));
