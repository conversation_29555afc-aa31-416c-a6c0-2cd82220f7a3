"use strict";var e=require("util"),n=require("stream"),t=require("path"),a=require("http"),i=require("https"),o=require("url"),s=require("fs"),r=require("assert"),c=require("tty"),p=require("os"),u=require("zlib"),l=require("events"),d=function(e,n){return d=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,n){e.__proto__=n}||function(e,n){for(var t in n)Object.prototype.hasOwnProperty.call(n,t)&&(e[t]=n[t])},d(e,n)};function m(e,n){if("function"!=typeof n&&null!==n)throw new TypeError("Class extends value "+String(n)+" is not a constructor or null");function t(){this.constructor=e}d(e,n),e.prototype=null===n?Object.create(n):(t.prototype=n.prototype,new t)}var f=function(){return f=Object.assign||function(e){for(var n,t=1,a=arguments.length;t<a;t++)for(var i in n=arguments[t])Object.prototype.hasOwnProperty.call(n,i)&&(e[i]=n[i]);return e},f.apply(this,arguments)};function h(e,n,t,a){return new(t||(t=Promise))((function(i,o){function s(e){try{c(a.next(e))}catch(e){o(e)}}function r(e){try{c(a.throw(e))}catch(e){o(e)}}function c(e){var n;e.done?i(e.value):(n=e.value,n instanceof t?n:new t((function(e){e(n)}))).then(s,r)}c((a=a.apply(e,n||[])).next())}))}function v(e,n){var t,a,i,o={label:0,sent:function(){if(1&i[0])throw i[1];return i[1]},trys:[],ops:[]},s=Object.create(("function"==typeof Iterator?Iterator:Object).prototype);return s.next=r(0),s.throw=r(1),s.return=r(2),"function"==typeof Symbol&&(s[Symbol.iterator]=function(){return this}),s;function r(r){return function(c){return function(r){if(t)throw new TypeError("Generator is already executing.");for(;s&&(s=0,r[0]&&(o=0)),o;)try{if(t=1,a&&(i=2&r[0]?a.return:r[0]?a.throw||((i=a.return)&&i.call(a),0):a.next)&&!(i=i.call(a,r[1])).done)return i;switch(a=0,i&&(r=[2&r[0],i.value]),r[0]){case 0:case 1:i=r;break;case 4:return o.label++,{value:r[1],done:!1};case 5:o.label++,a=r[1],r=[0];continue;case 7:r=o.ops.pop(),o.trys.pop();continue;default:if(!(i=o.trys,(i=i.length>0&&i[i.length-1])||6!==r[0]&&2!==r[0])){o=0;continue}if(3===r[0]&&(!i||r[1]>i[0]&&r[1]<i[3])){o.label=r[1];break}if(6===r[0]&&o.label<i[1]){o.label=i[1],i=r;break}if(i&&o.label<i[2]){o.label=i[2],o.ops.push(r);break}i[2]&&o.ops.pop(),o.trys.pop();continue}r=n.call(e,o)}catch(e){r=[6,e],a=0}finally{t=i=0}if(5&r[0])throw r[1];return{value:r[0]?r[1]:void 0,done:!0}}([r,c])}}}function x(e,n,t){if(t||2===arguments.length)for(var a,i=0,o=n.length;i<o;i++)!a&&i in n||(a||(a=Array.prototype.slice.call(n,0,i)),a[i]=n[i]);return e.concat(a||Array.prototype.slice.call(n))}"function"==typeof SuppressedError&&SuppressedError;var b="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{};function g(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var y,w={exports:{}},k=w.exports;
/*! https://mths.be/base64 v1.0.0 by @mathias | MIT license */var _,j=(y||(y=1,function(e,n){!function(t){var a=n,i=e&&e.exports==a&&e,o="object"==typeof b&&b;o.global!==o&&o.window!==o||(t=o);var s=function(e){this.message=e};(s.prototype=new Error).name="InvalidCharacterError";var r=function(e){throw new s(e)},c="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",p=/[\t\n\f\r ]/g,u={encode:function(e){e=String(e),/[^\0-\xFF]/.test(e)&&r("The string to be encoded contains characters outside of the Latin1 range.");for(var n,t,a,i,o=e.length%3,s="",p=-1,u=e.length-o;++p<u;)n=e.charCodeAt(p)<<16,t=e.charCodeAt(++p)<<8,a=e.charCodeAt(++p),s+=c.charAt((i=n+t+a)>>18&63)+c.charAt(i>>12&63)+c.charAt(i>>6&63)+c.charAt(63&i);return 2==o?(n=e.charCodeAt(p)<<8,t=e.charCodeAt(++p),s+=c.charAt((i=n+t)>>10)+c.charAt(i>>4&63)+c.charAt(i<<2&63)+"="):1==o&&(i=e.charCodeAt(p),s+=c.charAt(i>>2)+c.charAt(i<<4&63)+"=="),s},decode:function(e){var n=(e=String(e).replace(p,"")).length;n%4==0&&(n=(e=e.replace(/==?$/,"")).length),(n%4==1||/[^+a-zA-Z0-9/]/.test(e))&&r("Invalid character: the string to be decoded is not correctly encoded.");for(var t,a,i=0,o="",s=-1;++s<n;)a=c.indexOf(e.charAt(s)),t=i%4?64*t+a:a,i++%4&&(o+=String.fromCharCode(255&t>>(-2*i&6)));return o},version:"1.0.0"};if(a&&!a.nodeType)if(i)i.exports=u;else for(var l in u)u.hasOwnProperty(l)&&(a[l]=u[l]);else t.base64=u}(k)}(w,w.exports)),w.exports),R={exports:{}},S=R.exports;var E=g((_||(_=1,function(e){var n,t;n=S,t=function(){return function(){return function(e){var n=[];if(0===e.length)return"";if("string"!=typeof e[0])throw new TypeError("Url must be a string. Received "+e[0]);if(e[0].match(/^[^/:]+:\/*$/)&&e.length>1){var t=e.shift();e[0]=t+e[0]}e[0].match(/^file:\/\/\//)?e[0]=e[0].replace(/^([^/:]+):\/*/,"$1:///"):e[0]=e[0].replace(/^([^/:]+):\/*/,"$1://");for(var a=0;a<e.length;a++){var i=e[a];if("string"!=typeof i)throw new TypeError("Url must be a string. Received "+i);""!==i&&(a>0&&(i=i.replace(/^[\/]+/,"")),i=a<e.length-1?i.replace(/[\/]+$/,""):i.replace(/[\/]+$/,"/"),n.push(i))}var o=n.join("/"),s=(o=o.replace(/\/(\?|&|#[^!])/g,"$1")).split("?");return s.shift()+(s.length>0?"?":"")+s.join("&")}("object"==typeof arguments[0]?arguments[0]:[].slice.call(arguments))}},e.exports?e.exports=t():n.urljoin=t()}(R)),R.exports));function T(e,n){return function(){return e.apply(n,arguments)}}const{toString:C}=Object.prototype,{getPrototypeOf:O}=Object,q=(A=Object.create(null),e=>{const n=C.call(e);return A[n]||(A[n]=n.slice(8,-1).toLowerCase())});var A;const F=e=>(e=e.toLowerCase(),n=>q(n)===e),D=e=>n=>typeof n===e,{isArray:P}=Array,B=D("undefined");const L=F("ArrayBuffer");const U=D("string"),z=D("function"),I=D("number"),N=e=>null!==e&&"object"==typeof e,M=e=>{if("object"!==q(e))return!1;const n=O(e);return!(null!==n&&n!==Object.prototype&&null!==Object.getPrototypeOf(n)||Symbol.toStringTag in e||Symbol.iterator in e)},W=F("Date"),H=F("File"),V=F("Blob"),$=F("FileList"),G=F("URLSearchParams"),[J,K,Q,Y]=["ReadableStream","Request","Response","Headers"].map(F);function X(e,n,{allOwnKeys:t=!1}={}){if(null==e)return;let a,i;if("object"!=typeof e&&(e=[e]),P(e))for(a=0,i=e.length;a<i;a++)n.call(null,e[a],a,e);else{const i=t?Object.getOwnPropertyNames(e):Object.keys(e),o=i.length;let s;for(a=0;a<o;a++)s=i[a],n.call(null,e[s],s,e)}}function Z(e,n){n=n.toLowerCase();const t=Object.keys(e);let a,i=t.length;for(;i-- >0;)if(a=t[i],n===a.toLowerCase())return a;return null}const ee="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:"undefined"!=typeof window?window:global,ne=e=>!B(e)&&e!==ee;const te=(ae="undefined"!=typeof Uint8Array&&O(Uint8Array),e=>ae&&e instanceof ae);var ae;const ie=F("HTMLFormElement"),oe=(({hasOwnProperty:e})=>(n,t)=>e.call(n,t))(Object.prototype),se=F("RegExp"),re=(e,n)=>{const t=Object.getOwnPropertyDescriptors(e),a={};X(t,((t,i)=>{let o;!1!==(o=n(t,i,e))&&(a[i]=o||t)})),Object.defineProperties(e,a)},ce="abcdefghijklmnopqrstuvwxyz",pe="0123456789",ue={DIGIT:pe,ALPHA:ce,ALPHA_DIGIT:ce+ce.toUpperCase()+pe};const le=F("AsyncFunction"),de=(me="function"==typeof setImmediate,fe=z(ee.postMessage),me?setImmediate:fe?(he=`axios@${Math.random()}`,ve=[],ee.addEventListener("message",(({source:e,data:n})=>{e===ee&&n===he&&ve.length&&ve.shift()()}),!1),e=>{ve.push(e),ee.postMessage(he,"*")}):e=>setTimeout(e));var me,fe,he,ve;const xe="undefined"!=typeof queueMicrotask?queueMicrotask.bind(ee):"undefined"!=typeof process&&process.nextTick||de;var be={isArray:P,isArrayBuffer:L,isBuffer:function(e){return null!==e&&!B(e)&&null!==e.constructor&&!B(e.constructor)&&z(e.constructor.isBuffer)&&e.constructor.isBuffer(e)},isFormData:e=>{let n;return e&&("function"==typeof FormData&&e instanceof FormData||z(e.append)&&("formdata"===(n=q(e))||"object"===n&&z(e.toString)&&"[object FormData]"===e.toString()))},isArrayBufferView:function(e){let n;return n="undefined"!=typeof ArrayBuffer&&ArrayBuffer.isView?ArrayBuffer.isView(e):e&&e.buffer&&L(e.buffer),n},isString:U,isNumber:I,isBoolean:e=>!0===e||!1===e,isObject:N,isPlainObject:M,isReadableStream:J,isRequest:K,isResponse:Q,isHeaders:Y,isUndefined:B,isDate:W,isFile:H,isBlob:V,isRegExp:se,isFunction:z,isStream:e=>N(e)&&z(e.pipe),isURLSearchParams:G,isTypedArray:te,isFileList:$,forEach:X,merge:function e(){const{caseless:n}=ne(this)&&this||{},t={},a=(a,i)=>{const o=n&&Z(t,i)||i;M(t[o])&&M(a)?t[o]=e(t[o],a):M(a)?t[o]=e({},a):P(a)?t[o]=a.slice():t[o]=a};for(let e=0,n=arguments.length;e<n;e++)arguments[e]&&X(arguments[e],a);return t},extend:(e,n,t,{allOwnKeys:a}={})=>(X(n,((n,a)=>{t&&z(n)?e[a]=T(n,t):e[a]=n}),{allOwnKeys:a}),e),trim:e=>e.trim?e.trim():e.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,""),stripBOM:e=>(65279===e.charCodeAt(0)&&(e=e.slice(1)),e),inherits:(e,n,t,a)=>{e.prototype=Object.create(n.prototype,a),e.prototype.constructor=e,Object.defineProperty(e,"super",{value:n.prototype}),t&&Object.assign(e.prototype,t)},toFlatObject:(e,n,t,a)=>{let i,o,s;const r={};if(n=n||{},null==e)return n;do{for(i=Object.getOwnPropertyNames(e),o=i.length;o-- >0;)s=i[o],a&&!a(s,e,n)||r[s]||(n[s]=e[s],r[s]=!0);e=!1!==t&&O(e)}while(e&&(!t||t(e,n))&&e!==Object.prototype);return n},kindOf:q,kindOfTest:F,endsWith:(e,n,t)=>{e=String(e),(void 0===t||t>e.length)&&(t=e.length),t-=n.length;const a=e.indexOf(n,t);return-1!==a&&a===t},toArray:e=>{if(!e)return null;if(P(e))return e;let n=e.length;if(!I(n))return null;const t=new Array(n);for(;n-- >0;)t[n]=e[n];return t},forEachEntry:(e,n)=>{const t=(e&&e[Symbol.iterator]).call(e);let a;for(;(a=t.next())&&!a.done;){const t=a.value;n.call(e,t[0],t[1])}},matchAll:(e,n)=>{let t;const a=[];for(;null!==(t=e.exec(n));)a.push(t);return a},isHTMLForm:ie,hasOwnProperty:oe,hasOwnProp:oe,reduceDescriptors:re,freezeMethods:e=>{re(e,((n,t)=>{if(z(e)&&-1!==["arguments","caller","callee"].indexOf(t))return!1;const a=e[t];z(a)&&(n.enumerable=!1,"writable"in n?n.writable=!1:n.set||(n.set=()=>{throw Error("Can not rewrite read-only method '"+t+"'")}))}))},toObjectSet:(e,n)=>{const t={},a=e=>{e.forEach((e=>{t[e]=!0}))};return P(e)?a(e):a(String(e).split(n)),t},toCamelCase:e=>e.toLowerCase().replace(/[-_\s]([a-z\d])(\w*)/g,(function(e,n,t){return n.toUpperCase()+t})),noop:()=>{},toFiniteNumber:(e,n)=>null!=e&&Number.isFinite(e=+e)?e:n,findKey:Z,global:ee,isContextDefined:ne,ALPHABET:ue,generateString:(e=16,n=ue.ALPHA_DIGIT)=>{let t="";const{length:a}=n;for(;e--;)t+=n[Math.random()*a|0];return t},isSpecCompliantForm:function(e){return!!(e&&z(e.append)&&"FormData"===e[Symbol.toStringTag]&&e[Symbol.iterator])},toJSONObject:e=>{const n=new Array(10),t=(e,a)=>{if(N(e)){if(n.indexOf(e)>=0)return;if(!("toJSON"in e)){n[a]=e;const i=P(e)?[]:{};return X(e,((e,n)=>{const o=t(e,a+1);!B(o)&&(i[n]=o)})),n[a]=void 0,i}}return e};return t(e,0)},isAsyncFn:le,isThenable:e=>e&&(N(e)||z(e))&&z(e.then)&&z(e.catch),setImmediate:de,asap:xe};function ge(e,n,t,a,i){Error.call(this),Error.captureStackTrace?Error.captureStackTrace(this,this.constructor):this.stack=(new Error).stack,this.message=e,this.name="AxiosError",n&&(this.code=n),t&&(this.config=t),a&&(this.request=a),i&&(this.response=i,this.status=i.status?i.status:null)}be.inherits(ge,Error,{toJSON:function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:be.toJSONObject(this.config),code:this.code,status:this.status}}});const ye=ge.prototype,we={};var ke,_e,je,Re;function Se(){if(Re)return je;Re=1;var t=e,a=n.Stream,i=function(){if(_e)return ke;_e=1;var t=n.Stream;function a(){this.source=null,this.dataSize=0,this.maxDataSize=1048576,this.pauseStream=!0,this._maxDataSizeExceeded=!1,this._released=!1,this._bufferedEvents=[]}return ke=a,e.inherits(a,t),a.create=function(e,n){var t=new this;for(var a in n=n||{})t[a]=n[a];t.source=e;var i=e.emit;return e.emit=function(){return t._handleEmit(arguments),i.apply(e,arguments)},e.on("error",(function(){})),t.pauseStream&&e.pause(),t},Object.defineProperty(a.prototype,"readable",{configurable:!0,enumerable:!0,get:function(){return this.source.readable}}),a.prototype.setEncoding=function(){return this.source.setEncoding.apply(this.source,arguments)},a.prototype.resume=function(){this._released||this.release(),this.source.resume()},a.prototype.pause=function(){this.source.pause()},a.prototype.release=function(){this._released=!0,this._bufferedEvents.forEach(function(e){this.emit.apply(this,e)}.bind(this)),this._bufferedEvents=[]},a.prototype.pipe=function(){var e=t.prototype.pipe.apply(this,arguments);return this.resume(),e},a.prototype._handleEmit=function(e){this._released?this.emit.apply(this,e):("data"===e[0]&&(this.dataSize+=e[1].length,this._checkIfMaxDataSizeExceeded()),this._bufferedEvents.push(e))},a.prototype._checkIfMaxDataSizeExceeded=function(){if(!(this._maxDataSizeExceeded||this.dataSize<=this.maxDataSize)){this._maxDataSizeExceeded=!0;var e="DelayedStream#maxDataSize of "+this.maxDataSize+" bytes exceeded.";this.emit("error",new Error(e))}},ke}();function o(){this.writable=!1,this.readable=!0,this.dataSize=0,this.maxDataSize=2097152,this.pauseStreams=!0,this._released=!1,this._streams=[],this._currentStream=null,this._insideLoop=!1,this._pendingNext=!1}return je=o,t.inherits(o,a),o.create=function(e){var n=new this;for(var t in e=e||{})n[t]=e[t];return n},o.isStreamLike=function(e){return"function"!=typeof e&&"string"!=typeof e&&"boolean"!=typeof e&&"number"!=typeof e&&!Buffer.isBuffer(e)},o.prototype.append=function(e){if(o.isStreamLike(e)){if(!(e instanceof i)){var n=i.create(e,{maxDataSize:1/0,pauseStream:this.pauseStreams});e.on("data",this._checkDataSize.bind(this)),e=n}this._handleErrors(e),this.pauseStreams&&e.pause()}return this._streams.push(e),this},o.prototype.pipe=function(e,n){return a.prototype.pipe.call(this,e,n),this.resume(),e},o.prototype._getNext=function(){if(this._currentStream=null,this._insideLoop)this._pendingNext=!0;else{this._insideLoop=!0;try{do{this._pendingNext=!1,this._realGetNext()}while(this._pendingNext)}finally{this._insideLoop=!1}}},o.prototype._realGetNext=function(){var e=this._streams.shift();void 0!==e?"function"==typeof e?e(function(e){o.isStreamLike(e)&&(e.on("data",this._checkDataSize.bind(this)),this._handleErrors(e)),this._pipeNext(e)}.bind(this)):this._pipeNext(e):this.end()},o.prototype._pipeNext=function(e){if(this._currentStream=e,o.isStreamLike(e))return e.on("end",this._getNext.bind(this)),void e.pipe(this,{end:!1});var n=e;this.write(n),this._getNext()},o.prototype._handleErrors=function(e){var n=this;e.on("error",(function(e){n._emitError(e)}))},o.prototype.write=function(e){this.emit("data",e)},o.prototype.pause=function(){this.pauseStreams&&(this.pauseStreams&&this._currentStream&&"function"==typeof this._currentStream.pause&&this._currentStream.pause(),this.emit("pause"))},o.prototype.resume=function(){this._released||(this._released=!0,this.writable=!0,this._getNext()),this.pauseStreams&&this._currentStream&&"function"==typeof this._currentStream.resume&&this._currentStream.resume(),this.emit("resume")},o.prototype.end=function(){this._reset(),this.emit("end")},o.prototype.destroy=function(){this._reset(),this.emit("close")},o.prototype._reset=function(){this.writable=!1,this._streams=[],this._currentStream=null},o.prototype._checkDataSize=function(){if(this._updateDataSize(),!(this.dataSize<=this.maxDataSize)){var e="DelayedStream#maxDataSize of "+this.maxDataSize+" bytes exceeded.";this._emitError(new Error(e))}},o.prototype._updateDataSize=function(){this.dataSize=0;var e=this;this._streams.forEach((function(n){n.dataSize&&(e.dataSize+=n.dataSize)})),this._currentStream&&this._currentStream.dataSize&&(this.dataSize+=this._currentStream.dataSize)},o.prototype._emitError=function(e){this._reset(),this.emit("error",e)},je}["ERR_BAD_OPTION_VALUE","ERR_BAD_OPTION","ECONNABORTED","ETIMEDOUT","ERR_NETWORK","ERR_FR_TOO_MANY_REDIRECTS","ERR_DEPRECATED","ERR_BAD_RESPONSE","ERR_BAD_REQUEST","ERR_CANCELED","ERR_NOT_SUPPORT","ERR_INVALID_URL"].forEach((e=>{we[e]={value:e}})),Object.defineProperties(ge,we),Object.defineProperty(ye,"isAxiosError",{value:!0}),ge.from=(e,n,t,a,i,o)=>{const s=Object.create(ye);return be.toFlatObject(e,s,(function(e){return e!==Error.prototype}),(e=>"isAxiosError"!==e)),ge.call(s,e.message,n,t,a,i),s.cause=e,s.name=e.name,o&&Object.assign(s,o),s};var Ee,Te,Ce,Oe,qe,Ae,Fe,De,Pe,Be,Le,Ue,ze,Ie,Ne,Me,We,He={},Ve={"application/1d-interleaved-parityfec":{source:"iana"},"application/3gpdash-qoe-report+xml":{source:"iana",charset:"UTF-8",compressible:!0},"application/3gpp-ims+xml":{source:"iana",compressible:!0},"application/3gpphal+json":{source:"iana",compressible:!0},"application/3gpphalforms+json":{source:"iana",compressible:!0},"application/a2l":{source:"iana"},"application/ace+cbor":{source:"iana"},"application/activemessage":{source:"iana"},"application/activity+json":{source:"iana",compressible:!0},"application/alto-costmap+json":{source:"iana",compressible:!0},"application/alto-costmapfilter+json":{source:"iana",compressible:!0},"application/alto-directory+json":{source:"iana",compressible:!0},"application/alto-endpointcost+json":{source:"iana",compressible:!0},"application/alto-endpointcostparams+json":{source:"iana",compressible:!0},"application/alto-endpointprop+json":{source:"iana",compressible:!0},"application/alto-endpointpropparams+json":{source:"iana",compressible:!0},"application/alto-error+json":{source:"iana",compressible:!0},"application/alto-networkmap+json":{source:"iana",compressible:!0},"application/alto-networkmapfilter+json":{source:"iana",compressible:!0},"application/alto-updatestreamcontrol+json":{source:"iana",compressible:!0},"application/alto-updatestreamparams+json":{source:"iana",compressible:!0},"application/aml":{source:"iana"},"application/andrew-inset":{source:"iana",extensions:["ez"]},"application/applefile":{source:"iana"},"application/applixware":{source:"apache",extensions:["aw"]},"application/at+jwt":{source:"iana"},"application/atf":{source:"iana"},"application/atfx":{source:"iana"},"application/atom+xml":{source:"iana",compressible:!0,extensions:["atom"]},"application/atomcat+xml":{source:"iana",compressible:!0,extensions:["atomcat"]},"application/atomdeleted+xml":{source:"iana",compressible:!0,extensions:["atomdeleted"]},"application/atomicmail":{source:"iana"},"application/atomsvc+xml":{source:"iana",compressible:!0,extensions:["atomsvc"]},"application/atsc-dwd+xml":{source:"iana",compressible:!0,extensions:["dwd"]},"application/atsc-dynamic-event-message":{source:"iana"},"application/atsc-held+xml":{source:"iana",compressible:!0,extensions:["held"]},"application/atsc-rdt+json":{source:"iana",compressible:!0},"application/atsc-rsat+xml":{source:"iana",compressible:!0,extensions:["rsat"]},"application/atxml":{source:"iana"},"application/auth-policy+xml":{source:"iana",compressible:!0},"application/bacnet-xdd+zip":{source:"iana",compressible:!1},"application/batch-smtp":{source:"iana"},"application/bdoc":{compressible:!1,extensions:["bdoc"]},"application/beep+xml":{source:"iana",charset:"UTF-8",compressible:!0},"application/calendar+json":{source:"iana",compressible:!0},"application/calendar+xml":{source:"iana",compressible:!0,extensions:["xcs"]},"application/call-completion":{source:"iana"},"application/cals-1840":{source:"iana"},"application/captive+json":{source:"iana",compressible:!0},"application/cbor":{source:"iana"},"application/cbor-seq":{source:"iana"},"application/cccex":{source:"iana"},"application/ccmp+xml":{source:"iana",compressible:!0},"application/ccxml+xml":{source:"iana",compressible:!0,extensions:["ccxml"]},"application/cdfx+xml":{source:"iana",compressible:!0,extensions:["cdfx"]},"application/cdmi-capability":{source:"iana",extensions:["cdmia"]},"application/cdmi-container":{source:"iana",extensions:["cdmic"]},"application/cdmi-domain":{source:"iana",extensions:["cdmid"]},"application/cdmi-object":{source:"iana",extensions:["cdmio"]},"application/cdmi-queue":{source:"iana",extensions:["cdmiq"]},"application/cdni":{source:"iana"},"application/cea":{source:"iana"},"application/cea-2018+xml":{source:"iana",compressible:!0},"application/cellml+xml":{source:"iana",compressible:!0},"application/cfw":{source:"iana"},"application/city+json":{source:"iana",compressible:!0},"application/clr":{source:"iana"},"application/clue+xml":{source:"iana",compressible:!0},"application/clue_info+xml":{source:"iana",compressible:!0},"application/cms":{source:"iana"},"application/cnrp+xml":{source:"iana",compressible:!0},"application/coap-group+json":{source:"iana",compressible:!0},"application/coap-payload":{source:"iana"},"application/commonground":{source:"iana"},"application/conference-info+xml":{source:"iana",compressible:!0},"application/cose":{source:"iana"},"application/cose-key":{source:"iana"},"application/cose-key-set":{source:"iana"},"application/cpl+xml":{source:"iana",compressible:!0,extensions:["cpl"]},"application/csrattrs":{source:"iana"},"application/csta+xml":{source:"iana",compressible:!0},"application/cstadata+xml":{source:"iana",compressible:!0},"application/csvm+json":{source:"iana",compressible:!0},"application/cu-seeme":{source:"apache",extensions:["cu"]},"application/cwt":{source:"iana"},"application/cybercash":{source:"iana"},"application/dart":{compressible:!0},"application/dash+xml":{source:"iana",compressible:!0,extensions:["mpd"]},"application/dash-patch+xml":{source:"iana",compressible:!0,extensions:["mpp"]},"application/dashdelta":{source:"iana"},"application/davmount+xml":{source:"iana",compressible:!0,extensions:["davmount"]},"application/dca-rft":{source:"iana"},"application/dcd":{source:"iana"},"application/dec-dx":{source:"iana"},"application/dialog-info+xml":{source:"iana",compressible:!0},"application/dicom":{source:"iana"},"application/dicom+json":{source:"iana",compressible:!0},"application/dicom+xml":{source:"iana",compressible:!0},"application/dii":{source:"iana"},"application/dit":{source:"iana"},"application/dns":{source:"iana"},"application/dns+json":{source:"iana",compressible:!0},"application/dns-message":{source:"iana"},"application/docbook+xml":{source:"apache",compressible:!0,extensions:["dbk"]},"application/dots+cbor":{source:"iana"},"application/dskpp+xml":{source:"iana",compressible:!0},"application/dssc+der":{source:"iana",extensions:["dssc"]},"application/dssc+xml":{source:"iana",compressible:!0,extensions:["xdssc"]},"application/dvcs":{source:"iana"},"application/ecmascript":{source:"iana",compressible:!0,extensions:["es","ecma"]},"application/edi-consent":{source:"iana"},"application/edi-x12":{source:"iana",compressible:!1},"application/edifact":{source:"iana",compressible:!1},"application/efi":{source:"iana"},"application/elm+json":{source:"iana",charset:"UTF-8",compressible:!0},"application/elm+xml":{source:"iana",compressible:!0},"application/emergencycalldata.cap+xml":{source:"iana",charset:"UTF-8",compressible:!0},"application/emergencycalldata.comment+xml":{source:"iana",compressible:!0},"application/emergencycalldata.control+xml":{source:"iana",compressible:!0},"application/emergencycalldata.deviceinfo+xml":{source:"iana",compressible:!0},"application/emergencycalldata.ecall.msd":{source:"iana"},"application/emergencycalldata.providerinfo+xml":{source:"iana",compressible:!0},"application/emergencycalldata.serviceinfo+xml":{source:"iana",compressible:!0},"application/emergencycalldata.subscriberinfo+xml":{source:"iana",compressible:!0},"application/emergencycalldata.veds+xml":{source:"iana",compressible:!0},"application/emma+xml":{source:"iana",compressible:!0,extensions:["emma"]},"application/emotionml+xml":{source:"iana",compressible:!0,extensions:["emotionml"]},"application/encaprtp":{source:"iana"},"application/epp+xml":{source:"iana",compressible:!0},"application/epub+zip":{source:"iana",compressible:!1,extensions:["epub"]},"application/eshop":{source:"iana"},"application/exi":{source:"iana",extensions:["exi"]},"application/expect-ct-report+json":{source:"iana",compressible:!0},"application/express":{source:"iana",extensions:["exp"]},"application/fastinfoset":{source:"iana"},"application/fastsoap":{source:"iana"},"application/fdt+xml":{source:"iana",compressible:!0,extensions:["fdt"]},"application/fhir+json":{source:"iana",charset:"UTF-8",compressible:!0},"application/fhir+xml":{source:"iana",charset:"UTF-8",compressible:!0},"application/fido.trusted-apps+json":{compressible:!0},"application/fits":{source:"iana"},"application/flexfec":{source:"iana"},"application/font-sfnt":{source:"iana"},"application/font-tdpfr":{source:"iana",extensions:["pfr"]},"application/font-woff":{source:"iana",compressible:!1},"application/framework-attributes+xml":{source:"iana",compressible:!0},"application/geo+json":{source:"iana",compressible:!0,extensions:["geojson"]},"application/geo+json-seq":{source:"iana"},"application/geopackage+sqlite3":{source:"iana"},"application/geoxacml+xml":{source:"iana",compressible:!0},"application/gltf-buffer":{source:"iana"},"application/gml+xml":{source:"iana",compressible:!0,extensions:["gml"]},"application/gpx+xml":{source:"apache",compressible:!0,extensions:["gpx"]},"application/gxf":{source:"apache",extensions:["gxf"]},"application/gzip":{source:"iana",compressible:!1,extensions:["gz"]},"application/h224":{source:"iana"},"application/held+xml":{source:"iana",compressible:!0},"application/hjson":{extensions:["hjson"]},"application/http":{source:"iana"},"application/hyperstudio":{source:"iana",extensions:["stk"]},"application/ibe-key-request+xml":{source:"iana",compressible:!0},"application/ibe-pkg-reply+xml":{source:"iana",compressible:!0},"application/ibe-pp-data":{source:"iana"},"application/iges":{source:"iana"},"application/im-iscomposing+xml":{source:"iana",charset:"UTF-8",compressible:!0},"application/index":{source:"iana"},"application/index.cmd":{source:"iana"},"application/index.obj":{source:"iana"},"application/index.response":{source:"iana"},"application/index.vnd":{source:"iana"},"application/inkml+xml":{source:"iana",compressible:!0,extensions:["ink","inkml"]},"application/iotp":{source:"iana"},"application/ipfix":{source:"iana",extensions:["ipfix"]},"application/ipp":{source:"iana"},"application/isup":{source:"iana"},"application/its+xml":{source:"iana",compressible:!0,extensions:["its"]},"application/java-archive":{source:"apache",compressible:!1,extensions:["jar","war","ear"]},"application/java-serialized-object":{source:"apache",compressible:!1,extensions:["ser"]},"application/java-vm":{source:"apache",compressible:!1,extensions:["class"]},"application/javascript":{source:"iana",charset:"UTF-8",compressible:!0,extensions:["js","mjs"]},"application/jf2feed+json":{source:"iana",compressible:!0},"application/jose":{source:"iana"},"application/jose+json":{source:"iana",compressible:!0},"application/jrd+json":{source:"iana",compressible:!0},"application/jscalendar+json":{source:"iana",compressible:!0},"application/json":{source:"iana",charset:"UTF-8",compressible:!0,extensions:["json","map"]},"application/json-patch+json":{source:"iana",compressible:!0},"application/json-seq":{source:"iana"},"application/json5":{extensions:["json5"]},"application/jsonml+json":{source:"apache",compressible:!0,extensions:["jsonml"]},"application/jwk+json":{source:"iana",compressible:!0},"application/jwk-set+json":{source:"iana",compressible:!0},"application/jwt":{source:"iana"},"application/kpml-request+xml":{source:"iana",compressible:!0},"application/kpml-response+xml":{source:"iana",compressible:!0},"application/ld+json":{source:"iana",compressible:!0,extensions:["jsonld"]},"application/lgr+xml":{source:"iana",compressible:!0,extensions:["lgr"]},"application/link-format":{source:"iana"},"application/load-control+xml":{source:"iana",compressible:!0},"application/lost+xml":{source:"iana",compressible:!0,extensions:["lostxml"]},"application/lostsync+xml":{source:"iana",compressible:!0},"application/lpf+zip":{source:"iana",compressible:!1},"application/lxf":{source:"iana"},"application/mac-binhex40":{source:"iana",extensions:["hqx"]},"application/mac-compactpro":{source:"apache",extensions:["cpt"]},"application/macwriteii":{source:"iana"},"application/mads+xml":{source:"iana",compressible:!0,extensions:["mads"]},"application/manifest+json":{source:"iana",charset:"UTF-8",compressible:!0,extensions:["webmanifest"]},"application/marc":{source:"iana",extensions:["mrc"]},"application/marcxml+xml":{source:"iana",compressible:!0,extensions:["mrcx"]},"application/mathematica":{source:"iana",extensions:["ma","nb","mb"]},"application/mathml+xml":{source:"iana",compressible:!0,extensions:["mathml"]},"application/mathml-content+xml":{source:"iana",compressible:!0},"application/mathml-presentation+xml":{source:"iana",compressible:!0},"application/mbms-associated-procedure-description+xml":{source:"iana",compressible:!0},"application/mbms-deregister+xml":{source:"iana",compressible:!0},"application/mbms-envelope+xml":{source:"iana",compressible:!0},"application/mbms-msk+xml":{source:"iana",compressible:!0},"application/mbms-msk-response+xml":{source:"iana",compressible:!0},"application/mbms-protection-description+xml":{source:"iana",compressible:!0},"application/mbms-reception-report+xml":{source:"iana",compressible:!0},"application/mbms-register+xml":{source:"iana",compressible:!0},"application/mbms-register-response+xml":{source:"iana",compressible:!0},"application/mbms-schedule+xml":{source:"iana",compressible:!0},"application/mbms-user-service-description+xml":{source:"iana",compressible:!0},"application/mbox":{source:"iana",extensions:["mbox"]},"application/media-policy-dataset+xml":{source:"iana",compressible:!0,extensions:["mpf"]},"application/media_control+xml":{source:"iana",compressible:!0},"application/mediaservercontrol+xml":{source:"iana",compressible:!0,extensions:["mscml"]},"application/merge-patch+json":{source:"iana",compressible:!0},"application/metalink+xml":{source:"apache",compressible:!0,extensions:["metalink"]},"application/metalink4+xml":{source:"iana",compressible:!0,extensions:["meta4"]},"application/mets+xml":{source:"iana",compressible:!0,extensions:["mets"]},"application/mf4":{source:"iana"},"application/mikey":{source:"iana"},"application/mipc":{source:"iana"},"application/missing-blocks+cbor-seq":{source:"iana"},"application/mmt-aei+xml":{source:"iana",compressible:!0,extensions:["maei"]},"application/mmt-usd+xml":{source:"iana",compressible:!0,extensions:["musd"]},"application/mods+xml":{source:"iana",compressible:!0,extensions:["mods"]},"application/moss-keys":{source:"iana"},"application/moss-signature":{source:"iana"},"application/mosskey-data":{source:"iana"},"application/mosskey-request":{source:"iana"},"application/mp21":{source:"iana",extensions:["m21","mp21"]},"application/mp4":{source:"iana",extensions:["mp4s","m4p"]},"application/mpeg4-generic":{source:"iana"},"application/mpeg4-iod":{source:"iana"},"application/mpeg4-iod-xmt":{source:"iana"},"application/mrb-consumer+xml":{source:"iana",compressible:!0},"application/mrb-publish+xml":{source:"iana",compressible:!0},"application/msc-ivr+xml":{source:"iana",charset:"UTF-8",compressible:!0},"application/msc-mixer+xml":{source:"iana",charset:"UTF-8",compressible:!0},"application/msword":{source:"iana",compressible:!1,extensions:["doc","dot"]},"application/mud+json":{source:"iana",compressible:!0},"application/multipart-core":{source:"iana"},"application/mxf":{source:"iana",extensions:["mxf"]},"application/n-quads":{source:"iana",extensions:["nq"]},"application/n-triples":{source:"iana",extensions:["nt"]},"application/nasdata":{source:"iana"},"application/news-checkgroups":{source:"iana",charset:"US-ASCII"},"application/news-groupinfo":{source:"iana",charset:"US-ASCII"},"application/news-transmission":{source:"iana"},"application/nlsml+xml":{source:"iana",compressible:!0},"application/node":{source:"iana",extensions:["cjs"]},"application/nss":{source:"iana"},"application/oauth-authz-req+jwt":{source:"iana"},"application/oblivious-dns-message":{source:"iana"},"application/ocsp-request":{source:"iana"},"application/ocsp-response":{source:"iana"},"application/octet-stream":{source:"iana",compressible:!1,extensions:["bin","dms","lrf","mar","so","dist","distz","pkg","bpk","dump","elc","deploy","exe","dll","deb","dmg","iso","img","msi","msp","msm","buffer"]},"application/oda":{source:"iana",extensions:["oda"]},"application/odm+xml":{source:"iana",compressible:!0},"application/odx":{source:"iana"},"application/oebps-package+xml":{source:"iana",compressible:!0,extensions:["opf"]},"application/ogg":{source:"iana",compressible:!1,extensions:["ogx"]},"application/omdoc+xml":{source:"apache",compressible:!0,extensions:["omdoc"]},"application/onenote":{source:"apache",extensions:["onetoc","onetoc2","onetmp","onepkg"]},"application/opc-nodeset+xml":{source:"iana",compressible:!0},"application/oscore":{source:"iana"},"application/oxps":{source:"iana",extensions:["oxps"]},"application/p21":{source:"iana"},"application/p21+zip":{source:"iana",compressible:!1},"application/p2p-overlay+xml":{source:"iana",compressible:!0,extensions:["relo"]},"application/parityfec":{source:"iana"},"application/passport":{source:"iana"},"application/patch-ops-error+xml":{source:"iana",compressible:!0,extensions:["xer"]},"application/pdf":{source:"iana",compressible:!1,extensions:["pdf"]},"application/pdx":{source:"iana"},"application/pem-certificate-chain":{source:"iana"},"application/pgp-encrypted":{source:"iana",compressible:!1,extensions:["pgp"]},"application/pgp-keys":{source:"iana",extensions:["asc"]},"application/pgp-signature":{source:"iana",extensions:["asc","sig"]},"application/pics-rules":{source:"apache",extensions:["prf"]},"application/pidf+xml":{source:"iana",charset:"UTF-8",compressible:!0},"application/pidf-diff+xml":{source:"iana",charset:"UTF-8",compressible:!0},"application/pkcs10":{source:"iana",extensions:["p10"]},"application/pkcs12":{source:"iana"},"application/pkcs7-mime":{source:"iana",extensions:["p7m","p7c"]},"application/pkcs7-signature":{source:"iana",extensions:["p7s"]},"application/pkcs8":{source:"iana",extensions:["p8"]},"application/pkcs8-encrypted":{source:"iana"},"application/pkix-attr-cert":{source:"iana",extensions:["ac"]},"application/pkix-cert":{source:"iana",extensions:["cer"]},"application/pkix-crl":{source:"iana",extensions:["crl"]},"application/pkix-pkipath":{source:"iana",extensions:["pkipath"]},"application/pkixcmp":{source:"iana",extensions:["pki"]},"application/pls+xml":{source:"iana",compressible:!0,extensions:["pls"]},"application/poc-settings+xml":{source:"iana",charset:"UTF-8",compressible:!0},"application/postscript":{source:"iana",compressible:!0,extensions:["ai","eps","ps"]},"application/ppsp-tracker+json":{source:"iana",compressible:!0},"application/problem+json":{source:"iana",compressible:!0},"application/problem+xml":{source:"iana",compressible:!0},"application/provenance+xml":{source:"iana",compressible:!0,extensions:["provx"]},"application/prs.alvestrand.titrax-sheet":{source:"iana"},"application/prs.cww":{source:"iana",extensions:["cww"]},"application/prs.cyn":{source:"iana",charset:"7-BIT"},"application/prs.hpub+zip":{source:"iana",compressible:!1},"application/prs.nprend":{source:"iana"},"application/prs.plucker":{source:"iana"},"application/prs.rdf-xml-crypt":{source:"iana"},"application/prs.xsf+xml":{source:"iana",compressible:!0},"application/pskc+xml":{source:"iana",compressible:!0,extensions:["pskcxml"]},"application/pvd+json":{source:"iana",compressible:!0},"application/qsig":{source:"iana"},"application/raml+yaml":{compressible:!0,extensions:["raml"]},"application/raptorfec":{source:"iana"},"application/rdap+json":{source:"iana",compressible:!0},"application/rdf+xml":{source:"iana",compressible:!0,extensions:["rdf","owl"]},"application/reginfo+xml":{source:"iana",compressible:!0,extensions:["rif"]},"application/relax-ng-compact-syntax":{source:"iana",extensions:["rnc"]},"application/remote-printing":{source:"iana"},"application/reputon+json":{source:"iana",compressible:!0},"application/resource-lists+xml":{source:"iana",compressible:!0,extensions:["rl"]},"application/resource-lists-diff+xml":{source:"iana",compressible:!0,extensions:["rld"]},"application/rfc+xml":{source:"iana",compressible:!0},"application/riscos":{source:"iana"},"application/rlmi+xml":{source:"iana",compressible:!0},"application/rls-services+xml":{source:"iana",compressible:!0,extensions:["rs"]},"application/route-apd+xml":{source:"iana",compressible:!0,extensions:["rapd"]},"application/route-s-tsid+xml":{source:"iana",compressible:!0,extensions:["sls"]},"application/route-usd+xml":{source:"iana",compressible:!0,extensions:["rusd"]},"application/rpki-ghostbusters":{source:"iana",extensions:["gbr"]},"application/rpki-manifest":{source:"iana",extensions:["mft"]},"application/rpki-publication":{source:"iana"},"application/rpki-roa":{source:"iana",extensions:["roa"]},"application/rpki-updown":{source:"iana"},"application/rsd+xml":{source:"apache",compressible:!0,extensions:["rsd"]},"application/rss+xml":{source:"apache",compressible:!0,extensions:["rss"]},"application/rtf":{source:"iana",compressible:!0,extensions:["rtf"]},"application/rtploopback":{source:"iana"},"application/rtx":{source:"iana"},"application/samlassertion+xml":{source:"iana",compressible:!0},"application/samlmetadata+xml":{source:"iana",compressible:!0},"application/sarif+json":{source:"iana",compressible:!0},"application/sarif-external-properties+json":{source:"iana",compressible:!0},"application/sbe":{source:"iana"},"application/sbml+xml":{source:"iana",compressible:!0,extensions:["sbml"]},"application/scaip+xml":{source:"iana",compressible:!0},"application/scim+json":{source:"iana",compressible:!0},"application/scvp-cv-request":{source:"iana",extensions:["scq"]},"application/scvp-cv-response":{source:"iana",extensions:["scs"]},"application/scvp-vp-request":{source:"iana",extensions:["spq"]},"application/scvp-vp-response":{source:"iana",extensions:["spp"]},"application/sdp":{source:"iana",extensions:["sdp"]},"application/secevent+jwt":{source:"iana"},"application/senml+cbor":{source:"iana"},"application/senml+json":{source:"iana",compressible:!0},"application/senml+xml":{source:"iana",compressible:!0,extensions:["senmlx"]},"application/senml-etch+cbor":{source:"iana"},"application/senml-etch+json":{source:"iana",compressible:!0},"application/senml-exi":{source:"iana"},"application/sensml+cbor":{source:"iana"},"application/sensml+json":{source:"iana",compressible:!0},"application/sensml+xml":{source:"iana",compressible:!0,extensions:["sensmlx"]},"application/sensml-exi":{source:"iana"},"application/sep+xml":{source:"iana",compressible:!0},"application/sep-exi":{source:"iana"},"application/session-info":{source:"iana"},"application/set-payment":{source:"iana"},"application/set-payment-initiation":{source:"iana",extensions:["setpay"]},"application/set-registration":{source:"iana"},"application/set-registration-initiation":{source:"iana",extensions:["setreg"]},"application/sgml":{source:"iana"},"application/sgml-open-catalog":{source:"iana"},"application/shf+xml":{source:"iana",compressible:!0,extensions:["shf"]},"application/sieve":{source:"iana",extensions:["siv","sieve"]},"application/simple-filter+xml":{source:"iana",compressible:!0},"application/simple-message-summary":{source:"iana"},"application/simplesymbolcontainer":{source:"iana"},"application/sipc":{source:"iana"},"application/slate":{source:"iana"},"application/smil":{source:"iana"},"application/smil+xml":{source:"iana",compressible:!0,extensions:["smi","smil"]},"application/smpte336m":{source:"iana"},"application/soap+fastinfoset":{source:"iana"},"application/soap+xml":{source:"iana",compressible:!0},"application/sparql-query":{source:"iana",extensions:["rq"]},"application/sparql-results+xml":{source:"iana",compressible:!0,extensions:["srx"]},"application/spdx+json":{source:"iana",compressible:!0},"application/spirits-event+xml":{source:"iana",compressible:!0},"application/sql":{source:"iana"},"application/srgs":{source:"iana",extensions:["gram"]},"application/srgs+xml":{source:"iana",compressible:!0,extensions:["grxml"]},"application/sru+xml":{source:"iana",compressible:!0,extensions:["sru"]},"application/ssdl+xml":{source:"apache",compressible:!0,extensions:["ssdl"]},"application/ssml+xml":{source:"iana",compressible:!0,extensions:["ssml"]},"application/stix+json":{source:"iana",compressible:!0},"application/swid+xml":{source:"iana",compressible:!0,extensions:["swidtag"]},"application/tamp-apex-update":{source:"iana"},"application/tamp-apex-update-confirm":{source:"iana"},"application/tamp-community-update":{source:"iana"},"application/tamp-community-update-confirm":{source:"iana"},"application/tamp-error":{source:"iana"},"application/tamp-sequence-adjust":{source:"iana"},"application/tamp-sequence-adjust-confirm":{source:"iana"},"application/tamp-status-query":{source:"iana"},"application/tamp-status-response":{source:"iana"},"application/tamp-update":{source:"iana"},"application/tamp-update-confirm":{source:"iana"},"application/tar":{compressible:!0},"application/taxii+json":{source:"iana",compressible:!0},"application/td+json":{source:"iana",compressible:!0},"application/tei+xml":{source:"iana",compressible:!0,extensions:["tei","teicorpus"]},"application/tetra_isi":{source:"iana"},"application/thraud+xml":{source:"iana",compressible:!0,extensions:["tfi"]},"application/timestamp-query":{source:"iana"},"application/timestamp-reply":{source:"iana"},"application/timestamped-data":{source:"iana",extensions:["tsd"]},"application/tlsrpt+gzip":{source:"iana"},"application/tlsrpt+json":{source:"iana",compressible:!0},"application/tnauthlist":{source:"iana"},"application/token-introspection+jwt":{source:"iana"},"application/toml":{compressible:!0,extensions:["toml"]},"application/trickle-ice-sdpfrag":{source:"iana"},"application/trig":{source:"iana",extensions:["trig"]},"application/ttml+xml":{source:"iana",compressible:!0,extensions:["ttml"]},"application/tve-trigger":{source:"iana"},"application/tzif":{source:"iana"},"application/tzif-leap":{source:"iana"},"application/ubjson":{compressible:!1,extensions:["ubj"]},"application/ulpfec":{source:"iana"},"application/urc-grpsheet+xml":{source:"iana",compressible:!0},"application/urc-ressheet+xml":{source:"iana",compressible:!0,extensions:["rsheet"]},"application/urc-targetdesc+xml":{source:"iana",compressible:!0,extensions:["td"]},"application/urc-uisocketdesc+xml":{source:"iana",compressible:!0},"application/vcard+json":{source:"iana",compressible:!0},"application/vcard+xml":{source:"iana",compressible:!0},"application/vemmi":{source:"iana"},"application/vividence.scriptfile":{source:"apache"},"application/vnd.1000minds.decision-model+xml":{source:"iana",compressible:!0,extensions:["1km"]},"application/vnd.3gpp-prose+xml":{source:"iana",compressible:!0},"application/vnd.3gpp-prose-pc3ch+xml":{source:"iana",compressible:!0},"application/vnd.3gpp-v2x-local-service-information":{source:"iana"},"application/vnd.3gpp.5gnas":{source:"iana"},"application/vnd.3gpp.access-transfer-events+xml":{source:"iana",compressible:!0},"application/vnd.3gpp.bsf+xml":{source:"iana",compressible:!0},"application/vnd.3gpp.gmop+xml":{source:"iana",compressible:!0},"application/vnd.3gpp.gtpc":{source:"iana"},"application/vnd.3gpp.interworking-data":{source:"iana"},"application/vnd.3gpp.lpp":{source:"iana"},"application/vnd.3gpp.mc-signalling-ear":{source:"iana"},"application/vnd.3gpp.mcdata-affiliation-command+xml":{source:"iana",compressible:!0},"application/vnd.3gpp.mcdata-info+xml":{source:"iana",compressible:!0},"application/vnd.3gpp.mcdata-payload":{source:"iana"},"application/vnd.3gpp.mcdata-service-config+xml":{source:"iana",compressible:!0},"application/vnd.3gpp.mcdata-signalling":{source:"iana"},"application/vnd.3gpp.mcdata-ue-config+xml":{source:"iana",compressible:!0},"application/vnd.3gpp.mcdata-user-profile+xml":{source:"iana",compressible:!0},"application/vnd.3gpp.mcptt-affiliation-command+xml":{source:"iana",compressible:!0},"application/vnd.3gpp.mcptt-floor-request+xml":{source:"iana",compressible:!0},"application/vnd.3gpp.mcptt-info+xml":{source:"iana",compressible:!0},"application/vnd.3gpp.mcptt-location-info+xml":{source:"iana",compressible:!0},"application/vnd.3gpp.mcptt-mbms-usage-info+xml":{source:"iana",compressible:!0},"application/vnd.3gpp.mcptt-service-config+xml":{source:"iana",compressible:!0},"application/vnd.3gpp.mcptt-signed+xml":{source:"iana",compressible:!0},"application/vnd.3gpp.mcptt-ue-config+xml":{source:"iana",compressible:!0},"application/vnd.3gpp.mcptt-ue-init-config+xml":{source:"iana",compressible:!0},"application/vnd.3gpp.mcptt-user-profile+xml":{source:"iana",compressible:!0},"application/vnd.3gpp.mcvideo-affiliation-command+xml":{source:"iana",compressible:!0},"application/vnd.3gpp.mcvideo-affiliation-info+xml":{source:"iana",compressible:!0},"application/vnd.3gpp.mcvideo-info+xml":{source:"iana",compressible:!0},"application/vnd.3gpp.mcvideo-location-info+xml":{source:"iana",compressible:!0},"application/vnd.3gpp.mcvideo-mbms-usage-info+xml":{source:"iana",compressible:!0},"application/vnd.3gpp.mcvideo-service-config+xml":{source:"iana",compressible:!0},"application/vnd.3gpp.mcvideo-transmission-request+xml":{source:"iana",compressible:!0},"application/vnd.3gpp.mcvideo-ue-config+xml":{source:"iana",compressible:!0},"application/vnd.3gpp.mcvideo-user-profile+xml":{source:"iana",compressible:!0},"application/vnd.3gpp.mid-call+xml":{source:"iana",compressible:!0},"application/vnd.3gpp.ngap":{source:"iana"},"application/vnd.3gpp.pfcp":{source:"iana"},"application/vnd.3gpp.pic-bw-large":{source:"iana",extensions:["plb"]},"application/vnd.3gpp.pic-bw-small":{source:"iana",extensions:["psb"]},"application/vnd.3gpp.pic-bw-var":{source:"iana",extensions:["pvb"]},"application/vnd.3gpp.s1ap":{source:"iana"},"application/vnd.3gpp.sms":{source:"iana"},"application/vnd.3gpp.sms+xml":{source:"iana",compressible:!0},"application/vnd.3gpp.srvcc-ext+xml":{source:"iana",compressible:!0},"application/vnd.3gpp.srvcc-info+xml":{source:"iana",compressible:!0},"application/vnd.3gpp.state-and-event-info+xml":{source:"iana",compressible:!0},"application/vnd.3gpp.ussd+xml":{source:"iana",compressible:!0},"application/vnd.3gpp2.bcmcsinfo+xml":{source:"iana",compressible:!0},"application/vnd.3gpp2.sms":{source:"iana"},"application/vnd.3gpp2.tcap":{source:"iana",extensions:["tcap"]},"application/vnd.3lightssoftware.imagescal":{source:"iana"},"application/vnd.3m.post-it-notes":{source:"iana",extensions:["pwn"]},"application/vnd.accpac.simply.aso":{source:"iana",extensions:["aso"]},"application/vnd.accpac.simply.imp":{source:"iana",extensions:["imp"]},"application/vnd.acucobol":{source:"iana",extensions:["acu"]},"application/vnd.acucorp":{source:"iana",extensions:["atc","acutc"]},"application/vnd.adobe.air-application-installer-package+zip":{source:"apache",compressible:!1,extensions:["air"]},"application/vnd.adobe.flash.movie":{source:"iana"},"application/vnd.adobe.formscentral.fcdt":{source:"iana",extensions:["fcdt"]},"application/vnd.adobe.fxp":{source:"iana",extensions:["fxp","fxpl"]},"application/vnd.adobe.partial-upload":{source:"iana"},"application/vnd.adobe.xdp+xml":{source:"iana",compressible:!0,extensions:["xdp"]},"application/vnd.adobe.xfdf":{source:"iana",extensions:["xfdf"]},"application/vnd.aether.imp":{source:"iana"},"application/vnd.afpc.afplinedata":{source:"iana"},"application/vnd.afpc.afplinedata-pagedef":{source:"iana"},"application/vnd.afpc.cmoca-cmresource":{source:"iana"},"application/vnd.afpc.foca-charset":{source:"iana"},"application/vnd.afpc.foca-codedfont":{source:"iana"},"application/vnd.afpc.foca-codepage":{source:"iana"},"application/vnd.afpc.modca":{source:"iana"},"application/vnd.afpc.modca-cmtable":{source:"iana"},"application/vnd.afpc.modca-formdef":{source:"iana"},"application/vnd.afpc.modca-mediummap":{source:"iana"},"application/vnd.afpc.modca-objectcontainer":{source:"iana"},"application/vnd.afpc.modca-overlay":{source:"iana"},"application/vnd.afpc.modca-pagesegment":{source:"iana"},"application/vnd.age":{source:"iana",extensions:["age"]},"application/vnd.ah-barcode":{source:"iana"},"application/vnd.ahead.space":{source:"iana",extensions:["ahead"]},"application/vnd.airzip.filesecure.azf":{source:"iana",extensions:["azf"]},"application/vnd.airzip.filesecure.azs":{source:"iana",extensions:["azs"]},"application/vnd.amadeus+json":{source:"iana",compressible:!0},"application/vnd.amazon.ebook":{source:"apache",extensions:["azw"]},"application/vnd.amazon.mobi8-ebook":{source:"iana"},"application/vnd.americandynamics.acc":{source:"iana",extensions:["acc"]},"application/vnd.amiga.ami":{source:"iana",extensions:["ami"]},"application/vnd.amundsen.maze+xml":{source:"iana",compressible:!0},"application/vnd.android.ota":{source:"iana"},"application/vnd.android.package-archive":{source:"apache",compressible:!1,extensions:["apk"]},"application/vnd.anki":{source:"iana"},"application/vnd.anser-web-certificate-issue-initiation":{source:"iana",extensions:["cii"]},"application/vnd.anser-web-funds-transfer-initiation":{source:"apache",extensions:["fti"]},"application/vnd.antix.game-component":{source:"iana",extensions:["atx"]},"application/vnd.apache.arrow.file":{source:"iana"},"application/vnd.apache.arrow.stream":{source:"iana"},"application/vnd.apache.thrift.binary":{source:"iana"},"application/vnd.apache.thrift.compact":{source:"iana"},"application/vnd.apache.thrift.json":{source:"iana"},"application/vnd.api+json":{source:"iana",compressible:!0},"application/vnd.aplextor.warrp+json":{source:"iana",compressible:!0},"application/vnd.apothekende.reservation+json":{source:"iana",compressible:!0},"application/vnd.apple.installer+xml":{source:"iana",compressible:!0,extensions:["mpkg"]},"application/vnd.apple.keynote":{source:"iana",extensions:["key"]},"application/vnd.apple.mpegurl":{source:"iana",extensions:["m3u8"]},"application/vnd.apple.numbers":{source:"iana",extensions:["numbers"]},"application/vnd.apple.pages":{source:"iana",extensions:["pages"]},"application/vnd.apple.pkpass":{compressible:!1,extensions:["pkpass"]},"application/vnd.arastra.swi":{source:"iana"},"application/vnd.aristanetworks.swi":{source:"iana",extensions:["swi"]},"application/vnd.artisan+json":{source:"iana",compressible:!0},"application/vnd.artsquare":{source:"iana"},"application/vnd.astraea-software.iota":{source:"iana",extensions:["iota"]},"application/vnd.audiograph":{source:"iana",extensions:["aep"]},"application/vnd.autopackage":{source:"iana"},"application/vnd.avalon+json":{source:"iana",compressible:!0},"application/vnd.avistar+xml":{source:"iana",compressible:!0},"application/vnd.balsamiq.bmml+xml":{source:"iana",compressible:!0,extensions:["bmml"]},"application/vnd.balsamiq.bmpr":{source:"iana"},"application/vnd.banana-accounting":{source:"iana"},"application/vnd.bbf.usp.error":{source:"iana"},"application/vnd.bbf.usp.msg":{source:"iana"},"application/vnd.bbf.usp.msg+json":{source:"iana",compressible:!0},"application/vnd.bekitzur-stech+json":{source:"iana",compressible:!0},"application/vnd.bint.med-content":{source:"iana"},"application/vnd.biopax.rdf+xml":{source:"iana",compressible:!0},"application/vnd.blink-idb-value-wrapper":{source:"iana"},"application/vnd.blueice.multipass":{source:"iana",extensions:["mpm"]},"application/vnd.bluetooth.ep.oob":{source:"iana"},"application/vnd.bluetooth.le.oob":{source:"iana"},"application/vnd.bmi":{source:"iana",extensions:["bmi"]},"application/vnd.bpf":{source:"iana"},"application/vnd.bpf3":{source:"iana"},"application/vnd.businessobjects":{source:"iana",extensions:["rep"]},"application/vnd.byu.uapi+json":{source:"iana",compressible:!0},"application/vnd.cab-jscript":{source:"iana"},"application/vnd.canon-cpdl":{source:"iana"},"application/vnd.canon-lips":{source:"iana"},"application/vnd.capasystems-pg+json":{source:"iana",compressible:!0},"application/vnd.cendio.thinlinc.clientconf":{source:"iana"},"application/vnd.century-systems.tcp_stream":{source:"iana"},"application/vnd.chemdraw+xml":{source:"iana",compressible:!0,extensions:["cdxml"]},"application/vnd.chess-pgn":{source:"iana"},"application/vnd.chipnuts.karaoke-mmd":{source:"iana",extensions:["mmd"]},"application/vnd.ciedi":{source:"iana"},"application/vnd.cinderella":{source:"iana",extensions:["cdy"]},"application/vnd.cirpack.isdn-ext":{source:"iana"},"application/vnd.citationstyles.style+xml":{source:"iana",compressible:!0,extensions:["csl"]},"application/vnd.claymore":{source:"iana",extensions:["cla"]},"application/vnd.cloanto.rp9":{source:"iana",extensions:["rp9"]},"application/vnd.clonk.c4group":{source:"iana",extensions:["c4g","c4d","c4f","c4p","c4u"]},"application/vnd.cluetrust.cartomobile-config":{source:"iana",extensions:["c11amc"]},"application/vnd.cluetrust.cartomobile-config-pkg":{source:"iana",extensions:["c11amz"]},"application/vnd.coffeescript":{source:"iana"},"application/vnd.collabio.xodocuments.document":{source:"iana"},"application/vnd.collabio.xodocuments.document-template":{source:"iana"},"application/vnd.collabio.xodocuments.presentation":{source:"iana"},"application/vnd.collabio.xodocuments.presentation-template":{source:"iana"},"application/vnd.collabio.xodocuments.spreadsheet":{source:"iana"},"application/vnd.collabio.xodocuments.spreadsheet-template":{source:"iana"},"application/vnd.collection+json":{source:"iana",compressible:!0},"application/vnd.collection.doc+json":{source:"iana",compressible:!0},"application/vnd.collection.next+json":{source:"iana",compressible:!0},"application/vnd.comicbook+zip":{source:"iana",compressible:!1},"application/vnd.comicbook-rar":{source:"iana"},"application/vnd.commerce-battelle":{source:"iana"},"application/vnd.commonspace":{source:"iana",extensions:["csp"]},"application/vnd.contact.cmsg":{source:"iana",extensions:["cdbcmsg"]},"application/vnd.coreos.ignition+json":{source:"iana",compressible:!0},"application/vnd.cosmocaller":{source:"iana",extensions:["cmc"]},"application/vnd.crick.clicker":{source:"iana",extensions:["clkx"]},"application/vnd.crick.clicker.keyboard":{source:"iana",extensions:["clkk"]},"application/vnd.crick.clicker.palette":{source:"iana",extensions:["clkp"]},"application/vnd.crick.clicker.template":{source:"iana",extensions:["clkt"]},"application/vnd.crick.clicker.wordbank":{source:"iana",extensions:["clkw"]},"application/vnd.criticaltools.wbs+xml":{source:"iana",compressible:!0,extensions:["wbs"]},"application/vnd.cryptii.pipe+json":{source:"iana",compressible:!0},"application/vnd.crypto-shade-file":{source:"iana"},"application/vnd.cryptomator.encrypted":{source:"iana"},"application/vnd.cryptomator.vault":{source:"iana"},"application/vnd.ctc-posml":{source:"iana",extensions:["pml"]},"application/vnd.ctct.ws+xml":{source:"iana",compressible:!0},"application/vnd.cups-pdf":{source:"iana"},"application/vnd.cups-postscript":{source:"iana"},"application/vnd.cups-ppd":{source:"iana",extensions:["ppd"]},"application/vnd.cups-raster":{source:"iana"},"application/vnd.cups-raw":{source:"iana"},"application/vnd.curl":{source:"iana"},"application/vnd.curl.car":{source:"apache",extensions:["car"]},"application/vnd.curl.pcurl":{source:"apache",extensions:["pcurl"]},"application/vnd.cyan.dean.root+xml":{source:"iana",compressible:!0},"application/vnd.cybank":{source:"iana"},"application/vnd.cyclonedx+json":{source:"iana",compressible:!0},"application/vnd.cyclonedx+xml":{source:"iana",compressible:!0},"application/vnd.d2l.coursepackage1p0+zip":{source:"iana",compressible:!1},"application/vnd.d3m-dataset":{source:"iana"},"application/vnd.d3m-problem":{source:"iana"},"application/vnd.dart":{source:"iana",compressible:!0,extensions:["dart"]},"application/vnd.data-vision.rdz":{source:"iana",extensions:["rdz"]},"application/vnd.datapackage+json":{source:"iana",compressible:!0},"application/vnd.dataresource+json":{source:"iana",compressible:!0},"application/vnd.dbf":{source:"iana",extensions:["dbf"]},"application/vnd.debian.binary-package":{source:"iana"},"application/vnd.dece.data":{source:"iana",extensions:["uvf","uvvf","uvd","uvvd"]},"application/vnd.dece.ttml+xml":{source:"iana",compressible:!0,extensions:["uvt","uvvt"]},"application/vnd.dece.unspecified":{source:"iana",extensions:["uvx","uvvx"]},"application/vnd.dece.zip":{source:"iana",extensions:["uvz","uvvz"]},"application/vnd.denovo.fcselayout-link":{source:"iana",extensions:["fe_launch"]},"application/vnd.desmume.movie":{source:"iana"},"application/vnd.dir-bi.plate-dl-nosuffix":{source:"iana"},"application/vnd.dm.delegation+xml":{source:"iana",compressible:!0},"application/vnd.dna":{source:"iana",extensions:["dna"]},"application/vnd.document+json":{source:"iana",compressible:!0},"application/vnd.dolby.mlp":{source:"apache",extensions:["mlp"]},"application/vnd.dolby.mobile.1":{source:"iana"},"application/vnd.dolby.mobile.2":{source:"iana"},"application/vnd.doremir.scorecloud-binary-document":{source:"iana"},"application/vnd.dpgraph":{source:"iana",extensions:["dpg"]},"application/vnd.dreamfactory":{source:"iana",extensions:["dfac"]},"application/vnd.drive+json":{source:"iana",compressible:!0},"application/vnd.ds-keypoint":{source:"apache",extensions:["kpxx"]},"application/vnd.dtg.local":{source:"iana"},"application/vnd.dtg.local.flash":{source:"iana"},"application/vnd.dtg.local.html":{source:"iana"},"application/vnd.dvb.ait":{source:"iana",extensions:["ait"]},"application/vnd.dvb.dvbisl+xml":{source:"iana",compressible:!0},"application/vnd.dvb.dvbj":{source:"iana"},"application/vnd.dvb.esgcontainer":{source:"iana"},"application/vnd.dvb.ipdcdftnotifaccess":{source:"iana"},"application/vnd.dvb.ipdcesgaccess":{source:"iana"},"application/vnd.dvb.ipdcesgaccess2":{source:"iana"},"application/vnd.dvb.ipdcesgpdd":{source:"iana"},"application/vnd.dvb.ipdcroaming":{source:"iana"},"application/vnd.dvb.iptv.alfec-base":{source:"iana"},"application/vnd.dvb.iptv.alfec-enhancement":{source:"iana"},"application/vnd.dvb.notif-aggregate-root+xml":{source:"iana",compressible:!0},"application/vnd.dvb.notif-container+xml":{source:"iana",compressible:!0},"application/vnd.dvb.notif-generic+xml":{source:"iana",compressible:!0},"application/vnd.dvb.notif-ia-msglist+xml":{source:"iana",compressible:!0},"application/vnd.dvb.notif-ia-registration-request+xml":{source:"iana",compressible:!0},"application/vnd.dvb.notif-ia-registration-response+xml":{source:"iana",compressible:!0},"application/vnd.dvb.notif-init+xml":{source:"iana",compressible:!0},"application/vnd.dvb.pfr":{source:"iana"},"application/vnd.dvb.service":{source:"iana",extensions:["svc"]},"application/vnd.dxr":{source:"iana"},"application/vnd.dynageo":{source:"iana",extensions:["geo"]},"application/vnd.dzr":{source:"iana"},"application/vnd.easykaraoke.cdgdownload":{source:"iana"},"application/vnd.ecdis-update":{source:"iana"},"application/vnd.ecip.rlp":{source:"iana"},"application/vnd.eclipse.ditto+json":{source:"iana",compressible:!0},"application/vnd.ecowin.chart":{source:"iana",extensions:["mag"]},"application/vnd.ecowin.filerequest":{source:"iana"},"application/vnd.ecowin.fileupdate":{source:"iana"},"application/vnd.ecowin.series":{source:"iana"},"application/vnd.ecowin.seriesrequest":{source:"iana"},"application/vnd.ecowin.seriesupdate":{source:"iana"},"application/vnd.efi.img":{source:"iana"},"application/vnd.efi.iso":{source:"iana"},"application/vnd.emclient.accessrequest+xml":{source:"iana",compressible:!0},"application/vnd.enliven":{source:"iana",extensions:["nml"]},"application/vnd.enphase.envoy":{source:"iana"},"application/vnd.eprints.data+xml":{source:"iana",compressible:!0},"application/vnd.epson.esf":{source:"iana",extensions:["esf"]},"application/vnd.epson.msf":{source:"iana",extensions:["msf"]},"application/vnd.epson.quickanime":{source:"iana",extensions:["qam"]},"application/vnd.epson.salt":{source:"iana",extensions:["slt"]},"application/vnd.epson.ssf":{source:"iana",extensions:["ssf"]},"application/vnd.ericsson.quickcall":{source:"iana"},"application/vnd.espass-espass+zip":{source:"iana",compressible:!1},"application/vnd.eszigno3+xml":{source:"iana",compressible:!0,extensions:["es3","et3"]},"application/vnd.etsi.aoc+xml":{source:"iana",compressible:!0},"application/vnd.etsi.asic-e+zip":{source:"iana",compressible:!1},"application/vnd.etsi.asic-s+zip":{source:"iana",compressible:!1},"application/vnd.etsi.cug+xml":{source:"iana",compressible:!0},"application/vnd.etsi.iptvcommand+xml":{source:"iana",compressible:!0},"application/vnd.etsi.iptvdiscovery+xml":{source:"iana",compressible:!0},"application/vnd.etsi.iptvprofile+xml":{source:"iana",compressible:!0},"application/vnd.etsi.iptvsad-bc+xml":{source:"iana",compressible:!0},"application/vnd.etsi.iptvsad-cod+xml":{source:"iana",compressible:!0},"application/vnd.etsi.iptvsad-npvr+xml":{source:"iana",compressible:!0},"application/vnd.etsi.iptvservice+xml":{source:"iana",compressible:!0},"application/vnd.etsi.iptvsync+xml":{source:"iana",compressible:!0},"application/vnd.etsi.iptvueprofile+xml":{source:"iana",compressible:!0},"application/vnd.etsi.mcid+xml":{source:"iana",compressible:!0},"application/vnd.etsi.mheg5":{source:"iana"},"application/vnd.etsi.overload-control-policy-dataset+xml":{source:"iana",compressible:!0},"application/vnd.etsi.pstn+xml":{source:"iana",compressible:!0},"application/vnd.etsi.sci+xml":{source:"iana",compressible:!0},"application/vnd.etsi.simservs+xml":{source:"iana",compressible:!0},"application/vnd.etsi.timestamp-token":{source:"iana"},"application/vnd.etsi.tsl+xml":{source:"iana",compressible:!0},"application/vnd.etsi.tsl.der":{source:"iana"},"application/vnd.eu.kasparian.car+json":{source:"iana",compressible:!0},"application/vnd.eudora.data":{source:"iana"},"application/vnd.evolv.ecig.profile":{source:"iana"},"application/vnd.evolv.ecig.settings":{source:"iana"},"application/vnd.evolv.ecig.theme":{source:"iana"},"application/vnd.exstream-empower+zip":{source:"iana",compressible:!1},"application/vnd.exstream-package":{source:"iana"},"application/vnd.ezpix-album":{source:"iana",extensions:["ez2"]},"application/vnd.ezpix-package":{source:"iana",extensions:["ez3"]},"application/vnd.f-secure.mobile":{source:"iana"},"application/vnd.familysearch.gedcom+zip":{source:"iana",compressible:!1},"application/vnd.fastcopy-disk-image":{source:"iana"},"application/vnd.fdf":{source:"iana",extensions:["fdf"]},"application/vnd.fdsn.mseed":{source:"iana",extensions:["mseed"]},"application/vnd.fdsn.seed":{source:"iana",extensions:["seed","dataless"]},"application/vnd.ffsns":{source:"iana"},"application/vnd.ficlab.flb+zip":{source:"iana",compressible:!1},"application/vnd.filmit.zfc":{source:"iana"},"application/vnd.fints":{source:"iana"},"application/vnd.firemonkeys.cloudcell":{source:"iana"},"application/vnd.flographit":{source:"iana",extensions:["gph"]},"application/vnd.fluxtime.clip":{source:"iana",extensions:["ftc"]},"application/vnd.font-fontforge-sfd":{source:"iana"},"application/vnd.framemaker":{source:"iana",extensions:["fm","frame","maker","book"]},"application/vnd.frogans.fnc":{source:"iana",extensions:["fnc"]},"application/vnd.frogans.ltf":{source:"iana",extensions:["ltf"]},"application/vnd.fsc.weblaunch":{source:"iana",extensions:["fsc"]},"application/vnd.fujifilm.fb.docuworks":{source:"iana"},"application/vnd.fujifilm.fb.docuworks.binder":{source:"iana"},"application/vnd.fujifilm.fb.docuworks.container":{source:"iana"},"application/vnd.fujifilm.fb.jfi+xml":{source:"iana",compressible:!0},"application/vnd.fujitsu.oasys":{source:"iana",extensions:["oas"]},"application/vnd.fujitsu.oasys2":{source:"iana",extensions:["oa2"]},"application/vnd.fujitsu.oasys3":{source:"iana",extensions:["oa3"]},"application/vnd.fujitsu.oasysgp":{source:"iana",extensions:["fg5"]},"application/vnd.fujitsu.oasysprs":{source:"iana",extensions:["bh2"]},"application/vnd.fujixerox.art-ex":{source:"iana"},"application/vnd.fujixerox.art4":{source:"iana"},"application/vnd.fujixerox.ddd":{source:"iana",extensions:["ddd"]},"application/vnd.fujixerox.docuworks":{source:"iana",extensions:["xdw"]},"application/vnd.fujixerox.docuworks.binder":{source:"iana",extensions:["xbd"]},"application/vnd.fujixerox.docuworks.container":{source:"iana"},"application/vnd.fujixerox.hbpl":{source:"iana"},"application/vnd.fut-misnet":{source:"iana"},"application/vnd.futoin+cbor":{source:"iana"},"application/vnd.futoin+json":{source:"iana",compressible:!0},"application/vnd.fuzzysheet":{source:"iana",extensions:["fzs"]},"application/vnd.genomatix.tuxedo":{source:"iana",extensions:["txd"]},"application/vnd.gentics.grd+json":{source:"iana",compressible:!0},"application/vnd.geo+json":{source:"iana",compressible:!0},"application/vnd.geocube+xml":{source:"iana",compressible:!0},"application/vnd.geogebra.file":{source:"iana",extensions:["ggb"]},"application/vnd.geogebra.slides":{source:"iana"},"application/vnd.geogebra.tool":{source:"iana",extensions:["ggt"]},"application/vnd.geometry-explorer":{source:"iana",extensions:["gex","gre"]},"application/vnd.geonext":{source:"iana",extensions:["gxt"]},"application/vnd.geoplan":{source:"iana",extensions:["g2w"]},"application/vnd.geospace":{source:"iana",extensions:["g3w"]},"application/vnd.gerber":{source:"iana"},"application/vnd.globalplatform.card-content-mgt":{source:"iana"},"application/vnd.globalplatform.card-content-mgt-response":{source:"iana"},"application/vnd.gmx":{source:"iana",extensions:["gmx"]},"application/vnd.google-apps.document":{compressible:!1,extensions:["gdoc"]},"application/vnd.google-apps.presentation":{compressible:!1,extensions:["gslides"]},"application/vnd.google-apps.spreadsheet":{compressible:!1,extensions:["gsheet"]},"application/vnd.google-earth.kml+xml":{source:"iana",compressible:!0,extensions:["kml"]},"application/vnd.google-earth.kmz":{source:"iana",compressible:!1,extensions:["kmz"]},"application/vnd.gov.sk.e-form+xml":{source:"iana",compressible:!0},"application/vnd.gov.sk.e-form+zip":{source:"iana",compressible:!1},"application/vnd.gov.sk.xmldatacontainer+xml":{source:"iana",compressible:!0},"application/vnd.grafeq":{source:"iana",extensions:["gqf","gqs"]},"application/vnd.gridmp":{source:"iana"},"application/vnd.groove-account":{source:"iana",extensions:["gac"]},"application/vnd.groove-help":{source:"iana",extensions:["ghf"]},"application/vnd.groove-identity-message":{source:"iana",extensions:["gim"]},"application/vnd.groove-injector":{source:"iana",extensions:["grv"]},"application/vnd.groove-tool-message":{source:"iana",extensions:["gtm"]},"application/vnd.groove-tool-template":{source:"iana",extensions:["tpl"]},"application/vnd.groove-vcard":{source:"iana",extensions:["vcg"]},"application/vnd.hal+json":{source:"iana",compressible:!0},"application/vnd.hal+xml":{source:"iana",compressible:!0,extensions:["hal"]},"application/vnd.handheld-entertainment+xml":{source:"iana",compressible:!0,extensions:["zmm"]},"application/vnd.hbci":{source:"iana",extensions:["hbci"]},"application/vnd.hc+json":{source:"iana",compressible:!0},"application/vnd.hcl-bireports":{source:"iana"},"application/vnd.hdt":{source:"iana"},"application/vnd.heroku+json":{source:"iana",compressible:!0},"application/vnd.hhe.lesson-player":{source:"iana",extensions:["les"]},"application/vnd.hl7cda+xml":{source:"iana",charset:"UTF-8",compressible:!0},"application/vnd.hl7v2+xml":{source:"iana",charset:"UTF-8",compressible:!0},"application/vnd.hp-hpgl":{source:"iana",extensions:["hpgl"]},"application/vnd.hp-hpid":{source:"iana",extensions:["hpid"]},"application/vnd.hp-hps":{source:"iana",extensions:["hps"]},"application/vnd.hp-jlyt":{source:"iana",extensions:["jlt"]},"application/vnd.hp-pcl":{source:"iana",extensions:["pcl"]},"application/vnd.hp-pclxl":{source:"iana",extensions:["pclxl"]},"application/vnd.httphone":{source:"iana"},"application/vnd.hydrostatix.sof-data":{source:"iana",extensions:["sfd-hdstx"]},"application/vnd.hyper+json":{source:"iana",compressible:!0},"application/vnd.hyper-item+json":{source:"iana",compressible:!0},"application/vnd.hyperdrive+json":{source:"iana",compressible:!0},"application/vnd.hzn-3d-crossword":{source:"iana"},"application/vnd.ibm.afplinedata":{source:"iana"},"application/vnd.ibm.electronic-media":{source:"iana"},"application/vnd.ibm.minipay":{source:"iana",extensions:["mpy"]},"application/vnd.ibm.modcap":{source:"iana",extensions:["afp","listafp","list3820"]},"application/vnd.ibm.rights-management":{source:"iana",extensions:["irm"]},"application/vnd.ibm.secure-container":{source:"iana",extensions:["sc"]},"application/vnd.iccprofile":{source:"iana",extensions:["icc","icm"]},"application/vnd.ieee.1905":{source:"iana"},"application/vnd.igloader":{source:"iana",extensions:["igl"]},"application/vnd.imagemeter.folder+zip":{source:"iana",compressible:!1},"application/vnd.imagemeter.image+zip":{source:"iana",compressible:!1},"application/vnd.immervision-ivp":{source:"iana",extensions:["ivp"]},"application/vnd.immervision-ivu":{source:"iana",extensions:["ivu"]},"application/vnd.ims.imsccv1p1":{source:"iana"},"application/vnd.ims.imsccv1p2":{source:"iana"},"application/vnd.ims.imsccv1p3":{source:"iana"},"application/vnd.ims.lis.v2.result+json":{source:"iana",compressible:!0},"application/vnd.ims.lti.v2.toolconsumerprofile+json":{source:"iana",compressible:!0},"application/vnd.ims.lti.v2.toolproxy+json":{source:"iana",compressible:!0},"application/vnd.ims.lti.v2.toolproxy.id+json":{source:"iana",compressible:!0},"application/vnd.ims.lti.v2.toolsettings+json":{source:"iana",compressible:!0},"application/vnd.ims.lti.v2.toolsettings.simple+json":{source:"iana",compressible:!0},"application/vnd.informedcontrol.rms+xml":{source:"iana",compressible:!0},"application/vnd.informix-visionary":{source:"iana"},"application/vnd.infotech.project":{source:"iana"},"application/vnd.infotech.project+xml":{source:"iana",compressible:!0},"application/vnd.innopath.wamp.notification":{source:"iana"},"application/vnd.insors.igm":{source:"iana",extensions:["igm"]},"application/vnd.intercon.formnet":{source:"iana",extensions:["xpw","xpx"]},"application/vnd.intergeo":{source:"iana",extensions:["i2g"]},"application/vnd.intertrust.digibox":{source:"iana"},"application/vnd.intertrust.nncp":{source:"iana"},"application/vnd.intu.qbo":{source:"iana",extensions:["qbo"]},"application/vnd.intu.qfx":{source:"iana",extensions:["qfx"]},"application/vnd.iptc.g2.catalogitem+xml":{source:"iana",compressible:!0},"application/vnd.iptc.g2.conceptitem+xml":{source:"iana",compressible:!0},"application/vnd.iptc.g2.knowledgeitem+xml":{source:"iana",compressible:!0},"application/vnd.iptc.g2.newsitem+xml":{source:"iana",compressible:!0},"application/vnd.iptc.g2.newsmessage+xml":{source:"iana",compressible:!0},"application/vnd.iptc.g2.packageitem+xml":{source:"iana",compressible:!0},"application/vnd.iptc.g2.planningitem+xml":{source:"iana",compressible:!0},"application/vnd.ipunplugged.rcprofile":{source:"iana",extensions:["rcprofile"]},"application/vnd.irepository.package+xml":{source:"iana",compressible:!0,extensions:["irp"]},"application/vnd.is-xpr":{source:"iana",extensions:["xpr"]},"application/vnd.isac.fcs":{source:"iana",extensions:["fcs"]},"application/vnd.iso11783-10+zip":{source:"iana",compressible:!1},"application/vnd.jam":{source:"iana",extensions:["jam"]},"application/vnd.japannet-directory-service":{source:"iana"},"application/vnd.japannet-jpnstore-wakeup":{source:"iana"},"application/vnd.japannet-payment-wakeup":{source:"iana"},"application/vnd.japannet-registration":{source:"iana"},"application/vnd.japannet-registration-wakeup":{source:"iana"},"application/vnd.japannet-setstore-wakeup":{source:"iana"},"application/vnd.japannet-verification":{source:"iana"},"application/vnd.japannet-verification-wakeup":{source:"iana"},"application/vnd.jcp.javame.midlet-rms":{source:"iana",extensions:["rms"]},"application/vnd.jisp":{source:"iana",extensions:["jisp"]},"application/vnd.joost.joda-archive":{source:"iana",extensions:["joda"]},"application/vnd.jsk.isdn-ngn":{source:"iana"},"application/vnd.kahootz":{source:"iana",extensions:["ktz","ktr"]},"application/vnd.kde.karbon":{source:"iana",extensions:["karbon"]},"application/vnd.kde.kchart":{source:"iana",extensions:["chrt"]},"application/vnd.kde.kformula":{source:"iana",extensions:["kfo"]},"application/vnd.kde.kivio":{source:"iana",extensions:["flw"]},"application/vnd.kde.kontour":{source:"iana",extensions:["kon"]},"application/vnd.kde.kpresenter":{source:"iana",extensions:["kpr","kpt"]},"application/vnd.kde.kspread":{source:"iana",extensions:["ksp"]},"application/vnd.kde.kword":{source:"iana",extensions:["kwd","kwt"]},"application/vnd.kenameaapp":{source:"iana",extensions:["htke"]},"application/vnd.kidspiration":{source:"iana",extensions:["kia"]},"application/vnd.kinar":{source:"iana",extensions:["kne","knp"]},"application/vnd.koan":{source:"iana",extensions:["skp","skd","skt","skm"]},"application/vnd.kodak-descriptor":{source:"iana",extensions:["sse"]},"application/vnd.las":{source:"iana"},"application/vnd.las.las+json":{source:"iana",compressible:!0},"application/vnd.las.las+xml":{source:"iana",compressible:!0,extensions:["lasxml"]},"application/vnd.laszip":{source:"iana"},"application/vnd.leap+json":{source:"iana",compressible:!0},"application/vnd.liberty-request+xml":{source:"iana",compressible:!0},"application/vnd.llamagraphics.life-balance.desktop":{source:"iana",extensions:["lbd"]},"application/vnd.llamagraphics.life-balance.exchange+xml":{source:"iana",compressible:!0,extensions:["lbe"]},"application/vnd.logipipe.circuit+zip":{source:"iana",compressible:!1},"application/vnd.loom":{source:"iana"},"application/vnd.lotus-1-2-3":{source:"iana",extensions:["123"]},"application/vnd.lotus-approach":{source:"iana",extensions:["apr"]},"application/vnd.lotus-freelance":{source:"iana",extensions:["pre"]},"application/vnd.lotus-notes":{source:"iana",extensions:["nsf"]},"application/vnd.lotus-organizer":{source:"iana",extensions:["org"]},"application/vnd.lotus-screencam":{source:"iana",extensions:["scm"]},"application/vnd.lotus-wordpro":{source:"iana",extensions:["lwp"]},"application/vnd.macports.portpkg":{source:"iana",extensions:["portpkg"]},"application/vnd.mapbox-vector-tile":{source:"iana",extensions:["mvt"]},"application/vnd.marlin.drm.actiontoken+xml":{source:"iana",compressible:!0},"application/vnd.marlin.drm.conftoken+xml":{source:"iana",compressible:!0},"application/vnd.marlin.drm.license+xml":{source:"iana",compressible:!0},"application/vnd.marlin.drm.mdcf":{source:"iana"},"application/vnd.mason+json":{source:"iana",compressible:!0},"application/vnd.maxar.archive.3tz+zip":{source:"iana",compressible:!1},"application/vnd.maxmind.maxmind-db":{source:"iana"},"application/vnd.mcd":{source:"iana",extensions:["mcd"]},"application/vnd.medcalcdata":{source:"iana",extensions:["mc1"]},"application/vnd.mediastation.cdkey":{source:"iana",extensions:["cdkey"]},"application/vnd.meridian-slingshot":{source:"iana"},"application/vnd.mfer":{source:"iana",extensions:["mwf"]},"application/vnd.mfmp":{source:"iana",extensions:["mfm"]},"application/vnd.micro+json":{source:"iana",compressible:!0},"application/vnd.micrografx.flo":{source:"iana",extensions:["flo"]},"application/vnd.micrografx.igx":{source:"iana",extensions:["igx"]},"application/vnd.microsoft.portable-executable":{source:"iana"},"application/vnd.microsoft.windows.thumbnail-cache":{source:"iana"},"application/vnd.miele+json":{source:"iana",compressible:!0},"application/vnd.mif":{source:"iana",extensions:["mif"]},"application/vnd.minisoft-hp3000-save":{source:"iana"},"application/vnd.mitsubishi.misty-guard.trustweb":{source:"iana"},"application/vnd.mobius.daf":{source:"iana",extensions:["daf"]},"application/vnd.mobius.dis":{source:"iana",extensions:["dis"]},"application/vnd.mobius.mbk":{source:"iana",extensions:["mbk"]},"application/vnd.mobius.mqy":{source:"iana",extensions:["mqy"]},"application/vnd.mobius.msl":{source:"iana",extensions:["msl"]},"application/vnd.mobius.plc":{source:"iana",extensions:["plc"]},"application/vnd.mobius.txf":{source:"iana",extensions:["txf"]},"application/vnd.mophun.application":{source:"iana",extensions:["mpn"]},"application/vnd.mophun.certificate":{source:"iana",extensions:["mpc"]},"application/vnd.motorola.flexsuite":{source:"iana"},"application/vnd.motorola.flexsuite.adsi":{source:"iana"},"application/vnd.motorola.flexsuite.fis":{source:"iana"},"application/vnd.motorola.flexsuite.gotap":{source:"iana"},"application/vnd.motorola.flexsuite.kmr":{source:"iana"},"application/vnd.motorola.flexsuite.ttc":{source:"iana"},"application/vnd.motorola.flexsuite.wem":{source:"iana"},"application/vnd.motorola.iprm":{source:"iana"},"application/vnd.mozilla.xul+xml":{source:"iana",compressible:!0,extensions:["xul"]},"application/vnd.ms-3mfdocument":{source:"iana"},"application/vnd.ms-artgalry":{source:"iana",extensions:["cil"]},"application/vnd.ms-asf":{source:"iana"},"application/vnd.ms-cab-compressed":{source:"iana",extensions:["cab"]},"application/vnd.ms-color.iccprofile":{source:"apache"},"application/vnd.ms-excel":{source:"iana",compressible:!1,extensions:["xls","xlm","xla","xlc","xlt","xlw"]},"application/vnd.ms-excel.addin.macroenabled.12":{source:"iana",extensions:["xlam"]},"application/vnd.ms-excel.sheet.binary.macroenabled.12":{source:"iana",extensions:["xlsb"]},"application/vnd.ms-excel.sheet.macroenabled.12":{source:"iana",extensions:["xlsm"]},"application/vnd.ms-excel.template.macroenabled.12":{source:"iana",extensions:["xltm"]},"application/vnd.ms-fontobject":{source:"iana",compressible:!0,extensions:["eot"]},"application/vnd.ms-htmlhelp":{source:"iana",extensions:["chm"]},"application/vnd.ms-ims":{source:"iana",extensions:["ims"]},"application/vnd.ms-lrm":{source:"iana",extensions:["lrm"]},"application/vnd.ms-office.activex+xml":{source:"iana",compressible:!0},"application/vnd.ms-officetheme":{source:"iana",extensions:["thmx"]},"application/vnd.ms-opentype":{source:"apache",compressible:!0},"application/vnd.ms-outlook":{compressible:!1,extensions:["msg"]},"application/vnd.ms-package.obfuscated-opentype":{source:"apache"},"application/vnd.ms-pki.seccat":{source:"apache",extensions:["cat"]},"application/vnd.ms-pki.stl":{source:"apache",extensions:["stl"]},"application/vnd.ms-playready.initiator+xml":{source:"iana",compressible:!0},"application/vnd.ms-powerpoint":{source:"iana",compressible:!1,extensions:["ppt","pps","pot"]},"application/vnd.ms-powerpoint.addin.macroenabled.12":{source:"iana",extensions:["ppam"]},"application/vnd.ms-powerpoint.presentation.macroenabled.12":{source:"iana",extensions:["pptm"]},"application/vnd.ms-powerpoint.slide.macroenabled.12":{source:"iana",extensions:["sldm"]},"application/vnd.ms-powerpoint.slideshow.macroenabled.12":{source:"iana",extensions:["ppsm"]},"application/vnd.ms-powerpoint.template.macroenabled.12":{source:"iana",extensions:["potm"]},"application/vnd.ms-printdevicecapabilities+xml":{source:"iana",compressible:!0},"application/vnd.ms-printing.printticket+xml":{source:"apache",compressible:!0},"application/vnd.ms-printschematicket+xml":{source:"iana",compressible:!0},"application/vnd.ms-project":{source:"iana",extensions:["mpp","mpt"]},"application/vnd.ms-tnef":{source:"iana"},"application/vnd.ms-windows.devicepairing":{source:"iana"},"application/vnd.ms-windows.nwprinting.oob":{source:"iana"},"application/vnd.ms-windows.printerpairing":{source:"iana"},"application/vnd.ms-windows.wsd.oob":{source:"iana"},"application/vnd.ms-wmdrm.lic-chlg-req":{source:"iana"},"application/vnd.ms-wmdrm.lic-resp":{source:"iana"},"application/vnd.ms-wmdrm.meter-chlg-req":{source:"iana"},"application/vnd.ms-wmdrm.meter-resp":{source:"iana"},"application/vnd.ms-word.document.macroenabled.12":{source:"iana",extensions:["docm"]},"application/vnd.ms-word.template.macroenabled.12":{source:"iana",extensions:["dotm"]},"application/vnd.ms-works":{source:"iana",extensions:["wps","wks","wcm","wdb"]},"application/vnd.ms-wpl":{source:"iana",extensions:["wpl"]},"application/vnd.ms-xpsdocument":{source:"iana",compressible:!1,extensions:["xps"]},"application/vnd.msa-disk-image":{source:"iana"},"application/vnd.mseq":{source:"iana",extensions:["mseq"]},"application/vnd.msign":{source:"iana"},"application/vnd.multiad.creator":{source:"iana"},"application/vnd.multiad.creator.cif":{source:"iana"},"application/vnd.music-niff":{source:"iana"},"application/vnd.musician":{source:"iana",extensions:["mus"]},"application/vnd.muvee.style":{source:"iana",extensions:["msty"]},"application/vnd.mynfc":{source:"iana",extensions:["taglet"]},"application/vnd.nacamar.ybrid+json":{source:"iana",compressible:!0},"application/vnd.ncd.control":{source:"iana"},"application/vnd.ncd.reference":{source:"iana"},"application/vnd.nearst.inv+json":{source:"iana",compressible:!0},"application/vnd.nebumind.line":{source:"iana"},"application/vnd.nervana":{source:"iana"},"application/vnd.netfpx":{source:"iana"},"application/vnd.neurolanguage.nlu":{source:"iana",extensions:["nlu"]},"application/vnd.nimn":{source:"iana"},"application/vnd.nintendo.nitro.rom":{source:"iana"},"application/vnd.nintendo.snes.rom":{source:"iana"},"application/vnd.nitf":{source:"iana",extensions:["ntf","nitf"]},"application/vnd.noblenet-directory":{source:"iana",extensions:["nnd"]},"application/vnd.noblenet-sealer":{source:"iana",extensions:["nns"]},"application/vnd.noblenet-web":{source:"iana",extensions:["nnw"]},"application/vnd.nokia.catalogs":{source:"iana"},"application/vnd.nokia.conml+wbxml":{source:"iana"},"application/vnd.nokia.conml+xml":{source:"iana",compressible:!0},"application/vnd.nokia.iptv.config+xml":{source:"iana",compressible:!0},"application/vnd.nokia.isds-radio-presets":{source:"iana"},"application/vnd.nokia.landmark+wbxml":{source:"iana"},"application/vnd.nokia.landmark+xml":{source:"iana",compressible:!0},"application/vnd.nokia.landmarkcollection+xml":{source:"iana",compressible:!0},"application/vnd.nokia.n-gage.ac+xml":{source:"iana",compressible:!0,extensions:["ac"]},"application/vnd.nokia.n-gage.data":{source:"iana",extensions:["ngdat"]},"application/vnd.nokia.n-gage.symbian.install":{source:"iana",extensions:["n-gage"]},"application/vnd.nokia.ncd":{source:"iana"},"application/vnd.nokia.pcd+wbxml":{source:"iana"},"application/vnd.nokia.pcd+xml":{source:"iana",compressible:!0},"application/vnd.nokia.radio-preset":{source:"iana",extensions:["rpst"]},"application/vnd.nokia.radio-presets":{source:"iana",extensions:["rpss"]},"application/vnd.novadigm.edm":{source:"iana",extensions:["edm"]},"application/vnd.novadigm.edx":{source:"iana",extensions:["edx"]},"application/vnd.novadigm.ext":{source:"iana",extensions:["ext"]},"application/vnd.ntt-local.content-share":{source:"iana"},"application/vnd.ntt-local.file-transfer":{source:"iana"},"application/vnd.ntt-local.ogw_remote-access":{source:"iana"},"application/vnd.ntt-local.sip-ta_remote":{source:"iana"},"application/vnd.ntt-local.sip-ta_tcp_stream":{source:"iana"},"application/vnd.oasis.opendocument.chart":{source:"iana",extensions:["odc"]},"application/vnd.oasis.opendocument.chart-template":{source:"iana",extensions:["otc"]},"application/vnd.oasis.opendocument.database":{source:"iana",extensions:["odb"]},"application/vnd.oasis.opendocument.formula":{source:"iana",extensions:["odf"]},"application/vnd.oasis.opendocument.formula-template":{source:"iana",extensions:["odft"]},"application/vnd.oasis.opendocument.graphics":{source:"iana",compressible:!1,extensions:["odg"]},"application/vnd.oasis.opendocument.graphics-template":{source:"iana",extensions:["otg"]},"application/vnd.oasis.opendocument.image":{source:"iana",extensions:["odi"]},"application/vnd.oasis.opendocument.image-template":{source:"iana",extensions:["oti"]},"application/vnd.oasis.opendocument.presentation":{source:"iana",compressible:!1,extensions:["odp"]},"application/vnd.oasis.opendocument.presentation-template":{source:"iana",extensions:["otp"]},"application/vnd.oasis.opendocument.spreadsheet":{source:"iana",compressible:!1,extensions:["ods"]},"application/vnd.oasis.opendocument.spreadsheet-template":{source:"iana",extensions:["ots"]},"application/vnd.oasis.opendocument.text":{source:"iana",compressible:!1,extensions:["odt"]},"application/vnd.oasis.opendocument.text-master":{source:"iana",extensions:["odm"]},"application/vnd.oasis.opendocument.text-template":{source:"iana",extensions:["ott"]},"application/vnd.oasis.opendocument.text-web":{source:"iana",extensions:["oth"]},"application/vnd.obn":{source:"iana"},"application/vnd.ocf+cbor":{source:"iana"},"application/vnd.oci.image.manifest.v1+json":{source:"iana",compressible:!0},"application/vnd.oftn.l10n+json":{source:"iana",compressible:!0},"application/vnd.oipf.contentaccessdownload+xml":{source:"iana",compressible:!0},"application/vnd.oipf.contentaccessstreaming+xml":{source:"iana",compressible:!0},"application/vnd.oipf.cspg-hexbinary":{source:"iana"},"application/vnd.oipf.dae.svg+xml":{source:"iana",compressible:!0},"application/vnd.oipf.dae.xhtml+xml":{source:"iana",compressible:!0},"application/vnd.oipf.mippvcontrolmessage+xml":{source:"iana",compressible:!0},"application/vnd.oipf.pae.gem":{source:"iana"},"application/vnd.oipf.spdiscovery+xml":{source:"iana",compressible:!0},"application/vnd.oipf.spdlist+xml":{source:"iana",compressible:!0},"application/vnd.oipf.ueprofile+xml":{source:"iana",compressible:!0},"application/vnd.oipf.userprofile+xml":{source:"iana",compressible:!0},"application/vnd.olpc-sugar":{source:"iana",extensions:["xo"]},"application/vnd.oma-scws-config":{source:"iana"},"application/vnd.oma-scws-http-request":{source:"iana"},"application/vnd.oma-scws-http-response":{source:"iana"},"application/vnd.oma.bcast.associated-procedure-parameter+xml":{source:"iana",compressible:!0},"application/vnd.oma.bcast.drm-trigger+xml":{source:"iana",compressible:!0},"application/vnd.oma.bcast.imd+xml":{source:"iana",compressible:!0},"application/vnd.oma.bcast.ltkm":{source:"iana"},"application/vnd.oma.bcast.notification+xml":{source:"iana",compressible:!0},"application/vnd.oma.bcast.provisioningtrigger":{source:"iana"},"application/vnd.oma.bcast.sgboot":{source:"iana"},"application/vnd.oma.bcast.sgdd+xml":{source:"iana",compressible:!0},"application/vnd.oma.bcast.sgdu":{source:"iana"},"application/vnd.oma.bcast.simple-symbol-container":{source:"iana"},"application/vnd.oma.bcast.smartcard-trigger+xml":{source:"iana",compressible:!0},"application/vnd.oma.bcast.sprov+xml":{source:"iana",compressible:!0},"application/vnd.oma.bcast.stkm":{source:"iana"},"application/vnd.oma.cab-address-book+xml":{source:"iana",compressible:!0},"application/vnd.oma.cab-feature-handler+xml":{source:"iana",compressible:!0},"application/vnd.oma.cab-pcc+xml":{source:"iana",compressible:!0},"application/vnd.oma.cab-subs-invite+xml":{source:"iana",compressible:!0},"application/vnd.oma.cab-user-prefs+xml":{source:"iana",compressible:!0},"application/vnd.oma.dcd":{source:"iana"},"application/vnd.oma.dcdc":{source:"iana"},"application/vnd.oma.dd2+xml":{source:"iana",compressible:!0,extensions:["dd2"]},"application/vnd.oma.drm.risd+xml":{source:"iana",compressible:!0},"application/vnd.oma.group-usage-list+xml":{source:"iana",compressible:!0},"application/vnd.oma.lwm2m+cbor":{source:"iana"},"application/vnd.oma.lwm2m+json":{source:"iana",compressible:!0},"application/vnd.oma.lwm2m+tlv":{source:"iana"},"application/vnd.oma.pal+xml":{source:"iana",compressible:!0},"application/vnd.oma.poc.detailed-progress-report+xml":{source:"iana",compressible:!0},"application/vnd.oma.poc.final-report+xml":{source:"iana",compressible:!0},"application/vnd.oma.poc.groups+xml":{source:"iana",compressible:!0},"application/vnd.oma.poc.invocation-descriptor+xml":{source:"iana",compressible:!0},"application/vnd.oma.poc.optimized-progress-report+xml":{source:"iana",compressible:!0},"application/vnd.oma.push":{source:"iana"},"application/vnd.oma.scidm.messages+xml":{source:"iana",compressible:!0},"application/vnd.oma.xcap-directory+xml":{source:"iana",compressible:!0},"application/vnd.omads-email+xml":{source:"iana",charset:"UTF-8",compressible:!0},"application/vnd.omads-file+xml":{source:"iana",charset:"UTF-8",compressible:!0},"application/vnd.omads-folder+xml":{source:"iana",charset:"UTF-8",compressible:!0},"application/vnd.omaloc-supl-init":{source:"iana"},"application/vnd.onepager":{source:"iana"},"application/vnd.onepagertamp":{source:"iana"},"application/vnd.onepagertamx":{source:"iana"},"application/vnd.onepagertat":{source:"iana"},"application/vnd.onepagertatp":{source:"iana"},"application/vnd.onepagertatx":{source:"iana"},"application/vnd.openblox.game+xml":{source:"iana",compressible:!0,extensions:["obgx"]},"application/vnd.openblox.game-binary":{source:"iana"},"application/vnd.openeye.oeb":{source:"iana"},"application/vnd.openofficeorg.extension":{source:"apache",extensions:["oxt"]},"application/vnd.openstreetmap.data+xml":{source:"iana",compressible:!0,extensions:["osm"]},"application/vnd.opentimestamps.ots":{source:"iana"},"application/vnd.openxmlformats-officedocument.custom-properties+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.customxmlproperties+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.drawing+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.drawingml.chart+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.drawingml.chartshapes+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.drawingml.diagramcolors+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.drawingml.diagramdata+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.drawingml.diagramlayout+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.drawingml.diagramstyle+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.extended-properties+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.presentationml.commentauthors+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.presentationml.comments+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.presentationml.handoutmaster+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.presentationml.notesmaster+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.presentationml.notesslide+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.presentationml.presentation":{source:"iana",compressible:!1,extensions:["pptx"]},"application/vnd.openxmlformats-officedocument.presentationml.presentation.main+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.presentationml.presprops+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.presentationml.slide":{source:"iana",extensions:["sldx"]},"application/vnd.openxmlformats-officedocument.presentationml.slide+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.presentationml.slidelayout+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.presentationml.slidemaster+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.presentationml.slideshow":{source:"iana",extensions:["ppsx"]},"application/vnd.openxmlformats-officedocument.presentationml.slideshow.main+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.presentationml.slideupdateinfo+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.presentationml.tablestyles+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.presentationml.tags+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.presentationml.template":{source:"iana",extensions:["potx"]},"application/vnd.openxmlformats-officedocument.presentationml.template.main+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.presentationml.viewprops+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.spreadsheetml.calcchain+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.spreadsheetml.chartsheet+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.spreadsheetml.comments+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.spreadsheetml.connections+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.spreadsheetml.dialogsheet+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.spreadsheetml.externallink+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.spreadsheetml.pivotcachedefinition+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.spreadsheetml.pivotcacherecords+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.spreadsheetml.pivottable+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.spreadsheetml.querytable+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.spreadsheetml.revisionheaders+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.spreadsheetml.revisionlog+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.spreadsheetml.sharedstrings+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet":{source:"iana",compressible:!1,extensions:["xlsx"]},"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet.main+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.spreadsheetml.sheetmetadata+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.spreadsheetml.styles+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.spreadsheetml.table+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.spreadsheetml.tablesinglecells+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.spreadsheetml.template":{source:"iana",extensions:["xltx"]},"application/vnd.openxmlformats-officedocument.spreadsheetml.template.main+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.spreadsheetml.usernames+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.spreadsheetml.volatiledependencies+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.spreadsheetml.worksheet+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.theme+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.themeoverride+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.vmldrawing":{source:"iana"},"application/vnd.openxmlformats-officedocument.wordprocessingml.comments+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.wordprocessingml.document":{source:"iana",compressible:!1,extensions:["docx"]},"application/vnd.openxmlformats-officedocument.wordprocessingml.document.glossary+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.wordprocessingml.document.main+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.wordprocessingml.endnotes+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.wordprocessingml.fonttable+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.wordprocessingml.footer+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.wordprocessingml.footnotes+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.wordprocessingml.numbering+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.wordprocessingml.settings+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.wordprocessingml.styles+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.wordprocessingml.template":{source:"iana",extensions:["dotx"]},"application/vnd.openxmlformats-officedocument.wordprocessingml.template.main+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.wordprocessingml.websettings+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-package.core-properties+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-package.digital-signature-xmlsignature+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-package.relationships+xml":{source:"iana",compressible:!0},"application/vnd.oracle.resource+json":{source:"iana",compressible:!0},"application/vnd.orange.indata":{source:"iana"},"application/vnd.osa.netdeploy":{source:"iana"},"application/vnd.osgeo.mapguide.package":{source:"iana",extensions:["mgp"]},"application/vnd.osgi.bundle":{source:"iana"},"application/vnd.osgi.dp":{source:"iana",extensions:["dp"]},"application/vnd.osgi.subsystem":{source:"iana",extensions:["esa"]},"application/vnd.otps.ct-kip+xml":{source:"iana",compressible:!0},"application/vnd.oxli.countgraph":{source:"iana"},"application/vnd.pagerduty+json":{source:"iana",compressible:!0},"application/vnd.palm":{source:"iana",extensions:["pdb","pqa","oprc"]},"application/vnd.panoply":{source:"iana"},"application/vnd.paos.xml":{source:"iana"},"application/vnd.patentdive":{source:"iana"},"application/vnd.patientecommsdoc":{source:"iana"},"application/vnd.pawaafile":{source:"iana",extensions:["paw"]},"application/vnd.pcos":{source:"iana"},"application/vnd.pg.format":{source:"iana",extensions:["str"]},"application/vnd.pg.osasli":{source:"iana",extensions:["ei6"]},"application/vnd.piaccess.application-licence":{source:"iana"},"application/vnd.picsel":{source:"iana",extensions:["efif"]},"application/vnd.pmi.widget":{source:"iana",extensions:["wg"]},"application/vnd.poc.group-advertisement+xml":{source:"iana",compressible:!0},"application/vnd.pocketlearn":{source:"iana",extensions:["plf"]},"application/vnd.powerbuilder6":{source:"iana",extensions:["pbd"]},"application/vnd.powerbuilder6-s":{source:"iana"},"application/vnd.powerbuilder7":{source:"iana"},"application/vnd.powerbuilder7-s":{source:"iana"},"application/vnd.powerbuilder75":{source:"iana"},"application/vnd.powerbuilder75-s":{source:"iana"},"application/vnd.preminet":{source:"iana"},"application/vnd.previewsystems.box":{source:"iana",extensions:["box"]},"application/vnd.proteus.magazine":{source:"iana",extensions:["mgz"]},"application/vnd.psfs":{source:"iana"},"application/vnd.publishare-delta-tree":{source:"iana",extensions:["qps"]},"application/vnd.pvi.ptid1":{source:"iana",extensions:["ptid"]},"application/vnd.pwg-multiplexed":{source:"iana"},"application/vnd.pwg-xhtml-print+xml":{source:"iana",compressible:!0},"application/vnd.qualcomm.brew-app-res":{source:"iana"},"application/vnd.quarantainenet":{source:"iana"},"application/vnd.quark.quarkxpress":{source:"iana",extensions:["qxd","qxt","qwd","qwt","qxl","qxb"]},"application/vnd.quobject-quoxdocument":{source:"iana"},"application/vnd.radisys.moml+xml":{source:"iana",compressible:!0},"application/vnd.radisys.msml+xml":{source:"iana",compressible:!0},"application/vnd.radisys.msml-audit+xml":{source:"iana",compressible:!0},"application/vnd.radisys.msml-audit-conf+xml":{source:"iana",compressible:!0},"application/vnd.radisys.msml-audit-conn+xml":{source:"iana",compressible:!0},"application/vnd.radisys.msml-audit-dialog+xml":{source:"iana",compressible:!0},"application/vnd.radisys.msml-audit-stream+xml":{source:"iana",compressible:!0},"application/vnd.radisys.msml-conf+xml":{source:"iana",compressible:!0},"application/vnd.radisys.msml-dialog+xml":{source:"iana",compressible:!0},"application/vnd.radisys.msml-dialog-base+xml":{source:"iana",compressible:!0},"application/vnd.radisys.msml-dialog-fax-detect+xml":{source:"iana",compressible:!0},"application/vnd.radisys.msml-dialog-fax-sendrecv+xml":{source:"iana",compressible:!0},"application/vnd.radisys.msml-dialog-group+xml":{source:"iana",compressible:!0},"application/vnd.radisys.msml-dialog-speech+xml":{source:"iana",compressible:!0},"application/vnd.radisys.msml-dialog-transform+xml":{source:"iana",compressible:!0},"application/vnd.rainstor.data":{source:"iana"},"application/vnd.rapid":{source:"iana"},"application/vnd.rar":{source:"iana",extensions:["rar"]},"application/vnd.realvnc.bed":{source:"iana",extensions:["bed"]},"application/vnd.recordare.musicxml":{source:"iana",extensions:["mxl"]},"application/vnd.recordare.musicxml+xml":{source:"iana",compressible:!0,extensions:["musicxml"]},"application/vnd.renlearn.rlprint":{source:"iana"},"application/vnd.resilient.logic":{source:"iana"},"application/vnd.restful+json":{source:"iana",compressible:!0},"application/vnd.rig.cryptonote":{source:"iana",extensions:["cryptonote"]},"application/vnd.rim.cod":{source:"apache",extensions:["cod"]},"application/vnd.rn-realmedia":{source:"apache",extensions:["rm"]},"application/vnd.rn-realmedia-vbr":{source:"apache",extensions:["rmvb"]},"application/vnd.route66.link66+xml":{source:"iana",compressible:!0,extensions:["link66"]},"application/vnd.rs-274x":{source:"iana"},"application/vnd.ruckus.download":{source:"iana"},"application/vnd.s3sms":{source:"iana"},"application/vnd.sailingtracker.track":{source:"iana",extensions:["st"]},"application/vnd.sar":{source:"iana"},"application/vnd.sbm.cid":{source:"iana"},"application/vnd.sbm.mid2":{source:"iana"},"application/vnd.scribus":{source:"iana"},"application/vnd.sealed.3df":{source:"iana"},"application/vnd.sealed.csf":{source:"iana"},"application/vnd.sealed.doc":{source:"iana"},"application/vnd.sealed.eml":{source:"iana"},"application/vnd.sealed.mht":{source:"iana"},"application/vnd.sealed.net":{source:"iana"},"application/vnd.sealed.ppt":{source:"iana"},"application/vnd.sealed.tiff":{source:"iana"},"application/vnd.sealed.xls":{source:"iana"},"application/vnd.sealedmedia.softseal.html":{source:"iana"},"application/vnd.sealedmedia.softseal.pdf":{source:"iana"},"application/vnd.seemail":{source:"iana",extensions:["see"]},"application/vnd.seis+json":{source:"iana",compressible:!0},"application/vnd.sema":{source:"iana",extensions:["sema"]},"application/vnd.semd":{source:"iana",extensions:["semd"]},"application/vnd.semf":{source:"iana",extensions:["semf"]},"application/vnd.shade-save-file":{source:"iana"},"application/vnd.shana.informed.formdata":{source:"iana",extensions:["ifm"]},"application/vnd.shana.informed.formtemplate":{source:"iana",extensions:["itp"]},"application/vnd.shana.informed.interchange":{source:"iana",extensions:["iif"]},"application/vnd.shana.informed.package":{source:"iana",extensions:["ipk"]},"application/vnd.shootproof+json":{source:"iana",compressible:!0},"application/vnd.shopkick+json":{source:"iana",compressible:!0},"application/vnd.shp":{source:"iana"},"application/vnd.shx":{source:"iana"},"application/vnd.sigrok.session":{source:"iana"},"application/vnd.simtech-mindmapper":{source:"iana",extensions:["twd","twds"]},"application/vnd.siren+json":{source:"iana",compressible:!0},"application/vnd.smaf":{source:"iana",extensions:["mmf"]},"application/vnd.smart.notebook":{source:"iana"},"application/vnd.smart.teacher":{source:"iana",extensions:["teacher"]},"application/vnd.snesdev-page-table":{source:"iana"},"application/vnd.software602.filler.form+xml":{source:"iana",compressible:!0,extensions:["fo"]},"application/vnd.software602.filler.form-xml-zip":{source:"iana"},"application/vnd.solent.sdkm+xml":{source:"iana",compressible:!0,extensions:["sdkm","sdkd"]},"application/vnd.spotfire.dxp":{source:"iana",extensions:["dxp"]},"application/vnd.spotfire.sfs":{source:"iana",extensions:["sfs"]},"application/vnd.sqlite3":{source:"iana"},"application/vnd.sss-cod":{source:"iana"},"application/vnd.sss-dtf":{source:"iana"},"application/vnd.sss-ntf":{source:"iana"},"application/vnd.stardivision.calc":{source:"apache",extensions:["sdc"]},"application/vnd.stardivision.draw":{source:"apache",extensions:["sda"]},"application/vnd.stardivision.impress":{source:"apache",extensions:["sdd"]},"application/vnd.stardivision.math":{source:"apache",extensions:["smf"]},"application/vnd.stardivision.writer":{source:"apache",extensions:["sdw","vor"]},"application/vnd.stardivision.writer-global":{source:"apache",extensions:["sgl"]},"application/vnd.stepmania.package":{source:"iana",extensions:["smzip"]},"application/vnd.stepmania.stepchart":{source:"iana",extensions:["sm"]},"application/vnd.street-stream":{source:"iana"},"application/vnd.sun.wadl+xml":{source:"iana",compressible:!0,extensions:["wadl"]},"application/vnd.sun.xml.calc":{source:"apache",extensions:["sxc"]},"application/vnd.sun.xml.calc.template":{source:"apache",extensions:["stc"]},"application/vnd.sun.xml.draw":{source:"apache",extensions:["sxd"]},"application/vnd.sun.xml.draw.template":{source:"apache",extensions:["std"]},"application/vnd.sun.xml.impress":{source:"apache",extensions:["sxi"]},"application/vnd.sun.xml.impress.template":{source:"apache",extensions:["sti"]},"application/vnd.sun.xml.math":{source:"apache",extensions:["sxm"]},"application/vnd.sun.xml.writer":{source:"apache",extensions:["sxw"]},"application/vnd.sun.xml.writer.global":{source:"apache",extensions:["sxg"]},"application/vnd.sun.xml.writer.template":{source:"apache",extensions:["stw"]},"application/vnd.sus-calendar":{source:"iana",extensions:["sus","susp"]},"application/vnd.svd":{source:"iana",extensions:["svd"]},"application/vnd.swiftview-ics":{source:"iana"},"application/vnd.sycle+xml":{source:"iana",compressible:!0},"application/vnd.syft+json":{source:"iana",compressible:!0},"application/vnd.symbian.install":{source:"apache",extensions:["sis","sisx"]},"application/vnd.syncml+xml":{source:"iana",charset:"UTF-8",compressible:!0,extensions:["xsm"]},"application/vnd.syncml.dm+wbxml":{source:"iana",charset:"UTF-8",extensions:["bdm"]},"application/vnd.syncml.dm+xml":{source:"iana",charset:"UTF-8",compressible:!0,extensions:["xdm"]},"application/vnd.syncml.dm.notification":{source:"iana"},"application/vnd.syncml.dmddf+wbxml":{source:"iana"},"application/vnd.syncml.dmddf+xml":{source:"iana",charset:"UTF-8",compressible:!0,extensions:["ddf"]},"application/vnd.syncml.dmtnds+wbxml":{source:"iana"},"application/vnd.syncml.dmtnds+xml":{source:"iana",charset:"UTF-8",compressible:!0},"application/vnd.syncml.ds.notification":{source:"iana"},"application/vnd.tableschema+json":{source:"iana",compressible:!0},"application/vnd.tao.intent-module-archive":{source:"iana",extensions:["tao"]},"application/vnd.tcpdump.pcap":{source:"iana",extensions:["pcap","cap","dmp"]},"application/vnd.think-cell.ppttc+json":{source:"iana",compressible:!0},"application/vnd.tmd.mediaflex.api+xml":{source:"iana",compressible:!0},"application/vnd.tml":{source:"iana"},"application/vnd.tmobile-livetv":{source:"iana",extensions:["tmo"]},"application/vnd.tri.onesource":{source:"iana"},"application/vnd.trid.tpt":{source:"iana",extensions:["tpt"]},"application/vnd.triscape.mxs":{source:"iana",extensions:["mxs"]},"application/vnd.trueapp":{source:"iana",extensions:["tra"]},"application/vnd.truedoc":{source:"iana"},"application/vnd.ubisoft.webplayer":{source:"iana"},"application/vnd.ufdl":{source:"iana",extensions:["ufd","ufdl"]},"application/vnd.uiq.theme":{source:"iana",extensions:["utz"]},"application/vnd.umajin":{source:"iana",extensions:["umj"]},"application/vnd.unity":{source:"iana",extensions:["unityweb"]},"application/vnd.uoml+xml":{source:"iana",compressible:!0,extensions:["uoml"]},"application/vnd.uplanet.alert":{source:"iana"},"application/vnd.uplanet.alert-wbxml":{source:"iana"},"application/vnd.uplanet.bearer-choice":{source:"iana"},"application/vnd.uplanet.bearer-choice-wbxml":{source:"iana"},"application/vnd.uplanet.cacheop":{source:"iana"},"application/vnd.uplanet.cacheop-wbxml":{source:"iana"},"application/vnd.uplanet.channel":{source:"iana"},"application/vnd.uplanet.channel-wbxml":{source:"iana"},"application/vnd.uplanet.list":{source:"iana"},"application/vnd.uplanet.list-wbxml":{source:"iana"},"application/vnd.uplanet.listcmd":{source:"iana"},"application/vnd.uplanet.listcmd-wbxml":{source:"iana"},"application/vnd.uplanet.signal":{source:"iana"},"application/vnd.uri-map":{source:"iana"},"application/vnd.valve.source.material":{source:"iana"},"application/vnd.vcx":{source:"iana",extensions:["vcx"]},"application/vnd.vd-study":{source:"iana"},"application/vnd.vectorworks":{source:"iana"},"application/vnd.vel+json":{source:"iana",compressible:!0},"application/vnd.verimatrix.vcas":{source:"iana"},"application/vnd.veritone.aion+json":{source:"iana",compressible:!0},"application/vnd.veryant.thin":{source:"iana"},"application/vnd.ves.encrypted":{source:"iana"},"application/vnd.vidsoft.vidconference":{source:"iana"},"application/vnd.visio":{source:"iana",extensions:["vsd","vst","vss","vsw"]},"application/vnd.visionary":{source:"iana",extensions:["vis"]},"application/vnd.vividence.scriptfile":{source:"iana"},"application/vnd.vsf":{source:"iana",extensions:["vsf"]},"application/vnd.wap.sic":{source:"iana"},"application/vnd.wap.slc":{source:"iana"},"application/vnd.wap.wbxml":{source:"iana",charset:"UTF-8",extensions:["wbxml"]},"application/vnd.wap.wmlc":{source:"iana",extensions:["wmlc"]},"application/vnd.wap.wmlscriptc":{source:"iana",extensions:["wmlsc"]},"application/vnd.webturbo":{source:"iana",extensions:["wtb"]},"application/vnd.wfa.dpp":{source:"iana"},"application/vnd.wfa.p2p":{source:"iana"},"application/vnd.wfa.wsc":{source:"iana"},"application/vnd.windows.devicepairing":{source:"iana"},"application/vnd.wmc":{source:"iana"},"application/vnd.wmf.bootstrap":{source:"iana"},"application/vnd.wolfram.mathematica":{source:"iana"},"application/vnd.wolfram.mathematica.package":{source:"iana"},"application/vnd.wolfram.player":{source:"iana",extensions:["nbp"]},"application/vnd.wordperfect":{source:"iana",extensions:["wpd"]},"application/vnd.wqd":{source:"iana",extensions:["wqd"]},"application/vnd.wrq-hp3000-labelled":{source:"iana"},"application/vnd.wt.stf":{source:"iana",extensions:["stf"]},"application/vnd.wv.csp+wbxml":{source:"iana"},"application/vnd.wv.csp+xml":{source:"iana",compressible:!0},"application/vnd.wv.ssp+xml":{source:"iana",compressible:!0},"application/vnd.xacml+json":{source:"iana",compressible:!0},"application/vnd.xara":{source:"iana",extensions:["xar"]},"application/vnd.xfdl":{source:"iana",extensions:["xfdl"]},"application/vnd.xfdl.webform":{source:"iana"},"application/vnd.xmi+xml":{source:"iana",compressible:!0},"application/vnd.xmpie.cpkg":{source:"iana"},"application/vnd.xmpie.dpkg":{source:"iana"},"application/vnd.xmpie.plan":{source:"iana"},"application/vnd.xmpie.ppkg":{source:"iana"},"application/vnd.xmpie.xlim":{source:"iana"},"application/vnd.yamaha.hv-dic":{source:"iana",extensions:["hvd"]},"application/vnd.yamaha.hv-script":{source:"iana",extensions:["hvs"]},"application/vnd.yamaha.hv-voice":{source:"iana",extensions:["hvp"]},"application/vnd.yamaha.openscoreformat":{source:"iana",extensions:["osf"]},"application/vnd.yamaha.openscoreformat.osfpvg+xml":{source:"iana",compressible:!0,extensions:["osfpvg"]},"application/vnd.yamaha.remote-setup":{source:"iana"},"application/vnd.yamaha.smaf-audio":{source:"iana",extensions:["saf"]},"application/vnd.yamaha.smaf-phrase":{source:"iana",extensions:["spf"]},"application/vnd.yamaha.through-ngn":{source:"iana"},"application/vnd.yamaha.tunnel-udpencap":{source:"iana"},"application/vnd.yaoweme":{source:"iana"},"application/vnd.yellowriver-custom-menu":{source:"iana",extensions:["cmp"]},"application/vnd.youtube.yt":{source:"iana"},"application/vnd.zul":{source:"iana",extensions:["zir","zirz"]},"application/vnd.zzazz.deck+xml":{source:"iana",compressible:!0,extensions:["zaz"]},"application/voicexml+xml":{source:"iana",compressible:!0,extensions:["vxml"]},"application/voucher-cms+json":{source:"iana",compressible:!0},"application/vq-rtcpxr":{source:"iana"},"application/wasm":{source:"iana",compressible:!0,extensions:["wasm"]},"application/watcherinfo+xml":{source:"iana",compressible:!0,extensions:["wif"]},"application/webpush-options+json":{source:"iana",compressible:!0},"application/whoispp-query":{source:"iana"},"application/whoispp-response":{source:"iana"},"application/widget":{source:"iana",extensions:["wgt"]},"application/winhlp":{source:"apache",extensions:["hlp"]},"application/wita":{source:"iana"},"application/wordperfect5.1":{source:"iana"},"application/wsdl+xml":{source:"iana",compressible:!0,extensions:["wsdl"]},"application/wspolicy+xml":{source:"iana",compressible:!0,extensions:["wspolicy"]},"application/x-7z-compressed":{source:"apache",compressible:!1,extensions:["7z"]},"application/x-abiword":{source:"apache",extensions:["abw"]},"application/x-ace-compressed":{source:"apache",extensions:["ace"]},"application/x-amf":{source:"apache"},"application/x-apple-diskimage":{source:"apache",extensions:["dmg"]},"application/x-arj":{compressible:!1,extensions:["arj"]},"application/x-authorware-bin":{source:"apache",extensions:["aab","x32","u32","vox"]},"application/x-authorware-map":{source:"apache",extensions:["aam"]},"application/x-authorware-seg":{source:"apache",extensions:["aas"]},"application/x-bcpio":{source:"apache",extensions:["bcpio"]},"application/x-bdoc":{compressible:!1,extensions:["bdoc"]},"application/x-bittorrent":{source:"apache",extensions:["torrent"]},"application/x-blorb":{source:"apache",extensions:["blb","blorb"]},"application/x-bzip":{source:"apache",compressible:!1,extensions:["bz"]},"application/x-bzip2":{source:"apache",compressible:!1,extensions:["bz2","boz"]},"application/x-cbr":{source:"apache",extensions:["cbr","cba","cbt","cbz","cb7"]},"application/x-cdlink":{source:"apache",extensions:["vcd"]},"application/x-cfs-compressed":{source:"apache",extensions:["cfs"]},"application/x-chat":{source:"apache",extensions:["chat"]},"application/x-chess-pgn":{source:"apache",extensions:["pgn"]},"application/x-chrome-extension":{extensions:["crx"]},"application/x-cocoa":{source:"nginx",extensions:["cco"]},"application/x-compress":{source:"apache"},"application/x-conference":{source:"apache",extensions:["nsc"]},"application/x-cpio":{source:"apache",extensions:["cpio"]},"application/x-csh":{source:"apache",extensions:["csh"]},"application/x-deb":{compressible:!1},"application/x-debian-package":{source:"apache",extensions:["deb","udeb"]},"application/x-dgc-compressed":{source:"apache",extensions:["dgc"]},"application/x-director":{source:"apache",extensions:["dir","dcr","dxr","cst","cct","cxt","w3d","fgd","swa"]},"application/x-doom":{source:"apache",extensions:["wad"]},"application/x-dtbncx+xml":{source:"apache",compressible:!0,extensions:["ncx"]},"application/x-dtbook+xml":{source:"apache",compressible:!0,extensions:["dtb"]},"application/x-dtbresource+xml":{source:"apache",compressible:!0,extensions:["res"]},"application/x-dvi":{source:"apache",compressible:!1,extensions:["dvi"]},"application/x-envoy":{source:"apache",extensions:["evy"]},"application/x-eva":{source:"apache",extensions:["eva"]},"application/x-font-bdf":{source:"apache",extensions:["bdf"]},"application/x-font-dos":{source:"apache"},"application/x-font-framemaker":{source:"apache"},"application/x-font-ghostscript":{source:"apache",extensions:["gsf"]},"application/x-font-libgrx":{source:"apache"},"application/x-font-linux-psf":{source:"apache",extensions:["psf"]},"application/x-font-pcf":{source:"apache",extensions:["pcf"]},"application/x-font-snf":{source:"apache",extensions:["snf"]},"application/x-font-speedo":{source:"apache"},"application/x-font-sunos-news":{source:"apache"},"application/x-font-type1":{source:"apache",extensions:["pfa","pfb","pfm","afm"]},"application/x-font-vfont":{source:"apache"},"application/x-freearc":{source:"apache",extensions:["arc"]},"application/x-futuresplash":{source:"apache",extensions:["spl"]},"application/x-gca-compressed":{source:"apache",extensions:["gca"]},"application/x-glulx":{source:"apache",extensions:["ulx"]},"application/x-gnumeric":{source:"apache",extensions:["gnumeric"]},"application/x-gramps-xml":{source:"apache",extensions:["gramps"]},"application/x-gtar":{source:"apache",extensions:["gtar"]},"application/x-gzip":{source:"apache"},"application/x-hdf":{source:"apache",extensions:["hdf"]},"application/x-httpd-php":{compressible:!0,extensions:["php"]},"application/x-install-instructions":{source:"apache",extensions:["install"]},"application/x-iso9660-image":{source:"apache",extensions:["iso"]},"application/x-iwork-keynote-sffkey":{extensions:["key"]},"application/x-iwork-numbers-sffnumbers":{extensions:["numbers"]},"application/x-iwork-pages-sffpages":{extensions:["pages"]},"application/x-java-archive-diff":{source:"nginx",extensions:["jardiff"]},"application/x-java-jnlp-file":{source:"apache",compressible:!1,extensions:["jnlp"]},"application/x-javascript":{compressible:!0},"application/x-keepass2":{extensions:["kdbx"]},"application/x-latex":{source:"apache",compressible:!1,extensions:["latex"]},"application/x-lua-bytecode":{extensions:["luac"]},"application/x-lzh-compressed":{source:"apache",extensions:["lzh","lha"]},"application/x-makeself":{source:"nginx",extensions:["run"]},"application/x-mie":{source:"apache",extensions:["mie"]},"application/x-mobipocket-ebook":{source:"apache",extensions:["prc","mobi"]},"application/x-mpegurl":{compressible:!1},"application/x-ms-application":{source:"apache",extensions:["application"]},"application/x-ms-shortcut":{source:"apache",extensions:["lnk"]},"application/x-ms-wmd":{source:"apache",extensions:["wmd"]},"application/x-ms-wmz":{source:"apache",extensions:["wmz"]},"application/x-ms-xbap":{source:"apache",extensions:["xbap"]},"application/x-msaccess":{source:"apache",extensions:["mdb"]},"application/x-msbinder":{source:"apache",extensions:["obd"]},"application/x-mscardfile":{source:"apache",extensions:["crd"]},"application/x-msclip":{source:"apache",extensions:["clp"]},"application/x-msdos-program":{extensions:["exe"]},"application/x-msdownload":{source:"apache",extensions:["exe","dll","com","bat","msi"]},"application/x-msmediaview":{source:"apache",extensions:["mvb","m13","m14"]},"application/x-msmetafile":{source:"apache",extensions:["wmf","wmz","emf","emz"]},"application/x-msmoney":{source:"apache",extensions:["mny"]},"application/x-mspublisher":{source:"apache",extensions:["pub"]},"application/x-msschedule":{source:"apache",extensions:["scd"]},"application/x-msterminal":{source:"apache",extensions:["trm"]},"application/x-mswrite":{source:"apache",extensions:["wri"]},"application/x-netcdf":{source:"apache",extensions:["nc","cdf"]},"application/x-ns-proxy-autoconfig":{compressible:!0,extensions:["pac"]},"application/x-nzb":{source:"apache",extensions:["nzb"]},"application/x-perl":{source:"nginx",extensions:["pl","pm"]},"application/x-pilot":{source:"nginx",extensions:["prc","pdb"]},"application/x-pkcs12":{source:"apache",compressible:!1,extensions:["p12","pfx"]},"application/x-pkcs7-certificates":{source:"apache",extensions:["p7b","spc"]},"application/x-pkcs7-certreqresp":{source:"apache",extensions:["p7r"]},"application/x-pki-message":{source:"iana"},"application/x-rar-compressed":{source:"apache",compressible:!1,extensions:["rar"]},"application/x-redhat-package-manager":{source:"nginx",extensions:["rpm"]},"application/x-research-info-systems":{source:"apache",extensions:["ris"]},"application/x-sea":{source:"nginx",extensions:["sea"]},"application/x-sh":{source:"apache",compressible:!0,extensions:["sh"]},"application/x-shar":{source:"apache",extensions:["shar"]},"application/x-shockwave-flash":{source:"apache",compressible:!1,extensions:["swf"]},"application/x-silverlight-app":{source:"apache",extensions:["xap"]},"application/x-sql":{source:"apache",extensions:["sql"]},"application/x-stuffit":{source:"apache",compressible:!1,extensions:["sit"]},"application/x-stuffitx":{source:"apache",extensions:["sitx"]},"application/x-subrip":{source:"apache",extensions:["srt"]},"application/x-sv4cpio":{source:"apache",extensions:["sv4cpio"]},"application/x-sv4crc":{source:"apache",extensions:["sv4crc"]},"application/x-t3vm-image":{source:"apache",extensions:["t3"]},"application/x-tads":{source:"apache",extensions:["gam"]},"application/x-tar":{source:"apache",compressible:!0,extensions:["tar"]},"application/x-tcl":{source:"apache",extensions:["tcl","tk"]},"application/x-tex":{source:"apache",extensions:["tex"]},"application/x-tex-tfm":{source:"apache",extensions:["tfm"]},"application/x-texinfo":{source:"apache",extensions:["texinfo","texi"]},"application/x-tgif":{source:"apache",extensions:["obj"]},"application/x-ustar":{source:"apache",extensions:["ustar"]},"application/x-virtualbox-hdd":{compressible:!0,extensions:["hdd"]},"application/x-virtualbox-ova":{compressible:!0,extensions:["ova"]},"application/x-virtualbox-ovf":{compressible:!0,extensions:["ovf"]},"application/x-virtualbox-vbox":{compressible:!0,extensions:["vbox"]},"application/x-virtualbox-vbox-extpack":{compressible:!1,extensions:["vbox-extpack"]},"application/x-virtualbox-vdi":{compressible:!0,extensions:["vdi"]},"application/x-virtualbox-vhd":{compressible:!0,extensions:["vhd"]},"application/x-virtualbox-vmdk":{compressible:!0,extensions:["vmdk"]},"application/x-wais-source":{source:"apache",extensions:["src"]},"application/x-web-app-manifest+json":{compressible:!0,extensions:["webapp"]},"application/x-www-form-urlencoded":{source:"iana",compressible:!0},"application/x-x509-ca-cert":{source:"iana",extensions:["der","crt","pem"]},"application/x-x509-ca-ra-cert":{source:"iana"},"application/x-x509-next-ca-cert":{source:"iana"},"application/x-xfig":{source:"apache",extensions:["fig"]},"application/x-xliff+xml":{source:"apache",compressible:!0,extensions:["xlf"]},"application/x-xpinstall":{source:"apache",compressible:!1,extensions:["xpi"]},"application/x-xz":{source:"apache",extensions:["xz"]},"application/x-zmachine":{source:"apache",extensions:["z1","z2","z3","z4","z5","z6","z7","z8"]},"application/x400-bp":{source:"iana"},"application/xacml+xml":{source:"iana",compressible:!0},"application/xaml+xml":{source:"apache",compressible:!0,extensions:["xaml"]},"application/xcap-att+xml":{source:"iana",compressible:!0,extensions:["xav"]},"application/xcap-caps+xml":{source:"iana",compressible:!0,extensions:["xca"]},"application/xcap-diff+xml":{source:"iana",compressible:!0,extensions:["xdf"]},"application/xcap-el+xml":{source:"iana",compressible:!0,extensions:["xel"]},"application/xcap-error+xml":{source:"iana",compressible:!0},"application/xcap-ns+xml":{source:"iana",compressible:!0,extensions:["xns"]},"application/xcon-conference-info+xml":{source:"iana",compressible:!0},"application/xcon-conference-info-diff+xml":{source:"iana",compressible:!0},"application/xenc+xml":{source:"iana",compressible:!0,extensions:["xenc"]},"application/xhtml+xml":{source:"iana",compressible:!0,extensions:["xhtml","xht"]},"application/xhtml-voice+xml":{source:"apache",compressible:!0},"application/xliff+xml":{source:"iana",compressible:!0,extensions:["xlf"]},"application/xml":{source:"iana",compressible:!0,extensions:["xml","xsl","xsd","rng"]},"application/xml-dtd":{source:"iana",compressible:!0,extensions:["dtd"]},"application/xml-external-parsed-entity":{source:"iana"},"application/xml-patch+xml":{source:"iana",compressible:!0},"application/xmpp+xml":{source:"iana",compressible:!0},"application/xop+xml":{source:"iana",compressible:!0,extensions:["xop"]},"application/xproc+xml":{source:"apache",compressible:!0,extensions:["xpl"]},"application/xslt+xml":{source:"iana",compressible:!0,extensions:["xsl","xslt"]},"application/xspf+xml":{source:"apache",compressible:!0,extensions:["xspf"]},"application/xv+xml":{source:"iana",compressible:!0,extensions:["mxml","xhvml","xvml","xvm"]},"application/yang":{source:"iana",extensions:["yang"]},"application/yang-data+json":{source:"iana",compressible:!0},"application/yang-data+xml":{source:"iana",compressible:!0},"application/yang-patch+json":{source:"iana",compressible:!0},"application/yang-patch+xml":{source:"iana",compressible:!0},"application/yin+xml":{source:"iana",compressible:!0,extensions:["yin"]},"application/zip":{source:"iana",compressible:!1,extensions:["zip"]},"application/zlib":{source:"iana"},"application/zstd":{source:"iana"},"audio/1d-interleaved-parityfec":{source:"iana"},"audio/32kadpcm":{source:"iana"},"audio/3gpp":{source:"iana",compressible:!1,extensions:["3gpp"]},"audio/3gpp2":{source:"iana"},"audio/aac":{source:"iana"},"audio/ac3":{source:"iana"},"audio/adpcm":{source:"apache",extensions:["adp"]},"audio/amr":{source:"iana",extensions:["amr"]},"audio/amr-wb":{source:"iana"},"audio/amr-wb+":{source:"iana"},"audio/aptx":{source:"iana"},"audio/asc":{source:"iana"},"audio/atrac-advanced-lossless":{source:"iana"},"audio/atrac-x":{source:"iana"},"audio/atrac3":{source:"iana"},"audio/basic":{source:"iana",compressible:!1,extensions:["au","snd"]},"audio/bv16":{source:"iana"},"audio/bv32":{source:"iana"},"audio/clearmode":{source:"iana"},"audio/cn":{source:"iana"},"audio/dat12":{source:"iana"},"audio/dls":{source:"iana"},"audio/dsr-es201108":{source:"iana"},"audio/dsr-es202050":{source:"iana"},"audio/dsr-es202211":{source:"iana"},"audio/dsr-es202212":{source:"iana"},"audio/dv":{source:"iana"},"audio/dvi4":{source:"iana"},"audio/eac3":{source:"iana"},"audio/encaprtp":{source:"iana"},"audio/evrc":{source:"iana"},"audio/evrc-qcp":{source:"iana"},"audio/evrc0":{source:"iana"},"audio/evrc1":{source:"iana"},"audio/evrcb":{source:"iana"},"audio/evrcb0":{source:"iana"},"audio/evrcb1":{source:"iana"},"audio/evrcnw":{source:"iana"},"audio/evrcnw0":{source:"iana"},"audio/evrcnw1":{source:"iana"},"audio/evrcwb":{source:"iana"},"audio/evrcwb0":{source:"iana"},"audio/evrcwb1":{source:"iana"},"audio/evs":{source:"iana"},"audio/flexfec":{source:"iana"},"audio/fwdred":{source:"iana"},"audio/g711-0":{source:"iana"},"audio/g719":{source:"iana"},"audio/g722":{source:"iana"},"audio/g7221":{source:"iana"},"audio/g723":{source:"iana"},"audio/g726-16":{source:"iana"},"audio/g726-24":{source:"iana"},"audio/g726-32":{source:"iana"},"audio/g726-40":{source:"iana"},"audio/g728":{source:"iana"},"audio/g729":{source:"iana"},"audio/g7291":{source:"iana"},"audio/g729d":{source:"iana"},"audio/g729e":{source:"iana"},"audio/gsm":{source:"iana"},"audio/gsm-efr":{source:"iana"},"audio/gsm-hr-08":{source:"iana"},"audio/ilbc":{source:"iana"},"audio/ip-mr_v2.5":{source:"iana"},"audio/isac":{source:"apache"},"audio/l16":{source:"iana"},"audio/l20":{source:"iana"},"audio/l24":{source:"iana",compressible:!1},"audio/l8":{source:"iana"},"audio/lpc":{source:"iana"},"audio/melp":{source:"iana"},"audio/melp1200":{source:"iana"},"audio/melp2400":{source:"iana"},"audio/melp600":{source:"iana"},"audio/mhas":{source:"iana"},"audio/midi":{source:"apache",extensions:["mid","midi","kar","rmi"]},"audio/mobile-xmf":{source:"iana",extensions:["mxmf"]},"audio/mp3":{compressible:!1,extensions:["mp3"]},"audio/mp4":{source:"iana",compressible:!1,extensions:["m4a","mp4a"]},"audio/mp4a-latm":{source:"iana"},"audio/mpa":{source:"iana"},"audio/mpa-robust":{source:"iana"},"audio/mpeg":{source:"iana",compressible:!1,extensions:["mpga","mp2","mp2a","mp3","m2a","m3a"]},"audio/mpeg4-generic":{source:"iana"},"audio/musepack":{source:"apache"},"audio/ogg":{source:"iana",compressible:!1,extensions:["oga","ogg","spx","opus"]},"audio/opus":{source:"iana"},"audio/parityfec":{source:"iana"},"audio/pcma":{source:"iana"},"audio/pcma-wb":{source:"iana"},"audio/pcmu":{source:"iana"},"audio/pcmu-wb":{source:"iana"},"audio/prs.sid":{source:"iana"},"audio/qcelp":{source:"iana"},"audio/raptorfec":{source:"iana"},"audio/red":{source:"iana"},"audio/rtp-enc-aescm128":{source:"iana"},"audio/rtp-midi":{source:"iana"},"audio/rtploopback":{source:"iana"},"audio/rtx":{source:"iana"},"audio/s3m":{source:"apache",extensions:["s3m"]},"audio/scip":{source:"iana"},"audio/silk":{source:"apache",extensions:["sil"]},"audio/smv":{source:"iana"},"audio/smv-qcp":{source:"iana"},"audio/smv0":{source:"iana"},"audio/sofa":{source:"iana"},"audio/sp-midi":{source:"iana"},"audio/speex":{source:"iana"},"audio/t140c":{source:"iana"},"audio/t38":{source:"iana"},"audio/telephone-event":{source:"iana"},"audio/tetra_acelp":{source:"iana"},"audio/tetra_acelp_bb":{source:"iana"},"audio/tone":{source:"iana"},"audio/tsvcis":{source:"iana"},"audio/uemclip":{source:"iana"},"audio/ulpfec":{source:"iana"},"audio/usac":{source:"iana"},"audio/vdvi":{source:"iana"},"audio/vmr-wb":{source:"iana"},"audio/vnd.3gpp.iufp":{source:"iana"},"audio/vnd.4sb":{source:"iana"},"audio/vnd.audiokoz":{source:"iana"},"audio/vnd.celp":{source:"iana"},"audio/vnd.cisco.nse":{source:"iana"},"audio/vnd.cmles.radio-events":{source:"iana"},"audio/vnd.cns.anp1":{source:"iana"},"audio/vnd.cns.inf1":{source:"iana"},"audio/vnd.dece.audio":{source:"iana",extensions:["uva","uvva"]},"audio/vnd.digital-winds":{source:"iana",extensions:["eol"]},"audio/vnd.dlna.adts":{source:"iana"},"audio/vnd.dolby.heaac.1":{source:"iana"},"audio/vnd.dolby.heaac.2":{source:"iana"},"audio/vnd.dolby.mlp":{source:"iana"},"audio/vnd.dolby.mps":{source:"iana"},"audio/vnd.dolby.pl2":{source:"iana"},"audio/vnd.dolby.pl2x":{source:"iana"},"audio/vnd.dolby.pl2z":{source:"iana"},"audio/vnd.dolby.pulse.1":{source:"iana"},"audio/vnd.dra":{source:"iana",extensions:["dra"]},"audio/vnd.dts":{source:"iana",extensions:["dts"]},"audio/vnd.dts.hd":{source:"iana",extensions:["dtshd"]},"audio/vnd.dts.uhd":{source:"iana"},"audio/vnd.dvb.file":{source:"iana"},"audio/vnd.everad.plj":{source:"iana"},"audio/vnd.hns.audio":{source:"iana"},"audio/vnd.lucent.voice":{source:"iana",extensions:["lvp"]},"audio/vnd.ms-playready.media.pya":{source:"iana",extensions:["pya"]},"audio/vnd.nokia.mobile-xmf":{source:"iana"},"audio/vnd.nortel.vbk":{source:"iana"},"audio/vnd.nuera.ecelp4800":{source:"iana",extensions:["ecelp4800"]},"audio/vnd.nuera.ecelp7470":{source:"iana",extensions:["ecelp7470"]},"audio/vnd.nuera.ecelp9600":{source:"iana",extensions:["ecelp9600"]},"audio/vnd.octel.sbc":{source:"iana"},"audio/vnd.presonus.multitrack":{source:"iana"},"audio/vnd.qcelp":{source:"iana"},"audio/vnd.rhetorex.32kadpcm":{source:"iana"},"audio/vnd.rip":{source:"iana",extensions:["rip"]},"audio/vnd.rn-realaudio":{compressible:!1},"audio/vnd.sealedmedia.softseal.mpeg":{source:"iana"},"audio/vnd.vmx.cvsd":{source:"iana"},"audio/vnd.wave":{compressible:!1},"audio/vorbis":{source:"iana",compressible:!1},"audio/vorbis-config":{source:"iana"},"audio/wav":{compressible:!1,extensions:["wav"]},"audio/wave":{compressible:!1,extensions:["wav"]},"audio/webm":{source:"apache",compressible:!1,extensions:["weba"]},"audio/x-aac":{source:"apache",compressible:!1,extensions:["aac"]},"audio/x-aiff":{source:"apache",extensions:["aif","aiff","aifc"]},"audio/x-caf":{source:"apache",compressible:!1,extensions:["caf"]},"audio/x-flac":{source:"apache",extensions:["flac"]},"audio/x-m4a":{source:"nginx",extensions:["m4a"]},"audio/x-matroska":{source:"apache",extensions:["mka"]},"audio/x-mpegurl":{source:"apache",extensions:["m3u"]},"audio/x-ms-wax":{source:"apache",extensions:["wax"]},"audio/x-ms-wma":{source:"apache",extensions:["wma"]},"audio/x-pn-realaudio":{source:"apache",extensions:["ram","ra"]},"audio/x-pn-realaudio-plugin":{source:"apache",extensions:["rmp"]},"audio/x-realaudio":{source:"nginx",extensions:["ra"]},"audio/x-tta":{source:"apache"},"audio/x-wav":{source:"apache",extensions:["wav"]},"audio/xm":{source:"apache",extensions:["xm"]},"chemical/x-cdx":{source:"apache",extensions:["cdx"]},"chemical/x-cif":{source:"apache",extensions:["cif"]},"chemical/x-cmdf":{source:"apache",extensions:["cmdf"]},"chemical/x-cml":{source:"apache",extensions:["cml"]},"chemical/x-csml":{source:"apache",extensions:["csml"]},"chemical/x-pdb":{source:"apache"},"chemical/x-xyz":{source:"apache",extensions:["xyz"]},"font/collection":{source:"iana",extensions:["ttc"]},"font/otf":{source:"iana",compressible:!0,extensions:["otf"]},"font/sfnt":{source:"iana"},"font/ttf":{source:"iana",compressible:!0,extensions:["ttf"]},"font/woff":{source:"iana",extensions:["woff"]},"font/woff2":{source:"iana",extensions:["woff2"]},"image/aces":{source:"iana",extensions:["exr"]},"image/apng":{compressible:!1,extensions:["apng"]},"image/avci":{source:"iana",extensions:["avci"]},"image/avcs":{source:"iana",extensions:["avcs"]},"image/avif":{source:"iana",compressible:!1,extensions:["avif"]},"image/bmp":{source:"iana",compressible:!0,extensions:["bmp"]},"image/cgm":{source:"iana",extensions:["cgm"]},"image/dicom-rle":{source:"iana",extensions:["drle"]},"image/emf":{source:"iana",extensions:["emf"]},"image/fits":{source:"iana",extensions:["fits"]},"image/g3fax":{source:"iana",extensions:["g3"]},"image/gif":{source:"iana",compressible:!1,extensions:["gif"]},"image/heic":{source:"iana",extensions:["heic"]},"image/heic-sequence":{source:"iana",extensions:["heics"]},"image/heif":{source:"iana",extensions:["heif"]},"image/heif-sequence":{source:"iana",extensions:["heifs"]},"image/hej2k":{source:"iana",extensions:["hej2"]},"image/hsj2":{source:"iana",extensions:["hsj2"]},"image/ief":{source:"iana",extensions:["ief"]},"image/jls":{source:"iana",extensions:["jls"]},"image/jp2":{source:"iana",compressible:!1,extensions:["jp2","jpg2"]},"image/jpeg":{source:"iana",compressible:!1,extensions:["jpeg","jpg","jpe"]},"image/jph":{source:"iana",extensions:["jph"]},"image/jphc":{source:"iana",extensions:["jhc"]},"image/jpm":{source:"iana",compressible:!1,extensions:["jpm"]},"image/jpx":{source:"iana",compressible:!1,extensions:["jpx","jpf"]},"image/jxr":{source:"iana",extensions:["jxr"]},"image/jxra":{source:"iana",extensions:["jxra"]},"image/jxrs":{source:"iana",extensions:["jxrs"]},"image/jxs":{source:"iana",extensions:["jxs"]},"image/jxsc":{source:"iana",extensions:["jxsc"]},"image/jxsi":{source:"iana",extensions:["jxsi"]},"image/jxss":{source:"iana",extensions:["jxss"]},"image/ktx":{source:"iana",extensions:["ktx"]},"image/ktx2":{source:"iana",extensions:["ktx2"]},"image/naplps":{source:"iana"},"image/pjpeg":{compressible:!1},"image/png":{source:"iana",compressible:!1,extensions:["png"]},"image/prs.btif":{source:"iana",extensions:["btif"]},"image/prs.pti":{source:"iana",extensions:["pti"]},"image/pwg-raster":{source:"iana"},"image/sgi":{source:"apache",extensions:["sgi"]},"image/svg+xml":{source:"iana",compressible:!0,extensions:["svg","svgz"]},"image/t38":{source:"iana",extensions:["t38"]},"image/tiff":{source:"iana",compressible:!1,extensions:["tif","tiff"]},"image/tiff-fx":{source:"iana",extensions:["tfx"]},"image/vnd.adobe.photoshop":{source:"iana",compressible:!0,extensions:["psd"]},"image/vnd.airzip.accelerator.azv":{source:"iana",extensions:["azv"]},"image/vnd.cns.inf2":{source:"iana"},"image/vnd.dece.graphic":{source:"iana",extensions:["uvi","uvvi","uvg","uvvg"]},"image/vnd.djvu":{source:"iana",extensions:["djvu","djv"]},"image/vnd.dvb.subtitle":{source:"iana",extensions:["sub"]},"image/vnd.dwg":{source:"iana",extensions:["dwg"]},"image/vnd.dxf":{source:"iana",extensions:["dxf"]},"image/vnd.fastbidsheet":{source:"iana",extensions:["fbs"]},"image/vnd.fpx":{source:"iana",extensions:["fpx"]},"image/vnd.fst":{source:"iana",extensions:["fst"]},"image/vnd.fujixerox.edmics-mmr":{source:"iana",extensions:["mmr"]},"image/vnd.fujixerox.edmics-rlc":{source:"iana",extensions:["rlc"]},"image/vnd.globalgraphics.pgb":{source:"iana"},"image/vnd.microsoft.icon":{source:"iana",compressible:!0,extensions:["ico"]},"image/vnd.mix":{source:"iana"},"image/vnd.mozilla.apng":{source:"iana"},"image/vnd.ms-dds":{compressible:!0,extensions:["dds"]},"image/vnd.ms-modi":{source:"iana",extensions:["mdi"]},"image/vnd.ms-photo":{source:"apache",extensions:["wdp"]},"image/vnd.net-fpx":{source:"iana",extensions:["npx"]},"image/vnd.pco.b16":{source:"iana",extensions:["b16"]},"image/vnd.radiance":{source:"iana"},"image/vnd.sealed.png":{source:"iana"},"image/vnd.sealedmedia.softseal.gif":{source:"iana"},"image/vnd.sealedmedia.softseal.jpg":{source:"iana"},"image/vnd.svf":{source:"iana"},"image/vnd.tencent.tap":{source:"iana",extensions:["tap"]},"image/vnd.valve.source.texture":{source:"iana",extensions:["vtf"]},"image/vnd.wap.wbmp":{source:"iana",extensions:["wbmp"]},"image/vnd.xiff":{source:"iana",extensions:["xif"]},"image/vnd.zbrush.pcx":{source:"iana",extensions:["pcx"]},"image/webp":{source:"apache",extensions:["webp"]},"image/wmf":{source:"iana",extensions:["wmf"]},"image/x-3ds":{source:"apache",extensions:["3ds"]},"image/x-cmu-raster":{source:"apache",extensions:["ras"]},"image/x-cmx":{source:"apache",extensions:["cmx"]},"image/x-freehand":{source:"apache",extensions:["fh","fhc","fh4","fh5","fh7"]},"image/x-icon":{source:"apache",compressible:!0,extensions:["ico"]},"image/x-jng":{source:"nginx",extensions:["jng"]},"image/x-mrsid-image":{source:"apache",extensions:["sid"]},"image/x-ms-bmp":{source:"nginx",compressible:!0,extensions:["bmp"]},"image/x-pcx":{source:"apache",extensions:["pcx"]},"image/x-pict":{source:"apache",extensions:["pic","pct"]},"image/x-portable-anymap":{source:"apache",extensions:["pnm"]},"image/x-portable-bitmap":{source:"apache",extensions:["pbm"]},"image/x-portable-graymap":{source:"apache",extensions:["pgm"]},"image/x-portable-pixmap":{source:"apache",extensions:["ppm"]},"image/x-rgb":{source:"apache",extensions:["rgb"]},"image/x-tga":{source:"apache",extensions:["tga"]},"image/x-xbitmap":{source:"apache",extensions:["xbm"]},"image/x-xcf":{compressible:!1},"image/x-xpixmap":{source:"apache",extensions:["xpm"]},"image/x-xwindowdump":{source:"apache",extensions:["xwd"]},"message/cpim":{source:"iana"},"message/delivery-status":{source:"iana"},"message/disposition-notification":{source:"iana",extensions:["disposition-notification"]},"message/external-body":{source:"iana"},"message/feedback-report":{source:"iana"},"message/global":{source:"iana",extensions:["u8msg"]},"message/global-delivery-status":{source:"iana",extensions:["u8dsn"]},"message/global-disposition-notification":{source:"iana",extensions:["u8mdn"]},"message/global-headers":{source:"iana",extensions:["u8hdr"]},"message/http":{source:"iana",compressible:!1},"message/imdn+xml":{source:"iana",compressible:!0},"message/news":{source:"iana"},"message/partial":{source:"iana",compressible:!1},"message/rfc822":{source:"iana",compressible:!0,extensions:["eml","mime"]},"message/s-http":{source:"iana"},"message/sip":{source:"iana"},"message/sipfrag":{source:"iana"},"message/tracking-status":{source:"iana"},"message/vnd.si.simp":{source:"iana"},"message/vnd.wfa.wsc":{source:"iana",extensions:["wsc"]},"model/3mf":{source:"iana",extensions:["3mf"]},"model/e57":{source:"iana"},"model/gltf+json":{source:"iana",compressible:!0,extensions:["gltf"]},"model/gltf-binary":{source:"iana",compressible:!0,extensions:["glb"]},"model/iges":{source:"iana",compressible:!1,extensions:["igs","iges"]},"model/mesh":{source:"iana",compressible:!1,extensions:["msh","mesh","silo"]},"model/mtl":{source:"iana",extensions:["mtl"]},"model/obj":{source:"iana",extensions:["obj"]},"model/step":{source:"iana"},"model/step+xml":{source:"iana",compressible:!0,extensions:["stpx"]},"model/step+zip":{source:"iana",compressible:!1,extensions:["stpz"]},"model/step-xml+zip":{source:"iana",compressible:!1,extensions:["stpxz"]},"model/stl":{source:"iana",extensions:["stl"]},"model/vnd.collada+xml":{source:"iana",compressible:!0,extensions:["dae"]},"model/vnd.dwf":{source:"iana",extensions:["dwf"]},"model/vnd.flatland.3dml":{source:"iana"},"model/vnd.gdl":{source:"iana",extensions:["gdl"]},"model/vnd.gs-gdl":{source:"apache"},"model/vnd.gs.gdl":{source:"iana"},"model/vnd.gtw":{source:"iana",extensions:["gtw"]},"model/vnd.moml+xml":{source:"iana",compressible:!0},"model/vnd.mts":{source:"iana",extensions:["mts"]},"model/vnd.opengex":{source:"iana",extensions:["ogex"]},"model/vnd.parasolid.transmit.binary":{source:"iana",extensions:["x_b"]},"model/vnd.parasolid.transmit.text":{source:"iana",extensions:["x_t"]},"model/vnd.pytha.pyox":{source:"iana"},"model/vnd.rosette.annotated-data-model":{source:"iana"},"model/vnd.sap.vds":{source:"iana",extensions:["vds"]},"model/vnd.usdz+zip":{source:"iana",compressible:!1,extensions:["usdz"]},"model/vnd.valve.source.compiled-map":{source:"iana",extensions:["bsp"]},"model/vnd.vtu":{source:"iana",extensions:["vtu"]},"model/vrml":{source:"iana",compressible:!1,extensions:["wrl","vrml"]},"model/x3d+binary":{source:"apache",compressible:!1,extensions:["x3db","x3dbz"]},"model/x3d+fastinfoset":{source:"iana",extensions:["x3db"]},"model/x3d+vrml":{source:"apache",compressible:!1,extensions:["x3dv","x3dvz"]},"model/x3d+xml":{source:"iana",compressible:!0,extensions:["x3d","x3dz"]},"model/x3d-vrml":{source:"iana",extensions:["x3dv"]},"multipart/alternative":{source:"iana",compressible:!1},"multipart/appledouble":{source:"iana"},"multipart/byteranges":{source:"iana"},"multipart/digest":{source:"iana"},"multipart/encrypted":{source:"iana",compressible:!1},"multipart/form-data":{source:"iana",compressible:!1},"multipart/header-set":{source:"iana"},"multipart/mixed":{source:"iana"},"multipart/multilingual":{source:"iana"},"multipart/parallel":{source:"iana"},"multipart/related":{source:"iana",compressible:!1},"multipart/report":{source:"iana"},"multipart/signed":{source:"iana",compressible:!1},"multipart/vnd.bint.med-plus":{source:"iana"},"multipart/voice-message":{source:"iana"},"multipart/x-mixed-replace":{source:"iana"},"text/1d-interleaved-parityfec":{source:"iana"},"text/cache-manifest":{source:"iana",compressible:!0,extensions:["appcache","manifest"]},"text/calendar":{source:"iana",extensions:["ics","ifb"]},"text/calender":{compressible:!0},"text/cmd":{compressible:!0},"text/coffeescript":{extensions:["coffee","litcoffee"]},"text/cql":{source:"iana"},"text/cql-expression":{source:"iana"},"text/cql-identifier":{source:"iana"},"text/css":{source:"iana",charset:"UTF-8",compressible:!0,extensions:["css"]},"text/csv":{source:"iana",compressible:!0,extensions:["csv"]},"text/csv-schema":{source:"iana"},"text/directory":{source:"iana"},"text/dns":{source:"iana"},"text/ecmascript":{source:"iana"},"text/encaprtp":{source:"iana"},"text/enriched":{source:"iana"},"text/fhirpath":{source:"iana"},"text/flexfec":{source:"iana"},"text/fwdred":{source:"iana"},"text/gff3":{source:"iana"},"text/grammar-ref-list":{source:"iana"},"text/html":{source:"iana",compressible:!0,extensions:["html","htm","shtml"]},"text/jade":{extensions:["jade"]},"text/javascript":{source:"iana",compressible:!0},"text/jcr-cnd":{source:"iana"},"text/jsx":{compressible:!0,extensions:["jsx"]},"text/less":{compressible:!0,extensions:["less"]},"text/markdown":{source:"iana",compressible:!0,extensions:["markdown","md"]},"text/mathml":{source:"nginx",extensions:["mml"]},"text/mdx":{compressible:!0,extensions:["mdx"]},"text/mizar":{source:"iana"},"text/n3":{source:"iana",charset:"UTF-8",compressible:!0,extensions:["n3"]},"text/parameters":{source:"iana",charset:"UTF-8"},"text/parityfec":{source:"iana"},"text/plain":{source:"iana",compressible:!0,extensions:["txt","text","conf","def","list","log","in","ini"]},"text/provenance-notation":{source:"iana",charset:"UTF-8"},"text/prs.fallenstein.rst":{source:"iana"},"text/prs.lines.tag":{source:"iana",extensions:["dsc"]},"text/prs.prop.logic":{source:"iana"},"text/raptorfec":{source:"iana"},"text/red":{source:"iana"},"text/rfc822-headers":{source:"iana"},"text/richtext":{source:"iana",compressible:!0,extensions:["rtx"]},"text/rtf":{source:"iana",compressible:!0,extensions:["rtf"]},"text/rtp-enc-aescm128":{source:"iana"},"text/rtploopback":{source:"iana"},"text/rtx":{source:"iana"},"text/sgml":{source:"iana",extensions:["sgml","sgm"]},"text/shaclc":{source:"iana"},"text/shex":{source:"iana",extensions:["shex"]},"text/slim":{extensions:["slim","slm"]},"text/spdx":{source:"iana",extensions:["spdx"]},"text/strings":{source:"iana"},"text/stylus":{extensions:["stylus","styl"]},"text/t140":{source:"iana"},"text/tab-separated-values":{source:"iana",compressible:!0,extensions:["tsv"]},"text/troff":{source:"iana",extensions:["t","tr","roff","man","me","ms"]},"text/turtle":{source:"iana",charset:"UTF-8",extensions:["ttl"]},"text/ulpfec":{source:"iana"},"text/uri-list":{source:"iana",compressible:!0,extensions:["uri","uris","urls"]},"text/vcard":{source:"iana",compressible:!0,extensions:["vcard"]},"text/vnd.a":{source:"iana"},"text/vnd.abc":{source:"iana"},"text/vnd.ascii-art":{source:"iana"},"text/vnd.curl":{source:"iana",extensions:["curl"]},"text/vnd.curl.dcurl":{source:"apache",extensions:["dcurl"]},"text/vnd.curl.mcurl":{source:"apache",extensions:["mcurl"]},"text/vnd.curl.scurl":{source:"apache",extensions:["scurl"]},"text/vnd.debian.copyright":{source:"iana",charset:"UTF-8"},"text/vnd.dmclientscript":{source:"iana"},"text/vnd.dvb.subtitle":{source:"iana",extensions:["sub"]},"text/vnd.esmertec.theme-descriptor":{source:"iana",charset:"UTF-8"},"text/vnd.familysearch.gedcom":{source:"iana",extensions:["ged"]},"text/vnd.ficlab.flt":{source:"iana"},"text/vnd.fly":{source:"iana",extensions:["fly"]},"text/vnd.fmi.flexstor":{source:"iana",extensions:["flx"]},"text/vnd.gml":{source:"iana"},"text/vnd.graphviz":{source:"iana",extensions:["gv"]},"text/vnd.hans":{source:"iana"},"text/vnd.hgl":{source:"iana"},"text/vnd.in3d.3dml":{source:"iana",extensions:["3dml"]},"text/vnd.in3d.spot":{source:"iana",extensions:["spot"]},"text/vnd.iptc.newsml":{source:"iana"},"text/vnd.iptc.nitf":{source:"iana"},"text/vnd.latex-z":{source:"iana"},"text/vnd.motorola.reflex":{source:"iana"},"text/vnd.ms-mediapackage":{source:"iana"},"text/vnd.net2phone.commcenter.command":{source:"iana"},"text/vnd.radisys.msml-basic-layout":{source:"iana"},"text/vnd.senx.warpscript":{source:"iana"},"text/vnd.si.uricatalogue":{source:"iana"},"text/vnd.sosi":{source:"iana"},"text/vnd.sun.j2me.app-descriptor":{source:"iana",charset:"UTF-8",extensions:["jad"]},"text/vnd.trolltech.linguist":{source:"iana",charset:"UTF-8"},"text/vnd.wap.si":{source:"iana"},"text/vnd.wap.sl":{source:"iana"},"text/vnd.wap.wml":{source:"iana",extensions:["wml"]},"text/vnd.wap.wmlscript":{source:"iana",extensions:["wmls"]},"text/vtt":{source:"iana",charset:"UTF-8",compressible:!0,extensions:["vtt"]},"text/x-asm":{source:"apache",extensions:["s","asm"]},"text/x-c":{source:"apache",extensions:["c","cc","cxx","cpp","h","hh","dic"]},"text/x-component":{source:"nginx",extensions:["htc"]},"text/x-fortran":{source:"apache",extensions:["f","for","f77","f90"]},"text/x-gwt-rpc":{compressible:!0},"text/x-handlebars-template":{extensions:["hbs"]},"text/x-java-source":{source:"apache",extensions:["java"]},"text/x-jquery-tmpl":{compressible:!0},"text/x-lua":{extensions:["lua"]},"text/x-markdown":{compressible:!0,extensions:["mkd"]},"text/x-nfo":{source:"apache",extensions:["nfo"]},"text/x-opml":{source:"apache",extensions:["opml"]},"text/x-org":{compressible:!0,extensions:["org"]},"text/x-pascal":{source:"apache",extensions:["p","pas"]},"text/x-processing":{compressible:!0,extensions:["pde"]},"text/x-sass":{extensions:["sass"]},"text/x-scss":{extensions:["scss"]},"text/x-setext":{source:"apache",extensions:["etx"]},"text/x-sfv":{source:"apache",extensions:["sfv"]},"text/x-suse-ymp":{compressible:!0,extensions:["ymp"]},"text/x-uuencode":{source:"apache",extensions:["uu"]},"text/x-vcalendar":{source:"apache",extensions:["vcs"]},"text/x-vcard":{source:"apache",extensions:["vcf"]},"text/xml":{source:"iana",compressible:!0,extensions:["xml"]},"text/xml-external-parsed-entity":{source:"iana"},"text/yaml":{compressible:!0,extensions:["yaml","yml"]},"video/1d-interleaved-parityfec":{source:"iana"},"video/3gpp":{source:"iana",extensions:["3gp","3gpp"]},"video/3gpp-tt":{source:"iana"},"video/3gpp2":{source:"iana",extensions:["3g2"]},"video/av1":{source:"iana"},"video/bmpeg":{source:"iana"},"video/bt656":{source:"iana"},"video/celb":{source:"iana"},"video/dv":{source:"iana"},"video/encaprtp":{source:"iana"},"video/ffv1":{source:"iana"},"video/flexfec":{source:"iana"},"video/h261":{source:"iana",extensions:["h261"]},"video/h263":{source:"iana",extensions:["h263"]},"video/h263-1998":{source:"iana"},"video/h263-2000":{source:"iana"},"video/h264":{source:"iana",extensions:["h264"]},"video/h264-rcdo":{source:"iana"},"video/h264-svc":{source:"iana"},"video/h265":{source:"iana"},"video/iso.segment":{source:"iana",extensions:["m4s"]},"video/jpeg":{source:"iana",extensions:["jpgv"]},"video/jpeg2000":{source:"iana"},"video/jpm":{source:"apache",extensions:["jpm","jpgm"]},"video/jxsv":{source:"iana"},"video/mj2":{source:"iana",extensions:["mj2","mjp2"]},"video/mp1s":{source:"iana"},"video/mp2p":{source:"iana"},"video/mp2t":{source:"iana",extensions:["ts"]},"video/mp4":{source:"iana",compressible:!1,extensions:["mp4","mp4v","mpg4"]},"video/mp4v-es":{source:"iana"},"video/mpeg":{source:"iana",compressible:!1,extensions:["mpeg","mpg","mpe","m1v","m2v"]},"video/mpeg4-generic":{source:"iana"},"video/mpv":{source:"iana"},"video/nv":{source:"iana"},"video/ogg":{source:"iana",compressible:!1,extensions:["ogv"]},"video/parityfec":{source:"iana"},"video/pointer":{source:"iana"},"video/quicktime":{source:"iana",compressible:!1,extensions:["qt","mov"]},"video/raptorfec":{source:"iana"},"video/raw":{source:"iana"},"video/rtp-enc-aescm128":{source:"iana"},"video/rtploopback":{source:"iana"},"video/rtx":{source:"iana"},"video/scip":{source:"iana"},"video/smpte291":{source:"iana"},"video/smpte292m":{source:"iana"},"video/ulpfec":{source:"iana"},"video/vc1":{source:"iana"},"video/vc2":{source:"iana"},"video/vnd.cctv":{source:"iana"},"video/vnd.dece.hd":{source:"iana",extensions:["uvh","uvvh"]},"video/vnd.dece.mobile":{source:"iana",extensions:["uvm","uvvm"]},"video/vnd.dece.mp4":{source:"iana"},"video/vnd.dece.pd":{source:"iana",extensions:["uvp","uvvp"]},"video/vnd.dece.sd":{source:"iana",extensions:["uvs","uvvs"]},"video/vnd.dece.video":{source:"iana",extensions:["uvv","uvvv"]},"video/vnd.directv.mpeg":{source:"iana"},"video/vnd.directv.mpeg-tts":{source:"iana"},"video/vnd.dlna.mpeg-tts":{source:"iana"},"video/vnd.dvb.file":{source:"iana",extensions:["dvb"]},"video/vnd.fvt":{source:"iana",extensions:["fvt"]},"video/vnd.hns.video":{source:"iana"},"video/vnd.iptvforum.1dparityfec-1010":{source:"iana"},"video/vnd.iptvforum.1dparityfec-2005":{source:"iana"},"video/vnd.iptvforum.2dparityfec-1010":{source:"iana"},"video/vnd.iptvforum.2dparityfec-2005":{source:"iana"},"video/vnd.iptvforum.ttsavc":{source:"iana"},"video/vnd.iptvforum.ttsmpeg2":{source:"iana"},"video/vnd.motorola.video":{source:"iana"},"video/vnd.motorola.videop":{source:"iana"},"video/vnd.mpegurl":{source:"iana",extensions:["mxu","m4u"]},"video/vnd.ms-playready.media.pyv":{source:"iana",extensions:["pyv"]},"video/vnd.nokia.interleaved-multimedia":{source:"iana"},"video/vnd.nokia.mp4vr":{source:"iana"},"video/vnd.nokia.videovoip":{source:"iana"},"video/vnd.objectvideo":{source:"iana"},"video/vnd.radgamettools.bink":{source:"iana"},"video/vnd.radgamettools.smacker":{source:"iana"},"video/vnd.sealed.mpeg1":{source:"iana"},"video/vnd.sealed.mpeg4":{source:"iana"},"video/vnd.sealed.swf":{source:"iana"},"video/vnd.sealedmedia.softseal.mov":{source:"iana"},"video/vnd.uvvu.mp4":{source:"iana",extensions:["uvu","uvvu"]},"video/vnd.vivo":{source:"iana",extensions:["viv"]},"video/vnd.youtube.yt":{source:"iana"},"video/vp8":{source:"iana"},"video/vp9":{source:"iana"},"video/webm":{source:"apache",compressible:!1,extensions:["webm"]},"video/x-f4v":{source:"apache",extensions:["f4v"]},"video/x-fli":{source:"apache",extensions:["fli"]},"video/x-flv":{source:"apache",compressible:!1,extensions:["flv"]},"video/x-m4v":{source:"apache",extensions:["m4v"]},"video/x-matroska":{source:"apache",compressible:!1,extensions:["mkv","mk3d","mks"]},"video/x-mng":{source:"apache",extensions:["mng"]},"video/x-ms-asf":{source:"apache",extensions:["asf","asx"]},"video/x-ms-vob":{source:"apache",extensions:["vob"]},"video/x-ms-wm":{source:"apache",extensions:["wm"]},"video/x-ms-wmv":{source:"apache",compressible:!1,extensions:["wmv"]},"video/x-ms-wmx":{source:"apache",extensions:["wmx"]},"video/x-ms-wvx":{source:"apache",extensions:["wvx"]},"video/x-msvideo":{source:"apache",extensions:["avi"]},"video/x-sgi-movie":{source:"apache",extensions:["movie"]},"video/x-smv":{source:"apache",extensions:["smv"]},"x-conference/x-cooltalk":{source:"apache",extensions:["ice"]},"x-shader/x-fragment":{compressible:!0},"x-shader/x-vertex":{compressible:!0}};function $e(){return Ce||(Ce=1,function(e){var n,a,i,o=Te?Ee:(Te=1,Ee=Ve),s=t.extname,r=/^\s*([^;\s]*)(?:;|\s|$)/,c=/^text\//i;function p(e){if(!e||"string"!=typeof e)return!1;var n=r.exec(e),t=n&&o[n[1].toLowerCase()];return t&&t.charset?t.charset:!(!n||!c.test(n[1]))&&"UTF-8"}e.charset=p,e.charsets={lookup:p},e.contentType=function(n){if(!n||"string"!=typeof n)return!1;var t=-1===n.indexOf("/")?e.lookup(n):n;if(!t)return!1;if(-1===t.indexOf("charset")){var a=e.charset(t);a&&(t+="; charset="+a.toLowerCase())}return t},e.extension=function(n){if(!n||"string"!=typeof n)return!1;var t=r.exec(n),a=t&&e.extensions[t[1].toLowerCase()];if(!a||!a.length)return!1;return a[0]},e.extensions=Object.create(null),e.lookup=function(n){if(!n||"string"!=typeof n)return!1;var t=s("x."+n).toLowerCase().substr(1);if(!t)return!1;return e.types[t]||!1},e.types=Object.create(null),n=e.extensions,a=e.types,i=["nginx","apache",void 0,"iana"],Object.keys(o).forEach((function(e){var t=o[e],s=t.extensions;if(s&&s.length){n[e]=s;for(var r=0;r<s.length;r++){var c=s[r];if(a[c]){var p=i.indexOf(o[a[c]].source),u=i.indexOf(t.source);if("application/octet-stream"!==a[c]&&(p>u||p===u&&"application/"===a[c].substr(0,12)))continue}a[c]=e}}}))}(He)),He}function Ge(){if(Fe)return Ae;Fe=1;var e=qe?Oe:(qe=1,Oe=function(e){var n="function"==typeof setImmediate?setImmediate:"object"==typeof process&&"function"==typeof process.nextTick?process.nextTick:null;n?n(e):setTimeout(e,0)});return Ae=function(n){var t=!1;return e((function(){t=!0})),function(a,i){t?n(a,i):e((function(){n(a,i)}))}}}function Je(){if(Pe)return De;function e(e){"function"==typeof this.jobs[e]&&this.jobs[e]()}return Pe=1,De=function(n){Object.keys(n.jobs).forEach(e.bind(n)),n.jobs={}}}function Ke(){if(Le)return Be;Le=1;var e=Ge(),n=Je();return Be=function(t,a,i,o){var s=i.keyedList?i.keyedList[i.index]:i.index;i.jobs[s]=function(n,t,a,i){var o;o=2==n.length?n(a,e(i)):n(a,t,e(i));return o}(a,s,t[s],(function(e,t){s in i.jobs&&(delete i.jobs[s],e?n(i):i.results[s]=t,o(e,i.results))}))}}function Qe(){if(ze)return Ue;return ze=1,Ue=function(e,n){var t=!Array.isArray(e),a={index:0,keyedList:t||n?Object.keys(e):null,jobs:{},results:t?{}:[],size:t?Object.keys(e).length:e.length};n&&a.keyedList.sort(t?n:function(t,a){return n(e[t],e[a])});return a}}function Ye(){if(Ne)return Ie;Ne=1;var e=Je(),n=Ge();return Ie=function(t){if(!Object.keys(this.jobs).length)return;this.index=this.size,e(this),n(t)(null,this.results)}}function Xe(){if(We)return Me;We=1;var e=Ke(),n=Qe(),t=Ye();return Me=function(a,i,o){var s=n(a);for(;s.index<(s.keyedList||a).length;)e(a,i,s,(function(e,n){e?o(e,n):0!==Object.keys(s.jobs).length||o(null,s.results)})),s.index++;return t.bind(s,o)}}var Ze,en,nn,tn,an,on,sn,rn,cn,pn,un,ln,dn,mn,fn,hn,vn,xn,bn,gn,yn,wn,kn,_n,jn,Rn,Sn,En,Tn,Cn,On,qn,An,Fn,Dn,Pn,Bn,Ln,Un,zn,In,Nn,Mn,Wn,Hn,Vn,$n,Gn,Jn,Kn,Qn,Yn,Xn,Zn,et,nt,tt,at,it,ot,st,rt,ct,pt,ut,lt,dt,mt,ft,ht,vt,xt,bt,gt,yt,wt,kt,_t,jt,Rt,St,Et={exports:{}};function Tt(){if(Ze)return Et.exports;Ze=1;var e=Ke(),n=Qe(),t=Ye();function a(e,n){return e<n?-1:e>n?1:0}return Et.exports=function(a,i,o,s){var r=n(a,o);return e(a,i,r,(function n(t,o){t?s(t,o):(r.index++,r.index<(r.keyedList||a).length?e(a,i,r,n):s(null,r.results))})),t.bind(r,s)},Et.exports.ascending=a,Et.exports.descending=function(e,n){return-1*a(e,n)},Et.exports}function Ct(){if(nn)return en;nn=1;var e=Tt();return en=function(n,t,a){return e(n,t,null,a)}}function Ot(){return an?tn:(an=1,tn={parallel:Xe(),serial:Ct(),serialOrdered:Tt()})}function qt(){return sn?on:(sn=1,on=Object)}function At(){return cn?rn:(cn=1,rn=Error)}function Ft(){return un?pn:(un=1,pn=EvalError)}function Dt(){return dn?ln:(dn=1,ln=RangeError)}function Pt(){return fn?mn:(fn=1,mn=ReferenceError)}function Bt(){return vn?hn:(vn=1,hn=SyntaxError)}function Lt(){return bn?xn:(bn=1,xn=TypeError)}function Ut(){return yn?gn:(yn=1,gn=URIError)}function zt(){return kn?wn:(kn=1,wn=Math.abs)}function It(){return jn?_n:(jn=1,_n=Math.floor)}function Nt(){return Sn?Rn:(Sn=1,Rn=Math.max)}function Mt(){return Tn?En:(Tn=1,En=Math.min)}function Wt(){return On?Cn:(On=1,Cn=Math.pow)}function Ht(){return An?qn:(An=1,qn=Math.round)}function Vt(){return Dn?Fn:(Dn=1,Fn=Number.isNaN||function(e){return e!=e})}function $t(){if(Bn)return Pn;Bn=1;var e=Vt();return Pn=function(n){return e(n)||0===n?n:n<0?-1:1}}function Gt(){return Un?Ln:(Un=1,Ln=Object.getOwnPropertyDescriptor)}function Jt(){if(In)return zn;In=1;var e=Gt();if(e)try{e([],"length")}catch(n){e=null}return zn=e}function Kt(){if(Mn)return Nn;Mn=1;var e=Object.defineProperty||!1;if(e)try{e({},"a",{value:1})}catch(n){e=!1}return Nn=e}function Qt(){return Hn?Wn:(Hn=1,Wn=function(){if("function"!=typeof Symbol||"function"!=typeof Object.getOwnPropertySymbols)return!1;if("symbol"==typeof Symbol.iterator)return!0;var e={},n=Symbol("test"),t=Object(n);if("string"==typeof n)return!1;if("[object Symbol]"!==Object.prototype.toString.call(n))return!1;if("[object Symbol]"!==Object.prototype.toString.call(t))return!1;for(var a in e[n]=42,e)return!1;if("function"==typeof Object.keys&&0!==Object.keys(e).length)return!1;if("function"==typeof Object.getOwnPropertyNames&&0!==Object.getOwnPropertyNames(e).length)return!1;var i=Object.getOwnPropertySymbols(e);if(1!==i.length||i[0]!==n)return!1;if(!Object.prototype.propertyIsEnumerable.call(e,n))return!1;if("function"==typeof Object.getOwnPropertyDescriptor){var o=Object.getOwnPropertyDescriptor(e,n);if(42!==o.value||!0!==o.enumerable)return!1}return!0})}function Yt(){if($n)return Vn;$n=1;var e="undefined"!=typeof Symbol&&Symbol,n=Qt();return Vn=function(){return"function"==typeof e&&("function"==typeof Symbol&&("symbol"==typeof e("foo")&&("symbol"==typeof Symbol("bar")&&n())))}}function Xt(){return Jn?Gn:(Jn=1,Gn="undefined"!=typeof Reflect&&Reflect.getPrototypeOf||null)}function Zt(){return Qn?Kn:(Qn=1,Kn=qt().getPrototypeOf||null)}function ea(){if(Xn)return Yn;Xn=1;var e=Object.prototype.toString,n=Math.max,t=function(e,n){for(var t=[],a=0;a<e.length;a+=1)t[a]=e[a];for(var i=0;i<n.length;i+=1)t[i+e.length]=n[i];return t};return Yn=function(a){var i=this;if("function"!=typeof i||"[object Function]"!==e.apply(i))throw new TypeError("Function.prototype.bind called on incompatible "+i);for(var o,s=function(e,n){for(var t=[],a=n,i=0;a<e.length;a+=1,i+=1)t[i]=e[a];return t}(arguments,1),r=n(0,i.length-s.length),c=[],p=0;p<r;p++)c[p]="$"+p;if(o=Function("binder","return function ("+function(e,n){for(var t="",a=0;a<e.length;a+=1)t+=e[a],a+1<e.length&&(t+=n);return t}(c,",")+"){ return binder.apply(this,arguments); }")((function(){if(this instanceof o){var e=i.apply(this,t(s,arguments));return Object(e)===e?e:this}return i.apply(a,t(s,arguments))})),i.prototype){var u=function(){};u.prototype=i.prototype,o.prototype=new u,u.prototype=null}return o},Yn}function na(){if(et)return Zn;et=1;var e=ea();return Zn=Function.prototype.bind||e}function ta(){return tt?nt:(tt=1,nt=Function.prototype.call)}function aa(){return it?at:(it=1,at=Function.prototype.apply)}function ia(){if(ct)return rt;ct=1;var e=na(),n=aa(),t=ta(),a=st?ot:(st=1,ot="undefined"!=typeof Reflect&&Reflect&&Reflect.apply);return rt=a||e.call(t,n)}function oa(){if(dt)return lt;dt=1;var e,n=function(){if(ut)return pt;ut=1;var e=na(),n=Lt(),t=ta(),a=ia();return pt=function(i){if(i.length<1||"function"!=typeof i[0])throw new n("a function is required");return a(e,t,i)}}(),t=Jt();try{e=[].__proto__===Array.prototype}catch(e){if(!e||"object"!=typeof e||!("code"in e)||"ERR_PROTO_ACCESS"!==e.code)throw e}var a=!!e&&t&&t(Object.prototype,"__proto__"),i=Object,o=i.getPrototypeOf;return lt=a&&"function"==typeof a.get?n([a.get]):"function"==typeof o&&function(e){return o(null==e?e:i(e))}}function sa(){if(ft)return mt;ft=1;var e=Xt(),n=Zt(),t=oa();return mt=e?function(n){return e(n)}:n?function(e){if(!e||"object"!=typeof e&&"function"!=typeof e)throw new TypeError("getProto: not an object");return n(e)}:t?function(e){return t(e)}:null}function ra(){if(vt)return ht;vt=1;var e=Function.prototype.call,n=Object.prototype.hasOwnProperty,t=na();return ht=t.call(e,n)}function ca(){if(bt)return xt;var e;bt=1;var n=qt(),t=At(),a=Ft(),i=Dt(),o=Pt(),s=Bt(),r=Lt(),c=Ut(),p=zt(),u=It(),l=Nt(),d=Mt(),m=Wt(),f=Ht(),h=$t(),v=Function,x=function(e){try{return v('"use strict"; return ('+e+").constructor;")()}catch(e){}},b=Jt(),g=Kt(),y=function(){throw new r},w=b?function(){try{return y}catch(e){try{return b(arguments,"callee").get}catch(e){return y}}}():y,k=Yt()(),_=sa(),j=Zt(),R=Xt(),S=aa(),E=ta(),T={},C="undefined"!=typeof Uint8Array&&_?_(Uint8Array):e,O={__proto__:null,"%AggregateError%":"undefined"==typeof AggregateError?e:AggregateError,"%Array%":Array,"%ArrayBuffer%":"undefined"==typeof ArrayBuffer?e:ArrayBuffer,"%ArrayIteratorPrototype%":k&&_?_([][Symbol.iterator]()):e,"%AsyncFromSyncIteratorPrototype%":e,"%AsyncFunction%":T,"%AsyncGenerator%":T,"%AsyncGeneratorFunction%":T,"%AsyncIteratorPrototype%":T,"%Atomics%":"undefined"==typeof Atomics?e:Atomics,"%BigInt%":"undefined"==typeof BigInt?e:BigInt,"%BigInt64Array%":"undefined"==typeof BigInt64Array?e:BigInt64Array,"%BigUint64Array%":"undefined"==typeof BigUint64Array?e:BigUint64Array,"%Boolean%":Boolean,"%DataView%":"undefined"==typeof DataView?e:DataView,"%Date%":Date,"%decodeURI%":decodeURI,"%decodeURIComponent%":decodeURIComponent,"%encodeURI%":encodeURI,"%encodeURIComponent%":encodeURIComponent,"%Error%":t,"%eval%":eval,"%EvalError%":a,"%Float32Array%":"undefined"==typeof Float32Array?e:Float32Array,"%Float64Array%":"undefined"==typeof Float64Array?e:Float64Array,"%FinalizationRegistry%":"undefined"==typeof FinalizationRegistry?e:FinalizationRegistry,"%Function%":v,"%GeneratorFunction%":T,"%Int8Array%":"undefined"==typeof Int8Array?e:Int8Array,"%Int16Array%":"undefined"==typeof Int16Array?e:Int16Array,"%Int32Array%":"undefined"==typeof Int32Array?e:Int32Array,"%isFinite%":isFinite,"%isNaN%":isNaN,"%IteratorPrototype%":k&&_?_(_([][Symbol.iterator]())):e,"%JSON%":"object"==typeof JSON?JSON:e,"%Map%":"undefined"==typeof Map?e:Map,"%MapIteratorPrototype%":"undefined"!=typeof Map&&k&&_?_((new Map)[Symbol.iterator]()):e,"%Math%":Math,"%Number%":Number,"%Object%":n,"%Object.getOwnPropertyDescriptor%":b,"%parseFloat%":parseFloat,"%parseInt%":parseInt,"%Promise%":"undefined"==typeof Promise?e:Promise,"%Proxy%":"undefined"==typeof Proxy?e:Proxy,"%RangeError%":i,"%ReferenceError%":o,"%Reflect%":"undefined"==typeof Reflect?e:Reflect,"%RegExp%":RegExp,"%Set%":"undefined"==typeof Set?e:Set,"%SetIteratorPrototype%":"undefined"!=typeof Set&&k&&_?_((new Set)[Symbol.iterator]()):e,"%SharedArrayBuffer%":"undefined"==typeof SharedArrayBuffer?e:SharedArrayBuffer,"%String%":String,"%StringIteratorPrototype%":k&&_?_(""[Symbol.iterator]()):e,"%Symbol%":k?Symbol:e,"%SyntaxError%":s,"%ThrowTypeError%":w,"%TypedArray%":C,"%TypeError%":r,"%Uint8Array%":"undefined"==typeof Uint8Array?e:Uint8Array,"%Uint8ClampedArray%":"undefined"==typeof Uint8ClampedArray?e:Uint8ClampedArray,"%Uint16Array%":"undefined"==typeof Uint16Array?e:Uint16Array,"%Uint32Array%":"undefined"==typeof Uint32Array?e:Uint32Array,"%URIError%":c,"%WeakMap%":"undefined"==typeof WeakMap?e:WeakMap,"%WeakRef%":"undefined"==typeof WeakRef?e:WeakRef,"%WeakSet%":"undefined"==typeof WeakSet?e:WeakSet,"%Function.prototype.call%":E,"%Function.prototype.apply%":S,"%Object.defineProperty%":g,"%Object.getPrototypeOf%":j,"%Math.abs%":p,"%Math.floor%":u,"%Math.max%":l,"%Math.min%":d,"%Math.pow%":m,"%Math.round%":f,"%Math.sign%":h,"%Reflect.getPrototypeOf%":R};if(_)try{null.error}catch(e){var q=_(_(e));O["%Error.prototype%"]=q}var A=function e(n){var t;if("%AsyncFunction%"===n)t=x("async function () {}");else if("%GeneratorFunction%"===n)t=x("function* () {}");else if("%AsyncGeneratorFunction%"===n)t=x("async function* () {}");else if("%AsyncGenerator%"===n){var a=e("%AsyncGeneratorFunction%");a&&(t=a.prototype)}else if("%AsyncIteratorPrototype%"===n){var i=e("%AsyncGenerator%");i&&_&&(t=_(i.prototype))}return O[n]=t,t},F={__proto__:null,"%ArrayBufferPrototype%":["ArrayBuffer","prototype"],"%ArrayPrototype%":["Array","prototype"],"%ArrayProto_entries%":["Array","prototype","entries"],"%ArrayProto_forEach%":["Array","prototype","forEach"],"%ArrayProto_keys%":["Array","prototype","keys"],"%ArrayProto_values%":["Array","prototype","values"],"%AsyncFunctionPrototype%":["AsyncFunction","prototype"],"%AsyncGenerator%":["AsyncGeneratorFunction","prototype"],"%AsyncGeneratorPrototype%":["AsyncGeneratorFunction","prototype","prototype"],"%BooleanPrototype%":["Boolean","prototype"],"%DataViewPrototype%":["DataView","prototype"],"%DatePrototype%":["Date","prototype"],"%ErrorPrototype%":["Error","prototype"],"%EvalErrorPrototype%":["EvalError","prototype"],"%Float32ArrayPrototype%":["Float32Array","prototype"],"%Float64ArrayPrototype%":["Float64Array","prototype"],"%FunctionPrototype%":["Function","prototype"],"%Generator%":["GeneratorFunction","prototype"],"%GeneratorPrototype%":["GeneratorFunction","prototype","prototype"],"%Int8ArrayPrototype%":["Int8Array","prototype"],"%Int16ArrayPrototype%":["Int16Array","prototype"],"%Int32ArrayPrototype%":["Int32Array","prototype"],"%JSONParse%":["JSON","parse"],"%JSONStringify%":["JSON","stringify"],"%MapPrototype%":["Map","prototype"],"%NumberPrototype%":["Number","prototype"],"%ObjectPrototype%":["Object","prototype"],"%ObjProto_toString%":["Object","prototype","toString"],"%ObjProto_valueOf%":["Object","prototype","valueOf"],"%PromisePrototype%":["Promise","prototype"],"%PromiseProto_then%":["Promise","prototype","then"],"%Promise_all%":["Promise","all"],"%Promise_reject%":["Promise","reject"],"%Promise_resolve%":["Promise","resolve"],"%RangeErrorPrototype%":["RangeError","prototype"],"%ReferenceErrorPrototype%":["ReferenceError","prototype"],"%RegExpPrototype%":["RegExp","prototype"],"%SetPrototype%":["Set","prototype"],"%SharedArrayBufferPrototype%":["SharedArrayBuffer","prototype"],"%StringPrototype%":["String","prototype"],"%SymbolPrototype%":["Symbol","prototype"],"%SyntaxErrorPrototype%":["SyntaxError","prototype"],"%TypedArrayPrototype%":["TypedArray","prototype"],"%TypeErrorPrototype%":["TypeError","prototype"],"%Uint8ArrayPrototype%":["Uint8Array","prototype"],"%Uint8ClampedArrayPrototype%":["Uint8ClampedArray","prototype"],"%Uint16ArrayPrototype%":["Uint16Array","prototype"],"%Uint32ArrayPrototype%":["Uint32Array","prototype"],"%URIErrorPrototype%":["URIError","prototype"],"%WeakMapPrototype%":["WeakMap","prototype"],"%WeakSetPrototype%":["WeakSet","prototype"]},D=na(),P=ra(),B=D.call(E,Array.prototype.concat),L=D.call(S,Array.prototype.splice),U=D.call(E,String.prototype.replace),z=D.call(E,String.prototype.slice),I=D.call(E,RegExp.prototype.exec),N=/[^%.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|%$))/g,M=/\\(\\)?/g,W=function(e,n){var t,a=e;if(P(F,a)&&(a="%"+(t=F[a])[0]+"%"),P(O,a)){var i=O[a];if(i===T&&(i=A(a)),void 0===i&&!n)throw new r("intrinsic "+e+" exists, but is not available. Please file an issue!");return{alias:t,name:a,value:i}}throw new s("intrinsic "+e+" does not exist!")};return xt=function(e,n){if("string"!=typeof e||0===e.length)throw new r("intrinsic name must be a non-empty string");if(arguments.length>1&&"boolean"!=typeof n)throw new r('"allowMissing" argument must be a boolean');if(null===I(/^%?[^%]*%?$/,e))throw new s("`%` may not be present anywhere but at the beginning and end of the intrinsic name");var t=function(e){var n=z(e,0,1),t=z(e,-1);if("%"===n&&"%"!==t)throw new s("invalid intrinsic syntax, expected closing `%`");if("%"===t&&"%"!==n)throw new s("invalid intrinsic syntax, expected opening `%`");var a=[];return U(e,N,(function(e,n,t,i){a[a.length]=t?U(i,M,"$1"):n||e})),a}(e),a=t.length>0?t[0]:"",i=W("%"+a+"%",n),o=i.name,c=i.value,p=!1,u=i.alias;u&&(a=u[0],L(t,B([0,1],u)));for(var l=1,d=!0;l<t.length;l+=1){var m=t[l],f=z(m,0,1),h=z(m,-1);if(('"'===f||"'"===f||"`"===f||'"'===h||"'"===h||"`"===h)&&f!==h)throw new s("property names with quotes must have matching quotes");if("constructor"!==m&&d||(p=!0),P(O,o="%"+(a+="."+m)+"%"))c=O[o];else if(null!=c){if(!(m in c)){if(!n)throw new r("base intrinsic for "+e+" exists, but the property is not available.");return}if(b&&l+1>=t.length){var v=b(c,m);c=(d=!!v)&&"get"in v&&!("originalValue"in v.get)?v.get:c[m]}else d=P(c,m),c=c[m];d&&!p&&(O[o]=c)}}return c},xt}function pa(){if(kt)return wt;kt=1;var e=ca()("%Object.defineProperty%",!0),n=function(){if(yt)return gt;yt=1;var e=Qt();return gt=function(){return e()&&!!Symbol.toStringTag}}()(),t=ra(),a=Lt(),i=n?Symbol.toStringTag:null;return wt=function(n,o){var s=arguments.length>2&&!!arguments[2]&&arguments[2].force,r=arguments.length>2&&!!arguments[2]&&arguments[2].nonConfigurable;if(void 0!==s&&"boolean"!=typeof s||void 0!==r&&"boolean"!=typeof r)throw new a("if provided, the `overrideIfSet` and `nonConfigurable` options must be booleans");!i||!s&&t(n,i)||(e?e(n,i,{configurable:!r,enumerable:!1,value:o,writable:!1}):n[i]=o)},wt}function ua(){return jt||(jt=1,_t=function(e,n){return Object.keys(n).forEach((function(t){e[t]=e[t]||n[t]})),e}),_t}var la=function(){if(St)return Rt;St=1;var r=Se(),c=e,p=t,u=a,l=i,d=o.parse,m=s,f=n.Stream,h=$e(),v=Ot(),x=pa(),b=ua();function g(e){if(!(this instanceof g))return new g(e);for(var n in this._overheadLength=0,this._valueLength=0,this._valuesToMeasure=[],r.call(this),e=e||{})this[n]=e[n]}return Rt=g,c.inherits(g,r),g.LINE_BREAK="\r\n",g.DEFAULT_CONTENT_TYPE="application/octet-stream",g.prototype.append=function(e,n,t){"string"==typeof(t=t||{})&&(t={filename:t});var a=r.prototype.append.bind(this);if("number"==typeof n&&(n=""+n),Array.isArray(n))this._error(new Error("Arrays are not supported."));else{var i=this._multiPartHeader(e,n,t),o=this._multiPartFooter();a(i),a(n),a(o),this._trackLength(i,n,t)}},g.prototype._trackLength=function(e,n,t){var a=0;null!=t.knownLength?a+=+t.knownLength:Buffer.isBuffer(n)?a=n.length:"string"==typeof n&&(a=Buffer.byteLength(n)),this._valueLength+=a,this._overheadLength+=Buffer.byteLength(e)+g.LINE_BREAK.length,n&&(n.path||n.readable&&Object.prototype.hasOwnProperty.call(n,"httpVersion")||n instanceof f)&&(t.knownLength||this._valuesToMeasure.push(n))},g.prototype._lengthRetriever=function(e,n){Object.prototype.hasOwnProperty.call(e,"fd")?null!=e.end&&e.end!=1/0&&null!=e.start?n(null,e.end+1-(e.start?e.start:0)):m.stat(e.path,(function(t,a){var i;t?n(t):(i=a.size-(e.start?e.start:0),n(null,i))})):Object.prototype.hasOwnProperty.call(e,"httpVersion")?n(null,+e.headers["content-length"]):Object.prototype.hasOwnProperty.call(e,"httpModule")?(e.on("response",(function(t){e.pause(),n(null,+t.headers["content-length"])})),e.resume()):n("Unknown stream")},g.prototype._multiPartHeader=function(e,n,t){if("string"==typeof t.header)return t.header;var a,i=this._getContentDisposition(n,t),o=this._getContentType(n,t),s="",r={"Content-Disposition":["form-data",'name="'+e+'"'].concat(i||[]),"Content-Type":[].concat(o||[])};for(var c in"object"==typeof t.header&&b(r,t.header),r)if(Object.prototype.hasOwnProperty.call(r,c)){if(null==(a=r[c]))continue;Array.isArray(a)||(a=[a]),a.length&&(s+=c+": "+a.join("; ")+g.LINE_BREAK)}return"--"+this.getBoundary()+g.LINE_BREAK+s+g.LINE_BREAK},g.prototype._getContentDisposition=function(e,n){var t,a;return"string"==typeof n.filepath?t=p.normalize(n.filepath).replace(/\\/g,"/"):n.filename||e.name||e.path?t=p.basename(n.filename||e.name||e.path):e.readable&&Object.prototype.hasOwnProperty.call(e,"httpVersion")&&(t=p.basename(e.client._httpMessage.path||"")),t&&(a='filename="'+t+'"'),a},g.prototype._getContentType=function(e,n){var t=n.contentType;return!t&&e.name&&(t=h.lookup(e.name)),!t&&e.path&&(t=h.lookup(e.path)),!t&&e.readable&&Object.prototype.hasOwnProperty.call(e,"httpVersion")&&(t=e.headers["content-type"]),t||!n.filepath&&!n.filename||(t=h.lookup(n.filepath||n.filename)),t||"object"!=typeof e||(t=g.DEFAULT_CONTENT_TYPE),t},g.prototype._multiPartFooter=function(){return function(e){var n=g.LINE_BREAK;0===this._streams.length&&(n+=this._lastBoundary()),e(n)}.bind(this)},g.prototype._lastBoundary=function(){return"--"+this.getBoundary()+"--"+g.LINE_BREAK},g.prototype.getHeaders=function(e){var n,t={"content-type":"multipart/form-data; boundary="+this.getBoundary()};for(n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[n.toLowerCase()]=e[n]);return t},g.prototype.setBoundary=function(e){this._boundary=e},g.prototype.getBoundary=function(){return this._boundary||this._generateBoundary(),this._boundary},g.prototype.getBuffer=function(){for(var e=new Buffer.alloc(0),n=this.getBoundary(),t=0,a=this._streams.length;t<a;t++)"function"!=typeof this._streams[t]&&(e=Buffer.isBuffer(this._streams[t])?Buffer.concat([e,this._streams[t]]):Buffer.concat([e,Buffer.from(this._streams[t])]),"string"==typeof this._streams[t]&&this._streams[t].substring(2,n.length+2)===n||(e=Buffer.concat([e,Buffer.from(g.LINE_BREAK)])));return Buffer.concat([e,Buffer.from(this._lastBoundary())])},g.prototype._generateBoundary=function(){for(var e="--------------------------",n=0;n<24;n++)e+=Math.floor(10*Math.random()).toString(16);this._boundary=e},g.prototype.getLengthSync=function(){var e=this._overheadLength+this._valueLength;return this._streams.length&&(e+=this._lastBoundary().length),this.hasKnownLength()||this._error(new Error("Cannot calculate proper length in synchronous way.")),e},g.prototype.hasKnownLength=function(){var e=!0;return this._valuesToMeasure.length&&(e=!1),e},g.prototype.getLength=function(e){var n=this._overheadLength+this._valueLength;this._streams.length&&(n+=this._lastBoundary().length),this._valuesToMeasure.length?v.parallel(this._valuesToMeasure,this._lengthRetriever,(function(t,a){t?e(t):(a.forEach((function(e){n+=e})),e(null,n))})):process.nextTick(e.bind(this,null,n))},g.prototype.submit=function(e,n){var t,a,i={method:"post"};return"string"==typeof e?(e=d(e),a=b({port:e.port,path:e.pathname,host:e.hostname,protocol:e.protocol},i)):(a=b(e,i)).port||(a.port="https:"==a.protocol?443:80),a.headers=this.getHeaders(e.headers),t="https:"==a.protocol?l.request(a):u.request(a),this.getLength(function(e,a){if(e&&"Unknown stream"!==e)this._error(e);else if(a&&t.setHeader("Content-Length",a),this.pipe(t),n){var i,o=function(e,a){return t.removeListener("error",o),t.removeListener("response",i),n.call(this,e,a)};i=o.bind(this,null),t.on("error",o),t.on("response",i)}}.bind(this)),t},g.prototype._error=function(e){this.error||(this.error=e,this.pause(),this.emit("error",e))},g.prototype.toString=function(){return"[object FormData]"},x(g,"FormData"),Rt}(),da=g(la);function ma(e){return be.isPlainObject(e)||be.isArray(e)}function fa(e){return be.endsWith(e,"[]")?e.slice(0,-2):e}function ha(e,n,t){return e?e.concat(n).map((function(e,n){return e=fa(e),!t&&n?"["+e+"]":e})).join(t?".":""):n}const va=be.toFlatObject(be,{},null,(function(e){return/^is[A-Z]/.test(e)}));function xa(e,n,t){if(!be.isObject(e))throw new TypeError("target must be an object");n=n||new(da||FormData);const a=(t=be.toFlatObject(t,{metaTokens:!0,dots:!1,indexes:!1},!1,(function(e,n){return!be.isUndefined(n[e])}))).metaTokens,i=t.visitor||p,o=t.dots,s=t.indexes,r=(t.Blob||"undefined"!=typeof Blob&&Blob)&&be.isSpecCompliantForm(n);if(!be.isFunction(i))throw new TypeError("visitor must be a function");function c(e){if(null===e)return"";if(be.isDate(e))return e.toISOString();if(!r&&be.isBlob(e))throw new ge("Blob is not supported. Use a Buffer instead.");return be.isArrayBuffer(e)||be.isTypedArray(e)?r&&"function"==typeof Blob?new Blob([e]):Buffer.from(e):e}function p(e,t,i){let r=e;if(e&&!i&&"object"==typeof e)if(be.endsWith(t,"{}"))t=a?t:t.slice(0,-2),e=JSON.stringify(e);else if(be.isArray(e)&&function(e){return be.isArray(e)&&!e.some(ma)}(e)||(be.isFileList(e)||be.endsWith(t,"[]"))&&(r=be.toArray(e)))return t=fa(t),r.forEach((function(e,a){!be.isUndefined(e)&&null!==e&&n.append(!0===s?ha([t],a,o):null===s?t:t+"[]",c(e))})),!1;return!!ma(e)||(n.append(ha(i,t,o),c(e)),!1)}const u=[],l=Object.assign(va,{defaultVisitor:p,convertValue:c,isVisitable:ma});if(!be.isObject(e))throw new TypeError("data must be an object");return function e(t,a){if(!be.isUndefined(t)){if(-1!==u.indexOf(t))throw Error("Circular reference detected in "+a.join("."));u.push(t),be.forEach(t,(function(t,o){!0===(!(be.isUndefined(t)||null===t)&&i.call(n,t,be.isString(o)?o.trim():o,a,l))&&e(t,a?a.concat(o):[o])})),u.pop()}}(e),n}function ba(e){const n={"!":"%21","'":"%27","(":"%28",")":"%29","~":"%7E","%20":"+","%00":"\0"};return encodeURIComponent(e).replace(/[!'()~]|%20|%00/g,(function(e){return n[e]}))}function ga(e,n){this._pairs=[],e&&xa(e,this,n)}const ya=ga.prototype;function wa(e){return encodeURIComponent(e).replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}function ka(e,n,t){if(!n)return e;const a=t&&t.encode||wa;be.isFunction(t)&&(t={serialize:t});const i=t&&t.serialize;let o;if(o=i?i(n,t):be.isURLSearchParams(n)?n.toString():new ga(n,t).toString(a),o){const n=e.indexOf("#");-1!==n&&(e=e.slice(0,n)),e+=(-1===e.indexOf("?")?"?":"&")+o}return e}ya.append=function(e,n){this._pairs.push([e,n])},ya.toString=function(e){const n=e?function(n){return e.call(this,n,ba)}:ba;return this._pairs.map((function(e){return n(e[0])+"="+n(e[1])}),"").join("&")};class _a{constructor(){this.handlers=[]}use(e,n,t){return this.handlers.push({fulfilled:e,rejected:n,synchronous:!!t&&t.synchronous,runWhen:t?t.runWhen:null}),this.handlers.length-1}eject(e){this.handlers[e]&&(this.handlers[e]=null)}clear(){this.handlers&&(this.handlers=[])}forEach(e){be.forEach(this.handlers,(function(n){null!==n&&e(n)}))}}var ja={silentJSONParsing:!0,forcedJSONParsing:!0,clarifyTimeoutError:!1},Ra={isNode:!0,classes:{URLSearchParams:o.URLSearchParams,FormData:da,Blob:"undefined"!=typeof Blob&&Blob||null},protocols:["http","https","file","data"]};const Sa="undefined"!=typeof window&&"undefined"!=typeof document,Ea="object"==typeof navigator&&navigator||void 0,Ta=Sa&&(!Ea||["ReactNative","NativeScript","NS"].indexOf(Ea.product)<0),Ca="undefined"!=typeof WorkerGlobalScope&&self instanceof WorkerGlobalScope&&"function"==typeof self.importScripts,Oa=Sa&&window.location.href||"http://localhost";var qa={...Object.freeze({__proto__:null,hasBrowserEnv:Sa,hasStandardBrowserEnv:Ta,hasStandardBrowserWebWorkerEnv:Ca,navigator:Ea,origin:Oa}),...Ra};function Aa(e){function n(e,t,a,i){let o=e[i++];if("__proto__"===o)return!0;const s=Number.isFinite(+o),r=i>=e.length;if(o=!o&&be.isArray(a)?a.length:o,r)return be.hasOwnProp(a,o)?a[o]=[a[o],t]:a[o]=t,!s;a[o]&&be.isObject(a[o])||(a[o]=[]);return n(e,t,a[o],i)&&be.isArray(a[o])&&(a[o]=function(e){const n={},t=Object.keys(e);let a;const i=t.length;let o;for(a=0;a<i;a++)o=t[a],n[o]=e[o];return n}(a[o])),!s}if(be.isFormData(e)&&be.isFunction(e.entries)){const t={};return be.forEachEntry(e,((e,a)=>{n(function(e){return be.matchAll(/\w+|\[(\w*)]/g,e).map((e=>"[]"===e[0]?"":e[1]||e[0]))}(e),a,t,0)})),t}return null}const Fa={transitional:ja,adapter:["xhr","http","fetch"],transformRequest:[function(e,n){const t=n.getContentType()||"",a=t.indexOf("application/json")>-1,i=be.isObject(e);i&&be.isHTMLForm(e)&&(e=new FormData(e));if(be.isFormData(e))return a?JSON.stringify(Aa(e)):e;if(be.isArrayBuffer(e)||be.isBuffer(e)||be.isStream(e)||be.isFile(e)||be.isBlob(e)||be.isReadableStream(e))return e;if(be.isArrayBufferView(e))return e.buffer;if(be.isURLSearchParams(e))return n.setContentType("application/x-www-form-urlencoded;charset=utf-8",!1),e.toString();let o;if(i){if(t.indexOf("application/x-www-form-urlencoded")>-1)return function(e,n){return xa(e,new qa.classes.URLSearchParams,Object.assign({visitor:function(e,n,t,a){return qa.isNode&&be.isBuffer(e)?(this.append(n,e.toString("base64")),!1):a.defaultVisitor.apply(this,arguments)}},n))}(e,this.formSerializer).toString();if((o=be.isFileList(e))||t.indexOf("multipart/form-data")>-1){const n=this.env&&this.env.FormData;return xa(o?{"files[]":e}:e,n&&new n,this.formSerializer)}}return i||a?(n.setContentType("application/json",!1),function(e,n,t){if(be.isString(e))try{return(n||JSON.parse)(e),be.trim(e)}catch(e){if("SyntaxError"!==e.name)throw e}return(t||JSON.stringify)(e)}(e)):e}],transformResponse:[function(e){const n=this.transitional||Fa.transitional,t=n&&n.forcedJSONParsing,a="json"===this.responseType;if(be.isResponse(e)||be.isReadableStream(e))return e;if(e&&be.isString(e)&&(t&&!this.responseType||a)){const t=!(n&&n.silentJSONParsing)&&a;try{return JSON.parse(e)}catch(e){if(t){if("SyntaxError"===e.name)throw ge.from(e,ge.ERR_BAD_RESPONSE,this,null,this.response);throw e}}}return e}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,maxBodyLength:-1,env:{FormData:qa.classes.FormData,Blob:qa.classes.Blob},validateStatus:function(e){return e>=200&&e<300},headers:{common:{Accept:"application/json, text/plain, */*","Content-Type":void 0}}};be.forEach(["delete","get","head","post","put","patch"],(e=>{Fa.headers[e]={}}));const Da=be.toObjectSet(["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"]);const Pa=Symbol("internals");function Ba(e){return e&&String(e).trim().toLowerCase()}function La(e){return!1===e||null==e?e:be.isArray(e)?e.map(La):String(e)}function Ua(e,n,t,a,i){return be.isFunction(a)?a.call(this,n,t):(i&&(n=t),be.isString(n)?be.isString(a)?-1!==n.indexOf(a):be.isRegExp(a)?a.test(n):void 0:void 0)}let za=class{constructor(e){e&&this.set(e)}set(e,n,t){const a=this;function i(e,n,t){const i=Ba(n);if(!i)throw new Error("header name must be a non-empty string");const o=be.findKey(a,i);(!o||void 0===a[o]||!0===t||void 0===t&&!1!==a[o])&&(a[o||n]=La(e))}const o=(e,n)=>be.forEach(e,((e,t)=>i(e,t,n)));if(be.isPlainObject(e)||e instanceof this.constructor)o(e,n);else if(be.isString(e)&&(e=e.trim())&&!/^[-_a-zA-Z0-9^`|~,!#$%&'*+.]+$/.test(e.trim()))o((e=>{const n={};let t,a,i;return e&&e.split("\n").forEach((function(e){i=e.indexOf(":"),t=e.substring(0,i).trim().toLowerCase(),a=e.substring(i+1).trim(),!t||n[t]&&Da[t]||("set-cookie"===t?n[t]?n[t].push(a):n[t]=[a]:n[t]=n[t]?n[t]+", "+a:a)})),n})(e),n);else if(be.isHeaders(e))for(const[n,a]of e.entries())i(a,n,t);else null!=e&&i(n,e,t);return this}get(e,n){if(e=Ba(e)){const t=be.findKey(this,e);if(t){const e=this[t];if(!n)return e;if(!0===n)return function(e){const n=Object.create(null),t=/([^\s,;=]+)\s*(?:=\s*([^,;]+))?/g;let a;for(;a=t.exec(e);)n[a[1]]=a[2];return n}(e);if(be.isFunction(n))return n.call(this,e,t);if(be.isRegExp(n))return n.exec(e);throw new TypeError("parser must be boolean|regexp|function")}}}has(e,n){if(e=Ba(e)){const t=be.findKey(this,e);return!(!t||void 0===this[t]||n&&!Ua(0,this[t],t,n))}return!1}delete(e,n){const t=this;let a=!1;function i(e){if(e=Ba(e)){const i=be.findKey(t,e);!i||n&&!Ua(0,t[i],i,n)||(delete t[i],a=!0)}}return be.isArray(e)?e.forEach(i):i(e),a}clear(e){const n=Object.keys(this);let t=n.length,a=!1;for(;t--;){const i=n[t];e&&!Ua(0,this[i],i,e,!0)||(delete this[i],a=!0)}return a}normalize(e){const n=this,t={};return be.forEach(this,((a,i)=>{const o=be.findKey(t,i);if(o)return n[o]=La(a),void delete n[i];const s=e?function(e){return e.trim().toLowerCase().replace(/([a-z\d])(\w*)/g,((e,n,t)=>n.toUpperCase()+t))}(i):String(i).trim();s!==i&&delete n[i],n[s]=La(a),t[s]=!0})),this}concat(...e){return this.constructor.concat(this,...e)}toJSON(e){const n=Object.create(null);return be.forEach(this,((t,a)=>{null!=t&&!1!==t&&(n[a]=e&&be.isArray(t)?t.join(", "):t)})),n}[Symbol.iterator](){return Object.entries(this.toJSON())[Symbol.iterator]()}toString(){return Object.entries(this.toJSON()).map((([e,n])=>e+": "+n)).join("\n")}get[Symbol.toStringTag](){return"AxiosHeaders"}static from(e){return e instanceof this?e:new this(e)}static concat(e,...n){const t=new this(e);return n.forEach((e=>t.set(e))),t}static accessor(e){const n=(this[Pa]=this[Pa]={accessors:{}}).accessors,t=this.prototype;function a(e){const a=Ba(e);n[a]||(!function(e,n){const t=be.toCamelCase(" "+n);["get","set","has"].forEach((a=>{Object.defineProperty(e,a+t,{value:function(e,t,i){return this[a].call(this,n,e,t,i)},configurable:!0})}))}(t,e),n[a]=!0)}return be.isArray(e)?e.forEach(a):a(e),this}};function Ia(e,n){const t=this||Fa,a=n||t,i=za.from(a.headers);let o=a.data;return be.forEach(e,(function(e){o=e.call(t,o,i.normalize(),n?n.status:void 0)})),i.normalize(),o}function Na(e){return!(!e||!e.__CANCEL__)}function Ma(e,n,t){ge.call(this,null==e?"canceled":e,ge.ERR_CANCELED,n,t),this.name="CanceledError"}function Wa(e,n,t){const a=t.config.validateStatus;t.status&&a&&!a(t.status)?n(new ge("Request failed with status code "+t.status,[ge.ERR_BAD_REQUEST,ge.ERR_BAD_RESPONSE][Math.floor(t.status/100)-4],t.config,t.request,t)):e(t)}function Ha(e,n){return e&&!/^([a-z][a-z\d+\-.]*:)?\/\//i.test(n)?function(e,n){return n?e.replace(/\/?\/$/,"")+"/"+n.replace(/^\/+/,""):e}(e,n):n}za.accessor(["Content-Type","Content-Length","Accept","Accept-Encoding","User-Agent","Authorization"]),be.reduceDescriptors(za.prototype,(({value:e},n)=>{let t=n[0].toUpperCase()+n.slice(1);return{get:()=>e,set(e){this[t]=e}}})),be.freezeMethods(za),be.inherits(Ma,ge,{__CANCEL__:!0});var Va,$a={};var Ga,Ja,Ka,Qa,Ya,Xa=g(function(){if(Va)return $a;Va=1;var e=o.parse,n={ftp:21,gopher:70,http:80,https:443,ws:80,wss:443},t=String.prototype.endsWith||function(e){return e.length<=this.length&&-1!==this.indexOf(e,this.length-e.length)};function a(e){return process.env[e.toLowerCase()]||process.env[e.toUpperCase()]||""}return $a.getProxyForUrl=function(i){var o="string"==typeof i?e(i):i||{},s=o.protocol,r=o.host,c=o.port;if("string"!=typeof r||!r||"string"!=typeof s)return"";if(s=s.split(":",1)[0],!function(e,n){var i=(a("npm_config_no_proxy")||a("no_proxy")).toLowerCase();if(!i)return!0;if("*"===i)return!1;return i.split(/[,\s]/).every((function(a){if(!a)return!0;var i=a.match(/^(.+):(\d+)$/),o=i?i[1]:a,s=i?parseInt(i[2]):0;return!(!s||s===n)||(/^[.*]/.test(o)?("*"===o.charAt(0)&&(o=o.slice(1)),!t.call(e,o)):e!==o)}))}(r=r.replace(/:\d*$/,""),c=parseInt(c)||n[s]||0))return"";var p=a("npm_config_"+s+"_proxy")||a(s+"_proxy")||a("npm_config_proxy")||a("all_proxy");return p&&-1===p.indexOf("://")&&(p=s+"://"+p),p},$a}()),Za={exports:{}},ei={exports:{}},ni={exports:{}};function ti(){if(Ja)return Ga;Ja=1;var e=1e3,n=60*e,t=60*n,a=24*t,i=7*a,o=365.25*a;function s(e,n,t,a){var i=n>=1.5*t;return Math.round(e/t)+" "+a+(i?"s":"")}return Ga=function(r,c){c=c||{};var p=typeof r;if("string"===p&&r.length>0)return function(s){if((s=String(s)).length>100)return;var r=/^(-?(?:\d+)?\.?\d+) *(milliseconds?|msecs?|ms|seconds?|secs?|s|minutes?|mins?|m|hours?|hrs?|h|days?|d|weeks?|w|years?|yrs?|y)?$/i.exec(s);if(!r)return;var c=parseFloat(r[1]);switch((r[2]||"ms").toLowerCase()){case"years":case"year":case"yrs":case"yr":case"y":return c*o;case"weeks":case"week":case"w":return c*i;case"days":case"day":case"d":return c*a;case"hours":case"hour":case"hrs":case"hr":case"h":return c*t;case"minutes":case"minute":case"mins":case"min":case"m":return c*n;case"seconds":case"second":case"secs":case"sec":case"s":return c*e;case"milliseconds":case"millisecond":case"msecs":case"msec":case"ms":return c;default:return}}(r);if("number"===p&&isFinite(r))return c.long?function(i){var o=Math.abs(i);if(o>=a)return s(i,o,a,"day");if(o>=t)return s(i,o,t,"hour");if(o>=n)return s(i,o,n,"minute");if(o>=e)return s(i,o,e,"second");return i+" ms"}(r):function(i){var o=Math.abs(i);if(o>=a)return Math.round(i/a)+"d";if(o>=t)return Math.round(i/t)+"h";if(o>=n)return Math.round(i/n)+"m";if(o>=e)return Math.round(i/e)+"s";return i+"ms"}(r);throw new Error("val is not a non-empty string or a valid number. val="+JSON.stringify(r))},Ga}function ai(){if(Qa)return Ka;return Qa=1,Ka=function(e){function n(e){let a,i,o,s=null;function r(...e){if(!r.enabled)return;const t=r,i=Number(new Date),o=i-(a||i);t.diff=o,t.prev=a,t.curr=i,a=i,e[0]=n.coerce(e[0]),"string"!=typeof e[0]&&e.unshift("%O");let s=0;e[0]=e[0].replace(/%([a-zA-Z%])/g,((a,i)=>{if("%%"===a)return"%";s++;const o=n.formatters[i];if("function"==typeof o){const n=e[s];a=o.call(t,n),e.splice(s,1),s--}return a})),n.formatArgs.call(t,e);(t.log||n.log).apply(t,e)}return r.namespace=e,r.useColors=n.useColors(),r.color=n.selectColor(e),r.extend=t,r.destroy=n.destroy,Object.defineProperty(r,"enabled",{enumerable:!0,configurable:!1,get:()=>null!==s?s:(i!==n.namespaces&&(i=n.namespaces,o=n.enabled(e)),o),set:e=>{s=e}}),"function"==typeof n.init&&n.init(r),r}function t(e,t){const a=n(this.namespace+(void 0===t?":":t)+e);return a.log=this.log,a}function a(e,n){let t=0,a=0,i=-1,o=0;for(;t<e.length;)if(a<n.length&&(n[a]===e[t]||"*"===n[a]))"*"===n[a]?(i=a,o=t,a++):(t++,a++);else{if(-1===i)return!1;a=i+1,o++,t=o}for(;a<n.length&&"*"===n[a];)a++;return a===n.length}return n.debug=n,n.default=n,n.coerce=function(e){if(e instanceof Error)return e.stack||e.message;return e},n.disable=function(){const e=[...n.names,...n.skips.map((e=>"-"+e))].join(",");return n.enable(""),e},n.enable=function(e){n.save(e),n.namespaces=e,n.names=[],n.skips=[];const t=("string"==typeof e?e:"").trim().replace(" ",",").split(",").filter(Boolean);for(const e of t)"-"===e[0]?n.skips.push(e.slice(1)):n.names.push(e)},n.enabled=function(e){for(const t of n.skips)if(a(e,t))return!1;for(const t of n.names)if(a(e,t))return!0;return!1},n.humanize=ti(),n.destroy=function(){console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`.")},Object.keys(e).forEach((t=>{n[t]=e[t]})),n.names=[],n.skips=[],n.formatters={},n.selectColor=function(e){let t=0;for(let n=0;n<e.length;n++)t=(t<<5)-t+e.charCodeAt(n),t|=0;return n.colors[Math.abs(t)%n.colors.length]},n.enable(n.load()),n},Ka}var ii,oi,si,ri,ci,pi,ui,li,di,mi={exports:{}};function fi(){return oi?ii:(oi=1,ii=(e,n=process.argv)=>{const t=e.startsWith("-")?"":1===e.length?"-":"--",a=n.indexOf(t+e),i=n.indexOf("--");return-1!==a&&(-1===i||a<i)})}function hi(){return ci||(ci=1,function(n,t){const a=c,i=e;t.init=function(e){e.inspectOpts={};const n=Object.keys(t.inspectOpts);for(let a=0;a<n.length;a++)e.inspectOpts[n[a]]=t.inspectOpts[n[a]]},t.log=function(...e){return process.stderr.write(i.formatWithOptions(t.inspectOpts,...e)+"\n")},t.formatArgs=function(e){const{namespace:a,useColors:i}=this;if(i){const t=this.color,i="[3"+(t<8?t:"8;5;"+t),o=`  ${i};1m${a} [0m`;e[0]=o+e[0].split("\n").join("\n"+o),e.push(i+"m+"+n.exports.humanize(this.diff)+"[0m")}else e[0]=function(){if(t.inspectOpts.hideDate)return"";return(new Date).toISOString()+" "}()+a+" "+e[0]},t.save=function(e){e?process.env.DEBUG=e:delete process.env.DEBUG},t.load=function(){return process.env.DEBUG},t.useColors=function(){return"colors"in t.inspectOpts?Boolean(t.inspectOpts.colors):a.isatty(process.stderr.fd)},t.destroy=i.deprecate((()=>{}),"Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`."),t.colors=[6,2,3,4,5,1];try{const e=function(){if(ri)return si;ri=1;const e=p,n=c,t=fi(),{env:a}=process;let i;function o(e){return 0!==e&&{level:e,hasBasic:!0,has256:e>=2,has16m:e>=3}}function s(n,o){if(0===i)return 0;if(t("color=16m")||t("color=full")||t("color=truecolor"))return 3;if(t("color=256"))return 2;if(n&&!o&&void 0===i)return 0;const s=i||0;if("dumb"===a.TERM)return s;if("win32"===process.platform){const n=e.release().split(".");return Number(n[0])>=10&&Number(n[2])>=10586?Number(n[2])>=14931?3:2:1}if("CI"in a)return["TRAVIS","CIRCLECI","APPVEYOR","GITLAB_CI","GITHUB_ACTIONS","BUILDKITE"].some((e=>e in a))||"codeship"===a.CI_NAME?1:s;if("TEAMCITY_VERSION"in a)return/^(9\.(0*[1-9]\d*)\.|\d{2,}\.)/.test(a.TEAMCITY_VERSION)?1:0;if("truecolor"===a.COLORTERM)return 3;if("TERM_PROGRAM"in a){const e=parseInt((a.TERM_PROGRAM_VERSION||"").split(".")[0],10);switch(a.TERM_PROGRAM){case"iTerm.app":return e>=3?3:2;case"Apple_Terminal":return 2}}return/-256(color)?$/i.test(a.TERM)?2:/^screen|^xterm|^vt100|^vt220|^rxvt|color|ansi|cygwin|linux/i.test(a.TERM)||"COLORTERM"in a?1:s}return t("no-color")||t("no-colors")||t("color=false")||t("color=never")?i=0:(t("color")||t("colors")||t("color=true")||t("color=always"))&&(i=1),"FORCE_COLOR"in a&&(i="true"===a.FORCE_COLOR?1:"false"===a.FORCE_COLOR?0:0===a.FORCE_COLOR.length?1:Math.min(parseInt(a.FORCE_COLOR,10),3)),si={supportsColor:function(e){return o(s(e,e&&e.isTTY))},stdout:o(s(!0,n.isatty(1))),stderr:o(s(!0,n.isatty(2)))},si}();e&&(e.stderr||e).level>=2&&(t.colors=[20,21,26,27,32,33,38,39,40,41,42,43,44,45,56,57,62,63,68,69,74,75,76,77,78,79,80,81,92,93,98,99,112,113,128,129,134,135,148,149,160,161,162,163,164,165,166,167,168,169,170,171,172,173,178,179,184,185,196,197,198,199,200,201,202,203,204,205,206,207,208,209,214,215,220,221])}catch(e){}t.inspectOpts=Object.keys(process.env).filter((e=>/^debug_/i.test(e))).reduce(((e,n)=>{const t=n.substring(6).toLowerCase().replace(/_([a-z])/g,((e,n)=>n.toUpperCase()));let a=process.env[n];return a=!!/^(yes|on|true|enabled)$/i.test(a)||!/^(no|off|false|disabled)$/i.test(a)&&("null"===a?null:Number(a)),e[t]=a,e}),{}),n.exports=ai()(t);const{formatters:o}=n.exports;o.o=function(e){return this.inspectOpts.colors=this.useColors,i.inspect(e,this.inspectOpts).split("\n").map((e=>e.trim())).join(" ")},o.O=function(e){return this.inspectOpts.colors=this.useColors,i.inspect(e,this.inspectOpts)}}(mi,mi.exports)),mi.exports}function vi(){return pi||(pi=1,"undefined"==typeof process||"renderer"===process.type||!0===process.browser||process.__nwjs?ei.exports=(Ya||(Ya=1,function(e,n){n.formatArgs=function(n){if(n[0]=(this.useColors?"%c":"")+this.namespace+(this.useColors?" %c":" ")+n[0]+(this.useColors?"%c ":" ")+"+"+e.exports.humanize(this.diff),!this.useColors)return;const t="color: "+this.color;n.splice(1,0,t,"color: inherit");let a=0,i=0;n[0].replace(/%[a-zA-Z%]/g,(e=>{"%%"!==e&&(a++,"%c"===e&&(i=a))})),n.splice(i,0,t)},n.save=function(e){try{e?n.storage.setItem("debug",e):n.storage.removeItem("debug")}catch(e){}},n.load=function(){let e;try{e=n.storage.getItem("debug")}catch(e){}return!e&&"undefined"!=typeof process&&"env"in process&&(e=process.env.DEBUG),e},n.useColors=function(){if("undefined"!=typeof window&&window.process&&("renderer"===window.process.type||window.process.__nwjs))return!0;if("undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/(edge|trident)\/(\d+)/))return!1;let e;return"undefined"!=typeof document&&document.documentElement&&document.documentElement.style&&document.documentElement.style.WebkitAppearance||"undefined"!=typeof window&&window.console&&(window.console.firebug||window.console.exception&&window.console.table)||"undefined"!=typeof navigator&&navigator.userAgent&&(e=navigator.userAgent.toLowerCase().match(/firefox\/(\d+)/))&&parseInt(e[1],10)>=31||"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/applewebkit\/(\d+)/)},n.storage=function(){try{return localStorage}catch(e){}}(),n.destroy=(()=>{let e=!1;return()=>{e||(e=!0,console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`."))}})(),n.colors=["#0000CC","#0000FF","#0033CC","#0033FF","#0066CC","#0066FF","#0099CC","#0099FF","#00CC00","#00CC33","#00CC66","#00CC99","#00CCCC","#00CCFF","#3300CC","#3300FF","#3333CC","#3333FF","#3366CC","#3366FF","#3399CC","#3399FF","#33CC00","#33CC33","#33CC66","#33CC99","#33CCCC","#33CCFF","#6600CC","#6600FF","#6633CC","#6633FF","#66CC00","#66CC33","#9900CC","#9900FF","#9933CC","#9933FF","#99CC00","#99CC33","#CC0000","#CC0033","#CC0066","#CC0099","#CC00CC","#CC00FF","#CC3300","#CC3333","#CC3366","#CC3399","#CC33CC","#CC33FF","#CC6600","#CC6633","#CC9900","#CC9933","#CCCC00","#CCCC33","#FF0000","#FF0033","#FF0066","#FF0099","#FF00CC","#FF00FF","#FF3300","#FF3333","#FF3366","#FF3399","#FF33CC","#FF33FF","#FF6600","#FF6633","#FF9900","#FF9933","#FFCC00","#FFCC33"],n.log=console.debug||console.log||(()=>{}),e.exports=ai()(n);const{formatters:t}=e.exports;t.j=function(e){try{return JSON.stringify(e)}catch(e){return"[UnexpectedJSONParseError]: "+e.message}}}(ni,ni.exports)),ni.exports):ei.exports=hi()),ei.exports}var xi=function(){if(di)return Za.exports;di=1;var e,t,s,c=o,p=c.URL,u=a,l=i,d=n.Writable,m=r,f=function(){return li||(li=1,ui=function(){if(!e){try{e=vi()("follow-redirects")}catch(e){}"function"!=typeof e&&(e=function(){})}e.apply(null,arguments)}),ui;var e}();e="undefined"!=typeof process,t="undefined"!=typeof window&&"undefined"!=typeof document,s=P(Error.captureStackTrace),e||!t&&s||console.warn("The follow-redirects package should be excluded from browser builds.");var h=!1;try{m(new p(""))}catch(e){h="ERR_INVALID_URL"===e.code}var v=["auth","host","hostname","href","path","pathname","port","protocol","query","search","hash"],x=["abort","aborted","connect","error","socket","timeout"],b=Object.create(null);x.forEach((function(e){b[e]=function(n,t,a){this._redirectable.emit(e,n,t,a)}}));var g=A("ERR_INVALID_URL","Invalid URL",TypeError),y=A("ERR_FR_REDIRECTION_FAILURE","Redirected request failed"),w=A("ERR_FR_TOO_MANY_REDIRECTS","Maximum number of redirects exceeded",y),k=A("ERR_FR_MAX_BODY_LENGTH_EXCEEDED","Request body larger than maxBodyLength limit"),_=A("ERR_STREAM_WRITE_AFTER_END","write after end"),j=d.prototype.destroy||E;function R(e,n){d.call(this),this._sanitizeOptions(e),this._options=e,this._ended=!1,this._ending=!1,this._redirectCount=0,this._redirects=[],this._requestBodyLength=0,this._requestBodyBuffers=[],n&&this.on("response",n);var t=this;this._onNativeResponse=function(e){try{t._processResponse(e)}catch(e){t.emit("error",e instanceof y?e:new y({cause:e}))}},this._performRequest()}function S(e){var n={maxRedirects:21,maxBodyLength:10485760},t={};return Object.keys(e).forEach((function(a){var i=a+":",o=t[i]=e[a],s=n[a]=Object.create(o);Object.defineProperties(s,{request:{value:function(e,a,o){var s;return s=e,p&&s instanceof p?e=O(e):D(e)?e=O(T(e)):(o=a,a=C(e),e={protocol:i}),P(a)&&(o=a,a=null),(a=Object.assign({maxRedirects:n.maxRedirects,maxBodyLength:n.maxBodyLength},e,a)).nativeProtocols=t,D(a.host)||D(a.hostname)||(a.hostname="::1"),m.equal(a.protocol,i,"protocol mismatch"),f("options",a),new R(a,o)},configurable:!0,enumerable:!0,writable:!0},get:{value:function(e,n,t){var a=s.request(e,n,t);return a.end(),a},configurable:!0,enumerable:!0,writable:!0}})})),n}function E(){}function T(e){var n;if(h)n=new p(e);else if(!D((n=C(c.parse(e))).protocol))throw new g({input:e});return n}function C(e){if(/^\[/.test(e.hostname)&&!/^\[[:0-9a-f]+\]$/i.test(e.hostname))throw new g({input:e.href||e});if(/^\[/.test(e.host)&&!/^\[[:0-9a-f]+\](:\d+)?$/i.test(e.host))throw new g({input:e.href||e});return e}function O(e,n){var t=n||{};for(var a of v)t[a]=e[a];return t.hostname.startsWith("[")&&(t.hostname=t.hostname.slice(1,-1)),""!==t.port&&(t.port=Number(t.port)),t.path=t.search?t.pathname+t.search:t.pathname,t}function q(e,n){var t;for(var a in n)e.test(a)&&(t=n[a],delete n[a]);return null==t?void 0:String(t).trim()}function A(e,n,t){function a(t){P(Error.captureStackTrace)&&Error.captureStackTrace(this,this.constructor),Object.assign(this,t||{}),this.code=e,this.message=this.cause?n+": "+this.cause.message:n}return a.prototype=new(t||Error),Object.defineProperties(a.prototype,{constructor:{value:a,enumerable:!1},name:{value:"Error ["+e+"]",enumerable:!1}}),a}function F(e,n){for(var t of x)e.removeListener(t,b[t]);e.on("error",E),e.destroy(n)}function D(e){return"string"==typeof e||e instanceof String}function P(e){return"function"==typeof e}return R.prototype=Object.create(d.prototype),R.prototype.abort=function(){F(this._currentRequest),this._currentRequest.abort(),this.emit("abort")},R.prototype.destroy=function(e){return F(this._currentRequest,e),j.call(this,e),this},R.prototype.write=function(e,n,t){if(this._ending)throw new _;if(!D(e)&&("object"!=typeof(a=e)||!("length"in a)))throw new TypeError("data should be a string, Buffer or Uint8Array");var a;P(n)&&(t=n,n=null),0!==e.length?this._requestBodyLength+e.length<=this._options.maxBodyLength?(this._requestBodyLength+=e.length,this._requestBodyBuffers.push({data:e,encoding:n}),this._currentRequest.write(e,n,t)):(this.emit("error",new k),this.abort()):t&&t()},R.prototype.end=function(e,n,t){if(P(e)?(t=e,e=n=null):P(n)&&(t=n,n=null),e){var a=this,i=this._currentRequest;this.write(e,n,(function(){a._ended=!0,i.end(null,null,t)})),this._ending=!0}else this._ended=this._ending=!0,this._currentRequest.end(null,null,t)},R.prototype.setHeader=function(e,n){this._options.headers[e]=n,this._currentRequest.setHeader(e,n)},R.prototype.removeHeader=function(e){delete this._options.headers[e],this._currentRequest.removeHeader(e)},R.prototype.setTimeout=function(e,n){var t=this;function a(n){n.setTimeout(e),n.removeListener("timeout",n.destroy),n.addListener("timeout",n.destroy)}function i(n){t._timeout&&clearTimeout(t._timeout),t._timeout=setTimeout((function(){t.emit("timeout"),o()}),e),a(n)}function o(){t._timeout&&(clearTimeout(t._timeout),t._timeout=null),t.removeListener("abort",o),t.removeListener("error",o),t.removeListener("response",o),t.removeListener("close",o),n&&t.removeListener("timeout",n),t.socket||t._currentRequest.removeListener("socket",i)}return n&&this.on("timeout",n),this.socket?i(this.socket):this._currentRequest.once("socket",i),this.on("socket",a),this.on("abort",o),this.on("error",o),this.on("response",o),this.on("close",o),this},["flushHeaders","getHeader","setNoDelay","setSocketKeepAlive"].forEach((function(e){R.prototype[e]=function(n,t){return this._currentRequest[e](n,t)}})),["aborted","connection","socket"].forEach((function(e){Object.defineProperty(R.prototype,e,{get:function(){return this._currentRequest[e]}})})),R.prototype._sanitizeOptions=function(e){if(e.headers||(e.headers={}),e.host&&(e.hostname||(e.hostname=e.host),delete e.host),!e.pathname&&e.path){var n=e.path.indexOf("?");n<0?e.pathname=e.path:(e.pathname=e.path.substring(0,n),e.search=e.path.substring(n))}},R.prototype._performRequest=function(){var e=this._options.protocol,n=this._options.nativeProtocols[e];if(!n)throw new TypeError("Unsupported protocol "+e);if(this._options.agents){var t=e.slice(0,-1);this._options.agent=this._options.agents[t]}var a=this._currentRequest=n.request(this._options,this._onNativeResponse);for(var i of(a._redirectable=this,x))a.on(i,b[i]);if(this._currentUrl=/^\//.test(this._options.path)?c.format(this._options):this._options.path,this._isRedirect){var o=0,s=this,r=this._requestBodyBuffers;!function e(n){if(a===s._currentRequest)if(n)s.emit("error",n);else if(o<r.length){var t=r[o++];a.finished||a.write(t.data,t.encoding,e)}else s._ended&&a.end()}()}},R.prototype._processResponse=function(e){var n=e.statusCode;this._options.trackRedirects&&this._redirects.push({url:this._currentUrl,headers:e.headers,statusCode:n});var t,a=e.headers.location;if(!a||!1===this._options.followRedirects||n<300||n>=400)return e.responseUrl=this._currentUrl,e.redirects=this._redirects,this.emit("response",e),void(this._requestBodyBuffers=[]);if(F(this._currentRequest),e.destroy(),++this._redirectCount>this._options.maxRedirects)throw new w;var i=this._options.beforeRedirect;i&&(t=Object.assign({Host:e.req.getHeader("host")},this._options.headers));var o=this._options.method;((301===n||302===n)&&"POST"===this._options.method||303===n&&!/^(?:GET|HEAD)$/.test(this._options.method))&&(this._options.method="GET",this._requestBodyBuffers=[],q(/^content-/i,this._options.headers));var s,r,u=q(/^host$/i,this._options.headers),l=T(this._currentUrl),d=u||l.host,v=/^\w+:/.test(a)?this._currentUrl:c.format(Object.assign(l,{host:d})),x=(s=a,r=v,h?new p(s,r):T(c.resolve(r,s)));if(f("redirecting to",x.href),this._isRedirect=!0,O(x,this._options),(x.protocol!==l.protocol&&"https:"!==x.protocol||x.host!==d&&!function(e,n){m(D(e)&&D(n));var t=e.length-n.length-1;return t>0&&"."===e[t]&&e.endsWith(n)}(x.host,d))&&q(/^(?:(?:proxy-)?authorization|cookie)$/i,this._options.headers),P(i)){var b={headers:e.headers,statusCode:n},g={url:v,method:o,headers:t};i(this._options,b,g),this._sanitizeOptions(this._options)}this._performRequest()},Za.exports=S({http:u,https:l}),Za.exports.wrap=S,Za.exports}(),bi=g(xi);const gi="1.7.9";function yi(e){const n=/^([-+\w]{1,25})(:?\/\/|:)/.exec(e);return n&&n[1]||""}const wi=/^(?:([^;]+);)?(?:[^;]+;)?(base64|),([\s\S]*)$/;const ki=Symbol("internals");class _i extends n.Transform{constructor(e){super({readableHighWaterMark:(e=be.toFlatObject(e,{maxRate:0,chunkSize:65536,minChunkSize:100,timeWindow:500,ticksRate:2,samplesCount:15},null,((e,n)=>!be.isUndefined(n[e])))).chunkSize});const n=this[ki]={timeWindow:e.timeWindow,chunkSize:e.chunkSize,maxRate:e.maxRate,minChunkSize:e.minChunkSize,bytesSeen:0,isCaptured:!1,notifiedBytesLoaded:0,ts:Date.now(),bytes:0,onReadCallback:null};this.on("newListener",(e=>{"progress"===e&&(n.isCaptured||(n.isCaptured=!0))}))}_read(e){const n=this[ki];return n.onReadCallback&&n.onReadCallback(),super._read(e)}_transform(e,n,t){const a=this[ki],i=a.maxRate,o=this.readableHighWaterMark,s=a.timeWindow,r=i/(1e3/s),c=!1!==a.minChunkSize?Math.max(a.minChunkSize,.01*r):0,p=(e,n)=>{const t=Buffer.byteLength(e);a.bytesSeen+=t,a.bytes+=t,a.isCaptured&&this.emit("progress",a.bytesSeen),this.push(e)?process.nextTick(n):a.onReadCallback=()=>{a.onReadCallback=null,process.nextTick(n)}},u=(e,n)=>{const t=Buffer.byteLength(e);let u,l=null,d=o,m=0;if(i){const e=Date.now();(!a.ts||(m=e-a.ts)>=s)&&(a.ts=e,u=r-a.bytes,a.bytes=u<0?-u:0,m=0),u=r-a.bytes}if(i){if(u<=0)return setTimeout((()=>{n(null,e)}),s-m);u<d&&(d=u)}d&&t>d&&t-d>c&&(l=e.subarray(d),e=e.subarray(0,d)),p(e,l?()=>{process.nextTick(n,null,l)}:n)};u(e,(function e(n,a){if(n)return t(n);a?u(a,e):t(null)}))}}const{asyncIterator:ji}=Symbol,Ri=async function*(e){e.stream?yield*e.stream():e.arrayBuffer?yield await e.arrayBuffer():e[ji]?yield*e[ji]():yield e},Si=be.ALPHABET.ALPHA_DIGIT+"-_",Ei="function"==typeof TextEncoder?new TextEncoder:new e.TextEncoder,Ti="\r\n",Ci=Ei.encode(Ti);class Oi{constructor(e,n){const{escapeName:t}=this.constructor,a=be.isString(n);let i=`Content-Disposition: form-data; name="${t(e)}"${!a&&n.name?`; filename="${t(n.name)}"`:""}${Ti}`;a?n=Ei.encode(String(n).replace(/\r?\n|\r\n?/g,Ti)):i+=`Content-Type: ${n.type||"application/octet-stream"}${Ti}`,this.headers=Ei.encode(i+Ti),this.contentLength=a?n.byteLength:n.size,this.size=this.headers.byteLength+this.contentLength+2,this.name=e,this.value=n}async*encode(){yield this.headers;const{value:e}=this;be.isTypedArray(e)?yield e:yield*Ri(e),yield Ci}static escapeName(e){return String(e).replace(/[\r\n"]/g,(e=>({"\r":"%0D","\n":"%0A",'"':"%22"}[e])))}}class qi extends n.Transform{__transform(e,n,t){this.push(e),t()}_transform(e,n,t){if(0!==e.length&&(this._transform=this.__transform,120!==e[0])){const e=Buffer.alloc(2);e[0]=120,e[1]=156,this.push(e,n)}this.__transform(e,n,t)}}const Ai=(e,n)=>be.isAsyncFn(e)?function(...t){const a=t.pop();e.apply(this,t).then((e=>{try{n?a(null,...n(e)):a(null,e)}catch(e){a(e)}}),a)}:e;const Fi=(e,n,t=3)=>{let a=0;const i=function(e,n){e=e||10;const t=new Array(e),a=new Array(e);let i,o=0,s=0;return n=void 0!==n?n:1e3,function(r){const c=Date.now(),p=a[s];i||(i=c),t[o]=r,a[o]=c;let u=s,l=0;for(;u!==o;)l+=t[u++],u%=e;if(o=(o+1)%e,o===s&&(s=(s+1)%e),c-i<n)return;const d=p&&c-p;return d?Math.round(1e3*l/d):void 0}}(50,250);return function(e,n){let t,a,i=0,o=1e3/n;const s=(n,o=Date.now())=>{i=o,t=null,a&&(clearTimeout(a),a=null),e.apply(null,n)};return[(...e)=>{const n=Date.now(),r=n-i;r>=o?s(e,n):(t=e,a||(a=setTimeout((()=>{a=null,s(t)}),o-r)))},()=>t&&s(t)]}((t=>{const o=t.loaded,s=t.lengthComputable?t.total:void 0,r=o-a,c=i(r);a=o;e({loaded:o,total:s,progress:s?o/s:void 0,bytes:r,rate:c||void 0,estimated:c&&s&&o<=s?(s-o)/c:void 0,event:t,lengthComputable:null!=s,[n?"download":"upload"]:!0})}),t)},Di=(e,n)=>{const t=null!=e;return[a=>n[0]({lengthComputable:t,total:e,loaded:a}),n[1]]},Pi=e=>(...n)=>be.asap((()=>e(...n))),Bi={flush:u.constants.Z_SYNC_FLUSH,finishFlush:u.constants.Z_SYNC_FLUSH},Li={flush:u.constants.BROTLI_OPERATION_FLUSH,finishFlush:u.constants.BROTLI_OPERATION_FLUSH},Ui=be.isFunction(u.createBrotliDecompress),{http:zi,https:Ii}=bi,Ni=/https:?/,Mi=qa.protocols.map((e=>e+":")),Wi=(e,[n,t])=>(e.on("end",t).on("error",t),n);function Hi(e,n){e.beforeRedirects.proxy&&e.beforeRedirects.proxy(e),e.beforeRedirects.config&&e.beforeRedirects.config(e,n)}function Vi(e,n,t){let a=n;if(!a&&!1!==a){const e=Xa.getProxyForUrl(t);e&&(a=new URL(e))}if(a){if(a.username&&(a.auth=(a.username||"")+":"+(a.password||"")),a.auth){(a.auth.username||a.auth.password)&&(a.auth=(a.auth.username||"")+":"+(a.auth.password||""));const n=Buffer.from(a.auth,"utf8").toString("base64");e.headers["Proxy-Authorization"]="Basic "+n}e.headers.host=e.hostname+(e.port?":"+e.port:"");const n=a.hostname||a.host;e.hostname=n,e.host=n,e.port=a.port,e.path=t,a.protocol&&(e.protocol=a.protocol.includes(":")?a.protocol:`${a.protocol}:`)}e.beforeRedirects.proxy=function(e){Vi(e,n,e.href)}}const $i="undefined"!=typeof process&&"process"===be.kindOf(process),Gi=(e,n)=>(({address:e,family:n})=>{if(!be.isString(e))throw TypeError("address must be a string");return{address:e,family:n||(e.indexOf(".")<0?6:4)}})(be.isObject(e)?e:{address:e,family:n});var Ji=$i&&function(t){return o=async function(o,s,r){let{data:c,lookup:p,family:d}=t;const{responseType:m,responseEncoding:f}=t,h=t.method.toUpperCase();let v,x,b=!1;if(p){const e=Ai(p,(e=>be.isArray(e)?e:[e]));p=(n,t,a)=>{e(n,t,((e,n,i)=>{if(e)return a(e);const o=be.isArray(n)?n.map((e=>Gi(e))):[Gi(n,i)];t.all?a(e,o):a(e,o[0].address,o[0].family)}))}}const g=new l.EventEmitter,y=()=>{t.cancelToken&&t.cancelToken.unsubscribe(w),t.signal&&t.signal.removeEventListener("abort",w),g.removeAllListeners()};function w(e){g.emit("abort",!e||e.type?new Ma(null,t,x):e)}r(((e,n)=>{v=!0,n&&(b=!0,y())})),g.once("abort",s),(t.cancelToken||t.signal)&&(t.cancelToken&&t.cancelToken.subscribe(w),t.signal&&(t.signal.aborted?w():t.signal.addEventListener("abort",w)));const k=Ha(t.baseURL,t.url),_=new URL(k,qa.hasBrowserEnv?qa.origin:void 0),j=_.protocol||Mi[0];if("data:"===j){let e;if("GET"!==h)return Wa(o,s,{status:405,statusText:"method not allowed",headers:{},config:t});try{e=function(e,n,t){const a=t&&t.Blob||qa.classes.Blob,i=yi(e);if(void 0===n&&a&&(n=!0),"data"===i){e=i.length?e.slice(i.length+1):e;const t=wi.exec(e);if(!t)throw new ge("Invalid URL",ge.ERR_INVALID_URL);const o=t[1],s=t[2],r=t[3],c=Buffer.from(decodeURIComponent(r),s?"base64":"utf8");if(n){if(!a)throw new ge("Blob is not supported",ge.ERR_NOT_SUPPORT);return new a([c],{type:o})}return c}throw new ge("Unsupported protocol "+i,ge.ERR_NOT_SUPPORT)}(t.url,"blob"===m,{Blob:t.env&&t.env.Blob})}catch(e){throw ge.from(e,ge.ERR_BAD_REQUEST,t)}return"text"===m?(e=e.toString(f),f&&"utf8"!==f||(e=be.stripBOM(e))):"stream"===m&&(e=n.Readable.from(e)),Wa(o,s,{data:e,status:200,statusText:"OK",headers:new za,config:t})}if(-1===Mi.indexOf(j))return s(new ge("Unsupported protocol "+j,ge.ERR_BAD_REQUEST,t));const R=za.from(t.headers).normalize();R.set("User-Agent","axios/"+gi,!1);const{onUploadProgress:S,onDownloadProgress:E}=t,T=t.maxRate;let C,O;if(be.isSpecCompliantForm(c)){const e=R.getContentType(/boundary=([-_\w\d]{10,70})/i);c=((e,t,a)=>{const{tag:i="form-data-boundary",size:o=25,boundary:s=i+"-"+be.generateString(o,Si)}=a||{};if(!be.isFormData(e))throw TypeError("FormData instance required");if(s.length<1||s.length>70)throw Error("boundary must be 10-70 characters long");const r=Ei.encode("--"+s+Ti),c=Ei.encode("--"+s+"--"+Ti+Ti);let p=c.byteLength;const u=Array.from(e.entries()).map((([e,n])=>{const t=new Oi(e,n);return p+=t.size,t}));p+=r.byteLength*u.length,p=be.toFiniteNumber(p);const l={"Content-Type":`multipart/form-data; boundary=${s}`};return Number.isFinite(p)&&(l["Content-Length"]=p),t&&t(l),n.Readable.from(async function*(){for(const e of u)yield r,yield*e.encode();yield c}())})(c,(e=>{R.set(e)}),{tag:`axios-${gi}-boundary`,boundary:e&&e[1]||void 0})}else if(be.isFormData(c)&&be.isFunction(c.getHeaders)){if(R.set(c.getHeaders()),!R.hasContentLength())try{const n=await e.promisify(c.getLength).call(c);Number.isFinite(n)&&n>=0&&R.setContentLength(n)}catch(e){}}else if(be.isBlob(c)||be.isFile(c))c.size&&R.setContentType(c.type||"application/octet-stream"),R.setContentLength(c.size||0),c=n.Readable.from(Ri(c));else if(c&&!be.isStream(c)){if(Buffer.isBuffer(c));else if(be.isArrayBuffer(c))c=Buffer.from(new Uint8Array(c));else{if(!be.isString(c))return s(new ge("Data after transformation must be a string, an ArrayBuffer, a Buffer, or a Stream",ge.ERR_BAD_REQUEST,t));c=Buffer.from(c,"utf-8")}if(R.setContentLength(c.length,!1),t.maxBodyLength>-1&&c.length>t.maxBodyLength)return s(new ge("Request body larger than maxBodyLength limit",ge.ERR_BAD_REQUEST,t))}const q=be.toFiniteNumber(R.getContentLength());let A,F;be.isArray(T)?(C=T[0],O=T[1]):C=O=T,c&&(S||C)&&(be.isStream(c)||(c=n.Readable.from(c,{objectMode:!1})),c=n.pipeline([c,new _i({maxRate:be.toFiniteNumber(C)})],be.noop),S&&c.on("progress",Wi(c,Di(q,Fi(Pi(S),!1,3))))),t.auth&&(A=(t.auth.username||"")+":"+(t.auth.password||"")),!A&&_.username&&(A=_.username+":"+_.password),A&&R.delete("authorization");try{F=ka(_.pathname+_.search,t.params,t.paramsSerializer).replace(/^\?/,"")}catch(e){const n=new Error(e.message);return n.config=t,n.url=t.url,n.exists=!0,s(n)}R.set("Accept-Encoding","gzip, compress, deflate"+(Ui?", br":""),!1);const D={path:F,method:h,headers:R.toJSON(),agents:{http:t.httpAgent,https:t.httpsAgent},auth:A,protocol:j,family:d,beforeRedirect:Hi,beforeRedirects:{}};let P;!be.isUndefined(p)&&(D.lookup=p),t.socketPath?D.socketPath=t.socketPath:(D.hostname=_.hostname.startsWith("[")?_.hostname.slice(1,-1):_.hostname,D.port=_.port,Vi(D,t.proxy,j+"//"+_.hostname+(_.port?":"+_.port:"")+D.path));const B=Ni.test(D.protocol);if(D.agent=B?t.httpsAgent:t.httpAgent,t.transport?P=t.transport:0===t.maxRedirects?P=B?i:a:(t.maxRedirects&&(D.maxRedirects=t.maxRedirects),t.beforeRedirect&&(D.beforeRedirects.config=t.beforeRedirect),P=B?Ii:zi),t.maxBodyLength>-1?D.maxBodyLength=t.maxBodyLength:D.maxBodyLength=1/0,t.insecureHTTPParser&&(D.insecureHTTPParser=t.insecureHTTPParser),x=P.request(D,(function(e){if(x.destroyed)return;const a=[e],i=+e.headers["content-length"];if(E||O){const e=new _i({maxRate:be.toFiniteNumber(O)});E&&e.on("progress",Wi(e,Di(i,Fi(Pi(E),!0,3)))),a.push(e)}let r=e;const c=e.req||x;if(!1!==t.decompress&&e.headers["content-encoding"])switch("HEAD"!==h&&204!==e.statusCode||delete e.headers["content-encoding"],(e.headers["content-encoding"]||"").toLowerCase()){case"gzip":case"x-gzip":case"compress":case"x-compress":a.push(u.createUnzip(Bi)),delete e.headers["content-encoding"];break;case"deflate":a.push(new qi),a.push(u.createUnzip(Bi)),delete e.headers["content-encoding"];break;case"br":Ui&&(a.push(u.createBrotliDecompress(Li)),delete e.headers["content-encoding"])}r=a.length>1?n.pipeline(a,be.noop):a[0];const p=n.finished(r,(()=>{p(),y()})),l={status:e.statusCode,statusText:e.statusMessage,headers:new za(e.headers),config:t,request:c};if("stream"===m)l.data=r,Wa(o,s,l);else{const e=[];let n=0;r.on("data",(function(a){e.push(a),n+=a.length,t.maxContentLength>-1&&n>t.maxContentLength&&(b=!0,r.destroy(),s(new ge("maxContentLength size of "+t.maxContentLength+" exceeded",ge.ERR_BAD_RESPONSE,t,c)))})),r.on("aborted",(function(){if(b)return;const e=new ge("stream has been aborted",ge.ERR_BAD_RESPONSE,t,c);r.destroy(e),s(e)})),r.on("error",(function(e){x.destroyed||s(ge.from(e,null,t,c))})),r.on("end",(function(){try{let n=1===e.length?e[0]:Buffer.concat(e);"arraybuffer"!==m&&(n=n.toString(f),f&&"utf8"!==f||(n=be.stripBOM(n))),l.data=n}catch(e){return s(ge.from(e,null,t,l.request,l))}Wa(o,s,l)}))}g.once("abort",(e=>{r.destroyed||(r.emit("error",e),r.destroy())}))})),g.once("abort",(e=>{s(e),x.destroy(e)})),x.on("error",(function(e){s(ge.from(e,null,t,x))})),x.on("socket",(function(e){e.setKeepAlive(!0,6e4)})),t.timeout){const e=parseInt(t.timeout,10);if(Number.isNaN(e))return void s(new ge("error trying to parse `config.timeout` to int",ge.ERR_BAD_OPTION_VALUE,t,x));x.setTimeout(e,(function(){if(v)return;let e=t.timeout?"timeout of "+t.timeout+"ms exceeded":"timeout exceeded";const n=t.transitional||ja;t.timeoutErrorMessage&&(e=t.timeoutErrorMessage),s(new ge(e,n.clarifyTimeoutError?ge.ETIMEDOUT:ge.ECONNABORTED,t,x)),w()}))}if(be.isStream(c)){let e=!1,n=!1;c.on("end",(()=>{e=!0})),c.once("error",(e=>{n=!0,x.destroy(e)})),c.on("close",(()=>{e||n||w(new Ma("Request stream has been aborted",t,x))})),c.pipe(x)}else x.end(c)},new Promise(((e,n)=>{let t,a;const i=(e,n)=>{a||(a=!0,t&&t(e,n))},s=e=>{i(e,!0),n(e)};o((n=>{i(n),e(n)}),s,(e=>t=e)).catch(s)}));var o},Ki=qa.hasStandardBrowserEnv?((e,n)=>t=>(t=new URL(t,qa.origin),e.protocol===t.protocol&&e.host===t.host&&(n||e.port===t.port)))(new URL(qa.origin),qa.navigator&&/(msie|trident)/i.test(qa.navigator.userAgent)):()=>!0,Qi=qa.hasStandardBrowserEnv?{write(e,n,t,a,i,o){const s=[e+"="+encodeURIComponent(n)];be.isNumber(t)&&s.push("expires="+new Date(t).toGMTString()),be.isString(a)&&s.push("path="+a),be.isString(i)&&s.push("domain="+i),!0===o&&s.push("secure"),document.cookie=s.join("; ")},read(e){const n=document.cookie.match(new RegExp("(^|;\\s*)("+e+")=([^;]*)"));return n?decodeURIComponent(n[3]):null},remove(e){this.write(e,"",Date.now()-864e5)}}:{write(){},read:()=>null,remove(){}};const Yi=e=>e instanceof za?{...e}:e;function Xi(e,n){n=n||{};const t={};function a(e,n,t,a){return be.isPlainObject(e)&&be.isPlainObject(n)?be.merge.call({caseless:a},e,n):be.isPlainObject(n)?be.merge({},n):be.isArray(n)?n.slice():n}function i(e,n,t,i){return be.isUndefined(n)?be.isUndefined(e)?void 0:a(void 0,e,0,i):a(e,n,0,i)}function o(e,n){if(!be.isUndefined(n))return a(void 0,n)}function s(e,n){return be.isUndefined(n)?be.isUndefined(e)?void 0:a(void 0,e):a(void 0,n)}function r(t,i,o){return o in n?a(t,i):o in e?a(void 0,t):void 0}const c={url:o,method:o,data:o,baseURL:s,transformRequest:s,transformResponse:s,paramsSerializer:s,timeout:s,timeoutMessage:s,withCredentials:s,withXSRFToken:s,adapter:s,responseType:s,xsrfCookieName:s,xsrfHeaderName:s,onUploadProgress:s,onDownloadProgress:s,decompress:s,maxContentLength:s,maxBodyLength:s,beforeRedirect:s,transport:s,httpAgent:s,httpsAgent:s,cancelToken:s,socketPath:s,responseEncoding:s,validateStatus:r,headers:(e,n,t)=>i(Yi(e),Yi(n),0,!0)};return be.forEach(Object.keys(Object.assign({},e,n)),(function(a){const o=c[a]||i,s=o(e[a],n[a],a);be.isUndefined(s)&&o!==r||(t[a]=s)})),t}var Zi=e=>{const n=Xi({},e);let t,{data:a,withXSRFToken:i,xsrfHeaderName:o,xsrfCookieName:s,headers:r,auth:c}=n;if(n.headers=r=za.from(r),n.url=ka(Ha(n.baseURL,n.url),e.params,e.paramsSerializer),c&&r.set("Authorization","Basic "+btoa((c.username||"")+":"+(c.password?unescape(encodeURIComponent(c.password)):""))),be.isFormData(a))if(qa.hasStandardBrowserEnv||qa.hasStandardBrowserWebWorkerEnv)r.setContentType(void 0);else if(!1!==(t=r.getContentType())){const[e,...n]=t?t.split(";").map((e=>e.trim())).filter(Boolean):[];r.setContentType([e||"multipart/form-data",...n].join("; "))}if(qa.hasStandardBrowserEnv&&(i&&be.isFunction(i)&&(i=i(n)),i||!1!==i&&Ki(n.url))){const e=o&&s&&Qi.read(s);e&&r.set(o,e)}return n};var eo="undefined"!=typeof XMLHttpRequest&&function(e){return new Promise((function(n,t){const a=Zi(e);let i=a.data;const o=za.from(a.headers).normalize();let s,r,c,p,u,{responseType:l,onUploadProgress:d,onDownloadProgress:m}=a;function f(){p&&p(),u&&u(),a.cancelToken&&a.cancelToken.unsubscribe(s),a.signal&&a.signal.removeEventListener("abort",s)}let h=new XMLHttpRequest;function v(){if(!h)return;const a=za.from("getAllResponseHeaders"in h&&h.getAllResponseHeaders());Wa((function(e){n(e),f()}),(function(e){t(e),f()}),{data:l&&"text"!==l&&"json"!==l?h.response:h.responseText,status:h.status,statusText:h.statusText,headers:a,config:e,request:h}),h=null}h.open(a.method.toUpperCase(),a.url,!0),h.timeout=a.timeout,"onloadend"in h?h.onloadend=v:h.onreadystatechange=function(){h&&4===h.readyState&&(0!==h.status||h.responseURL&&0===h.responseURL.indexOf("file:"))&&setTimeout(v)},h.onabort=function(){h&&(t(new ge("Request aborted",ge.ECONNABORTED,e,h)),h=null)},h.onerror=function(){t(new ge("Network Error",ge.ERR_NETWORK,e,h)),h=null},h.ontimeout=function(){let n=a.timeout?"timeout of "+a.timeout+"ms exceeded":"timeout exceeded";const i=a.transitional||ja;a.timeoutErrorMessage&&(n=a.timeoutErrorMessage),t(new ge(n,i.clarifyTimeoutError?ge.ETIMEDOUT:ge.ECONNABORTED,e,h)),h=null},void 0===i&&o.setContentType(null),"setRequestHeader"in h&&be.forEach(o.toJSON(),(function(e,n){h.setRequestHeader(n,e)})),be.isUndefined(a.withCredentials)||(h.withCredentials=!!a.withCredentials),l&&"json"!==l&&(h.responseType=a.responseType),m&&([c,u]=Fi(m,!0),h.addEventListener("progress",c)),d&&h.upload&&([r,p]=Fi(d),h.upload.addEventListener("progress",r),h.upload.addEventListener("loadend",p)),(a.cancelToken||a.signal)&&(s=n=>{h&&(t(!n||n.type?new Ma(null,e,h):n),h.abort(),h=null)},a.cancelToken&&a.cancelToken.subscribe(s),a.signal&&(a.signal.aborted?s():a.signal.addEventListener("abort",s)));const x=yi(a.url);x&&-1===qa.protocols.indexOf(x)?t(new ge("Unsupported protocol "+x+":",ge.ERR_BAD_REQUEST,e)):h.send(i||null)}))};const no=(e,n)=>{const{length:t}=e=e?e.filter(Boolean):[];if(n||t){let t,a=new AbortController;const i=function(e){if(!t){t=!0,s();const n=e instanceof Error?e:this.reason;a.abort(n instanceof ge?n:new Ma(n instanceof Error?n.message:n))}};let o=n&&setTimeout((()=>{o=null,i(new ge(`timeout ${n} of ms exceeded`,ge.ETIMEDOUT))}),n);const s=()=>{e&&(o&&clearTimeout(o),o=null,e.forEach((e=>{e.unsubscribe?e.unsubscribe(i):e.removeEventListener("abort",i)})),e=null)};e.forEach((e=>e.addEventListener("abort",i)));const{signal:r}=a;return r.unsubscribe=()=>be.asap(s),r}},to=function*(e,n){let t=e.byteLength;if(t<n)return void(yield e);let a,i=0;for(;i<t;)a=i+n,yield e.slice(i,a),i=a},ao=async function*(e){if(e[Symbol.asyncIterator])return void(yield*e);const n=e.getReader();try{for(;;){const{done:e,value:t}=await n.read();if(e)break;yield t}}finally{await n.cancel()}},io=(e,n,t,a)=>{const i=async function*(e,n){for await(const t of ao(e))yield*to(t,n)}(e,n);let o,s=0,r=e=>{o||(o=!0,a&&a(e))};return new ReadableStream({async pull(e){try{const{done:n,value:a}=await i.next();if(n)return r(),void e.close();let o=a.byteLength;if(t){let e=s+=o;t(e)}e.enqueue(new Uint8Array(a))}catch(e){throw r(e),e}},cancel:e=>(r(e),i.return())},{highWaterMark:2})},oo="function"==typeof fetch&&"function"==typeof Request&&"function"==typeof Response,so=oo&&"function"==typeof ReadableStream,ro=oo&&("function"==typeof TextEncoder?(co=new TextEncoder,e=>co.encode(e)):async e=>new Uint8Array(await new Response(e).arrayBuffer()));var co;const po=(e,...n)=>{try{return!!e(...n)}catch(e){return!1}},uo=so&&po((()=>{let e=!1;const n=new Request(qa.origin,{body:new ReadableStream,method:"POST",get duplex(){return e=!0,"half"}}).headers.has("Content-Type");return e&&!n})),lo=so&&po((()=>be.isReadableStream(new Response("").body))),mo={stream:lo&&(e=>e.body)};var fo;oo&&(fo=new Response,["text","arrayBuffer","blob","formData","stream"].forEach((e=>{!mo[e]&&(mo[e]=be.isFunction(fo[e])?n=>n[e]():(n,t)=>{throw new ge(`Response type '${e}' is not supported`,ge.ERR_NOT_SUPPORT,t)})})));const ho=async(e,n)=>{const t=be.toFiniteNumber(e.getContentLength());return null==t?(async e=>{if(null==e)return 0;if(be.isBlob(e))return e.size;if(be.isSpecCompliantForm(e)){const n=new Request(qa.origin,{method:"POST",body:e});return(await n.arrayBuffer()).byteLength}return be.isArrayBufferView(e)||be.isArrayBuffer(e)?e.byteLength:(be.isURLSearchParams(e)&&(e+=""),be.isString(e)?(await ro(e)).byteLength:void 0)})(n):t};const vo={http:Ji,xhr:eo,fetch:oo&&(async e=>{let{url:n,method:t,data:a,signal:i,cancelToken:o,timeout:s,onDownloadProgress:r,onUploadProgress:c,responseType:p,headers:u,withCredentials:l="same-origin",fetchOptions:d}=Zi(e);p=p?(p+"").toLowerCase():"text";let m,f=no([i,o&&o.toAbortSignal()],s);const h=f&&f.unsubscribe&&(()=>{f.unsubscribe()});let v;try{if(c&&uo&&"get"!==t&&"head"!==t&&0!==(v=await ho(u,a))){let e,t=new Request(n,{method:"POST",body:a,duplex:"half"});if(be.isFormData(a)&&(e=t.headers.get("content-type"))&&u.setContentType(e),t.body){const[e,n]=Di(v,Fi(Pi(c)));a=io(t.body,65536,e,n)}}be.isString(l)||(l=l?"include":"omit");const i="credentials"in Request.prototype;m=new Request(n,{...d,signal:f,method:t.toUpperCase(),headers:u.normalize().toJSON(),body:a,duplex:"half",credentials:i?l:void 0});let o=await fetch(m);const s=lo&&("stream"===p||"response"===p);if(lo&&(r||s&&h)){const e={};["status","statusText","headers"].forEach((n=>{e[n]=o[n]}));const n=be.toFiniteNumber(o.headers.get("content-length")),[t,a]=r&&Di(n,Fi(Pi(r),!0))||[];o=new Response(io(o.body,65536,t,(()=>{a&&a(),h&&h()})),e)}p=p||"text";let x=await mo[be.findKey(mo,p)||"text"](o,e);return!s&&h&&h(),await new Promise(((n,t)=>{Wa(n,t,{data:x,headers:za.from(o.headers),status:o.status,statusText:o.statusText,config:e,request:m})}))}catch(n){if(h&&h(),n&&"TypeError"===n.name&&/fetch/i.test(n.message))throw Object.assign(new ge("Network Error",ge.ERR_NETWORK,e,m),{cause:n.cause||n});throw ge.from(n,n&&n.code,e,m)}})};be.forEach(vo,((e,n)=>{if(e){try{Object.defineProperty(e,"name",{value:n})}catch(e){}Object.defineProperty(e,"adapterName",{value:n})}}));const xo=e=>`- ${e}`,bo=e=>be.isFunction(e)||null===e||!1===e;var go=e=>{e=be.isArray(e)?e:[e];const{length:n}=e;let t,a;const i={};for(let o=0;o<n;o++){let n;if(t=e[o],a=t,!bo(t)&&(a=vo[(n=String(t)).toLowerCase()],void 0===a))throw new ge(`Unknown adapter '${n}'`);if(a)break;i[n||"#"+o]=a}if(!a){const e=Object.entries(i).map((([e,n])=>`adapter ${e} `+(!1===n?"is not supported by the environment":"is not available in the build")));throw new ge("There is no suitable adapter to dispatch the request "+(n?e.length>1?"since :\n"+e.map(xo).join("\n"):" "+xo(e[0]):"as no adapter specified"),"ERR_NOT_SUPPORT")}return a};function yo(e){if(e.cancelToken&&e.cancelToken.throwIfRequested(),e.signal&&e.signal.aborted)throw new Ma(null,e)}function wo(e){yo(e),e.headers=za.from(e.headers),e.data=Ia.call(e,e.transformRequest),-1!==["post","put","patch"].indexOf(e.method)&&e.headers.setContentType("application/x-www-form-urlencoded",!1);return go(e.adapter||Fa.adapter)(e).then((function(n){return yo(e),n.data=Ia.call(e,e.transformResponse,n),n.headers=za.from(n.headers),n}),(function(n){return Na(n)||(yo(e),n&&n.response&&(n.response.data=Ia.call(e,e.transformResponse,n.response),n.response.headers=za.from(n.response.headers))),Promise.reject(n)}))}const ko={};["object","boolean","number","function","string","symbol"].forEach(((e,n)=>{ko[e]=function(t){return typeof t===e||"a"+(n<1?"n ":" ")+e}}));const _o={};ko.transitional=function(e,n,t){function a(e,n){return"[Axios v1.7.9] Transitional option '"+e+"'"+n+(t?". "+t:"")}return(t,i,o)=>{if(!1===e)throw new ge(a(i," has been removed"+(n?" in "+n:"")),ge.ERR_DEPRECATED);return n&&!_o[i]&&(_o[i]=!0,console.warn(a(i," has been deprecated since v"+n+" and will be removed in the near future"))),!e||e(t,i,o)}},ko.spelling=function(e){return(n,t)=>(console.warn(`${t} is likely a misspelling of ${e}`),!0)};var jo={assertOptions:function(e,n,t){if("object"!=typeof e)throw new ge("options must be an object",ge.ERR_BAD_OPTION_VALUE);const a=Object.keys(e);let i=a.length;for(;i-- >0;){const o=a[i],s=n[o];if(s){const n=e[o],t=void 0===n||s(n,o,e);if(!0!==t)throw new ge("option "+o+" must be "+t,ge.ERR_BAD_OPTION_VALUE)}else if(!0!==t)throw new ge("Unknown option "+o,ge.ERR_BAD_OPTION)}},validators:ko};const Ro=jo.validators;let So=class{constructor(e){this.defaults=e,this.interceptors={request:new _a,response:new _a}}async request(e,n){try{return await this._request(e,n)}catch(e){if(e instanceof Error){let n={};Error.captureStackTrace?Error.captureStackTrace(n):n=new Error;const t=n.stack?n.stack.replace(/^.+\n/,""):"";try{e.stack?t&&!String(e.stack).endsWith(t.replace(/^.+\n.+\n/,""))&&(e.stack+="\n"+t):e.stack=t}catch(e){}}throw e}}_request(e,n){"string"==typeof e?(n=n||{}).url=e:n=e||{},n=Xi(this.defaults,n);const{transitional:t,paramsSerializer:a,headers:i}=n;void 0!==t&&jo.assertOptions(t,{silentJSONParsing:Ro.transitional(Ro.boolean),forcedJSONParsing:Ro.transitional(Ro.boolean),clarifyTimeoutError:Ro.transitional(Ro.boolean)},!1),null!=a&&(be.isFunction(a)?n.paramsSerializer={serialize:a}:jo.assertOptions(a,{encode:Ro.function,serialize:Ro.function},!0)),jo.assertOptions(n,{baseUrl:Ro.spelling("baseURL"),withXsrfToken:Ro.spelling("withXSRFToken")},!0),n.method=(n.method||this.defaults.method||"get").toLowerCase();let o=i&&be.merge(i.common,i[n.method]);i&&be.forEach(["delete","get","head","post","put","patch","common"],(e=>{delete i[e]})),n.headers=za.concat(o,i);const s=[];let r=!0;this.interceptors.request.forEach((function(e){"function"==typeof e.runWhen&&!1===e.runWhen(n)||(r=r&&e.synchronous,s.unshift(e.fulfilled,e.rejected))}));const c=[];let p;this.interceptors.response.forEach((function(e){c.push(e.fulfilled,e.rejected)}));let u,l=0;if(!r){const e=[wo.bind(this),void 0];for(e.unshift.apply(e,s),e.push.apply(e,c),u=e.length,p=Promise.resolve(n);l<u;)p=p.then(e[l++],e[l++]);return p}u=s.length;let d=n;for(l=0;l<u;){const e=s[l++],n=s[l++];try{d=e(d)}catch(e){n.call(this,e);break}}try{p=wo.call(this,d)}catch(e){return Promise.reject(e)}for(l=0,u=c.length;l<u;)p=p.then(c[l++],c[l++]);return p}getUri(e){return ka(Ha((e=Xi(this.defaults,e)).baseURL,e.url),e.params,e.paramsSerializer)}};be.forEach(["delete","get","head","options"],(function(e){So.prototype[e]=function(n,t){return this.request(Xi(t||{},{method:e,url:n,data:(t||{}).data}))}})),be.forEach(["post","put","patch"],(function(e){function n(n){return function(t,a,i){return this.request(Xi(i||{},{method:e,headers:n?{"Content-Type":"multipart/form-data"}:{},url:t,data:a}))}}So.prototype[e]=n(),So.prototype[e+"Form"]=n(!0)}));const Eo={Continue:100,SwitchingProtocols:101,Processing:102,EarlyHints:103,Ok:200,Created:201,Accepted:202,NonAuthoritativeInformation:203,NoContent:204,ResetContent:205,PartialContent:206,MultiStatus:207,AlreadyReported:208,ImUsed:226,MultipleChoices:300,MovedPermanently:301,Found:302,SeeOther:303,NotModified:304,UseProxy:305,Unused:306,TemporaryRedirect:307,PermanentRedirect:308,BadRequest:400,Unauthorized:401,PaymentRequired:402,Forbidden:403,NotFound:404,MethodNotAllowed:405,NotAcceptable:406,ProxyAuthenticationRequired:407,RequestTimeout:408,Conflict:409,Gone:410,LengthRequired:411,PreconditionFailed:412,PayloadTooLarge:413,UriTooLong:414,UnsupportedMediaType:415,RangeNotSatisfiable:416,ExpectationFailed:417,ImATeapot:418,MisdirectedRequest:421,UnprocessableEntity:422,Locked:423,FailedDependency:424,TooEarly:425,UpgradeRequired:426,PreconditionRequired:428,TooManyRequests:429,RequestHeaderFieldsTooLarge:431,UnavailableForLegalReasons:451,InternalServerError:500,NotImplemented:501,BadGateway:502,ServiceUnavailable:503,GatewayTimeout:504,HttpVersionNotSupported:505,VariantAlsoNegotiates:506,InsufficientStorage:507,LoopDetected:508,NotExtended:510,NetworkAuthenticationRequired:511};Object.entries(Eo).forEach((([e,n])=>{Eo[n]=e}));const To=function e(n){const t=new So(n),a=T(So.prototype.request,t);return be.extend(a,So.prototype,t,{allOwnKeys:!0}),be.extend(a,t,null,{allOwnKeys:!0}),a.create=function(t){return e(Xi(n,t))},a}(Fa);To.Axios=So,To.CanceledError=Ma,To.CancelToken=class e{constructor(e){if("function"!=typeof e)throw new TypeError("executor must be a function.");let n;this.promise=new Promise((function(e){n=e}));const t=this;this.promise.then((e=>{if(!t._listeners)return;let n=t._listeners.length;for(;n-- >0;)t._listeners[n](e);t._listeners=null})),this.promise.then=e=>{let n;const a=new Promise((e=>{t.subscribe(e),n=e})).then(e);return a.cancel=function(){t.unsubscribe(n)},a},e((function(e,a,i){t.reason||(t.reason=new Ma(e,a,i),n(t.reason))}))}throwIfRequested(){if(this.reason)throw this.reason}subscribe(e){this.reason?e(this.reason):this._listeners?this._listeners.push(e):this._listeners=[e]}unsubscribe(e){if(!this._listeners)return;const n=this._listeners.indexOf(e);-1!==n&&this._listeners.splice(n,1)}toAbortSignal(){const e=new AbortController,n=n=>{e.abort(n)};return this.subscribe(n),e.signal.unsubscribe=()=>this.unsubscribe(n),e.signal}static source(){let n;return{token:new e((function(e){n=e})),cancel:n}}},To.isCancel=Na,To.VERSION=gi,To.toFormData=xa,To.AxiosError=ge,To.Cancel=To.CanceledError,To.all=function(e){return Promise.all(e)},To.spread=function(e){return function(n){return e.apply(null,n)}},To.isAxiosError=function(e){return be.isObject(e)&&!0===e.isAxiosError},To.mergeConfig=Xi,To.AxiosHeaders=za,To.formToJSON=e=>Aa(be.isHTMLForm(e)?new FormData(e):e),To.getAdapter=go,To.HttpStatusCode=Eo,To.default=To;const{Axios:Co,AxiosError:Oo,CanceledError:qo,isCancel:Ao,CancelToken:Fo,VERSION:Do,all:Po,Cancel:Bo,isAxiosError:Lo,spread:Uo,toFormData:zo,AxiosHeaders:Io,HttpStatusCode:No,formToJSON:Mo,getAdapter:Wo,mergeConfig:Ho}=To;var Vo,$o,Go,Jo,Ko=function(e){function n(n){var t=n.status,a=n.statusText,i=n.message,o=n.body,s=void 0===o?{}:o,r=this,c="",p="";return"string"==typeof s?c=s:(c=(null==s?void 0:s.message)||"",p=(null==s?void 0:s.error)||""),(r=e.call(this)||this).stack="",r.status=t,r.message=i||p||a||"",r.details=c,r.type="MailgunAPIError",r}return m(n,e),n.getUserDataError=function(e,n){return new this({status:400,statusText:e,body:{message:n}})},n}(Error),Qo=function(){function e(e,n){this._stream=e,this.size=n}return e.prototype.stream=function(){return this._stream},Object.defineProperty(e.prototype,Symbol.toStringTag,{get:function(){return"Blob"},enumerable:!1,configurable:!0}),e}(),Yo=function(){function e(){}return e.prototype.getAttachmentOptions=function(e){var n=e.filename,t=e.contentType,a=e.knownLength;return f(f(f({},n?{filename:n}:{filename:"file"}),t&&{contentType:t}),a&&{knownLength:a})},e.prototype.getFileInfo=function(e){var n=e.name,t=e.type,a=e.size;return this.getAttachmentOptions({filename:n,contentType:t,knownLength:a})},e.prototype.getCustomFileInfo=function(e){var n=e.filename,t=e.contentType,a=e.knownLength;return this.getAttachmentOptions({filename:n,contentType:t,knownLength:a})},e.prototype.getBufferInfo=function(e){var n=e.byteLength;return this.getAttachmentOptions({filename:"file",contentType:"",knownLength:n})},e.prototype.isStream=function(e){return"object"==typeof e&&"function"==typeof e.pipe},e.prototype.isCustomFile=function(e){return"object"==typeof e&&!!e.data},e.prototype.isBrowserFile=function(e){return"object"==typeof e&&(!!e.name||"undefined"!=typeof Blob&&e instanceof Blob)},e.prototype.isBuffer=function(e){return"undefined"!=typeof Buffer&&Buffer.isBuffer(e)},e.prototype.getAttachmentInfo=function(e){var n=this.isBrowserFile(e),t=this.isCustomFile(e);if(!("string"==typeof e)){if(n)return this.getFileInfo(e);if("undefined"!=typeof Buffer&&Buffer.isBuffer(e))return this.getBufferInfo(e);if(t)return this.getCustomFileInfo(e)}return{filename:"file",contentType:void 0,knownLength:void 0}},e.prototype.convertToFDexpectedShape=function(e){var n,t=this.isStream(e),a=this.isBrowserFile(e),i=this.isCustomFile(e);if(t||"string"==typeof e||a||this.isBuffer(e))n=e;else{if(!i)throw Ko.getUserDataError("Unknown attachment type ".concat(typeof e),'The "attachment" property expects either Buffer, Blob, or String.\n          Also, It is possible to provide an object that has the property "data" with a value that is equal to one of the types counted before.\n          Additionally, you may use an array to send more than one attachment.');n=e.data}return n},e.prototype.getBlobFromStream=function(e,n){return new Qo(e,n)},e}(),Xo=function(){function e(e){this.FormDataConstructor=e,this.fileKeys=["attachment","inline","multipleValidationFile"],this.attachmentsHandler=new Yo}return e.prototype.createFormData=function(e){var n=this;if(!e)throw new Error("Please provide data object");return Object.keys(e).filter((function(n){return e[n]})).reduce((function(t,a){if(n.fileKeys.includes(a)){var i=e[a];if(n.isMessageAttachment(i))return n.addFilesToFD(a,i,t),t;throw Ko.getUserDataError("Unknown value ".concat(e[a]," with type ").concat(typeof e[a],' for property "').concat(a,'"'),'The key "'.concat(a,'" should have type of Buffer, Stream, File, or String '))}if("message"===a){var o=e[a];if(!o||!n.isMIME(o))throw Ko.getUserDataError('Unknown data type for "'.concat(a,'" property'),"The mime data should have type of Buffer, String or Blob");return n.addMimeDataToFD(a,o,t),t}return n.addCommonPropertyToFD(a,e[a],t),t}),new this.FormDataConstructor)},e.prototype.addMimeDataToFD=function(e,n,t){if("string"!=typeof n){if(this.isFormDataPackage(t))t.append(e,n,{filename:"MimeMessage"});else if(void 0!==typeof Blob){var a=t;if(n instanceof Blob)return void a.append(e,n,"MimeMessage");if(this.attachmentsHandler.isBuffer(n)){var i=new Blob([n]);a.append(e,i,"MimeMessage")}}}else t.append(e,n)},e.prototype.isMIME=function(e){return"string"==typeof e||"undefined"!=typeof Blob&&e instanceof Blob||this.attachmentsHandler.isBuffer(e)||"undefined"!=typeof ReadableStream&&e instanceof ReadableStream},e.prototype.isFormDataPackage=function(e){return"object"==typeof e&&null!==e&&"function"==typeof e.getHeaders},e.prototype.isMessageAttachment=function(e){var n=this;return this.attachmentsHandler.isCustomFile(e)||"string"==typeof e||"undefined"!=typeof File&&e instanceof File||"undefined"!=typeof Blob&&e instanceof Blob||this.attachmentsHandler.isBuffer(e)||this.attachmentsHandler.isStream(e)||Array.isArray(e)&&e.every((function(t){return n.attachmentsHandler.isCustomFile(t)||"undefined"!=typeof File&&t instanceof File||"undefined"!=typeof Blob&&e instanceof Blob||n.attachmentsHandler.isBuffer(t)||n.attachmentsHandler.isStream(t)}))},e.prototype.addFilesToFD=function(e,n,t){var a=this,i=function(e,n,i){var o="multipleValidationFile"===e?"file":e,s=a.attachmentsHandler.convertToFDexpectedShape(n),r=a.attachmentsHandler.getAttachmentInfo(n);if(a.isFormDataPackage(i)){var c=i,p="string"==typeof s?Buffer.from(s):s;c.append(o,p,r)}else if(void 0!==typeof Blob){var u=t;if("string"==typeof s||a.attachmentsHandler.isBuffer(s)){var l=new Blob([s]);return void u.append(o,l,r.filename)}if(s instanceof Blob)return void u.append(o,s,r.filename);if(a.attachmentsHandler.isStream(s)){var d=a.attachmentsHandler.getBlobFromStream(s,r.knownLength);u.set(o,d,r.filename)}}};Array.isArray(n)?n.forEach((function(n){i(e,n,t)})):i(e,n,t)},e.prototype.addCommonPropertyToFD=function(e,n,t){var a=this,i=function(e,n){if(a.isFormDataPackage(t))return"object"==typeof n?(console.warn('The received value is an object. \n"JSON.Stringify" will be used to avoid TypeError \nTo remove this warning: \nConsider switching to built-in FormData or converting the value on your own.\n'),t.append(e,JSON.stringify(n))):t.append(e,n);if("string"==typeof n)return t.append(e,n);if(void 0!==typeof Blob&&n instanceof Blob)return t.append(e,n);throw Ko.getUserDataError("Unknown value type for Form Data. String or Blob expected","Browser compliant FormData allows only string or Blob values for properties that are not attachments.")};Array.isArray(n)?n.forEach((function(n){i(e,n)})):null!=n&&i(e,n)},e}(),Zo=function(){function e(e){this.request=e}return e.prototype.list=function(e){return this.request.get("/v5/accounts/subaccounts",e).then((function(e){return e.body}))},e.prototype.get=function(e){return this.request.get("/v5/accounts/subaccounts/".concat(e)).then((function(e){return e.body}))},e.prototype.create=function(e){return this.request.postWithFD("/v5/accounts/subaccounts",{name:e}).then((function(e){return e.body}))},e.prototype.enable=function(e){return this.request.post("/v5/accounts/subaccounts/".concat(e,"/enable")).then((function(e){return e.body}))},e.prototype.disable=function(e){return this.request.post("/v5/accounts/subaccounts/".concat(e,"/disable")).then((function(e){return e.body}))},e.SUBACCOUNT_HEADER="X-Mailgun-On-Behalf-Of",e}(),es=function(){function e(e,n){this.username=e.username,this.key=e.key,this.url=e.url,this.timeout=e.timeout,this.headers=this.makeHeadersFromObject(e.headers),this.formDataBuilder=new Xo(n),this.maxBodyLength=********,this.proxy=null==e?void 0:e.proxy}return e.prototype.request=function(e,n,t){var a,i,o;return h(this,void 0,void 0,(function(){var s,r,c,p,u,l,d,m;return v(this,(function(h){switch(h.label){case 0:null==(s=f({},t))||delete s.headers,r=this.joinAndTransformHeaders(t),c=f({},s),(null==s?void 0:s.query)&&Object.getOwnPropertyNames(null==s?void 0:s.query).length>0&&(c.params=new URLSearchParams(s.query),delete c.query),(null==s?void 0:s.body)&&(p=null==s?void 0:s.body,c.data=p,delete c.body),l=E(this.url,n),h.label=1;case 1:return h.trys.push([1,3,,4]),[4,To.request(f(f({method:e.toLocaleUpperCase(),timeout:this.timeout,url:l,headers:r},c),{maxBodyLength:this.maxBodyLength,proxy:this.proxy}))];case 2:return u=h.sent(),[3,4];case 3:throw d=h.sent(),new Ko({status:(null===(a=null==(m=d)?void 0:m.response)||void 0===a?void 0:a.status)||400,statusText:(null===(i=null==m?void 0:m.response)||void 0===i?void 0:i.statusText)||m.code,body:(null===(o=null==m?void 0:m.response)||void 0===o?void 0:o.data)||m.message});case 4:return[4,this.getResponseBody(u)];case 5:return[2,h.sent()]}}))}))},e.prototype.getResponseBody=function(e){return h(this,void 0,void 0,(function(){var n;return v(this,(function(t){if(n={body:{},status:null==e?void 0:e.status},"string"==typeof e.data){if("Mailgun Magnificent API"===e.data)throw new Ko({status:400,statusText:"Incorrect url",body:e.data});n.body={message:e.data}}else n.body=e.data;return[2,n]}))}))},e.prototype.joinAndTransformHeaders=function(e){var n=new Io,t=j.encode("".concat(this.username,":").concat(this.key));n.setAuthorization("Basic ".concat(t)),n.set(this.headers);var a=e&&e.headers,i=this.makeHeadersFromObject(a);return n.set(i),n},e.prototype.makeHeadersFromObject=function(e){void 0===e&&(e={});var n=new Io;return n=Object.entries(e).reduce((function(e,n){var t=n[0],a=n[1];return e.set(t,a),e}),n)},e.prototype.setSubaccountHeader=function(e){var n,t=this.makeHeadersFromObject(f(f({},this.headers),((n={})[Zo.SUBACCOUNT_HEADER]=e,n)));this.headers.set(t)},e.prototype.resetSubaccountHeader=function(){this.headers.delete(Zo.SUBACCOUNT_HEADER)},e.prototype.query=function(e,n,t,a){return this.request(e,n,f({query:t},a))},e.prototype.command=function(e,n,t,a,i){void 0===i&&(i=!0);var o={};i&&(o={"Content-Type":"application/x-www-form-urlencoded"});var s=f(f(f({},o),{body:t}),a);return this.request(e,n,s)},e.prototype.get=function(e,n,t){return this.query("get",e,n,t)},e.prototype.post=function(e,n,t){return this.command("post",e,n,t)},e.prototype.postWithFD=function(e,n){var t=this.formDataBuilder.createFormData(n);return this.command("post",e,t,{headers:{"Content-Type":"multipart/form-data"}},!1)},e.prototype.putWithFD=function(e,n){var t=this.formDataBuilder.createFormData(n);return this.command("put",e,t,{headers:{"Content-Type":"multipart/form-data"}},!1)},e.prototype.patchWithFD=function(e,n){var t=this.formDataBuilder.createFormData(n);return this.command("patch",e,t,{headers:{"Content-Type":"multipart/form-data"}},!1)},e.prototype.put=function(e,n,t){return this.command("put",e,n,t)},e.prototype.delete=function(e,n){return this.command("delete",e,n)},e}(),ns=function(e,n,t){this.name=e.name,this.require_tls=e.require_tls,this.skip_verification=e.skip_verification,this.state=e.state,this.wildcard=e.wildcard,this.spam_action=e.spam_action,this.created_at=new Date(e.created_at),this.smtp_password=e.smtp_password,this.smtp_login=e.smtp_login,this.type=e.type,this.receiving_dns_records=n||null,this.sending_dns_records=t||null,this.id=e.id,this.is_disabled=e.is_disabled,this.web_prefix=e.web_prefix,this.web_scheme=e.web_scheme,this.use_automatic_sender_security=e.use_automatic_sender_security;var a=["dkim_host","mailfrom_host"].reduce((function(n,t){return e[t]&&(n[t]=e[t]),n}),{});Object.assign(this,a)},ts=function(){function e(e,n,t,a,i,o){void 0===o&&(o=console),this.request=e,this.domainCredentials=n,this.domainTemplates=t,this.domainTags=a,this.logger=o,this.domainTracking=i}return e.prototype._handleBoolValues=function(e){var n=e,t=Object.keys(n).reduce((function(e,t){var a=t;if("boolean"==typeof n[a]){var i=n[a];e[a]="true"===i.toString()?"true":"false"}return e}),{});return f(f({},e),t)},e.prototype._parseMessage=function(e){return e.body},e.prototype.parseDomainList=function(e){return e.body&&e.body.items?e.body.items.map((function(e){return new ns(e)})):[]},e.prototype._parseDomain=function(e){return new ns(e.body.domain,e.body.receiving_dns_records,e.body.sending_dns_records)},e.prototype.list=function(e){var n=this;return this.request.get("/v4/domains",e).then((function(e){return n.parseDomainList(e)}))},e.prototype.get=function(e,n){var t,a,i=this,o=n?{"h:extended":null!==(t=null==n?void 0:n.extended)&&void 0!==t&&t,"h:with_dns":null===(a=null==n?void 0:n.with_dns)||void 0===a||a}:{};return this.request.get("/v4/domains/".concat(e),o).then((function(e){return i._parseDomain(e)}))},e.prototype.create=function(e){var n=this,t=this._handleBoolValues(e);return this.request.postWithFD("/v4/domains",t).then((function(e){return n._parseDomain(e)}))},e.prototype.update=function(e,n){var t=this,a=this._handleBoolValues(n);return this.request.putWithFD("/v4/domains/".concat(e),a).then((function(e){return t._parseDomain(e)}))},e.prototype.verify=function(e){var n=this;return this.request.put("/v4/domains/".concat(e,"/verify")).then((function(e){return n._parseDomain(e)}))},e.prototype.destroy=function(e){var n=this;return this.request.delete("/v3/domains/".concat(e)).then((function(e){return n._parseMessage(e)}))},e.prototype.getConnection=function(e){return this.request.get("/v3/domains/".concat(e,"/connection")).then((function(e){return e})).then((function(e){return e.body}))},e.prototype.updateConnection=function(e,n){return this.request.put("/v3/domains/".concat(e,"/connection"),n).then((function(e){return e})).then((function(e){return e.body}))},e.prototype.getTracking=function(e){return this.logger.warn("\n      'domains.getTracking' method is deprecated, and will be removed. Please use 'domains.domainTracking.getTracking' instead.\n    "),this.domainTracking.getTracking(e)},e.prototype.updateTracking=function(e,n,t){return this.logger.warn("\n      'domains.updateTracking' method is deprecated, and will be removed. Please use 'domains.domainTracking.updateTracking' instead.\n    "),this.domainTracking.updateTracking(e,n,t)},e.prototype.getIps=function(e){return this.logger.warn('"domains.getIps" method is deprecated and will be removed in the future releases.'),this.request.get(E("/v3/domains",e,"ips")).then((function(e){var n;return null===(n=null==e?void 0:e.body)||void 0===n?void 0:n.items}))},e.prototype.assignIp=function(e,n){return this.logger.warn('"domains.assignIp" method is deprecated and will be removed in the future releases.'),this.request.postWithFD(E("/v3/domains",e,"ips"),{ip:n})},e.prototype.deleteIp=function(e,n){return this.logger.warn('"domains.deleteIp" method is deprecated and will be moved into the IpsClient in the future releases.'),this.request.delete(E("/v3/domains",e,"ips",n))},e.prototype.linkIpPool=function(e,n){return this.logger.warn('"domains.linkIpPool" method is deprecated, and will be removed in the future releases.'),this.request.postWithFD(E("/v3/domains",e,"ips"),{pool_id:n})},e.prototype.unlinkIpPoll=function(e,n){this.logger.warn('"domains.unlinkIpPoll" method is deprecated, and will be moved into the IpsClient in the future releases.');var t="";if(n.pool_id&&n.ip)throw Ko.getUserDataError("Too much data for replacement","Please specify either pool_id or ip (not both)");return n.pool_id?t="?pool_id=".concat(n.pool_id):n.ip&&(t="?ip=".concat(n.ip)),this.request.delete(E("/v3/domains",e,"ips","ip_pool",t))},e.prototype.updateDKIMAuthority=function(e,n){return this.request.put("/v3/domains/".concat(e,"/dkim_authority"),{},{query:"self=".concat(n.self)}).then((function(e){return e})).then((function(e){return e.body}))},e.prototype.updateDKIMSelector=function(e,n){var t;return h(this,void 0,void 0,(function(){var a;return v(this,(function(i){switch(i.label){case 0:return[4,this.request.put("/v3/domains/".concat(e,"/dkim_selector"),{},{query:"dkim_selector=".concat(n.dkimSelector)})];case 1:return[2,{status:(a=i.sent()).status,message:null===(t=null==a?void 0:a.body)||void 0===t?void 0:t.message}]}}))}))},e.prototype.updateWebPrefix=function(e,n){return this.logger.warn('"domains.updateWebPrefix" method is deprecated, please use domains.update to set new "web_prefix". Current method will be removed in the future releases.'),this.request.put("/v3/domains/".concat(e,"/web_prefix"),{},{query:"web_prefix=".concat(n.webPrefix)}).then((function(e){return e}))},e}(),as=function(){function e(e){e&&(this.request=e)}return e.prototype.parsePage=function(e,n,t,a){var i=new URL(n).searchParams,o=n&&"string"==typeof n&&n.split(t).pop()||"",s=null;return a&&(s=i.has(a)?i.get(a):void 0),{id:e,page:"?"===t?"?".concat(o):o,iteratorPosition:s,url:n}},e.prototype.parsePageLinks=function(e,n,t){var a=this;return Object.entries(e.body.paging).reduce((function(e,i){var o=i[0],s=i[1];return e[o]=a.parsePage(o,s,n,t),e}),{})},e.prototype.updateUrlAndQuery=function(e,n){var t=e,a=f({},n);return a.page&&(t=E(e,a.page),delete a.page),{url:t,updatedQuery:a}},e.prototype.requestListWithPages=function(e,n,t){return h(this,void 0,void 0,(function(){var a,i,o,s;return v(this,(function(r){switch(r.label){case 0:return a=this.updateUrlAndQuery(e,n),i=a.url,o=a.updatedQuery,this.request?[4,this.request.get(i,o)]:[3,2];case 1:return s=r.sent(),[2,this.parseList(s,t)];case 2:throw new Ko({status:500,statusText:"Request property is empty",body:{message:""}})}}))}))},e}(),is=function(e){function n(n){var t=e.call(this,n)||this;return t.request=n,t}return m(n,e),n.prototype.parseList=function(e){var n={};return n.items=e.body.items,n.pages=this.parsePageLinks(e,"/"),n.status=e.status,n},n.prototype.get=function(e,n){return h(this,void 0,void 0,(function(){return v(this,(function(t){return[2,this.requestListWithPages(E("/v3",e,"events"),n)]}))}))},n}(as),os=function(e){this.start=new Date(e.start),this.end=new Date(e.end),this.resolution=e.resolution,this.stats=e.stats.map((function(e){var n=f({},e);return n.time=new Date(e.time),n}))},ss=function(){function e(e,n){void 0===n&&(n=console),this.request=e,this.logger=n}return e.prototype.convertDateToUTC=function(e,n){return this.logger.warn('Date:"'.concat(n,'" was auto-converted to UTC time zone.\nValue "').concat(n.toUTCString(),'" will be used for request.\nConsider using string type for property "').concat(e,'" to avoid auto-converting')),[e,n.toUTCString()]},e.prototype.prepareSearchParams=function(e){var n=this,t=[];return"object"==typeof e&&Object.keys(e).length&&(t=Object.entries(e).reduce((function(e,t){var a=t[0],i=t[1];if(Array.isArray(i)&&i.length){var o=i.map((function(e){return[a,e]}));return x(x([],e,!0),o,!0)}return i instanceof Date?(e.push(n.convertDateToUTC(a,i)),e):("string"==typeof i&&e.push([a,i]),e)}),[])),t},e.prototype.parseStats=function(e){return new os(e.body)},e.prototype.getDomain=function(e,n){var t=this.prepareSearchParams(n);return this.request.get(E("/v3",e,"stats/total"),t).then(this.parseStats)},e.prototype.getAccount=function(e){var n=this.prepareSearchParams(e);return this.request.get("/v3/stats/total",n).then(this.parseStats)},e}();!function(e){e.HOUR="hour",e.DAY="day",e.MONTH="month"}(Vo||(Vo={})),function(e){e.BOUNCES="bounces",e.COMPLAINTS="complaints",e.UNSUBSCRIBES="unsubscribes",e.WHITELISTS="whitelists"}($o||($o={})),function(e){e.CLICKED="clicked",e.COMPLAINED="complained",e.DELIVERED="delivered",e.OPENED="opened",e.PERMANENT_FAIL="permanent_fail",e.TEMPORARY_FAIL="temporary_fail",e.UNSUBSCRIBED="unsubscribe"}(Go||(Go={})),function(e){e.YES="yes",e.NO="no"}(Jo||(Jo={}));var rs=function(e){this.type=e},cs=function(e){function n(n){var t=e.call(this,$o.BOUNCES)||this;return t.address=n.address,t.code=+n.code,t.error=n.error,t.created_at=new Date(n.created_at),t}return m(n,e),n}(rs),ps=function(e){function n(n){var t=e.call(this,$o.COMPLAINTS)||this;return t.address=n.address,t.created_at=new Date(n.created_at),t}return m(n,e),n}(rs),us=function(e){function n(n){var t=e.call(this,$o.UNSUBSCRIBES)||this;return t.address=n.address,t.tags=n.tags,t.created_at=new Date(n.created_at),t}return m(n,e),n}(rs),ls=function(e){function n(n){var t=e.call(this,$o.WHITELISTS)||this;return t.value=n.value,t.reason=n.reason,t.createdAt=new Date(n.createdAt),t}return m(n,e),n}(rs),ds={headers:{"Content-Type":"application/json"}},ms=function(e){function n(n){var t=e.call(this,n)||this;return t.request=n,t.models={bounces:cs,complaints:ps,unsubscribes:us,whitelists:ls},t}return m(n,e),n.prototype.parseList=function(e,n){var t,a={};return a.items=(null===(t=e.body.items)||void 0===t?void 0:t.map((function(e){return new n(e)})))||[],a.pages=this.parsePageLinks(e,"?","address"),a.status=e.status,a},n.prototype._parseItem=function(e,n){return new n(e)},n.prototype.createWhiteList=function(e,n,t){if(t)throw Ko.getUserDataError("Data property should be an object","Whitelist's creation process does not support multiple creations. Data property should be an object");return this.request.postWithFD(E("v3",e,"whitelists"),n).then(this.prepareResponse)},n.prototype.createUnsubscribe=function(e,n){if(Array.isArray(n)){if(n.some((function(e){return e.tag})))throw Ko.getUserDataError("Tag property should not be used for creating multiple unsubscribes.","Tag property can be used only if one unsubscribe provided as second argument of create method. Please use tags instead.");return this.request.post(E("v3",e,"unsubscribes"),JSON.stringify(n),ds).then(this.prepareResponse)}if(null==n?void 0:n.tags)throw Ko.getUserDataError("Tags property should not be used for creating one unsubscribe.","Tags property can be used if you provides an array of unsubscribes as second argument of create method. Please use tag instead");if(Array.isArray(n.tag))throw Ko.getUserDataError("Tag property can not be an array","Please use array of unsubscribes as second argument of create method to be able to provide few tags");return this.request.postWithFD(E("v3",e,"unsubscribes"),n).then(this.prepareResponse)},n.prototype.getModel=function(e){if(e in this.models)return this.models[e];throw Ko.getUserDataError("Unknown type value","Type may be only one of [bounces, complaints, unsubscribes, whitelists]")},n.prototype.prepareResponse=function(e){return{message:e.body.message,type:e.body.type||"",value:e.body.value||"",status:e.status}},n.prototype.list=function(e,n,t){return h(this,void 0,void 0,(function(){var a;return v(this,(function(i){return a=this.getModel(n),[2,this.requestListWithPages(E("v3",e,n),t,a)]}))}))},n.prototype.get=function(e,n,t){var a=this,i=this.getModel(n);return this.request.get(E("v3",e,n,encodeURIComponent(t))).then((function(e){return a._parseItem(e.body,i)}))},n.prototype.create=function(e,n,t){var a;this.getModel(n);var i=Array.isArray(t);return"whitelists"===n?this.createWhiteList(e,t,i):"unsubscribes"===n?this.createUnsubscribe(e,t):(a=i?x([],t,!0):[t],this.request.post(E("v3",e,n),JSON.stringify(a),ds).then(this.prepareResponse))},n.prototype.destroy=function(e,n,t){return this.getModel(n),this.request.delete(E("v3",e,n,encodeURIComponent(t))).then((function(e){return{message:e.body.message,value:e.body.value||"",address:e.body.address||"",status:e.status}}))},n}(as),fs=function(e,n,t){this.id=e,this.url=n,this.urls=t},hs=function(){function e(e){this.request=e}return e.prototype._parseWebhookList=function(e){return e.body.webhooks},e.prototype._parseWebhookWithID=function(e){return function(n){var t,a=null===(t=null==n?void 0:n.body)||void 0===t?void 0:t.webhook,i=null==a?void 0:a.url,o=null==a?void 0:a.urls;return i||(i=o&&o.length?o[0]:void 0),o&&0!==o.length||!i||(o=[i]),new fs(e,i,o)}},e.prototype._parseWebhookTest=function(e){return{code:e.body.code,message:e.body.message}},e.prototype.list=function(e,n){return this.request.get(E("/v3/domains",e,"webhooks"),n).then(this._parseWebhookList)},e.prototype.get=function(e,n){return this.request.get(E("/v3/domains",e,"webhooks",n)).then(this._parseWebhookWithID(n))},e.prototype.create=function(e,n,t,a){return void 0===a&&(a=!1),a?this.request.putWithFD(E("/v3/domains",e,"webhooks",n,"test"),{url:t}).then(this._parseWebhookTest):this.request.postWithFD(E("/v3/domains",e,"webhooks"),{id:n,url:t}).then(this._parseWebhookWithID(n))},e.prototype.update=function(e,n,t){return this.request.putWithFD(E("/v3/domains",e,"webhooks",n),{url:t}).then(this._parseWebhookWithID(n))},e.prototype.destroy=function(e,n){return this.request.delete(E("/v3/domains",e,"webhooks",n)).then(this._parseWebhookWithID(n))},e}(),vs=function(){function e(e){this.request=e}return e.prototype.prepareBooleanValues=function(e){var n=new Set(["o:testmode","t:text","o:dkim","o:tracking","o:tracking-clicks","o:tracking-opens","o:require-tls","o:skip-verification"]);if(!e||0===Object.keys(e).length)throw Ko.getUserDataError("Message data object can not be empty","Message data object can not be empty");return Object.keys(e).reduce((function(t,a){return n.has(a)&&"boolean"==typeof e[a]?t[a]=e[a]?"yes":"no":t[a]=e[a],t}),{})},e.prototype._parseResponse=function(e){return f({status:e.status},e.body)},e.prototype.create=function(e,n){if(n.message)return this.request.postWithFD("/v3/".concat(e,"/messages.mime"),n).then(this._parseResponse);var t=this.prepareBooleanValues(n);return this.request.postWithFD("/v3/".concat(e,"/messages"),t).then(this._parseResponse)},e}(),xs=function(){function e(e){this.request=e}return e.prototype.list=function(e){return this.request.get("/v3/routes",e).then((function(e){return e.body.items}))},e.prototype.get=function(e){return this.request.get("/v3/routes/".concat(e)).then((function(e){return e.body.route}))},e.prototype.create=function(e){return this.request.postWithFD("/v3/routes",e).then((function(e){return e.body.route}))},e.prototype.update=function(e,n){return this.request.putWithFD("/v3/routes/".concat(e),n).then((function(e){return e.body}))},e.prototype.destroy=function(e){return this.request.delete("/v3/routes/".concat(e)).then((function(e){return e.body}))},e}(),bs=function(){function e(e,n){this.request=e,this.multipleValidation=n}return e.prototype.get=function(e){return h(this,void 0,void 0,(function(){var n;return v(this,(function(t){switch(t.label){case 0:return n={address:e},[4,this.request.get("/v4/address/validate",n)];case 1:return[2,t.sent().body]}}))}))},e}(),gs=function(){function e(e){this.request=e}return e.prototype.list=function(e){return h(this,void 0,void 0,(function(){var n;return v(this,(function(t){switch(t.label){case 0:return[4,this.request.get("/v3/ips",e)];case 1:return n=t.sent(),[2,this.parseIpsResponse(n)]}}))}))},e.prototype.get=function(e){return h(this,void 0,void 0,(function(){var n;return v(this,(function(t){switch(t.label){case 0:return[4,this.request.get("/v3/ips/".concat(e))];case 1:return n=t.sent(),[2,this.parseIpsResponse(n)]}}))}))},e.prototype.parseIpsResponse=function(e){return e.body},e}(),ys=function(){function e(e){this.request=e}return e.prototype.list=function(){var e=this;return this.request.get("/v1/ip_pools").then((function(n){return e.parseIpPoolsResponse(n)}))},e.prototype.create=function(e){return h(this,void 0,void 0,(function(){var n;return v(this,(function(t){switch(t.label){case 0:return[4,this.request.postWithFD("/v1/ip_pools",e)];case 1:return n=t.sent(),[2,f({status:n.status},n.body)]}}))}))},e.prototype.update=function(e,n){return h(this,void 0,void 0,(function(){var t;return v(this,(function(a){switch(a.label){case 0:return[4,this.request.patchWithFD("/v1/ip_pools/".concat(e),n)];case 1:return t=a.sent(),[2,f({status:t.status},t.body)]}}))}))},e.prototype.delete=function(e,n){return h(this,void 0,void 0,(function(){var t;return v(this,(function(a){switch(a.label){case 0:return[4,this.request.delete("/v1/ip_pools/".concat(e),n)];case 1:return t=a.sent(),[2,f({status:t.status},t.body)]}}))}))},e.prototype.parseIpPoolsResponse=function(e){return f({status:e.status},e.body)},e}(),ws=function(e){function n(n,t){var a=e.call(this,n)||this;return a.request=n,a.baseRoute="/v3/lists",a.members=t,a}return m(n,e),n.prototype.parseValidationResult=function(e,n){return{status:e,validationResult:f(f({},n),{created_at:new Date(1e3*n.created_at)})}},n.prototype.parseList=function(e){var n={};return n.items=e.body.items,n.pages=this.parsePageLinks(e,"?","address"),n.status=e.status,n},n.prototype.list=function(e){return h(this,void 0,void 0,(function(){return v(this,(function(n){return[2,this.requestListWithPages("".concat(this.baseRoute,"/pages"),e)]}))}))},n.prototype.get=function(e){return this.request.get("".concat(this.baseRoute,"/").concat(e)).then((function(e){return e.body.list}))},n.prototype.create=function(e){return this.request.postWithFD(this.baseRoute,e).then((function(e){return e.body.list}))},n.prototype.update=function(e,n){return this.request.putWithFD("".concat(this.baseRoute,"/").concat(e),n).then((function(e){return e.body.list}))},n.prototype.destroy=function(e){return this.request.delete("".concat(this.baseRoute,"/").concat(e)).then((function(e){return e.body}))},n.prototype.validate=function(e){return this.request.post("".concat(this.baseRoute,"/").concat(e,"/validate"),{}).then((function(e){return f({status:e.status},e.body)}))},n.prototype.validationResult=function(e){var n=this;return this.request.get("".concat(this.baseRoute,"/").concat(e,"/validate")).then((function(e){return n.parseValidationResult(e.status,e.body)}))},n.prototype.cancelValidation=function(e){return this.request.delete("".concat(this.baseRoute,"/").concat(e,"/validate")).then((function(e){return{status:e.status,message:e.body.message}}))},n}(as),ks=function(e){function n(n){var t=e.call(this,n)||this;return t.request=n,t.baseRoute="/v3/lists",t}return m(n,e),n.prototype.checkAndUpdateData=function(e){var n=f({},e);return"object"==typeof e.vars&&(n.vars=JSON.stringify(n.vars)),"boolean"==typeof e.subscribed&&(n.subscribed=e.subscribed?"yes":"no"),n},n.prototype.parseList=function(e){var n={};return n.items=e.body.items,n.pages=this.parsePageLinks(e,"?","address"),n},n.prototype.listMembers=function(e,n){return h(this,void 0,void 0,(function(){return v(this,(function(t){return[2,this.requestListWithPages("".concat(this.baseRoute,"/").concat(e,"/members/pages"),n)]}))}))},n.prototype.getMember=function(e,n){return this.request.get("".concat(this.baseRoute,"/").concat(e,"/members/").concat(n)).then((function(e){return e.body.member}))},n.prototype.createMember=function(e,n){var t=this.checkAndUpdateData(n);return this.request.postWithFD("".concat(this.baseRoute,"/").concat(e,"/members"),t).then((function(e){return e.body.member}))},n.prototype.createMembers=function(e,n){var t={members:Array.isArray(n.members)?JSON.stringify(n.members):n.members,upsert:n.upsert};return this.request.postWithFD("".concat(this.baseRoute,"/").concat(e,"/members.json"),t).then((function(e){return e.body}))},n.prototype.updateMember=function(e,n,t){var a=this.checkAndUpdateData(t);return this.request.putWithFD("".concat(this.baseRoute,"/").concat(e,"/members/").concat(n),a).then((function(e){return e.body.member}))},n.prototype.destroyMember=function(e,n){return this.request.delete("".concat(this.baseRoute,"/").concat(e,"/members/").concat(n)).then((function(e){return e.body}))},n}(as),_s=function(){function e(e){this.request=e,this.baseRoute="/v3/domains/"}return e.prototype._parseDomainCredentialsList=function(e){return{items:e.body.items,totalCount:e.body.total_count}},e.prototype._parseMessageResponse=function(e){return{status:e.status,message:e.body.message}},e.prototype._parseDeletedResponse=function(e){return{status:e.status,message:e.body.message,spec:e.body.spec}},e.prototype.list=function(e,n){var t=this;return this.request.get(E(this.baseRoute,e,"/credentials"),n).then((function(e){return t._parseDomainCredentialsList(e)}))},e.prototype.create=function(e,n){var t=this;return this.request.postWithFD("".concat(this.baseRoute).concat(e,"/credentials"),n).then((function(e){return t._parseMessageResponse(e)}))},e.prototype.update=function(e,n,t){var a=this;return this.request.putWithFD("".concat(this.baseRoute).concat(e,"/credentials/").concat(n),t).then((function(e){return a._parseMessageResponse(e)}))},e.prototype.destroy=function(e,n){var t=this;return this.request.delete("".concat(this.baseRoute).concat(e,"/credentials/").concat(n)).then((function(e){return t._parseDeletedResponse(e)}))},e}(),js=function(e,n){var t,a;this.createdAt=new Date(e.created_at),this.id=e.id,this.quantity=e.quantity,this.recordsProcessed=e.records_processed,this.status=e.status,this.responseStatusCode=n,e.download_url&&(this.downloadUrl={csv:null===(t=e.download_url)||void 0===t?void 0:t.csv,json:null===(a=e.download_url)||void 0===a?void 0:a.json}),e.summary&&(this.summary={result:{catchAll:e.summary.result.catch_all,deliverable:e.summary.result.deliverable,doNotSend:e.summary.result.do_not_send,undeliverable:e.summary.result.undeliverable,unknown:e.summary.result.unknown},risk:{high:e.summary.risk.high,low:e.summary.risk.low,medium:e.summary.risk.medium,unknown:e.summary.risk.unknown}})},Rs=function(e){function n(n){var t=e.call(this)||this;return t.request=n,t.attachmentsHandler=new Yo,t}return m(n,e),n.prototype.handleResponse=function(e){return f({status:e.status},null==e?void 0:e.body)},n.prototype.parseList=function(e){var n={};return n.jobs=e.body.jobs.map((function(n){return new js(n,e.status)})),n.pages=this.parsePageLinks(e,"?","pivot"),n.total=e.body.total,n.status=e.status,n},n.prototype.list=function(e){return h(this,void 0,void 0,(function(){return v(this,(function(n){return[2,this.requestListWithPages("/v4/address/validate/bulk",e)]}))}))},n.prototype.get=function(e){return h(this,void 0,void 0,(function(){var n;return v(this,(function(t){switch(t.label){case 0:return[4,this.request.get("/v4/address/validate/bulk/".concat(e))];case 1:return n=t.sent(),[2,new js(n.body,n.status)]}}))}))},n.prototype.convertToExpectedShape=function(e){return this.attachmentsHandler.isBuffer(e.file)?{multipleValidationFile:e.file}:"string"==typeof e.file?{multipleValidationFile:{data:e.file}}:(this.attachmentsHandler.isStream(e.file),{multipleValidationFile:e.file})},n.prototype.create=function(e,n){return h(this,void 0,void 0,(function(){var t,a;return v(this,(function(i){switch(i.label){case 0:if(!n||!n.file)throw Ko.getUserDataError('"file" property expected.','Make sure second argument has "file" property.');return t=this.convertToExpectedShape(n),[4,this.request.postWithFD("/v4/address/validate/bulk/".concat(e),t)];case 1:return a=i.sent(),[2,this.handleResponse(a)]}}))}))},n.prototype.destroy=function(e){return h(this,void 0,void 0,(function(){var n;return v(this,(function(t){switch(t.label){case 0:return[4,this.request.delete("/v4/address/validate/bulk/".concat(e))];case 1:return n=t.sent(),[2,this.handleResponse(n)]}}))}))},n}(as),Ss=function(e){this.name=e.name,this.description=e.description,this.createdAt=e.createdAt?new Date(e.createdAt):"",this.createdBy=e.createdBy,this.id=e.id,e.version&&(this.version=e.version,this.version&&e.version.createdAt&&(this.version.createdAt=new Date(e.version.createdAt))),e.versions&&e.versions.length&&(this.versions=e.versions.map((function(e){var n=f({},e);return n.createdAt=new Date(e.createdAt),n})))},Es=function(e){function n(n){var t=e.call(this,n)||this;return t.request=n,t.baseRoute="/v3/",t}return m(n,e),n.prototype.parseCreationResponse=function(e){return new Ss(e.body.template)},n.prototype.parseCreationVersionResponse=function(e){var n={};return n.status=e.status,n.message=e.body.message,e.body&&e.body.template&&(n.template=new Ss(e.body.template)),n},n.prototype.parseMutationResponse=function(e){var n={};return n.status=e.status,n.message=e.body.message,e.body&&e.body.template&&(n.templateName=e.body.template.name),n},n.prototype.parseNotificationResponse=function(e){var n={};return n.status=e.status,n.message=e.body.message,n},n.prototype.parseMutateTemplateVersionResponse=function(e){var n={};return n.status=e.status,n.message=e.body.message,e.body.template&&(n.templateName=e.body.template.name,n.templateVersion={tag:e.body.template.version.tag}),n},n.prototype.parseList=function(e){var n={};return n.items=e.body.items.map((function(e){return new Ss(e)})),n.pages=this.parsePageLinks(e,"?","p"),n.status=e.status,n},n.prototype.parseListTemplateVersions=function(e){var n={};return n.template=new Ss(e.body.template),n.pages=this.parsePageLinks(e,"?","p"),n},n.prototype.list=function(e,n){return h(this,void 0,void 0,(function(){return v(this,(function(t){return[2,this.requestListWithPages(E(this.baseRoute,e,"/templates"),n)]}))}))},n.prototype.get=function(e,n,t){return this.request.get(E(this.baseRoute,e,"/templates/",n),t).then((function(e){return new Ss(e.body.template)}))},n.prototype.create=function(e,n){var t=this;return this.request.postWithFD(E(this.baseRoute,e,"/templates"),n).then((function(e){return t.parseCreationResponse(e)}))},n.prototype.update=function(e,n,t){var a=this;return this.request.putWithFD(E(this.baseRoute,e,"/templates/",n),t).then((function(e){return a.parseMutationResponse(e)}))},n.prototype.destroy=function(e,n){var t=this;return this.request.delete(E(this.baseRoute,e,"/templates/",n)).then((function(e){return t.parseMutationResponse(e)}))},n.prototype.destroyAll=function(e){var n=this;return this.request.delete(E(this.baseRoute,e,"/templates")).then((function(e){return n.parseNotificationResponse(e)}))},n.prototype.listVersions=function(e,n,t){var a=this;return this.request.get(E(this.baseRoute,e,"/templates",n,"/versions"),t).then((function(e){return a.parseListTemplateVersions(e)}))},n.prototype.getVersion=function(e,n,t){return this.request.get(E(this.baseRoute,e,"/templates/",n,"/versions/",t)).then((function(e){return new Ss(e.body.template)}))},n.prototype.createVersion=function(e,n,t){var a=this;return this.request.postWithFD(E(this.baseRoute,e,"/templates/",n,"/versions"),t).then((function(e){return a.parseCreationVersionResponse(e)}))},n.prototype.updateVersion=function(e,n,t,a){var i=this;return this.request.putWithFD(E(this.baseRoute,e,"/templates/",n,"/versions/",t),a).then((function(e){return i.parseMutateTemplateVersionResponse(e)}))},n.prototype.destroyVersion=function(e,n,t){var a=this;return this.request.delete(E(this.baseRoute,e,"/templates/",n,"/versions/",t)).then((function(e){return a.parseMutateTemplateVersionResponse(e)}))},n}(as),Ts=function(e){this.tag=e.tag,this.description=e.description,this["first-seen"]=new Date(e["first-seen"]),this["last-seen"]=new Date(e["last-seen"])},Cs=function(e){this.tag=e.body.tag,this.description=e.body.description,this.start=new Date(e.body.start),this.end=new Date(e.body.end),this.resolution=e.body.resolution,this.stats=e.body.stats.map((function(e){return f(f({},e),{time:new Date(e.time)})}))},Os=function(e){function n(n){var t=e.call(this,n)||this;return t.request=n,t.baseRoute="/v3/",t}return m(n,e),n.prototype.parseList=function(e){var n={};return n.items=e.body.items.map((function(e){return new Ts(e)})),n.pages=this.parsePageLinks(e,"?","tag"),n.status=e.status,n},n.prototype._parseTagStatistic=function(e){return new Cs(e)},n.prototype.list=function(e,n){return h(this,void 0,void 0,(function(){return v(this,(function(t){return[2,this.requestListWithPages(E(this.baseRoute,e,"/tags"),n)]}))}))},n.prototype.get=function(e,n){return this.request.get(E(this.baseRoute,e,"/tags",n)).then((function(e){return new Ts(e.body)}))},n.prototype.update=function(e,n,t){return this.request.put(E(this.baseRoute,e,"/tags",n),t).then((function(e){return e.body}))},n.prototype.destroy=function(e,n){return this.request.delete("".concat(this.baseRoute).concat(e,"/tags/").concat(n)).then((function(e){return{message:e.body.message,status:e.status}}))},n.prototype.statistic=function(e,n,t){var a=this;return this.request.get(E(this.baseRoute,e,"/tags",n,"stats"),t).then((function(e){return a._parseTagStatistic(e)}))},n.prototype.countries=function(e,n){return this.request.get(E(this.baseRoute,e,"/tags",n,"stats/aggregates/countries")).then((function(e){return e.body}))},n.prototype.providers=function(e,n){return this.request.get(E(this.baseRoute,e,"/tags",n,"stats/aggregates/providers")).then((function(e){return e.body}))},n.prototype.devices=function(e,n){return this.request.get(E(this.baseRoute,e,"/tags",n,"stats/aggregates/devices")).then((function(e){return e.body}))},n}(as),qs=function(e){function n(n,t,a,i){void 0===i&&(i=console);var o=e.call(this,n)||this;return o.request=n,o.attributes=t,o.filters=a,o.logger=i,o}return m(n,e),n.prototype.convertDateToUTC=function(e,n){return this.logger.warn('Date: "'.concat(n,'" was auto-converted to UTC time zone.\nValue "').concat(n.toISOString(),'" will be used for request.\nConsider using string type for property "').concat(e,'" to avoid auto-converting')),n.toISOString()},n.prototype.prepareQueryData=function(e){var n=this,t=e,a=Object.keys(t).reduce((function(a,i){var o=i;if(t[o]&&"object"==typeof t[o]){var s=e[o];a[o]=n.convertDateToUTC(o,s)}return a}),{});return f(f({},e),a)},n.prototype.prepareResult=function(e){var n=this.prepareSeedList(e.body);return f(f({},n),{status:e.status})},n.prototype.prepareSeedList=function(e){var n,t={created_at:new Date(e.created_at),updated_at:new Date(e.updated_at),last_result_at:new Date(e.last_result_at)};n=e.Seeds?e.Seeds.map((function(e){var n={created_at:new Date(e.created_at),updated_at:new Date(e.updated_at),max_email_count_hit_at:new Date(e.max_email_count_hit_at),last_sent_to_at:new Date(e.last_sent_to_at),last_delivered_at:new Date(e.last_delivered_at)};return f(f({},e),n)})):null;var a=f(f(f({},e),{Seeds:n}),t);return delete a.Id,a},n.prototype.parseList=function(e){var n,t=this,a={items:[]};return a.items=null===(n=e.body.items)||void 0===n?void 0:n.map((function(e){return t.prepareSeedList(e)})),a.pages=this.parsePageLinks(e,"?","address"),a.status=e.status,a},n.prototype.list=function(e){return h(this,void 0,void 0,(function(){var n,t;return v(this,(function(a){switch(a.label){case 0:return n=this.prepareQueryData(e),[4,this.request.get("/v4/inbox/seedlists",n)];case 1:return t=a.sent(),[2,f(f({},this.parseList(t)),{status:200})]}}))}))},n.prototype.get=function(e){return h(this,void 0,void 0,(function(){var n,t;return v(this,(function(a){switch(a.label){case 0:return[4,this.request.get("/v4/inbox/seedlists/".concat(e))];case 1:return n=a.sent(),t=this.prepareSeedList(n.body.seedlist),[2,f(f({},t),{status:n.status})]}}))}))},n.prototype.create=function(e){return h(this,void 0,void 0,(function(){var n;return v(this,(function(t){switch(t.label){case 0:return[4,this.request.postWithFD("/v4/inbox/seedlists",e)];case 1:return n=t.sent(),[2,this.prepareResult(n)]}}))}))},n.prototype.update=function(e,n){return h(this,void 0,void 0,(function(){var t;return v(this,(function(a){switch(a.label){case 0:return[4,this.request.put("/v4/inbox/seedlists/".concat(e),n)];case 1:return t=a.sent(),[2,this.prepareResult(t)]}}))}))},n.prototype.destroy=function(e){return h(this,void 0,void 0,(function(){return v(this,(function(n){return[2,this.request.delete("/v4/inbox/seedlists/".concat(e))]}))}))},n}(as),As=function(){function e(e,n,t,a){this.request=e,this.seedsLists=n,this.seedsLists=n,this.results=t,this.providers=a}return e.prototype.runTest=function(e){return h(this,void 0,void 0,(function(){var n;return v(this,(function(t){switch(t.label){case 0:return[4,this.request.post("/v4/inbox/tests",e)];case 1:return n=t.sent(),[2,f(f({},n.body),{status:n.status})]}}))}))},e}(),Fs=function(e){function n(n,t,a,i,o){void 0===o&&(o=console);var s=e.call(this,n)||this;return s.request=n,s.attributes=t,s.filters=a,s.sharing=i,s.logger=o,s}return m(n,e),n.prototype.convertDateToUTC=function(e,n){return this.logger.warn('Date: "'.concat(n,'" was auto-converted to UTC time zone.\nValue "').concat(n.toISOString(),'" will be used for request.\nConsider using string type for property "').concat(e,'" to avoid auto-converting')),n.toISOString()},n.prototype.prepareQueryData=function(e){var n=this,t=e,a=Object.keys(t).reduce((function(a,i){var o=i;if(t[o]&&"object"==typeof t[o]){var s=e[o];a[o]=n.convertDateToUTC(o,s)}return a}),{});return f(f({},e),a)},n.prototype.prepareInboxPlacementsResult=function(e){var n={},t={created_at:new Date(e.created_at),updated_at:new Date(e.updated_at),sharing_expires_at:new Date(e.sharing_expires_at)};e.Box&&delete(n=f(f({},e.Box),{created_at:new Date(e.Box.created_at),updated_at:new Date(e.Box.updated_at),last_result_at:new Date(e.Box.last_result_at)})).ID;var a=f(f(f(f({},e),{Box:n}),t),{id:e.Id});return delete a.ID,a},n.prototype.parseList=function(e){var n=this,t={};return t.items=e.body.items.map((function(e){return n.prepareInboxPlacementsResult(e)})),t.pages=this.parsePageLinks(e,"?","address"),t.status=e.status,t},n.prototype.list=function(e){return h(this,void 0,void 0,(function(){var n,t;return v(this,(function(a){switch(a.label){case 0:return n=this.prepareQueryData(e),[4,this.request.get("/v4/inbox/results",n)];case 1:return t=a.sent(),[2,this.parseList(t)]}}))}))},n.prototype.get=function(e){return h(this,void 0,void 0,(function(){var n,t;return v(this,(function(a){switch(a.label){case 0:return[4,this.request.get("/v4/inbox/results/".concat(e))];case 1:return n=a.sent(),t=this.prepareInboxPlacementsResult(n.body.result),[2,{status:n.status,inboxPlacementResult:t}]}}))}))},n.prototype.destroy=function(e){return h(this,void 0,void 0,(function(){var n;return v(this,(function(t){switch(t.label){case 0:return[4,this.request.delete("/v4/inbox/results/".concat(e))];case 1:return n=t.sent(),[2,f({status:n.status},n.body)]}}))}))},n.prototype.getResultByShareId=function(e){return h(this,void 0,void 0,(function(){var n,t;return v(this,(function(a){switch(a.label){case 0:return[4,this.request.get("/v4/inbox/sharing/public/".concat(e))];case 1:return n=a.sent(),t=this.prepareInboxPlacementsResult(n.body.result),[2,{status:n.status,inboxPlacementResult:t}]}}))}))},n}(as),Ds=function(){function e(e,n){this.path=n,this.request=e}return e.prototype.list=function(){return h(this,void 0,void 0,(function(){var e;return v(this,(function(n){switch(n.label){case 0:return[4,this.request.get(this.path)];case 1:return[2,{items:(e=n.sent()).body.items,status:e.status}]}}))}))},e.prototype.get=function(e){return h(this,void 0,void 0,(function(){var n;return v(this,(function(t){switch(t.label){case 0:return[4,this.request.get("".concat(this.path,"/").concat(e))];case 1:return n=t.sent(),[2,f(f({},n.body),{status:n.status})]}}))}))},e}(),Ps=function(){function e(e,n){this.request=e,this.path=n}return e.prototype.list=function(){return h(this,void 0,void 0,(function(){var e;return v(this,(function(n){switch(n.label){case 0:return[4,this.request.get(this.path)];case 1:return[2,{status:(e=n.sent()).status,supported_filters:e.body.supported_filters}]}}))}))},e}(),Bs=function(){function e(e){this.request=e}return e.prototype.prepareInboxPlacementsResultSharing=function(e){var n={expires_at:new Date(e.expires_at)};return f(f({},e),n)},e.prototype.get=function(e){return h(this,void 0,void 0,(function(){var n,t;return v(this,(function(a){switch(a.label){case 0:return[4,this.request.get("/v4/inbox/sharing/".concat(e))];case 1:return n=a.sent(),t=this.prepareInboxPlacementsResultSharing(n.body.sharing),[2,f({status:n.status},t)]}}))}))},e.prototype.update=function(e,n){return h(this,void 0,void 0,(function(){var t,a;return v(this,(function(i){switch(i.label){case 0:return[4,this.request.put("/v4/inbox/sharing/".concat(e),{},{query:"enabled=".concat(n.enabled)})];case 1:return t=i.sent(),a=this.prepareInboxPlacementsResultSharing(t.body.sharing),[2,f(f({},a),{status:t.status})]}}))}))},e}(),Ls=function(){function e(e){this.path="/v4/inbox/providers",this.request=e}return e.prototype.parseList=function(e){var n={};return n.items=e.body.items.map((function(e){var n={created_at:new Date(e.created_at),updated_at:new Date(e.updated_at)};return f(f({},e),n)})),n.status=e.status,n},e.prototype.list=function(){return h(this,void 0,void 0,(function(){var e;return v(this,(function(n){switch(n.label){case 0:return[4,this.request.get(this.path)];case 1:return e=n.sent(),[2,this.parseList(e)]}}))}))},e}(),Us=function(){function e(e,n){void 0===n&&(n=console),this.request=e,this.logger=n}return e.prototype.convertDateToUTC=function(e,n){return this.logger.warn('Date:"'.concat(n,'" was auto-converted to UTC time zone.\nValue "').concat(n.toUTCString(),'" will be used for request.\nConsider using string type for property "').concat(e,'" to avoid auto-converting')),n.toUTCString()},e.prototype.prepareQuery=function(e){var n,t;if(e){var a=null==e?void 0:e.start,i=null==e?void 0:e.end;n=a instanceof Date?this.convertDateToUTC("start",a):null!=a?a:"",t=i&&i instanceof Date?this.convertDateToUTC("end",i):null!=i?i:""}return f(f({},e),{start:n,end:t})},e.prototype.handleResponse=function(e){var n=e.body,t=Date.parse(n.start)?new Date(n.start):null,a=Date.parse(n.end)?new Date(n.end):null;return f(f({},n),{status:e.status,start:t,end:a})},e.prototype.getAccount=function(e){return h(this,void 0,void 0,(function(){var n,t;return v(this,(function(a){switch(a.label){case 0:return n=this.prepareQuery(e),[4,this.request.post("/v1/analytics/metrics",n)];case 1:return t=a.sent(),[2,this.handleResponse(t)]}}))}))},e.prototype.getAccountUsage=function(e){return h(this,void 0,void 0,(function(){var n,t;return v(this,(function(a){switch(a.label){case 0:return n=this.prepareQuery(e),[4,this.request.post("/v1/analytics/usage/metrics",n)];case 1:return t=a.sent(),[2,this.handleResponse(t)]}}))}))},e}(),zs=function(){function e(e){this.request=e}return e.prototype._parseTrackingSettings=function(e){return e.body.tracking},e.prototype._parseTrackingUpdate=function(e){return e.body},e.prototype._isOpenTrackingInfoWitPlace=function(e){return"object"==typeof e&&"place_at_the_top"in e},e.prototype.get=function(e){return h(this,void 0,void 0,(function(){var n;return v(this,(function(t){switch(t.label){case 0:return[4,this.request.get("/v2/x509/".concat(e,"/status"))];case 1:return n=t.sent(),[2,f(f({},n.body),{responseStatusCode:n.status})]}}))}))},e.prototype.generate=function(e){return h(this,void 0,void 0,(function(){var n;return v(this,(function(t){switch(t.label){case 0:return[4,this.request.post("/v2/x509/".concat(e))];case 1:return n=t.sent(),[2,f(f({},n.body),{status:n.status})]}}))}))},e.prototype.regenerate=function(e){return h(this,void 0,void 0,(function(){var n;return v(this,(function(t){switch(t.label){case 0:return[4,this.request.put("/v2/x509/".concat(e))];case 1:return n=t.sent(),[2,f(f({},n.body),{status:n.status})]}}))}))},e.prototype.getTracking=function(e){return h(this,void 0,void 0,(function(){var n;return v(this,(function(t){switch(t.label){case 0:return[4,this.request.get(E("/v3/domains",e,"tracking"))];case 1:return n=t.sent(),[2,this._parseTrackingSettings(n)]}}))}))},e.prototype.updateTracking=function(e,n,t){return h(this,void 0,void 0,(function(){var a,i;return v(this,(function(o){switch(o.label){case 0:return a=f({},t),"boolean"==typeof(null==t?void 0:t.active)&&(a.active=(null==t?void 0:t.active)?"yes":"no"),this._isOpenTrackingInfoWitPlace(t)&&"boolean"==typeof(null==t?void 0:t.place_at_the_top)&&(a.place_at_the_top=(null==t?void 0:t.place_at_the_top)?"yes":"no"),[4,this.request.putWithFD(E("/v3/domains",e,"tracking",n),a)];case 1:return i=o.sent(),[2,this._parseTrackingUpdate(i)]}}))}))},e}(),Is=function(){function e(e,n){var t=f({},e);if(t.url||(t.url="https://api.mailgun.net"),!t.username)throw new Error('Parameter "username" is required');if(!t.key)throw new Error('Parameter "key" is required');this.request=new es(t,n);var a=new ks(this.request),i=new _s(this.request),o=new Es(this.request),s=new Os(this.request),r=new zs(this.request),c=new Rs(this.request),p=new Bs(this.request),u=new Ds(this.request,"/v4/inbox/seedlists/a"),l=new Ds(this.request,"/v4/inbox/results/a"),d=new Ps(this.request,"/v4/inbox/seedlists/_filters"),m=new Ps(this.request,"/v4/inbox/results/_filters"),h=new qs(this.request,u,d),v=new Fs(this.request,l,m,p),x=new Ls(this.request);this.domains=new ts(this.request,i,o,s,r),this.webhooks=new hs(this.request),this.events=new is(this.request),this.stats=new ss(this.request),this.metrics=new Us(this.request),this.suppressions=new ms(this.request),this.messages=new vs(this.request),this.routes=new xs(this.request),this.ips=new gs(this.request),this.ip_pools=new ys(this.request),this.lists=new ws(this.request,a),this.validate=new bs(this.request,c),this.subaccounts=new Zo(this.request),this.inboxPlacements=new As(this.request,h,v,x)}return e.prototype.setSubaccount=function(e){var n;null===(n=this.request)||void 0===n||n.setSubaccountHeader(e)},e.prototype.resetSubaccount=function(){var e;null===(e=this.request)||void 0===e||e.resetSubaccountHeader()},e}(),Ns=function(){function e(e){this.formData=e}return Object.defineProperty(e,"default",{get:function(){return this},enumerable:!1,configurable:!0}),e.prototype.client=function(e){return new Is(e,this.formData)},e}();module.exports=Ns;
