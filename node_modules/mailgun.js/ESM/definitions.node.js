// mailgun.js v12.0.0 Copyright (c) 2025 Mailgun and contributors
var Resolution;
(function (Resolution) {
    Resolution["HOUR"] = "hour";
    Resolution["DAY"] = "day";
    Resolution["MONTH"] = "month";
})(Resolution = Resolution || (Resolution = {}));
var SuppressionModels;
(function (SuppressionModels) {
    SuppressionModels["BOUNCES"] = "bounces";
    SuppressionModels["COMPLAINTS"] = "complaints";
    SuppressionModels["UNSUBSCRIBES"] = "unsubscribes";
    SuppressionModels["WHITELISTS"] = "whitelists";
})(SuppressionModels = SuppressionModels || (SuppressionModels = {}));
var WebhooksIds;
(function (WebhooksIds) {
    WebhooksIds["CLICKED"] = "clicked";
    WebhooksIds["COMPLAINED"] = "complained";
    WebhooksIds["DELIVERED"] = "delivered";
    WebhooksIds["OPENED"] = "opened";
    WebhooksIds["PERMANENT_FAIL"] = "permanent_fail";
    WebhooksIds["TEMPORARY_FAIL"] = "temporary_fail";
    WebhooksIds["UNSUBSCRIBED"] = "unsubscribe";
})(WebhooksIds = WebhooksIds || (WebhooksIds = {}));
var YesNo;
(function (YesNo) {
    YesNo["YES"] = "yes";
    YesNo["NO"] = "no";
})(YesNo = YesNo || (YesNo = {}));

var index$1 = /*#__PURE__*/Object.freeze({
    __proto__: null,
    get Resolution () { return Resolution; },
    get SuppressionModels () { return SuppressionModels; },
    get WebhooksIds () { return WebhooksIds; },
    get YesNo () { return YesNo; }
});

var index = /*#__PURE__*/Object.freeze({
    __proto__: null
});

export { index$1 as Enums, index as Interfaces };
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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
