var e="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{};function t(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var s,r={exports:{}};
/*! https://mths.be/base64 v1.0.0 by @mathias | MIT license */var n,o,a,i=(s||(s=1,n=r,o=r.exports,function(t){var s=o,r=n&&n.exports==s&&n,a="object"==typeof e&&e;a.global!==a&&a.window!==a||(t=a);var i=function(e){this.message=e};(i.prototype=new Error).name="InvalidCharacterError";var u=function(e){throw new i(e)},c="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",l=/[\t\n\f\r ]/g,d={encode:function(e){e=String(e),/[^\0-\xFF]/.test(e)&&u("The string to be encoded contains characters outside of the Latin1 range.");for(var t,s,r,n,o=e.length%3,a="",i=-1,l=e.length-o;++i<l;)t=e.charCodeAt(i)<<16,s=e.charCodeAt(++i)<<8,r=e.charCodeAt(++i),a+=c.charAt((n=t+s+r)>>18&63)+c.charAt(n>>12&63)+c.charAt(n>>6&63)+c.charAt(63&n);return 2==o?(t=e.charCodeAt(i)<<8,s=e.charCodeAt(++i),a+=c.charAt((n=t+s)>>10)+c.charAt(n>>4&63)+c.charAt(n<<2&63)+"="):1==o&&(n=e.charCodeAt(i),a+=c.charAt(n>>2)+c.charAt(n<<4&63)+"=="),a},decode:function(e){var t=(e=String(e).replace(l,"")).length;t%4==0&&(t=(e=e.replace(/==?$/,"")).length),(t%4==1||/[^+a-zA-Z0-9/]/.test(e))&&u("Invalid character: the string to be decoded is not correctly encoded.");for(var s,r,n=0,o="",a=-1;++a<t;)r=c.indexOf(e.charAt(a)),s=n%4?64*s+r:r,n++%4&&(o+=String.fromCharCode(255&s>>(-2*n&6)));return o},version:"1.0.0"};if(s&&!s.nodeType)if(r)r.exports=d;else for(var h in d)d.hasOwnProperty(h)&&(s[h]=d[h]);else t.base64=d}(r.exports)),r.exports),u={exports:{}},c=u.exports;var l=(a||(a=1,function(e){var t,s;t=c,s=function(){return function(){return function(e){var t=[];if(0===e.length)return"";if("string"!=typeof e[0])throw new TypeError("Url must be a string. Received "+e[0]);if(e[0].match(/^[^/:]+:\/*$/)&&e.length>1){var s=e.shift();e[0]=s+e[0]}e[0].match(/^file:\/\/\//)?e[0]=e[0].replace(/^([^/:]+):\/*/,"$1:///"):e[0]=e[0].replace(/^([^/:]+):\/*/,"$1://");for(var r=0;r<e.length;r++){var n=e[r];if("string"!=typeof n)throw new TypeError("Url must be a string. Received "+n);""!==n&&(r>0&&(n=n.replace(/^[\/]+/,"")),n=r<e.length-1?n.replace(/[\/]+$/,""):n.replace(/[\/]+$/,"/"),t.push(n))}var o=t.join("/"),a=(o=o.replace(/\/(\?|&|#[^!])/g,"$1")).split("?");return a.shift()+(a.length>0?"?":"")+a.join("&")}("object"==typeof arguments[0]?arguments[0]:[].slice.call(arguments))}},e.exports?e.exports=s():t.urljoin=s()}(u)),u.exports),d=t(l);function h(e,t){return function(){return e.apply(t,arguments)}}const{toString:p}=Object.prototype,{getPrototypeOf:f}=Object,m=(g=Object.create(null),e=>{const t=p.call(e);return g[t]||(g[t]=t.slice(8,-1).toLowerCase())});var g;const y=e=>(e=e.toLowerCase(),t=>m(t)===e),b=e=>t=>typeof t===e,{isArray:w}=Array,v=b("undefined");const _=y("ArrayBuffer");const R=b("string"),q=b("function"),T=b("number"),S=e=>null!==e&&"object"==typeof e,D=e=>{if("object"!==m(e))return!1;const t=f(e);return!(null!==t&&t!==Object.prototype&&null!==Object.getPrototypeOf(t)||Symbol.toStringTag in e||Symbol.iterator in e)},E=y("Date"),O=y("File"),A=y("Blob"),x=y("FileList"),k=y("URLSearchParams"),[C,F,P,B]=["ReadableStream","Request","Response","Headers"].map(y);function L(e,t,{allOwnKeys:s=!1}={}){if(null==e)return;let r,n;if("object"!=typeof e&&(e=[e]),w(e))for(r=0,n=e.length;r<n;r++)t.call(null,e[r],r,e);else{const n=s?Object.getOwnPropertyNames(e):Object.keys(e),o=n.length;let a;for(r=0;r<o;r++)a=n[r],t.call(null,e[a],a,e)}}function U(e,t){t=t.toLowerCase();const s=Object.keys(e);let r,n=s.length;for(;n-- >0;)if(r=s[n],t===r.toLowerCase())return r;return null}const j="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:"undefined"!=typeof window?window:global,$=e=>!v(e)&&e!==j;const N=(I="undefined"!=typeof Uint8Array&&f(Uint8Array),e=>I&&e instanceof I);var I;const M=y("HTMLFormElement"),W=(({hasOwnProperty:e})=>(t,s)=>e.call(t,s))(Object.prototype),H=y("RegExp"),V=(e,t)=>{const s=Object.getOwnPropertyDescriptors(e),r={};L(s,((s,n)=>{let o;!1!==(o=t(s,n,e))&&(r[n]=o||s)})),Object.defineProperties(e,r)},z="abcdefghijklmnopqrstuvwxyz",J="0123456789",K={DIGIT:J,ALPHA:z,ALPHA_DIGIT:z+z.toUpperCase()+J};const Q=y("AsyncFunction"),X=(G="function"==typeof setImmediate,Y=q(j.postMessage),G?setImmediate:Y?(Z=`axios@${Math.random()}`,ee=[],j.addEventListener("message",(({source:e,data:t})=>{e===j&&t===Z&&ee.length&&ee.shift()()}),!1),e=>{ee.push(e),j.postMessage(Z,"*")}):e=>setTimeout(e));var G,Y,Z,ee;const te="undefined"!=typeof queueMicrotask?queueMicrotask.bind(j):"undefined"!=typeof process&&process.nextTick||X;var se={isArray:w,isArrayBuffer:_,isBuffer:function(e){return null!==e&&!v(e)&&null!==e.constructor&&!v(e.constructor)&&q(e.constructor.isBuffer)&&e.constructor.isBuffer(e)},isFormData:e=>{let t;return e&&("function"==typeof FormData&&e instanceof FormData||q(e.append)&&("formdata"===(t=m(e))||"object"===t&&q(e.toString)&&"[object FormData]"===e.toString()))},isArrayBufferView:function(e){let t;return t="undefined"!=typeof ArrayBuffer&&ArrayBuffer.isView?ArrayBuffer.isView(e):e&&e.buffer&&_(e.buffer),t},isString:R,isNumber:T,isBoolean:e=>!0===e||!1===e,isObject:S,isPlainObject:D,isReadableStream:C,isRequest:F,isResponse:P,isHeaders:B,isUndefined:v,isDate:E,isFile:O,isBlob:A,isRegExp:H,isFunction:q,isStream:e=>S(e)&&q(e.pipe),isURLSearchParams:k,isTypedArray:N,isFileList:x,forEach:L,merge:function e(){const{caseless:t}=$(this)&&this||{},s={},r=(r,n)=>{const o=t&&U(s,n)||n;D(s[o])&&D(r)?s[o]=e(s[o],r):D(r)?s[o]=e({},r):w(r)?s[o]=r.slice():s[o]=r};for(let e=0,t=arguments.length;e<t;e++)arguments[e]&&L(arguments[e],r);return s},extend:(e,t,s,{allOwnKeys:r}={})=>(L(t,((t,r)=>{s&&q(t)?e[r]=h(t,s):e[r]=t}),{allOwnKeys:r}),e),trim:e=>e.trim?e.trim():e.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,""),stripBOM:e=>(65279===e.charCodeAt(0)&&(e=e.slice(1)),e),inherits:(e,t,s,r)=>{e.prototype=Object.create(t.prototype,r),e.prototype.constructor=e,Object.defineProperty(e,"super",{value:t.prototype}),s&&Object.assign(e.prototype,s)},toFlatObject:(e,t,s,r)=>{let n,o,a;const i={};if(t=t||{},null==e)return t;do{for(n=Object.getOwnPropertyNames(e),o=n.length;o-- >0;)a=n[o],r&&!r(a,e,t)||i[a]||(t[a]=e[a],i[a]=!0);e=!1!==s&&f(e)}while(e&&(!s||s(e,t))&&e!==Object.prototype);return t},kindOf:m,kindOfTest:y,endsWith:(e,t,s)=>{e=String(e),(void 0===s||s>e.length)&&(s=e.length),s-=t.length;const r=e.indexOf(t,s);return-1!==r&&r===s},toArray:e=>{if(!e)return null;if(w(e))return e;let t=e.length;if(!T(t))return null;const s=new Array(t);for(;t-- >0;)s[t]=e[t];return s},forEachEntry:(e,t)=>{const s=(e&&e[Symbol.iterator]).call(e);let r;for(;(r=s.next())&&!r.done;){const s=r.value;t.call(e,s[0],s[1])}},matchAll:(e,t)=>{let s;const r=[];for(;null!==(s=e.exec(t));)r.push(s);return r},isHTMLForm:M,hasOwnProperty:W,hasOwnProp:W,reduceDescriptors:V,freezeMethods:e=>{V(e,((t,s)=>{if(q(e)&&-1!==["arguments","caller","callee"].indexOf(s))return!1;const r=e[s];q(r)&&(t.enumerable=!1,"writable"in t?t.writable=!1:t.set||(t.set=()=>{throw Error("Can not rewrite read-only method '"+s+"'")}))}))},toObjectSet:(e,t)=>{const s={},r=e=>{e.forEach((e=>{s[e]=!0}))};return w(e)?r(e):r(String(e).split(t)),s},toCamelCase:e=>e.toLowerCase().replace(/[-_\s]([a-z\d])(\w*)/g,(function(e,t,s){return t.toUpperCase()+s})),noop:()=>{},toFiniteNumber:(e,t)=>null!=e&&Number.isFinite(e=+e)?e:t,findKey:U,global:j,isContextDefined:$,ALPHABET:K,generateString:(e=16,t=K.ALPHA_DIGIT)=>{let s="";const{length:r}=t;for(;e--;)s+=t[Math.random()*r|0];return s},isSpecCompliantForm:function(e){return!!(e&&q(e.append)&&"FormData"===e[Symbol.toStringTag]&&e[Symbol.iterator])},toJSONObject:e=>{const t=new Array(10),s=(e,r)=>{if(S(e)){if(t.indexOf(e)>=0)return;if(!("toJSON"in e)){t[r]=e;const n=w(e)?[]:{};return L(e,((e,t)=>{const o=s(e,r+1);!v(o)&&(n[t]=o)})),t[r]=void 0,n}}return e};return s(e,0)},isAsyncFn:Q,isThenable:e=>e&&(S(e)||q(e))&&q(e.then)&&q(e.catch),setImmediate:X,asap:te};function re(e,t,s,r,n){Error.call(this),Error.captureStackTrace?Error.captureStackTrace(this,this.constructor):this.stack=(new Error).stack,this.message=e,this.name="AxiosError",t&&(this.code=t),s&&(this.config=s),r&&(this.request=r),n&&(this.response=n,this.status=n.status?n.status:null)}se.inherits(re,Error,{toJSON:function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:se.toJSONObject(this.config),code:this.code,status:this.status}}});const ne=re.prototype,oe={};["ERR_BAD_OPTION_VALUE","ERR_BAD_OPTION","ECONNABORTED","ETIMEDOUT","ERR_NETWORK","ERR_FR_TOO_MANY_REDIRECTS","ERR_DEPRECATED","ERR_BAD_RESPONSE","ERR_BAD_REQUEST","ERR_CANCELED","ERR_NOT_SUPPORT","ERR_INVALID_URL"].forEach((e=>{oe[e]={value:e}})),Object.defineProperties(re,oe),Object.defineProperty(ne,"isAxiosError",{value:!0}),re.from=(e,t,s,r,n,o)=>{const a=Object.create(ne);return se.toFlatObject(e,a,(function(e){return e!==Error.prototype}),(e=>"isAxiosError"!==e)),re.call(a,e.message,t,s,r,n),a.cause=e,a.name=e.name,o&&Object.assign(a,o),a};function ae(e){return se.isPlainObject(e)||se.isArray(e)}function ie(e){return se.endsWith(e,"[]")?e.slice(0,-2):e}function ue(e,t,s){return e?e.concat(t).map((function(e,t){return e=ie(e),!s&&t?"["+e+"]":e})).join(s?".":""):t}const ce=se.toFlatObject(se,{},null,(function(e){return/^is[A-Z]/.test(e)}));function le(e,t,s){if(!se.isObject(e))throw new TypeError("target must be an object");t=t||new FormData;const r=(s=se.toFlatObject(s,{metaTokens:!0,dots:!1,indexes:!1},!1,(function(e,t){return!se.isUndefined(t[e])}))).metaTokens,n=s.visitor||c,o=s.dots,a=s.indexes,i=(s.Blob||"undefined"!=typeof Blob&&Blob)&&se.isSpecCompliantForm(t);if(!se.isFunction(n))throw new TypeError("visitor must be a function");function u(e){if(null===e)return"";if(se.isDate(e))return e.toISOString();if(!i&&se.isBlob(e))throw new re("Blob is not supported. Use a Buffer instead.");return se.isArrayBuffer(e)||se.isTypedArray(e)?i&&"function"==typeof Blob?new Blob([e]):Buffer.from(e):e}function c(e,s,n){let i=e;if(e&&!n&&"object"==typeof e)if(se.endsWith(s,"{}"))s=r?s:s.slice(0,-2),e=JSON.stringify(e);else if(se.isArray(e)&&function(e){return se.isArray(e)&&!e.some(ae)}(e)||(se.isFileList(e)||se.endsWith(s,"[]"))&&(i=se.toArray(e)))return s=ie(s),i.forEach((function(e,r){!se.isUndefined(e)&&null!==e&&t.append(!0===a?ue([s],r,o):null===a?s:s+"[]",u(e))})),!1;return!!ae(e)||(t.append(ue(n,s,o),u(e)),!1)}const l=[],d=Object.assign(ce,{defaultVisitor:c,convertValue:u,isVisitable:ae});if(!se.isObject(e))throw new TypeError("data must be an object");return function e(s,r){if(!se.isUndefined(s)){if(-1!==l.indexOf(s))throw Error("Circular reference detected in "+r.join("."));l.push(s),se.forEach(s,(function(s,o){!0===(!(se.isUndefined(s)||null===s)&&n.call(t,s,se.isString(o)?o.trim():o,r,d))&&e(s,r?r.concat(o):[o])})),l.pop()}}(e),t}function de(e){const t={"!":"%21","'":"%27","(":"%28",")":"%29","~":"%7E","%20":"+","%00":"\0"};return encodeURIComponent(e).replace(/[!'()~]|%20|%00/g,(function(e){return t[e]}))}function he(e,t){this._pairs=[],e&&le(e,this,t)}const pe=he.prototype;function fe(e){return encodeURIComponent(e).replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}function me(e,t,s){if(!t)return e;const r=s&&s.encode||fe;se.isFunction(s)&&(s={serialize:s});const n=s&&s.serialize;let o;if(o=n?n(t,s):se.isURLSearchParams(t)?t.toString():new he(t,s).toString(r),o){const t=e.indexOf("#");-1!==t&&(e=e.slice(0,t)),e+=(-1===e.indexOf("?")?"?":"&")+o}return e}pe.append=function(e,t){this._pairs.push([e,t])},pe.toString=function(e){const t=e?function(t){return e.call(this,t,de)}:de;return this._pairs.map((function(e){return t(e[0])+"="+t(e[1])}),"").join("&")};class ge{constructor(){this.handlers=[]}use(e,t,s){return this.handlers.push({fulfilled:e,rejected:t,synchronous:!!s&&s.synchronous,runWhen:s?s.runWhen:null}),this.handlers.length-1}eject(e){this.handlers[e]&&(this.handlers[e]=null)}clear(){this.handlers&&(this.handlers=[])}forEach(e){se.forEach(this.handlers,(function(t){null!==t&&e(t)}))}}var ye={silentJSONParsing:!0,forcedJSONParsing:!0,clarifyTimeoutError:!1},be={isBrowser:!0,classes:{URLSearchParams:"undefined"!=typeof URLSearchParams?URLSearchParams:he,FormData:"undefined"!=typeof FormData?FormData:null,Blob:"undefined"!=typeof Blob?Blob:null},protocols:["http","https","file","blob","url","data"]};const we="undefined"!=typeof window&&"undefined"!=typeof document,ve="object"==typeof navigator&&navigator||void 0,_e=we&&(!ve||["ReactNative","NativeScript","NS"].indexOf(ve.product)<0),Re="undefined"!=typeof WorkerGlobalScope&&self instanceof WorkerGlobalScope&&"function"==typeof self.importScripts,qe=we&&window.location.href||"http://localhost";var Te={...Object.freeze({__proto__:null,hasBrowserEnv:we,hasStandardBrowserEnv:_e,hasStandardBrowserWebWorkerEnv:Re,navigator:ve,origin:qe}),...be};function Se(e){function t(e,s,r,n){let o=e[n++];if("__proto__"===o)return!0;const a=Number.isFinite(+o),i=n>=e.length;if(o=!o&&se.isArray(r)?r.length:o,i)return se.hasOwnProp(r,o)?r[o]=[r[o],s]:r[o]=s,!a;r[o]&&se.isObject(r[o])||(r[o]=[]);return t(e,s,r[o],n)&&se.isArray(r[o])&&(r[o]=function(e){const t={},s=Object.keys(e);let r;const n=s.length;let o;for(r=0;r<n;r++)o=s[r],t[o]=e[o];return t}(r[o])),!a}if(se.isFormData(e)&&se.isFunction(e.entries)){const s={};return se.forEachEntry(e,((e,r)=>{t(function(e){return se.matchAll(/\w+|\[(\w*)]/g,e).map((e=>"[]"===e[0]?"":e[1]||e[0]))}(e),r,s,0)})),s}return null}const De={transitional:ye,adapter:["xhr","http","fetch"],transformRequest:[function(e,t){const s=t.getContentType()||"",r=s.indexOf("application/json")>-1,n=se.isObject(e);n&&se.isHTMLForm(e)&&(e=new FormData(e));if(se.isFormData(e))return r?JSON.stringify(Se(e)):e;if(se.isArrayBuffer(e)||se.isBuffer(e)||se.isStream(e)||se.isFile(e)||se.isBlob(e)||se.isReadableStream(e))return e;if(se.isArrayBufferView(e))return e.buffer;if(se.isURLSearchParams(e))return t.setContentType("application/x-www-form-urlencoded;charset=utf-8",!1),e.toString();let o;if(n){if(s.indexOf("application/x-www-form-urlencoded")>-1)return function(e,t){return le(e,new Te.classes.URLSearchParams,Object.assign({visitor:function(e,t,s,r){return Te.isNode&&se.isBuffer(e)?(this.append(t,e.toString("base64")),!1):r.defaultVisitor.apply(this,arguments)}},t))}(e,this.formSerializer).toString();if((o=se.isFileList(e))||s.indexOf("multipart/form-data")>-1){const t=this.env&&this.env.FormData;return le(o?{"files[]":e}:e,t&&new t,this.formSerializer)}}return n||r?(t.setContentType("application/json",!1),function(e,t,s){if(se.isString(e))try{return(t||JSON.parse)(e),se.trim(e)}catch(e){if("SyntaxError"!==e.name)throw e}return(s||JSON.stringify)(e)}(e)):e}],transformResponse:[function(e){const t=this.transitional||De.transitional,s=t&&t.forcedJSONParsing,r="json"===this.responseType;if(se.isResponse(e)||se.isReadableStream(e))return e;if(e&&se.isString(e)&&(s&&!this.responseType||r)){const s=!(t&&t.silentJSONParsing)&&r;try{return JSON.parse(e)}catch(e){if(s){if("SyntaxError"===e.name)throw re.from(e,re.ERR_BAD_RESPONSE,this,null,this.response);throw e}}}return e}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,maxBodyLength:-1,env:{FormData:Te.classes.FormData,Blob:Te.classes.Blob},validateStatus:function(e){return e>=200&&e<300},headers:{common:{Accept:"application/json, text/plain, */*","Content-Type":void 0}}};se.forEach(["delete","get","head","post","put","patch"],(e=>{De.headers[e]={}}));const Ee=se.toObjectSet(["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"]);const Oe=Symbol("internals");function Ae(e){return e&&String(e).trim().toLowerCase()}function xe(e){return!1===e||null==e?e:se.isArray(e)?e.map(xe):String(e)}function ke(e,t,s,r,n){return se.isFunction(r)?r.call(this,t,s):(n&&(t=s),se.isString(t)?se.isString(r)?-1!==t.indexOf(r):se.isRegExp(r)?r.test(t):void 0:void 0)}let Ce=class{constructor(e){e&&this.set(e)}set(e,t,s){const r=this;function n(e,t,s){const n=Ae(t);if(!n)throw new Error("header name must be a non-empty string");const o=se.findKey(r,n);(!o||void 0===r[o]||!0===s||void 0===s&&!1!==r[o])&&(r[o||t]=xe(e))}const o=(e,t)=>se.forEach(e,((e,s)=>n(e,s,t)));if(se.isPlainObject(e)||e instanceof this.constructor)o(e,t);else if(se.isString(e)&&(e=e.trim())&&!/^[-_a-zA-Z0-9^`|~,!#$%&'*+.]+$/.test(e.trim()))o((e=>{const t={};let s,r,n;return e&&e.split("\n").forEach((function(e){n=e.indexOf(":"),s=e.substring(0,n).trim().toLowerCase(),r=e.substring(n+1).trim(),!s||t[s]&&Ee[s]||("set-cookie"===s?t[s]?t[s].push(r):t[s]=[r]:t[s]=t[s]?t[s]+", "+r:r)})),t})(e),t);else if(se.isHeaders(e))for(const[t,r]of e.entries())n(r,t,s);else null!=e&&n(t,e,s);return this}get(e,t){if(e=Ae(e)){const s=se.findKey(this,e);if(s){const e=this[s];if(!t)return e;if(!0===t)return function(e){const t=Object.create(null),s=/([^\s,;=]+)\s*(?:=\s*([^,;]+))?/g;let r;for(;r=s.exec(e);)t[r[1]]=r[2];return t}(e);if(se.isFunction(t))return t.call(this,e,s);if(se.isRegExp(t))return t.exec(e);throw new TypeError("parser must be boolean|regexp|function")}}}has(e,t){if(e=Ae(e)){const s=se.findKey(this,e);return!(!s||void 0===this[s]||t&&!ke(0,this[s],s,t))}return!1}delete(e,t){const s=this;let r=!1;function n(e){if(e=Ae(e)){const n=se.findKey(s,e);!n||t&&!ke(0,s[n],n,t)||(delete s[n],r=!0)}}return se.isArray(e)?e.forEach(n):n(e),r}clear(e){const t=Object.keys(this);let s=t.length,r=!1;for(;s--;){const n=t[s];e&&!ke(0,this[n],n,e,!0)||(delete this[n],r=!0)}return r}normalize(e){const t=this,s={};return se.forEach(this,((r,n)=>{const o=se.findKey(s,n);if(o)return t[o]=xe(r),void delete t[n];const a=e?function(e){return e.trim().toLowerCase().replace(/([a-z\d])(\w*)/g,((e,t,s)=>t.toUpperCase()+s))}(n):String(n).trim();a!==n&&delete t[n],t[a]=xe(r),s[a]=!0})),this}concat(...e){return this.constructor.concat(this,...e)}toJSON(e){const t=Object.create(null);return se.forEach(this,((s,r)=>{null!=s&&!1!==s&&(t[r]=e&&se.isArray(s)?s.join(", "):s)})),t}[Symbol.iterator](){return Object.entries(this.toJSON())[Symbol.iterator]()}toString(){return Object.entries(this.toJSON()).map((([e,t])=>e+": "+t)).join("\n")}get[Symbol.toStringTag](){return"AxiosHeaders"}static from(e){return e instanceof this?e:new this(e)}static concat(e,...t){const s=new this(e);return t.forEach((e=>s.set(e))),s}static accessor(e){const t=(this[Oe]=this[Oe]={accessors:{}}).accessors,s=this.prototype;function r(e){const r=Ae(e);t[r]||(!function(e,t){const s=se.toCamelCase(" "+t);["get","set","has"].forEach((r=>{Object.defineProperty(e,r+s,{value:function(e,s,n){return this[r].call(this,t,e,s,n)},configurable:!0})}))}(s,e),t[r]=!0)}return se.isArray(e)?e.forEach(r):r(e),this}};function Fe(e,t){const s=this||De,r=t||s,n=Ce.from(r.headers);let o=r.data;return se.forEach(e,(function(e){o=e.call(s,o,n.normalize(),t?t.status:void 0)})),n.normalize(),o}function Pe(e){return!(!e||!e.__CANCEL__)}function Be(e,t,s){re.call(this,null==e?"canceled":e,re.ERR_CANCELED,t,s),this.name="CanceledError"}function Le(e,t,s){const r=s.config.validateStatus;s.status&&r&&!r(s.status)?t(new re("Request failed with status code "+s.status,[re.ERR_BAD_REQUEST,re.ERR_BAD_RESPONSE][Math.floor(s.status/100)-4],s.config,s.request,s)):e(s)}Ce.accessor(["Content-Type","Content-Length","Accept","Accept-Encoding","User-Agent","Authorization"]),se.reduceDescriptors(Ce.prototype,(({value:e},t)=>{let s=t[0].toUpperCase()+t.slice(1);return{get:()=>e,set(e){this[s]=e}}})),se.freezeMethods(Ce),se.inherits(Be,re,{__CANCEL__:!0});const Ue=(e,t,s=3)=>{let r=0;const n=function(e,t){e=e||10;const s=new Array(e),r=new Array(e);let n,o=0,a=0;return t=void 0!==t?t:1e3,function(i){const u=Date.now(),c=r[a];n||(n=u),s[o]=i,r[o]=u;let l=a,d=0;for(;l!==o;)d+=s[l++],l%=e;if(o=(o+1)%e,o===a&&(a=(a+1)%e),u-n<t)return;const h=c&&u-c;return h?Math.round(1e3*d/h):void 0}}(50,250);return function(e,t){let s,r,n=0,o=1e3/t;const a=(t,o=Date.now())=>{n=o,s=null,r&&(clearTimeout(r),r=null),e.apply(null,t)};return[(...e)=>{const t=Date.now(),i=t-n;i>=o?a(e,t):(s=e,r||(r=setTimeout((()=>{r=null,a(s)}),o-i)))},()=>s&&a(s)]}((s=>{const o=s.loaded,a=s.lengthComputable?s.total:void 0,i=o-r,u=n(i);r=o;e({loaded:o,total:a,progress:a?o/a:void 0,bytes:i,rate:u||void 0,estimated:u&&a&&o<=a?(a-o)/u:void 0,event:s,lengthComputable:null!=a,[t?"download":"upload"]:!0})}),s)},je=(e,t)=>{const s=null!=e;return[r=>t[0]({lengthComputable:s,total:e,loaded:r}),t[1]]},$e=e=>(...t)=>se.asap((()=>e(...t)));var Ne=Te.hasStandardBrowserEnv?((e,t)=>s=>(s=new URL(s,Te.origin),e.protocol===s.protocol&&e.host===s.host&&(t||e.port===s.port)))(new URL(Te.origin),Te.navigator&&/(msie|trident)/i.test(Te.navigator.userAgent)):()=>!0,Ie=Te.hasStandardBrowserEnv?{write(e,t,s,r,n,o){const a=[e+"="+encodeURIComponent(t)];se.isNumber(s)&&a.push("expires="+new Date(s).toGMTString()),se.isString(r)&&a.push("path="+r),se.isString(n)&&a.push("domain="+n),!0===o&&a.push("secure"),document.cookie=a.join("; ")},read(e){const t=document.cookie.match(new RegExp("(^|;\\s*)("+e+")=([^;]*)"));return t?decodeURIComponent(t[3]):null},remove(e){this.write(e,"",Date.now()-864e5)}}:{write(){},read:()=>null,remove(){}};function Me(e,t){return e&&!/^([a-z][a-z\d+\-.]*:)?\/\//i.test(t)?function(e,t){return t?e.replace(/\/?\/$/,"")+"/"+t.replace(/^\/+/,""):e}(e,t):t}const We=e=>e instanceof Ce?{...e}:e;function He(e,t){t=t||{};const s={};function r(e,t,s,r){return se.isPlainObject(e)&&se.isPlainObject(t)?se.merge.call({caseless:r},e,t):se.isPlainObject(t)?se.merge({},t):se.isArray(t)?t.slice():t}function n(e,t,s,n){return se.isUndefined(t)?se.isUndefined(e)?void 0:r(void 0,e,0,n):r(e,t,0,n)}function o(e,t){if(!se.isUndefined(t))return r(void 0,t)}function a(e,t){return se.isUndefined(t)?se.isUndefined(e)?void 0:r(void 0,e):r(void 0,t)}function i(s,n,o){return o in t?r(s,n):o in e?r(void 0,s):void 0}const u={url:o,method:o,data:o,baseURL:a,transformRequest:a,transformResponse:a,paramsSerializer:a,timeout:a,timeoutMessage:a,withCredentials:a,withXSRFToken:a,adapter:a,responseType:a,xsrfCookieName:a,xsrfHeaderName:a,onUploadProgress:a,onDownloadProgress:a,decompress:a,maxContentLength:a,maxBodyLength:a,beforeRedirect:a,transport:a,httpAgent:a,httpsAgent:a,cancelToken:a,socketPath:a,responseEncoding:a,validateStatus:i,headers:(e,t,s)=>n(We(e),We(t),0,!0)};return se.forEach(Object.keys(Object.assign({},e,t)),(function(r){const o=u[r]||n,a=o(e[r],t[r],r);se.isUndefined(a)&&o!==i||(s[r]=a)})),s}var Ve=e=>{const t=He({},e);let s,{data:r,withXSRFToken:n,xsrfHeaderName:o,xsrfCookieName:a,headers:i,auth:u}=t;if(t.headers=i=Ce.from(i),t.url=me(Me(t.baseURL,t.url),e.params,e.paramsSerializer),u&&i.set("Authorization","Basic "+btoa((u.username||"")+":"+(u.password?unescape(encodeURIComponent(u.password)):""))),se.isFormData(r))if(Te.hasStandardBrowserEnv||Te.hasStandardBrowserWebWorkerEnv)i.setContentType(void 0);else if(!1!==(s=i.getContentType())){const[e,...t]=s?s.split(";").map((e=>e.trim())).filter(Boolean):[];i.setContentType([e||"multipart/form-data",...t].join("; "))}if(Te.hasStandardBrowserEnv&&(n&&se.isFunction(n)&&(n=n(t)),n||!1!==n&&Ne(t.url))){const e=o&&a&&Ie.read(a);e&&i.set(o,e)}return t};var ze="undefined"!=typeof XMLHttpRequest&&function(e){return new Promise((function(t,s){const r=Ve(e);let n=r.data;const o=Ce.from(r.headers).normalize();let a,i,u,c,l,{responseType:d,onUploadProgress:h,onDownloadProgress:p}=r;function f(){c&&c(),l&&l(),r.cancelToken&&r.cancelToken.unsubscribe(a),r.signal&&r.signal.removeEventListener("abort",a)}let m=new XMLHttpRequest;function g(){if(!m)return;const r=Ce.from("getAllResponseHeaders"in m&&m.getAllResponseHeaders());Le((function(e){t(e),f()}),(function(e){s(e),f()}),{data:d&&"text"!==d&&"json"!==d?m.response:m.responseText,status:m.status,statusText:m.statusText,headers:r,config:e,request:m}),m=null}m.open(r.method.toUpperCase(),r.url,!0),m.timeout=r.timeout,"onloadend"in m?m.onloadend=g:m.onreadystatechange=function(){m&&4===m.readyState&&(0!==m.status||m.responseURL&&0===m.responseURL.indexOf("file:"))&&setTimeout(g)},m.onabort=function(){m&&(s(new re("Request aborted",re.ECONNABORTED,e,m)),m=null)},m.onerror=function(){s(new re("Network Error",re.ERR_NETWORK,e,m)),m=null},m.ontimeout=function(){let t=r.timeout?"timeout of "+r.timeout+"ms exceeded":"timeout exceeded";const n=r.transitional||ye;r.timeoutErrorMessage&&(t=r.timeoutErrorMessage),s(new re(t,n.clarifyTimeoutError?re.ETIMEDOUT:re.ECONNABORTED,e,m)),m=null},void 0===n&&o.setContentType(null),"setRequestHeader"in m&&se.forEach(o.toJSON(),(function(e,t){m.setRequestHeader(t,e)})),se.isUndefined(r.withCredentials)||(m.withCredentials=!!r.withCredentials),d&&"json"!==d&&(m.responseType=r.responseType),p&&([u,l]=Ue(p,!0),m.addEventListener("progress",u)),h&&m.upload&&([i,c]=Ue(h),m.upload.addEventListener("progress",i),m.upload.addEventListener("loadend",c)),(r.cancelToken||r.signal)&&(a=t=>{m&&(s(!t||t.type?new Be(null,e,m):t),m.abort(),m=null)},r.cancelToken&&r.cancelToken.subscribe(a),r.signal&&(r.signal.aborted?a():r.signal.addEventListener("abort",a)));const y=function(e){const t=/^([-+\w]{1,25})(:?\/\/|:)/.exec(e);return t&&t[1]||""}(r.url);y&&-1===Te.protocols.indexOf(y)?s(new re("Unsupported protocol "+y+":",re.ERR_BAD_REQUEST,e)):m.send(n||null)}))};const Je=(e,t)=>{const{length:s}=e=e?e.filter(Boolean):[];if(t||s){let s,r=new AbortController;const n=function(e){if(!s){s=!0,a();const t=e instanceof Error?e:this.reason;r.abort(t instanceof re?t:new Be(t instanceof Error?t.message:t))}};let o=t&&setTimeout((()=>{o=null,n(new re(`timeout ${t} of ms exceeded`,re.ETIMEDOUT))}),t);const a=()=>{e&&(o&&clearTimeout(o),o=null,e.forEach((e=>{e.unsubscribe?e.unsubscribe(n):e.removeEventListener("abort",n)})),e=null)};e.forEach((e=>e.addEventListener("abort",n)));const{signal:i}=r;return i.unsubscribe=()=>se.asap(a),i}},Ke=function*(e,t){let s=e.byteLength;if(s<t)return void(yield e);let r,n=0;for(;n<s;)r=n+t,yield e.slice(n,r),n=r},Qe=async function*(e){if(e[Symbol.asyncIterator])return void(yield*e);const t=e.getReader();try{for(;;){const{done:e,value:s}=await t.read();if(e)break;yield s}}finally{await t.cancel()}},Xe=(e,t,s,r)=>{const n=async function*(e,t){for await(const s of Qe(e))yield*Ke(s,t)}(e,t);let o,a=0,i=e=>{o||(o=!0,r&&r(e))};return new ReadableStream({async pull(e){try{const{done:t,value:r}=await n.next();if(t)return i(),void e.close();let o=r.byteLength;if(s){let e=a+=o;s(e)}e.enqueue(new Uint8Array(r))}catch(e){throw i(e),e}},cancel:e=>(i(e),n.return())},{highWaterMark:2})},Ge="function"==typeof fetch&&"function"==typeof Request&&"function"==typeof Response,Ye=Ge&&"function"==typeof ReadableStream,Ze=Ge&&("function"==typeof TextEncoder?(et=new TextEncoder,e=>et.encode(e)):async e=>new Uint8Array(await new Response(e).arrayBuffer()));var et;const tt=(e,...t)=>{try{return!!e(...t)}catch(e){return!1}},st=Ye&&tt((()=>{let e=!1;const t=new Request(Te.origin,{body:new ReadableStream,method:"POST",get duplex(){return e=!0,"half"}}).headers.has("Content-Type");return e&&!t})),rt=Ye&&tt((()=>se.isReadableStream(new Response("").body))),nt={stream:rt&&(e=>e.body)};var ot;Ge&&(ot=new Response,["text","arrayBuffer","blob","formData","stream"].forEach((e=>{!nt[e]&&(nt[e]=se.isFunction(ot[e])?t=>t[e]():(t,s)=>{throw new re(`Response type '${e}' is not supported`,re.ERR_NOT_SUPPORT,s)})})));const at=async(e,t)=>{const s=se.toFiniteNumber(e.getContentLength());return null==s?(async e=>{if(null==e)return 0;if(se.isBlob(e))return e.size;if(se.isSpecCompliantForm(e)){const t=new Request(Te.origin,{method:"POST",body:e});return(await t.arrayBuffer()).byteLength}return se.isArrayBufferView(e)||se.isArrayBuffer(e)?e.byteLength:(se.isURLSearchParams(e)&&(e+=""),se.isString(e)?(await Ze(e)).byteLength:void 0)})(t):s};const it={http:null,xhr:ze,fetch:Ge&&(async e=>{let{url:t,method:s,data:r,signal:n,cancelToken:o,timeout:a,onDownloadProgress:i,onUploadProgress:u,responseType:c,headers:l,withCredentials:d="same-origin",fetchOptions:h}=Ve(e);c=c?(c+"").toLowerCase():"text";let p,f=Je([n,o&&o.toAbortSignal()],a);const m=f&&f.unsubscribe&&(()=>{f.unsubscribe()});let g;try{if(u&&st&&"get"!==s&&"head"!==s&&0!==(g=await at(l,r))){let e,s=new Request(t,{method:"POST",body:r,duplex:"half"});if(se.isFormData(r)&&(e=s.headers.get("content-type"))&&l.setContentType(e),s.body){const[e,t]=je(g,Ue($e(u)));r=Xe(s.body,65536,e,t)}}se.isString(d)||(d=d?"include":"omit");const n="credentials"in Request.prototype;p=new Request(t,{...h,signal:f,method:s.toUpperCase(),headers:l.normalize().toJSON(),body:r,duplex:"half",credentials:n?d:void 0});let o=await fetch(p);const a=rt&&("stream"===c||"response"===c);if(rt&&(i||a&&m)){const e={};["status","statusText","headers"].forEach((t=>{e[t]=o[t]}));const t=se.toFiniteNumber(o.headers.get("content-length")),[s,r]=i&&je(t,Ue($e(i),!0))||[];o=new Response(Xe(o.body,65536,s,(()=>{r&&r(),m&&m()})),e)}c=c||"text";let y=await nt[se.findKey(nt,c)||"text"](o,e);return!a&&m&&m(),await new Promise(((t,s)=>{Le(t,s,{data:y,headers:Ce.from(o.headers),status:o.status,statusText:o.statusText,config:e,request:p})}))}catch(t){if(m&&m(),t&&"TypeError"===t.name&&/fetch/i.test(t.message))throw Object.assign(new re("Network Error",re.ERR_NETWORK,e,p),{cause:t.cause||t});throw re.from(t,t&&t.code,e,p)}})};se.forEach(it,((e,t)=>{if(e){try{Object.defineProperty(e,"name",{value:t})}catch(e){}Object.defineProperty(e,"adapterName",{value:t})}}));const ut=e=>`- ${e}`,ct=e=>se.isFunction(e)||null===e||!1===e;var lt=e=>{e=se.isArray(e)?e:[e];const{length:t}=e;let s,r;const n={};for(let o=0;o<t;o++){let t;if(s=e[o],r=s,!ct(s)&&(r=it[(t=String(s)).toLowerCase()],void 0===r))throw new re(`Unknown adapter '${t}'`);if(r)break;n[t||"#"+o]=r}if(!r){const e=Object.entries(n).map((([e,t])=>`adapter ${e} `+(!1===t?"is not supported by the environment":"is not available in the build")));throw new re("There is no suitable adapter to dispatch the request "+(t?e.length>1?"since :\n"+e.map(ut).join("\n"):" "+ut(e[0]):"as no adapter specified"),"ERR_NOT_SUPPORT")}return r};function dt(e){if(e.cancelToken&&e.cancelToken.throwIfRequested(),e.signal&&e.signal.aborted)throw new Be(null,e)}function ht(e){dt(e),e.headers=Ce.from(e.headers),e.data=Fe.call(e,e.transformRequest),-1!==["post","put","patch"].indexOf(e.method)&&e.headers.setContentType("application/x-www-form-urlencoded",!1);return lt(e.adapter||De.adapter)(e).then((function(t){return dt(e),t.data=Fe.call(e,e.transformResponse,t),t.headers=Ce.from(t.headers),t}),(function(t){return Pe(t)||(dt(e),t&&t.response&&(t.response.data=Fe.call(e,e.transformResponse,t.response),t.response.headers=Ce.from(t.response.headers))),Promise.reject(t)}))}const pt="1.7.9",ft={};["object","boolean","number","function","string","symbol"].forEach(((e,t)=>{ft[e]=function(s){return typeof s===e||"a"+(t<1?"n ":" ")+e}}));const mt={};ft.transitional=function(e,t,s){function r(e,t){return"[Axios v1.7.9] Transitional option '"+e+"'"+t+(s?". "+s:"")}return(s,n,o)=>{if(!1===e)throw new re(r(n," has been removed"+(t?" in "+t:"")),re.ERR_DEPRECATED);return t&&!mt[n]&&(mt[n]=!0,console.warn(r(n," has been deprecated since v"+t+" and will be removed in the near future"))),!e||e(s,n,o)}},ft.spelling=function(e){return(t,s)=>(console.warn(`${s} is likely a misspelling of ${e}`),!0)};var gt={assertOptions:function(e,t,s){if("object"!=typeof e)throw new re("options must be an object",re.ERR_BAD_OPTION_VALUE);const r=Object.keys(e);let n=r.length;for(;n-- >0;){const o=r[n],a=t[o];if(a){const t=e[o],s=void 0===t||a(t,o,e);if(!0!==s)throw new re("option "+o+" must be "+s,re.ERR_BAD_OPTION_VALUE)}else if(!0!==s)throw new re("Unknown option "+o,re.ERR_BAD_OPTION)}},validators:ft};const yt=gt.validators;let bt=class{constructor(e){this.defaults=e,this.interceptors={request:new ge,response:new ge}}async request(e,t){try{return await this._request(e,t)}catch(e){if(e instanceof Error){let t={};Error.captureStackTrace?Error.captureStackTrace(t):t=new Error;const s=t.stack?t.stack.replace(/^.+\n/,""):"";try{e.stack?s&&!String(e.stack).endsWith(s.replace(/^.+\n.+\n/,""))&&(e.stack+="\n"+s):e.stack=s}catch(e){}}throw e}}_request(e,t){"string"==typeof e?(t=t||{}).url=e:t=e||{},t=He(this.defaults,t);const{transitional:s,paramsSerializer:r,headers:n}=t;void 0!==s&&gt.assertOptions(s,{silentJSONParsing:yt.transitional(yt.boolean),forcedJSONParsing:yt.transitional(yt.boolean),clarifyTimeoutError:yt.transitional(yt.boolean)},!1),null!=r&&(se.isFunction(r)?t.paramsSerializer={serialize:r}:gt.assertOptions(r,{encode:yt.function,serialize:yt.function},!0)),gt.assertOptions(t,{baseUrl:yt.spelling("baseURL"),withXsrfToken:yt.spelling("withXSRFToken")},!0),t.method=(t.method||this.defaults.method||"get").toLowerCase();let o=n&&se.merge(n.common,n[t.method]);n&&se.forEach(["delete","get","head","post","put","patch","common"],(e=>{delete n[e]})),t.headers=Ce.concat(o,n);const a=[];let i=!0;this.interceptors.request.forEach((function(e){"function"==typeof e.runWhen&&!1===e.runWhen(t)||(i=i&&e.synchronous,a.unshift(e.fulfilled,e.rejected))}));const u=[];let c;this.interceptors.response.forEach((function(e){u.push(e.fulfilled,e.rejected)}));let l,d=0;if(!i){const e=[ht.bind(this),void 0];for(e.unshift.apply(e,a),e.push.apply(e,u),l=e.length,c=Promise.resolve(t);d<l;)c=c.then(e[d++],e[d++]);return c}l=a.length;let h=t;for(d=0;d<l;){const e=a[d++],t=a[d++];try{h=e(h)}catch(e){t.call(this,e);break}}try{c=ht.call(this,h)}catch(e){return Promise.reject(e)}for(d=0,l=u.length;d<l;)c=c.then(u[d++],u[d++]);return c}getUri(e){return me(Me((e=He(this.defaults,e)).baseURL,e.url),e.params,e.paramsSerializer)}};se.forEach(["delete","get","head","options"],(function(e){bt.prototype[e]=function(t,s){return this.request(He(s||{},{method:e,url:t,data:(s||{}).data}))}})),se.forEach(["post","put","patch"],(function(e){function t(t){return function(s,r,n){return this.request(He(n||{},{method:e,headers:t?{"Content-Type":"multipart/form-data"}:{},url:s,data:r}))}}bt.prototype[e]=t(),bt.prototype[e+"Form"]=t(!0)}));const wt={Continue:100,SwitchingProtocols:101,Processing:102,EarlyHints:103,Ok:200,Created:201,Accepted:202,NonAuthoritativeInformation:203,NoContent:204,ResetContent:205,PartialContent:206,MultiStatus:207,AlreadyReported:208,ImUsed:226,MultipleChoices:300,MovedPermanently:301,Found:302,SeeOther:303,NotModified:304,UseProxy:305,Unused:306,TemporaryRedirect:307,PermanentRedirect:308,BadRequest:400,Unauthorized:401,PaymentRequired:402,Forbidden:403,NotFound:404,MethodNotAllowed:405,NotAcceptable:406,ProxyAuthenticationRequired:407,RequestTimeout:408,Conflict:409,Gone:410,LengthRequired:411,PreconditionFailed:412,PayloadTooLarge:413,UriTooLong:414,UnsupportedMediaType:415,RangeNotSatisfiable:416,ExpectationFailed:417,ImATeapot:418,MisdirectedRequest:421,UnprocessableEntity:422,Locked:423,FailedDependency:424,TooEarly:425,UpgradeRequired:426,PreconditionRequired:428,TooManyRequests:429,RequestHeaderFieldsTooLarge:431,UnavailableForLegalReasons:451,InternalServerError:500,NotImplemented:501,BadGateway:502,ServiceUnavailable:503,GatewayTimeout:504,HttpVersionNotSupported:505,VariantAlsoNegotiates:506,InsufficientStorage:507,LoopDetected:508,NotExtended:510,NetworkAuthenticationRequired:511};Object.entries(wt).forEach((([e,t])=>{wt[t]=e}));const vt=function e(t){const s=new bt(t),r=h(bt.prototype.request,s);return se.extend(r,bt.prototype,s,{allOwnKeys:!0}),se.extend(r,s,null,{allOwnKeys:!0}),r.create=function(s){return e(He(t,s))},r}(De);vt.Axios=bt,vt.CanceledError=Be,vt.CancelToken=class e{constructor(e){if("function"!=typeof e)throw new TypeError("executor must be a function.");let t;this.promise=new Promise((function(e){t=e}));const s=this;this.promise.then((e=>{if(!s._listeners)return;let t=s._listeners.length;for(;t-- >0;)s._listeners[t](e);s._listeners=null})),this.promise.then=e=>{let t;const r=new Promise((e=>{s.subscribe(e),t=e})).then(e);return r.cancel=function(){s.unsubscribe(t)},r},e((function(e,r,n){s.reason||(s.reason=new Be(e,r,n),t(s.reason))}))}throwIfRequested(){if(this.reason)throw this.reason}subscribe(e){this.reason?e(this.reason):this._listeners?this._listeners.push(e):this._listeners=[e]}unsubscribe(e){if(!this._listeners)return;const t=this._listeners.indexOf(e);-1!==t&&this._listeners.splice(t,1)}toAbortSignal(){const e=new AbortController,t=t=>{e.abort(t)};return this.subscribe(t),e.signal.unsubscribe=()=>this.unsubscribe(t),e.signal}static source(){let t;return{token:new e((function(e){t=e})),cancel:t}}},vt.isCancel=Pe,vt.VERSION=pt,vt.toFormData=le,vt.AxiosError=re,vt.Cancel=vt.CanceledError,vt.all=function(e){return Promise.all(e)},vt.spread=function(e){return function(t){return e.apply(null,t)}},vt.isAxiosError=function(e){return se.isObject(e)&&!0===e.isAxiosError},vt.mergeConfig=He,vt.AxiosHeaders=Ce,vt.formToJSON=e=>Se(se.isHTMLForm(e)?new FormData(e):e),vt.getAdapter=lt,vt.HttpStatusCode=wt,vt.default=vt;const{Axios:_t,AxiosError:Rt,CanceledError:qt,isCancel:Tt,CancelToken:St,VERSION:Dt,all:Et,Cancel:Ot,isAxiosError:At,spread:xt,toFormData:kt,AxiosHeaders:Ct,HttpStatusCode:Ft,formToJSON:Pt,getAdapter:Bt,mergeConfig:Lt}=vt;class Ut extends Error{status;stack;details;type;static getUserDataError(e,t){return new this({status:400,statusText:e,body:{message:t}})}constructor({status:e,statusText:t,message:s,body:r={}}){let n="",o="";"string"==typeof r?n=r:(n=r?.message||"",o=r?.error||""),super(),this.stack="",this.status=e,this.message=s||o||t||"",this.details=n,this.type="MailgunAPIError"}}class jt{_stream;size;constructor(e,t){this._stream=e,this.size=t}stream(){return this._stream}get[Symbol.toStringTag](){return"Blob"}}class $t{getAttachmentOptions(e){const{filename:t,contentType:s,knownLength:r}=e;return{...t?{filename:t}:{filename:"file"},...s&&{contentType:s},...r&&{knownLength:r}}}getFileInfo(e){const{name:t,type:s,size:r}=e;return this.getAttachmentOptions({filename:t,contentType:s,knownLength:r})}getCustomFileInfo(e){const{filename:t,contentType:s,knownLength:r}=e;return this.getAttachmentOptions({filename:t,contentType:s,knownLength:r})}getBufferInfo(e){const{byteLength:t}=e;return this.getAttachmentOptions({filename:"file",contentType:"",knownLength:t})}isStream(e){return"object"==typeof e&&"function"==typeof e.pipe}isCustomFile(e){return"object"==typeof e&&!!e.data}isBrowserFile(e){return"object"==typeof e&&(!!e.name||"undefined"!=typeof Blob&&e instanceof Blob)}isBuffer(e){return"undefined"!=typeof Buffer&&Buffer.isBuffer(e)}getAttachmentInfo(e){const t=this.isBrowserFile(e),s=this.isCustomFile(e);if(!("string"==typeof e)){if(t)return this.getFileInfo(e);if("undefined"!=typeof Buffer&&Buffer.isBuffer(e))return this.getBufferInfo(e);if(s)return this.getCustomFileInfo(e)}return{filename:"file",contentType:void 0,knownLength:void 0}}convertToFDexpectedShape(e){const t=this.isStream(e),s=this.isBrowserFile(e),r=this.isCustomFile(e);let n;if(t||"string"==typeof e||s||this.isBuffer(e))n=e;else{if(!r)throw Ut.getUserDataError("Unknown attachment type "+typeof e,'The "attachment" property expects either Buffer, Blob, or String.\n          Also, It is possible to provide an object that has the property "data" with a value that is equal to one of the types counted before.\n          Additionally, you may use an array to send more than one attachment.');n=e.data}return n}getBlobFromStream(e,t){return new jt(e,t)}}class Nt{FormDataConstructor;fileKeys;attachmentsHandler;constructor(e){this.FormDataConstructor=e,this.fileKeys=["attachment","inline","multipleValidationFile"],this.attachmentsHandler=new $t}createFormData(e){if(!e)throw new Error("Please provide data object");return Object.keys(e).filter((function(t){return e[t]})).reduce(((t,s)=>{if(this.fileKeys.includes(s)){const r=e[s];if(this.isMessageAttachment(r))return this.addFilesToFD(s,r,t),t;throw Ut.getUserDataError(`Unknown value ${e[s]} with type ${typeof e[s]} for property "${s}"`,`The key "${s}" should have type of Buffer, Stream, File, or String `)}if("message"===s){const r=e[s];if(!r||!this.isMIME(r))throw Ut.getUserDataError(`Unknown data type for "${s}" property`,"The mime data should have type of Buffer, String or Blob");return this.addMimeDataToFD(s,r,t),t}return this.addCommonPropertyToFD(s,e[s],t),t}),new this.FormDataConstructor)}addMimeDataToFD(e,t,s){if("string"!=typeof t){if(this.isFormDataPackage(s)){s.append(e,t,{filename:"MimeMessage"})}else if(void 0!==typeof Blob){const r=s;if(t instanceof Blob)return void r.append(e,t,"MimeMessage");if(this.attachmentsHandler.isBuffer(t)){const s=new Blob([t]);r.append(e,s,"MimeMessage")}}}else s.append(e,t)}isMIME(e){return"string"==typeof e||"undefined"!=typeof Blob&&e instanceof Blob||this.attachmentsHandler.isBuffer(e)||"undefined"!=typeof ReadableStream&&e instanceof ReadableStream}isFormDataPackage(e){return"object"==typeof e&&null!==e&&"function"==typeof e.getHeaders}isMessageAttachment(e){return this.attachmentsHandler.isCustomFile(e)||"string"==typeof e||"undefined"!=typeof File&&e instanceof File||"undefined"!=typeof Blob&&e instanceof Blob||this.attachmentsHandler.isBuffer(e)||this.attachmentsHandler.isStream(e)||Array.isArray(e)&&e.every((t=>this.attachmentsHandler.isCustomFile(t)||"undefined"!=typeof File&&t instanceof File||"undefined"!=typeof Blob&&e instanceof Blob||this.attachmentsHandler.isBuffer(t)||this.attachmentsHandler.isStream(t)))}addFilesToFD(e,t,s){const r=(e,t,r)=>{const n="multipleValidationFile"===e?"file":e,o=this.attachmentsHandler.convertToFDexpectedShape(t),a=this.attachmentsHandler.getAttachmentInfo(t);if(this.isFormDataPackage(r)){const e=r,t="string"==typeof o?Buffer.from(o):o;e.append(n,t,a)}else if(void 0!==typeof Blob){const e=s;if("string"==typeof o||this.attachmentsHandler.isBuffer(o)){const t=new Blob([o]);return void e.append(n,t,a.filename)}if(o instanceof Blob)return void e.append(n,o,a.filename);if(this.attachmentsHandler.isStream(o)){const t=this.attachmentsHandler.getBlobFromStream(o,a.knownLength);e.set(n,t,a.filename)}}};Array.isArray(t)?t.forEach((function(t){r(e,t,s)})):r(e,t,s)}addCommonPropertyToFD(e,t,s){const r=(e,t)=>{if(this.isFormDataPackage(s))return"object"==typeof t?(console.warn('The received value is an object. \n"JSON.Stringify" will be used to avoid TypeError \nTo remove this warning: \nConsider switching to built-in FormData or converting the value on your own.\n'),s.append(e,JSON.stringify(t))):s.append(e,t);if("string"==typeof t)return s.append(e,t);if(void 0!==typeof Blob&&t instanceof Blob)return s.append(e,t);throw Ut.getUserDataError("Unknown value type for Form Data. String or Blob expected","Browser compliant FormData allows only string or Blob values for properties that are not attachments.")};Array.isArray(t)?t.forEach((function(t){r(e,t)})):null!=t&&r(e,t)}}class It{request;static SUBACCOUNT_HEADER="X-Mailgun-On-Behalf-Of";constructor(e){this.request=e}list(e){return this.request.get("/v5/accounts/subaccounts",e).then((e=>e.body))}get(e){return this.request.get(`/v5/accounts/subaccounts/${e}`).then((e=>e.body))}create(e){return this.request.postWithFD("/v5/accounts/subaccounts",{name:e}).then((e=>e.body))}enable(e){return this.request.post(`/v5/accounts/subaccounts/${e}/enable`).then((e=>e.body))}disable(e){return this.request.post(`/v5/accounts/subaccounts/${e}/disable`).then((e=>e.body))}}let Mt=class{username;key;url;timeout;headers;formDataBuilder;maxBodyLength;proxy;constructor(e,t){this.username=e.username,this.key=e.key,this.url=e.url,this.timeout=e.timeout,this.headers=this.makeHeadersFromObject(e.headers),this.formDataBuilder=new Nt(t),this.maxBodyLength=********,this.proxy=e?.proxy}async request(e,t,s){const r={...s};delete r?.headers;const n=this.joinAndTransformHeaders(s),o={...r};if(r?.query&&Object.getOwnPropertyNames(r?.query).length>0&&(o.params=new URLSearchParams(r.query),delete o.query),r?.body){const e=r?.body;o.data=e,delete o.body}let a;const i=d(this.url,t);try{a=await vt.request({method:e.toLocaleUpperCase(),timeout:this.timeout,url:i,headers:n,...o,maxBodyLength:this.maxBodyLength,proxy:this.proxy})}catch(e){const t=e;throw new Ut({status:t?.response?.status||400,statusText:t?.response?.statusText||t.code,body:t?.response?.data||t.message})}return await this.getResponseBody(a)}async getResponseBody(e){const t={body:{},status:e?.status};if("string"==typeof e.data){if("Mailgun Magnificent API"===e.data)throw new Ut({status:400,statusText:"Incorrect url",body:e.data});t.body={message:e.data}}else t.body=e.data;return t}joinAndTransformHeaders(e){const t=new Ct,s=i.encode(`${this.username}:${this.key}`);t.setAuthorization(`Basic ${s}`),t.set(this.headers);const r=e&&e.headers,n=this.makeHeadersFromObject(r);return t.set(n),t}makeHeadersFromObject(e={}){let t=new Ct;return t=Object.entries(e).reduce(((e,t)=>{const[s,r]=t;return e.set(s,r),e}),t),t}setSubaccountHeader(e){const t=this.makeHeadersFromObject({...this.headers,[It.SUBACCOUNT_HEADER]:e});this.headers.set(t)}resetSubaccountHeader(){this.headers.delete(It.SUBACCOUNT_HEADER)}query(e,t,s,r){return this.request(e,t,{query:s,...r})}command(e,t,s,r,n=!0){let o={};n&&(o={"Content-Type":"application/x-www-form-urlencoded"});const a={...o,body:s,...r};return this.request(e,t,a)}get(e,t,s){return this.query("get",e,t,s)}post(e,t,s){return this.command("post",e,t,s)}postWithFD(e,t){const s=this.formDataBuilder.createFormData(t);return this.command("post",e,s,{headers:{"Content-Type":"multipart/form-data"}},!1)}putWithFD(e,t){const s=this.formDataBuilder.createFormData(t);return this.command("put",e,s,{headers:{"Content-Type":"multipart/form-data"}},!1)}patchWithFD(e,t){const s=this.formDataBuilder.createFormData(t);return this.command("patch",e,s,{headers:{"Content-Type":"multipart/form-data"}},!1)}put(e,t,s){return this.command("put",e,t,s)}delete(e,t){return this.command("delete",e,t)}};class Wt{name;require_tls;skip_verification;state;wildcard;spam_action;created_at;smtp_password;smtp_login;type;receiving_dns_records;sending_dns_records;id;is_disabled;web_prefix;web_scheme;use_automatic_sender_security;dkim_host;mailfrom_host;constructor(e,t,s){this.name=e.name,this.require_tls=e.require_tls,this.skip_verification=e.skip_verification,this.state=e.state,this.wildcard=e.wildcard,this.spam_action=e.spam_action,this.created_at=new Date(e.created_at),this.smtp_password=e.smtp_password,this.smtp_login=e.smtp_login,this.type=e.type,this.receiving_dns_records=t||null,this.sending_dns_records=s||null,this.id=e.id,this.is_disabled=e.is_disabled,this.web_prefix=e.web_prefix,this.web_scheme=e.web_scheme,this.use_automatic_sender_security=e.use_automatic_sender_security;const r=["dkim_host","mailfrom_host"].reduce(((t,s)=>{if(e[s]){t[s]=e[s]}return t}),{});Object.assign(this,r)}}class Ht{request;domainCredentials;domainTemplates;domainTags;domainTracking;logger;constructor(e,t,s,r,n,o=console){this.request=e,this.domainCredentials=t,this.domainTemplates=s,this.domainTags=r,this.logger=o,this.domainTracking=n}_handleBoolValues(e){const t=e,s=Object.keys(t).reduce(((e,s)=>{const r=s;if("boolean"==typeof t[r]){const s=t[r];e[r]="true"===s.toString()?"true":"false"}return e}),{});return{...e,...s}}_parseMessage(e){return e.body}parseDomainList(e){return e.body&&e.body.items?e.body.items.map((function(e){return new Wt(e)})):[]}_parseDomain(e){return new Wt(e.body.domain,e.body.receiving_dns_records,e.body.sending_dns_records)}list(e){return this.request.get("/v4/domains",e).then((e=>this.parseDomainList(e)))}get(e,t){const s=t?{"h:extended":t?.extended??!1,"h:with_dns":t?.with_dns??!0}:{};return this.request.get(`/v4/domains/${e}`,s).then((e=>this._parseDomain(e)))}create(e){const t=this._handleBoolValues(e);return this.request.postWithFD("/v4/domains",t).then((e=>this._parseDomain(e)))}update(e,t){const s=this._handleBoolValues(t);return this.request.putWithFD(`/v4/domains/${e}`,s).then((e=>this._parseDomain(e)))}verify(e){return this.request.put(`/v4/domains/${e}/verify`).then((e=>this._parseDomain(e)))}destroy(e){return this.request.delete(`/v3/domains/${e}`).then((e=>this._parseMessage(e)))}getConnection(e){return this.request.get(`/v3/domains/${e}/connection`).then((e=>e)).then((e=>e.body))}updateConnection(e,t){return this.request.put(`/v3/domains/${e}/connection`,t).then((e=>e)).then((e=>e.body))}getTracking(e){return this.logger.warn("\n      'domains.getTracking' method is deprecated, and will be removed. Please use 'domains.domainTracking.getTracking' instead.\n    "),this.domainTracking.getTracking(e)}updateTracking(e,t,s){return this.logger.warn("\n      'domains.updateTracking' method is deprecated, and will be removed. Please use 'domains.domainTracking.updateTracking' instead.\n    "),this.domainTracking.updateTracking(e,t,s)}getIps(e){return this.logger.warn('"domains.getIps" method is deprecated and will be removed in the future releases.'),this.request.get(d("/v3/domains",e,"ips")).then((e=>e?.body?.items))}assignIp(e,t){return this.logger.warn('"domains.assignIp" method is deprecated and will be removed in the future releases.'),this.request.postWithFD(d("/v3/domains",e,"ips"),{ip:t})}deleteIp(e,t){return this.logger.warn('"domains.deleteIp" method is deprecated and will be moved into the IpsClient in the future releases.'),this.request.delete(d("/v3/domains",e,"ips",t))}linkIpPool(e,t){return this.logger.warn('"domains.linkIpPool" method is deprecated, and will be removed in the future releases.'),this.request.postWithFD(d("/v3/domains",e,"ips"),{pool_id:t})}unlinkIpPoll(e,t){this.logger.warn('"domains.unlinkIpPoll" method is deprecated, and will be moved into the IpsClient in the future releases.');let s="";if(t.pool_id&&t.ip)throw Ut.getUserDataError("Too much data for replacement","Please specify either pool_id or ip (not both)");return t.pool_id?s=`?pool_id=${t.pool_id}`:t.ip&&(s=`?ip=${t.ip}`),this.request.delete(d("/v3/domains",e,"ips","ip_pool",s))}updateDKIMAuthority(e,t){return this.request.put(`/v3/domains/${e}/dkim_authority`,{},{query:`self=${t.self}`}).then((e=>e)).then((e=>e.body))}async updateDKIMSelector(e,t){const s=await this.request.put(`/v3/domains/${e}/dkim_selector`,{},{query:`dkim_selector=${t.dkimSelector}`});return{status:s.status,message:s?.body?.message}}updateWebPrefix(e,t){return this.logger.warn('"domains.updateWebPrefix" method is deprecated, please use domains.update to set new "web_prefix". Current method will be removed in the future releases.'),this.request.put(`/v3/domains/${e}/web_prefix`,{},{query:`web_prefix=${t.webPrefix}`}).then((e=>e))}}class Vt{request;constructor(e){e&&(this.request=e)}parsePage(e,t,s,r){const n=new URL(t),{searchParams:o}=n,a=t&&"string"==typeof t&&t.split(s).pop()||"";let i=null;return r&&(i=o.has(r)?o.get(r):void 0),{id:e,page:"?"===s?`?${a}`:a,iteratorPosition:i,url:t}}parsePageLinks(e,t,s){return Object.entries(e.body.paging).reduce(((e,[r,n])=>(e[r]=this.parsePage(r,n,t,s),e)),{})}updateUrlAndQuery(e,t){let s=e;const r={...t};return r.page&&(s=d(e,r.page),delete r.page),{url:s,updatedQuery:r}}async requestListWithPages(e,t,s){const{url:r,updatedQuery:n}=this.updateUrlAndQuery(e,t);if(this.request){const e=await this.request.get(r,n);return this.parseList(e,s)}throw new Ut({status:500,statusText:"Request property is empty",body:{message:""}})}}class zt extends Vt{request;constructor(e){super(e),this.request=e}parseList(e){const t={};return t.items=e.body.items,t.pages=this.parsePageLinks(e,"/"),t.status=e.status,t}async get(e,t){return this.requestListWithPages(d("/v3",e,"events"),t)}}class Jt{start;end;resolution;stats;constructor(e){this.start=new Date(e.start),this.end=new Date(e.end),this.resolution=e.resolution,this.stats=e.stats.map((function(e){const t={...e};return t.time=new Date(e.time),t}))}}class Kt{request;logger;constructor(e,t=console){this.request=e,this.logger=t}convertDateToUTC(e,t){return this.logger.warn(`Date:"${t}" was auto-converted to UTC time zone.\nValue "${t.toUTCString()}" will be used for request.\nConsider using string type for property "${e}" to avoid auto-converting`),[e,t.toUTCString()]}prepareSearchParams(e){let t=[];return"object"==typeof e&&Object.keys(e).length&&(t=Object.entries(e).reduce(((e,t)=>{const[s,r]=t;if(Array.isArray(r)&&r.length){const t=r.map((e=>[s,e]));return[...e,...t]}return r instanceof Date?(e.push(this.convertDateToUTC(s,r)),e):("string"==typeof r&&e.push([s,r]),e)}),[])),t}parseStats(e){return new Jt(e.body)}getDomain(e,t){const s=this.prepareSearchParams(t);return this.request.get(d("/v3",e,"stats/total"),s).then(this.parseStats)}getAccount(e){const t=this.prepareSearchParams(e);return this.request.get("/v3/stats/total",t).then(this.parseStats)}}var Qt,Xt,Gt,Yt;!function(e){e.HOUR="hour",e.DAY="day",e.MONTH="month"}(Qt||(Qt={})),function(e){e.BOUNCES="bounces",e.COMPLAINTS="complaints",e.UNSUBSCRIBES="unsubscribes",e.WHITELISTS="whitelists"}(Xt||(Xt={})),function(e){e.CLICKED="clicked",e.COMPLAINED="complained",e.DELIVERED="delivered",e.OPENED="opened",e.PERMANENT_FAIL="permanent_fail",e.TEMPORARY_FAIL="temporary_fail",e.UNSUBSCRIBED="unsubscribe"}(Gt||(Gt={})),function(e){e.YES="yes",e.NO="no"}(Yt||(Yt={}));class Zt{type;constructor(e){this.type=e}}class es extends Zt{address;code;error;created_at;constructor(e){super(Xt.BOUNCES),this.address=e.address,this.code=+e.code,this.error=e.error,this.created_at=new Date(e.created_at)}}class ts extends Zt{address;created_at;constructor(e){super(Xt.COMPLAINTS),this.address=e.address,this.created_at=new Date(e.created_at)}}class ss extends Zt{address;tags;created_at;constructor(e){super(Xt.UNSUBSCRIBES),this.address=e.address,this.tags=e.tags,this.created_at=new Date(e.created_at)}}class rs extends Zt{value;reason;createdAt;constructor(e){super(Xt.WHITELISTS),this.value=e.value,this.reason=e.reason,this.createdAt=new Date(e.createdAt)}}const ns={headers:{"Content-Type":"application/json"}};class os extends Vt{request;models;constructor(e){super(e),this.request=e,this.models={bounces:es,complaints:ts,unsubscribes:ss,whitelists:rs}}parseList(e,t){const s={};return s.items=e.body.items?.map((e=>new t(e)))||[],s.pages=this.parsePageLinks(e,"?","address"),s.status=e.status,s}_parseItem(e,t){return new t(e)}createWhiteList(e,t,s){if(s)throw Ut.getUserDataError("Data property should be an object","Whitelist's creation process does not support multiple creations. Data property should be an object");return this.request.postWithFD(d("v3",e,"whitelists"),t).then(this.prepareResponse)}createUnsubscribe(e,t){if(Array.isArray(t)){if(t.some((e=>e.tag)))throw Ut.getUserDataError("Tag property should not be used for creating multiple unsubscribes.","Tag property can be used only if one unsubscribe provided as second argument of create method. Please use tags instead.");return this.request.post(d("v3",e,"unsubscribes"),JSON.stringify(t),ns).then(this.prepareResponse)}if(t?.tags)throw Ut.getUserDataError("Tags property should not be used for creating one unsubscribe.","Tags property can be used if you provides an array of unsubscribes as second argument of create method. Please use tag instead");if(Array.isArray(t.tag))throw Ut.getUserDataError("Tag property can not be an array","Please use array of unsubscribes as second argument of create method to be able to provide few tags");return this.request.postWithFD(d("v3",e,"unsubscribes"),t).then(this.prepareResponse)}getModel(e){if(e in this.models)return this.models[e];throw Ut.getUserDataError("Unknown type value","Type may be only one of [bounces, complaints, unsubscribes, whitelists]")}prepareResponse(e){return{message:e.body.message,type:e.body.type||"",value:e.body.value||"",status:e.status}}async list(e,t,s){const r=this.getModel(t);return this.requestListWithPages(d("v3",e,t),s,r)}get(e,t,s){const r=this.getModel(t);return this.request.get(d("v3",e,t,encodeURIComponent(s))).then((e=>this._parseItem(e.body,r)))}create(e,t,s){let r;this.getModel(t);const n=Array.isArray(s);return"whitelists"===t?this.createWhiteList(e,s,n):"unsubscribes"===t?this.createUnsubscribe(e,s):(r=n?[...s]:[s],this.request.post(d("v3",e,t),JSON.stringify(r),ns).then(this.prepareResponse))}destroy(e,t,s){return this.getModel(t),this.request.delete(d("v3",e,t,encodeURIComponent(s))).then((e=>({message:e.body.message,value:e.body.value||"",address:e.body.address||"",status:e.status})))}}class as{id;url;urls;constructor(e,t,s){this.id=e,this.url=t,this.urls=s}}class is{request;constructor(e){this.request=e}_parseWebhookList(e){return e.body.webhooks}_parseWebhookWithID(e){return function(t){const s=t?.body?.webhook;let r=s?.url,n=s?.urls;return r||(r=n&&n.length?n[0]:void 0),n&&0!==n.length||!r||(n=[r]),new as(e,r,n)}}_parseWebhookTest(e){return{code:e.body.code,message:e.body.message}}list(e,t){return this.request.get(d("/v3/domains",e,"webhooks"),t).then(this._parseWebhookList)}get(e,t){return this.request.get(d("/v3/domains",e,"webhooks",t)).then(this._parseWebhookWithID(t))}create(e,t,s,r=!1){return r?this.request.putWithFD(d("/v3/domains",e,"webhooks",t,"test"),{url:s}).then(this._parseWebhookTest):this.request.postWithFD(d("/v3/domains",e,"webhooks"),{id:t,url:s}).then(this._parseWebhookWithID(t))}update(e,t,s){return this.request.putWithFD(d("/v3/domains",e,"webhooks",t),{url:s}).then(this._parseWebhookWithID(t))}destroy(e,t){return this.request.delete(d("/v3/domains",e,"webhooks",t)).then(this._parseWebhookWithID(t))}}class us{request;constructor(e){this.request=e}prepareBooleanValues(e){const t=new Set(["o:testmode","t:text","o:dkim","o:tracking","o:tracking-clicks","o:tracking-opens","o:require-tls","o:skip-verification"]);if(!e||0===Object.keys(e).length)throw Ut.getUserDataError("Message data object can not be empty","Message data object can not be empty");return Object.keys(e).reduce(((s,r)=>(t.has(r)&&"boolean"==typeof e[r]?s[r]=e[r]?"yes":"no":s[r]=e[r],s)),{})}_parseResponse(e){return{status:e.status,...e.body}}create(e,t){if(t.message)return this.request.postWithFD(`/v3/${e}/messages.mime`,t).then(this._parseResponse);const s=this.prepareBooleanValues(t);return this.request.postWithFD(`/v3/${e}/messages`,s).then(this._parseResponse)}}class cs{request;constructor(e){this.request=e}list(e){return this.request.get("/v3/routes",e).then((e=>e.body.items))}get(e){return this.request.get(`/v3/routes/${e}`).then((e=>e.body.route))}create(e){return this.request.postWithFD("/v3/routes",e).then((e=>e.body.route))}update(e,t){return this.request.putWithFD(`/v3/routes/${e}`,t).then((e=>e.body))}destroy(e){return this.request.delete(`/v3/routes/${e}`).then((e=>e.body))}}class ls{multipleValidation;request;constructor(e,t){this.request=e,this.multipleValidation=t}async get(e){const t={address:e};return(await this.request.get("/v4/address/validate",t)).body}}class ds{request;constructor(e){this.request=e}async list(e){const t=await this.request.get("/v3/ips",e);return this.parseIpsResponse(t)}async get(e){const t=await this.request.get(`/v3/ips/${e}`);return this.parseIpsResponse(t)}parseIpsResponse(e){return e.body}}class hs{request;constructor(e){this.request=e}list(){return this.request.get("/v1/ip_pools").then((e=>this.parseIpPoolsResponse(e)))}async create(e){const t=await this.request.postWithFD("/v1/ip_pools",e);return{status:t.status,...t.body}}async update(e,t){const s=await this.request.patchWithFD(`/v1/ip_pools/${e}`,t);return{status:s.status,...s.body}}async delete(e,t){const s=await this.request.delete(`/v1/ip_pools/${e}`,t);return{status:s.status,...s.body}}parseIpPoolsResponse(e){return{status:e.status,...e.body}}}class ps extends Vt{baseRoute;request;members;constructor(e,t){super(e),this.request=e,this.baseRoute="/v3/lists",this.members=t}parseValidationResult(e,t){return{status:e,validationResult:{...t,created_at:new Date(1e3*t.created_at)}}}parseList(e){const t={};return t.items=e.body.items,t.pages=this.parsePageLinks(e,"?","address"),t.status=e.status,t}async list(e){return this.requestListWithPages(`${this.baseRoute}/pages`,e)}get(e){return this.request.get(`${this.baseRoute}/${e}`).then((e=>e.body.list))}create(e){return this.request.postWithFD(this.baseRoute,e).then((e=>e.body.list))}update(e,t){return this.request.putWithFD(`${this.baseRoute}/${e}`,t).then((e=>e.body.list))}destroy(e){return this.request.delete(`${this.baseRoute}/${e}`).then((e=>e.body))}validate(e){return this.request.post(`${this.baseRoute}/${e}/validate`,{}).then((e=>({status:e.status,...e.body})))}validationResult(e){return this.request.get(`${this.baseRoute}/${e}/validate`).then((e=>this.parseValidationResult(e.status,e.body)))}cancelValidation(e){return this.request.delete(`${this.baseRoute}/${e}/validate`).then((e=>({status:e.status,message:e.body.message})))}}class fs extends Vt{baseRoute;request;constructor(e){super(e),this.request=e,this.baseRoute="/v3/lists"}checkAndUpdateData(e){const t={...e};return"object"==typeof e.vars&&(t.vars=JSON.stringify(t.vars)),"boolean"==typeof e.subscribed&&(t.subscribed=e.subscribed?"yes":"no"),t}parseList(e){const t={};return t.items=e.body.items,t.pages=this.parsePageLinks(e,"?","address"),t}async listMembers(e,t){return this.requestListWithPages(`${this.baseRoute}/${e}/members/pages`,t)}getMember(e,t){return this.request.get(`${this.baseRoute}/${e}/members/${t}`).then((e=>e.body.member))}createMember(e,t){const s=this.checkAndUpdateData(t);return this.request.postWithFD(`${this.baseRoute}/${e}/members`,s).then((e=>e.body.member))}createMembers(e,t){const s={members:Array.isArray(t.members)?JSON.stringify(t.members):t.members,upsert:t.upsert};return this.request.postWithFD(`${this.baseRoute}/${e}/members.json`,s).then((e=>e.body))}updateMember(e,t,s){const r=this.checkAndUpdateData(s);return this.request.putWithFD(`${this.baseRoute}/${e}/members/${t}`,r).then((e=>e.body.member))}destroyMember(e,t){return this.request.delete(`${this.baseRoute}/${e}/members/${t}`).then((e=>e.body))}}class ms{baseRoute;request;constructor(e){this.request=e,this.baseRoute="/v3/domains/"}_parseDomainCredentialsList(e){return{items:e.body.items,totalCount:e.body.total_count}}_parseMessageResponse(e){return{status:e.status,message:e.body.message}}_parseDeletedResponse(e){return{status:e.status,message:e.body.message,spec:e.body.spec}}list(e,t){return this.request.get(d(this.baseRoute,e,"/credentials"),t).then((e=>this._parseDomainCredentialsList(e)))}create(e,t){return this.request.postWithFD(`${this.baseRoute}${e}/credentials`,t).then((e=>this._parseMessageResponse(e)))}update(e,t,s){return this.request.putWithFD(`${this.baseRoute}${e}/credentials/${t}`,s).then((e=>this._parseMessageResponse(e)))}destroy(e,t){return this.request.delete(`${this.baseRoute}${e}/credentials/${t}`).then((e=>this._parseDeletedResponse(e)))}}class gs{createdAt;id;quantity;recordsProcessed;status;downloadUrl;responseStatusCode;summary;constructor(e,t){this.createdAt=new Date(e.created_at),this.id=e.id,this.quantity=e.quantity,this.recordsProcessed=e.records_processed,this.status=e.status,this.responseStatusCode=t,e.download_url&&(this.downloadUrl={csv:e.download_url?.csv,json:e.download_url?.json}),e.summary&&(this.summary={result:{catchAll:e.summary.result.catch_all,deliverable:e.summary.result.deliverable,doNotSend:e.summary.result.do_not_send,undeliverable:e.summary.result.undeliverable,unknown:e.summary.result.unknown},risk:{high:e.summary.risk.high,low:e.summary.risk.low,medium:e.summary.risk.medium,unknown:e.summary.risk.unknown}})}}class ys extends Vt{request;attachmentsHandler;constructor(e){super(),this.request=e,this.attachmentsHandler=new $t}handleResponse(e){return{status:e.status,...e?.body}}parseList(e){const t={};return t.jobs=e.body.jobs.map((t=>new gs(t,e.status))),t.pages=this.parsePageLinks(e,"?","pivot"),t.total=e.body.total,t.status=e.status,t}async list(e){return this.requestListWithPages("/v4/address/validate/bulk",e)}async get(e){const t=await this.request.get(`/v4/address/validate/bulk/${e}`);return new gs(t.body,t.status)}convertToExpectedShape(e){let t;return t=this.attachmentsHandler.isBuffer(e.file)?{multipleValidationFile:e.file}:"string"==typeof e.file?{multipleValidationFile:{data:e.file}}:(this.attachmentsHandler.isStream(e.file),{multipleValidationFile:e.file}),t}async create(e,t){if(!t||!t.file)throw Ut.getUserDataError('"file" property expected.','Make sure second argument has "file" property.');const s=this.convertToExpectedShape(t),r=await this.request.postWithFD(`/v4/address/validate/bulk/${e}`,s);return this.handleResponse(r)}async destroy(e){const t=await this.request.delete(`/v4/address/validate/bulk/${e}`);return this.handleResponse(t)}}class bs{name;description;createdAt;createdBy;id;version;versions;constructor(e){this.name=e.name,this.description=e.description,this.createdAt=e.createdAt?new Date(e.createdAt):"",this.createdBy=e.createdBy,this.id=e.id,e.version&&(this.version=e.version,this.version&&e.version.createdAt&&(this.version.createdAt=new Date(e.version.createdAt))),e.versions&&e.versions.length&&(this.versions=e.versions.map((e=>{const t={...e};return t.createdAt=new Date(e.createdAt),t})))}}class ws extends Vt{baseRoute;request;constructor(e){super(e),this.request=e,this.baseRoute="/v3/"}parseCreationResponse(e){return new bs(e.body.template)}parseCreationVersionResponse(e){const t={};return t.status=e.status,t.message=e.body.message,e.body&&e.body.template&&(t.template=new bs(e.body.template)),t}parseMutationResponse(e){const t={};return t.status=e.status,t.message=e.body.message,e.body&&e.body.template&&(t.templateName=e.body.template.name),t}parseNotificationResponse(e){const t={};return t.status=e.status,t.message=e.body.message,t}parseMutateTemplateVersionResponse(e){const t={};return t.status=e.status,t.message=e.body.message,e.body.template&&(t.templateName=e.body.template.name,t.templateVersion={tag:e.body.template.version.tag}),t}parseList(e){const t={};return t.items=e.body.items.map((e=>new bs(e))),t.pages=this.parsePageLinks(e,"?","p"),t.status=e.status,t}parseListTemplateVersions(e){const t={};return t.template=new bs(e.body.template),t.pages=this.parsePageLinks(e,"?","p"),t}async list(e,t){return this.requestListWithPages(d(this.baseRoute,e,"/templates"),t)}get(e,t,s){return this.request.get(d(this.baseRoute,e,"/templates/",t),s).then((e=>new bs(e.body.template)))}create(e,t){return this.request.postWithFD(d(this.baseRoute,e,"/templates"),t).then((e=>this.parseCreationResponse(e)))}update(e,t,s){return this.request.putWithFD(d(this.baseRoute,e,"/templates/",t),s).then((e=>this.parseMutationResponse(e)))}destroy(e,t){return this.request.delete(d(this.baseRoute,e,"/templates/",t)).then((e=>this.parseMutationResponse(e)))}destroyAll(e){return this.request.delete(d(this.baseRoute,e,"/templates")).then((e=>this.parseNotificationResponse(e)))}listVersions(e,t,s){return this.request.get(d(this.baseRoute,e,"/templates",t,"/versions"),s).then((e=>this.parseListTemplateVersions(e)))}getVersion(e,t,s){return this.request.get(d(this.baseRoute,e,"/templates/",t,"/versions/",s)).then((e=>new bs(e.body.template)))}createVersion(e,t,s){return this.request.postWithFD(d(this.baseRoute,e,"/templates/",t,"/versions"),s).then((e=>this.parseCreationVersionResponse(e)))}updateVersion(e,t,s,r){return this.request.putWithFD(d(this.baseRoute,e,"/templates/",t,"/versions/",s),r).then((e=>this.parseMutateTemplateVersionResponse(e)))}destroyVersion(e,t,s){return this.request.delete(d(this.baseRoute,e,"/templates/",t,"/versions/",s)).then((e=>this.parseMutateTemplateVersionResponse(e)))}}class vs{tag;description;"first-seen";"last-seen";constructor(e){this.tag=e.tag,this.description=e.description,this["first-seen"]=new Date(e["first-seen"]),this["last-seen"]=new Date(e["last-seen"])}}class _s{tag;description;start;end;resolution;stats;constructor(e){this.tag=e.body.tag,this.description=e.body.description,this.start=new Date(e.body.start),this.end=new Date(e.body.end),this.resolution=e.body.resolution,this.stats=e.body.stats.map((function(e){return{...e,time:new Date(e.time)}}))}}class Rs extends Vt{baseRoute;request;constructor(e){super(e),this.request=e,this.baseRoute="/v3/"}parseList(e){const t={};return t.items=e.body.items.map((e=>new vs(e))),t.pages=this.parsePageLinks(e,"?","tag"),t.status=e.status,t}_parseTagStatistic(e){return new _s(e)}async list(e,t){return this.requestListWithPages(d(this.baseRoute,e,"/tags"),t)}get(e,t){return this.request.get(d(this.baseRoute,e,"/tags",t)).then((e=>new vs(e.body)))}update(e,t,s){return this.request.put(d(this.baseRoute,e,"/tags",t),s).then((e=>e.body))}destroy(e,t){return this.request.delete(`${this.baseRoute}${e}/tags/${t}`).then((e=>({message:e.body.message,status:e.status})))}statistic(e,t,s){return this.request.get(d(this.baseRoute,e,"/tags",t,"stats"),s).then((e=>this._parseTagStatistic(e)))}countries(e,t){return this.request.get(d(this.baseRoute,e,"/tags",t,"stats/aggregates/countries")).then((e=>e.body))}providers(e,t){return this.request.get(d(this.baseRoute,e,"/tags",t,"stats/aggregates/providers")).then((e=>e.body))}devices(e,t){return this.request.get(d(this.baseRoute,e,"/tags",t,"stats/aggregates/devices")).then((e=>e.body))}}class qs extends Vt{request;attributes;filters;logger;constructor(e,t,s,r=console){super(e),this.request=e,this.attributes=t,this.filters=s,this.logger=r}convertDateToUTC(e,t){return this.logger.warn(`Date: "${t}" was auto-converted to UTC time zone.\nValue "${t.toISOString()}" will be used for request.\nConsider using string type for property "${e}" to avoid auto-converting`),t.toISOString()}prepareQueryData(e){const t=e,s=Object.keys(t).reduce(((s,r)=>{const n=r;if(t[n]&&"object"==typeof t[n]){const t=e[n];s[n]=this.convertDateToUTC(n,t)}return s}),{});return{...e,...s}}prepareResult(e){let t={};return t={...this.prepareSeedList(e.body),status:e.status},t}prepareSeedList(e){let t;const s={created_at:new Date(e.created_at),updated_at:new Date(e.updated_at),last_result_at:new Date(e.last_result_at)};t=e.Seeds?e.Seeds.map((e=>{let t={};const s={created_at:new Date(e.created_at),updated_at:new Date(e.updated_at),max_email_count_hit_at:new Date(e.max_email_count_hit_at),last_sent_to_at:new Date(e.last_sent_to_at),last_delivered_at:new Date(e.last_delivered_at)};return t={...e,...s},t})):null;const r={...e,Seeds:t,...s};return delete r.Id,r}parseList(e){const t={items:[]};return t.items=e.body.items?.map((e=>this.prepareSeedList(e))),t.pages=this.parsePageLinks(e,"?","address"),t.status=e.status,t}async list(e){const t=this.prepareQueryData(e),s=await this.request.get("/v4/inbox/seedlists",t);return{...this.parseList(s),status:200}}async get(e){const t=await this.request.get(`/v4/inbox/seedlists/${e}`);return{...this.prepareSeedList(t.body.seedlist),status:t.status}}async create(e){const t=await this.request.postWithFD("/v4/inbox/seedlists",e);return this.prepareResult(t)}async update(e,t){const s=await this.request.put(`/v4/inbox/seedlists/${e}`,t);return this.prepareResult(s)}async destroy(e){return this.request.delete(`/v4/inbox/seedlists/${e}`)}}class Ts{request;seedsLists;results;providers;constructor(e,t,s,r){this.request=e,this.seedsLists=t,this.seedsLists=t,this.results=s,this.providers=r}async runTest(e){const t=await this.request.post("/v4/inbox/tests",e);return{...t.body,status:t.status}}}class Ss extends Vt{request;attributes;filters;sharing;logger;constructor(e,t,s,r,n=console){super(e),this.request=e,this.attributes=t,this.filters=s,this.sharing=r,this.logger=n}convertDateToUTC(e,t){return this.logger.warn(`Date: "${t}" was auto-converted to UTC time zone.\nValue "${t.toISOString()}" will be used for request.\nConsider using string type for property "${e}" to avoid auto-converting`),t.toISOString()}prepareQueryData(e){const t=e,s=Object.keys(t).reduce(((s,r)=>{const n=r;if(t[n]&&"object"==typeof t[n]){const t=e[n];s[n]=this.convertDateToUTC(n,t)}return s}),{});return{...e,...s}}prepareInboxPlacementsResult(e){let t={};const s={created_at:new Date(e.created_at),updated_at:new Date(e.updated_at),sharing_expires_at:new Date(e.sharing_expires_at)};e.Box&&(t={...e.Box,created_at:new Date(e.Box.created_at),updated_at:new Date(e.Box.updated_at),last_result_at:new Date(e.Box.last_result_at)},delete t.ID);const r={...e,Box:t,...s,id:e.Id};return delete r.ID,r}parseList(e){const t={};return t.items=e.body.items.map((e=>this.prepareInboxPlacementsResult(e))),t.pages=this.parsePageLinks(e,"?","address"),t.status=e.status,t}async list(e){const t=this.prepareQueryData(e),s=await this.request.get("/v4/inbox/results",t);return this.parseList(s)}async get(e){const t=await this.request.get(`/v4/inbox/results/${e}`),s=this.prepareInboxPlacementsResult(t.body.result);return{status:t.status,inboxPlacementResult:s}}async destroy(e){const t=await this.request.delete(`/v4/inbox/results/${e}`);return{status:t.status,...t.body}}async getResultByShareId(e){const t=await this.request.get(`/v4/inbox/sharing/public/${e}`),s=this.prepareInboxPlacementsResult(t.body.result);return{status:t.status,inboxPlacementResult:s}}}class Ds{request;path;constructor(e,t){this.path=t,this.request=e}async list(){const e=await this.request.get(this.path);return{items:e.body.items,status:e.status}}async get(e){const t=await this.request.get(`${this.path}/${e}`);return{...t.body,status:t.status}}}class Es{request;path;constructor(e,t){this.request=e,this.path=t}async list(){const e=await this.request.get(this.path);return{status:e.status,supported_filters:e.body.supported_filters}}}class Os{request;constructor(e){this.request=e}prepareInboxPlacementsResultSharing(e){const t={expires_at:new Date(e.expires_at)};return{...e,...t}}async get(e){const t=await this.request.get(`/v4/inbox/sharing/${e}`),s=this.prepareInboxPlacementsResultSharing(t.body.sharing);return{status:t.status,...s}}async update(e,t){const s=await this.request.put(`/v4/inbox/sharing/${e}`,{},{query:`enabled=${t.enabled}`});return{...this.prepareInboxPlacementsResultSharing(s.body.sharing),status:s.status}}}class As{request;path;constructor(e){this.path="/v4/inbox/providers",this.request=e}parseList(e){const t={};return t.items=e.body.items.map((e=>{const t={created_at:new Date(e.created_at),updated_at:new Date(e.updated_at)};return{...e,...t}})),t.status=e.status,t}async list(){const e=await this.request.get(this.path);return this.parseList(e)}}class xs{request;logger;constructor(e,t=console){this.request=e,this.logger=t}convertDateToUTC(e,t){return this.logger.warn(`Date:"${t}" was auto-converted to UTC time zone.\nValue "${t.toUTCString()}" will be used for request.\nConsider using string type for property "${e}" to avoid auto-converting`),t.toUTCString()}prepareQuery(e){let t,s;if(e){const r=e?.start,n=e?.end;t=r instanceof Date?this.convertDateToUTC("start",r):r??"",s=n&&n instanceof Date?this.convertDateToUTC("end",n):n??""}return{...e,start:t,end:s}}handleResponse(e){const t=e.body,s=Date.parse(t.start)?new Date(t.start):null,r=Date.parse(t.end)?new Date(t.end):null;return{...t,status:e.status,start:s,end:r}}async getAccount(e){const t=this.prepareQuery(e),s=await this.request.post("/v1/analytics/metrics",t);return this.handleResponse(s)}async getAccountUsage(e){const t=this.prepareQuery(e),s=await this.request.post("/v1/analytics/usage/metrics",t);return this.handleResponse(s)}}class ks{request;constructor(e){this.request=e}_parseTrackingSettings(e){return e.body.tracking}_parseTrackingUpdate(e){return e.body}_isOpenTrackingInfoWitPlace(e){return"object"==typeof e&&"place_at_the_top"in e}async get(e){const t=await this.request.get(`/v2/x509/${e}/status`);return{...t.body,responseStatusCode:t.status}}async generate(e){const t=await this.request.post(`/v2/x509/${e}`);return{...t.body,status:t.status}}async regenerate(e){const t=await this.request.put(`/v2/x509/${e}`);return{...t.body,status:t.status}}async getTracking(e){const t=await this.request.get(d("/v3/domains",e,"tracking"));return this._parseTrackingSettings(t)}async updateTracking(e,t,s){const r={...s};"boolean"==typeof s?.active&&(r.active=s?.active?"yes":"no"),this._isOpenTrackingInfoWitPlace(s)&&"boolean"==typeof s?.place_at_the_top&&(r.place_at_the_top=s?.place_at_the_top?"yes":"no");const n=await this.request.putWithFD(d("/v3/domains",e,"tracking",t),r);return this._parseTrackingUpdate(n)}}class Cs{request;domains;webhooks;events;stats;metrics;suppressions;messages;routes;validate;ips;ip_pools;lists;subaccounts;inboxPlacements;constructor(e,t){const s={...e};if(s.url||(s.url="https://api.mailgun.net"),!s.username)throw new Error('Parameter "username" is required');if(!s.key)throw new Error('Parameter "key" is required');this.request=new Mt(s,t);const r=new fs(this.request),n=new ms(this.request),o=new ws(this.request),a=new Rs(this.request),i=new ks(this.request),u=new ys(this.request),c=new Os(this.request),l=new Ds(this.request,"/v4/inbox/seedlists/a"),d=new Ds(this.request,"/v4/inbox/results/a"),h=new Es(this.request,"/v4/inbox/seedlists/_filters"),p=new Es(this.request,"/v4/inbox/results/_filters"),f=new qs(this.request,l,h),m=new Ss(this.request,d,p,c),g=new As(this.request);this.domains=new Ht(this.request,n,o,a,i),this.webhooks=new is(this.request),this.events=new zt(this.request),this.stats=new Kt(this.request),this.metrics=new xs(this.request),this.suppressions=new os(this.request),this.messages=new us(this.request),this.routes=new cs(this.request),this.ips=new ds(this.request),this.ip_pools=new hs(this.request),this.lists=new ps(this.request,r),this.validate=new ls(this.request,u),this.subaccounts=new It(this.request),this.inboxPlacements=new Ts(this.request,f,m,g)}setSubaccount(e){this.request?.setSubaccountHeader(e)}resetSubaccount(){this.request?.resetSubaccountHeader()}}class Fs{static get default(){return this}formData;constructor(e){this.formData=e}client(e){return new Cs(e,this.formData)}}export{Fs as default};
