import e from"util";import a,{Readable as n}from"stream";import t from"path";import s from"http";import i from"https";import o from"url";import r from"fs";import c from"assert";import p from"tty";import l from"os";import u from"zlib";import{EventEmitter as d}from"events";var m="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{};function h(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var f,x={exports:{}};
/*! https://mths.be/base64 v1.0.0 by @mathias | MIT license */var v,b,g,y=(f||(f=1,v=x,b=x.exports,function(e){var a=b,n=v&&v.exports==a&&v,t="object"==typeof m&&m;t.global!==t&&t.window!==t||(e=t);var s=function(e){this.message=e};(s.prototype=new Error).name="InvalidCharacterError";var i=function(e){throw new s(e)},o="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",r=/[\t\n\f\r ]/g,c={encode:function(e){e=String(e),/[^\0-\xFF]/.test(e)&&i("The string to be encoded contains characters outside of the Latin1 range.");for(var a,n,t,s,r=e.length%3,c="",p=-1,l=e.length-r;++p<l;)a=e.charCodeAt(p)<<16,n=e.charCodeAt(++p)<<8,t=e.charCodeAt(++p),c+=o.charAt((s=a+n+t)>>18&63)+o.charAt(s>>12&63)+o.charAt(s>>6&63)+o.charAt(63&s);return 2==r?(a=e.charCodeAt(p)<<8,n=e.charCodeAt(++p),c+=o.charAt((s=a+n)>>10)+o.charAt(s>>4&63)+o.charAt(s<<2&63)+"="):1==r&&(s=e.charCodeAt(p),c+=o.charAt(s>>2)+o.charAt(s<<4&63)+"=="),c},decode:function(e){var a=(e=String(e).replace(r,"")).length;a%4==0&&(a=(e=e.replace(/==?$/,"")).length),(a%4==1||/[^+a-zA-Z0-9/]/.test(e))&&i("Invalid character: the string to be decoded is not correctly encoded.");for(var n,t,s=0,c="",p=-1;++p<a;)t=o.indexOf(e.charAt(p)),n=s%4?64*n+t:t,s++%4&&(c+=String.fromCharCode(255&n>>(-2*s&6)));return c},version:"1.0.0"};if(a&&!a.nodeType)if(n)n.exports=c;else for(var p in c)c.hasOwnProperty(p)&&(a[p]=c[p]);else e.base64=c}(x.exports)),x.exports),w={exports:{}},_=w.exports;var k=(g||(g=1,function(e){var a,n;a=_,n=function(){return function(){return function(e){var a=[];if(0===e.length)return"";if("string"!=typeof e[0])throw new TypeError("Url must be a string. Received "+e[0]);if(e[0].match(/^[^/:]+:\/*$/)&&e.length>1){var n=e.shift();e[0]=n+e[0]}e[0].match(/^file:\/\/\//)?e[0]=e[0].replace(/^([^/:]+):\/*/,"$1:///"):e[0]=e[0].replace(/^([^/:]+):\/*/,"$1://");for(var t=0;t<e.length;t++){var s=e[t];if("string"!=typeof s)throw new TypeError("Url must be a string. Received "+s);""!==s&&(t>0&&(s=s.replace(/^[\/]+/,"")),s=t<e.length-1?s.replace(/[\/]+$/,""):s.replace(/[\/]+$/,"/"),a.push(s))}var i=a.join("/"),o=(i=i.replace(/\/(\?|&|#[^!])/g,"$1")).split("?");return o.shift()+(o.length>0?"?":"")+o.join("&")}("object"==typeof arguments[0]?arguments[0]:[].slice.call(arguments))}},e.exports?e.exports=n():a.urljoin=n()}(w)),w.exports),j=h(k);function R(e,a){return function(){return e.apply(a,arguments)}}const{toString:S}=Object.prototype,{getPrototypeOf:E}=Object,T=(q=Object.create(null),e=>{const a=S.call(e);return q[a]||(q[a]=a.slice(8,-1).toLowerCase())});var q;const C=e=>(e=e.toLowerCase(),a=>T(a)===e),A=e=>a=>typeof a===e,{isArray:O}=Array,F=A("undefined");const D=C("ArrayBuffer");const P=A("string"),B=A("function"),L=A("number"),U=e=>null!==e&&"object"==typeof e,z=e=>{if("object"!==T(e))return!1;const a=E(e);return!(null!==a&&a!==Object.prototype&&null!==Object.getPrototypeOf(a)||Symbol.toStringTag in e||Symbol.iterator in e)},I=C("Date"),N=C("File"),M=C("Blob"),$=C("FileList"),W=C("URLSearchParams"),[H,V,G,J]=["ReadableStream","Request","Response","Headers"].map(C);function K(e,a,{allOwnKeys:n=!1}={}){if(null==e)return;let t,s;if("object"!=typeof e&&(e=[e]),O(e))for(t=0,s=e.length;t<s;t++)a.call(null,e[t],t,e);else{const s=n?Object.getOwnPropertyNames(e):Object.keys(e),i=s.length;let o;for(t=0;t<i;t++)o=s[t],a.call(null,e[o],o,e)}}function Q(e,a){a=a.toLowerCase();const n=Object.keys(e);let t,s=n.length;for(;s-- >0;)if(t=n[s],a===t.toLowerCase())return t;return null}const Y="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:"undefined"!=typeof window?window:global,X=e=>!F(e)&&e!==Y;const Z=(ee="undefined"!=typeof Uint8Array&&E(Uint8Array),e=>ee&&e instanceof ee);var ee;const ae=C("HTMLFormElement"),ne=(({hasOwnProperty:e})=>(a,n)=>e.call(a,n))(Object.prototype),te=C("RegExp"),se=(e,a)=>{const n=Object.getOwnPropertyDescriptors(e),t={};K(n,((n,s)=>{let i;!1!==(i=a(n,s,e))&&(t[s]=i||n)})),Object.defineProperties(e,t)},ie="abcdefghijklmnopqrstuvwxyz",oe="0123456789",re={DIGIT:oe,ALPHA:ie,ALPHA_DIGIT:ie+ie.toUpperCase()+oe};const ce=C("AsyncFunction"),pe=(le="function"==typeof setImmediate,ue=B(Y.postMessage),le?setImmediate:ue?(de=`axios@${Math.random()}`,me=[],Y.addEventListener("message",(({source:e,data:a})=>{e===Y&&a===de&&me.length&&me.shift()()}),!1),e=>{me.push(e),Y.postMessage(de,"*")}):e=>setTimeout(e));var le,ue,de,me;const he="undefined"!=typeof queueMicrotask?queueMicrotask.bind(Y):"undefined"!=typeof process&&process.nextTick||pe;var fe={isArray:O,isArrayBuffer:D,isBuffer:function(e){return null!==e&&!F(e)&&null!==e.constructor&&!F(e.constructor)&&B(e.constructor.isBuffer)&&e.constructor.isBuffer(e)},isFormData:e=>{let a;return e&&("function"==typeof FormData&&e instanceof FormData||B(e.append)&&("formdata"===(a=T(e))||"object"===a&&B(e.toString)&&"[object FormData]"===e.toString()))},isArrayBufferView:function(e){let a;return a="undefined"!=typeof ArrayBuffer&&ArrayBuffer.isView?ArrayBuffer.isView(e):e&&e.buffer&&D(e.buffer),a},isString:P,isNumber:L,isBoolean:e=>!0===e||!1===e,isObject:U,isPlainObject:z,isReadableStream:H,isRequest:V,isResponse:G,isHeaders:J,isUndefined:F,isDate:I,isFile:N,isBlob:M,isRegExp:te,isFunction:B,isStream:e=>U(e)&&B(e.pipe),isURLSearchParams:W,isTypedArray:Z,isFileList:$,forEach:K,merge:function e(){const{caseless:a}=X(this)&&this||{},n={},t=(t,s)=>{const i=a&&Q(n,s)||s;z(n[i])&&z(t)?n[i]=e(n[i],t):z(t)?n[i]=e({},t):O(t)?n[i]=t.slice():n[i]=t};for(let e=0,a=arguments.length;e<a;e++)arguments[e]&&K(arguments[e],t);return n},extend:(e,a,n,{allOwnKeys:t}={})=>(K(a,((a,t)=>{n&&B(a)?e[t]=R(a,n):e[t]=a}),{allOwnKeys:t}),e),trim:e=>e.trim?e.trim():e.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,""),stripBOM:e=>(65279===e.charCodeAt(0)&&(e=e.slice(1)),e),inherits:(e,a,n,t)=>{e.prototype=Object.create(a.prototype,t),e.prototype.constructor=e,Object.defineProperty(e,"super",{value:a.prototype}),n&&Object.assign(e.prototype,n)},toFlatObject:(e,a,n,t)=>{let s,i,o;const r={};if(a=a||{},null==e)return a;do{for(s=Object.getOwnPropertyNames(e),i=s.length;i-- >0;)o=s[i],t&&!t(o,e,a)||r[o]||(a[o]=e[o],r[o]=!0);e=!1!==n&&E(e)}while(e&&(!n||n(e,a))&&e!==Object.prototype);return a},kindOf:T,kindOfTest:C,endsWith:(e,a,n)=>{e=String(e),(void 0===n||n>e.length)&&(n=e.length),n-=a.length;const t=e.indexOf(a,n);return-1!==t&&t===n},toArray:e=>{if(!e)return null;if(O(e))return e;let a=e.length;if(!L(a))return null;const n=new Array(a);for(;a-- >0;)n[a]=e[a];return n},forEachEntry:(e,a)=>{const n=(e&&e[Symbol.iterator]).call(e);let t;for(;(t=n.next())&&!t.done;){const n=t.value;a.call(e,n[0],n[1])}},matchAll:(e,a)=>{let n;const t=[];for(;null!==(n=e.exec(a));)t.push(n);return t},isHTMLForm:ae,hasOwnProperty:ne,hasOwnProp:ne,reduceDescriptors:se,freezeMethods:e=>{se(e,((a,n)=>{if(B(e)&&-1!==["arguments","caller","callee"].indexOf(n))return!1;const t=e[n];B(t)&&(a.enumerable=!1,"writable"in a?a.writable=!1:a.set||(a.set=()=>{throw Error("Can not rewrite read-only method '"+n+"'")}))}))},toObjectSet:(e,a)=>{const n={},t=e=>{e.forEach((e=>{n[e]=!0}))};return O(e)?t(e):t(String(e).split(a)),n},toCamelCase:e=>e.toLowerCase().replace(/[-_\s]([a-z\d])(\w*)/g,(function(e,a,n){return a.toUpperCase()+n})),noop:()=>{},toFiniteNumber:(e,a)=>null!=e&&Number.isFinite(e=+e)?e:a,findKey:Q,global:Y,isContextDefined:X,ALPHABET:re,generateString:(e=16,a=re.ALPHA_DIGIT)=>{let n="";const{length:t}=a;for(;e--;)n+=a[Math.random()*t|0];return n},isSpecCompliantForm:function(e){return!!(e&&B(e.append)&&"FormData"===e[Symbol.toStringTag]&&e[Symbol.iterator])},toJSONObject:e=>{const a=new Array(10),n=(e,t)=>{if(U(e)){if(a.indexOf(e)>=0)return;if(!("toJSON"in e)){a[t]=e;const s=O(e)?[]:{};return K(e,((e,a)=>{const i=n(e,t+1);!F(i)&&(s[a]=i)})),a[t]=void 0,s}}return e};return n(e,0)},isAsyncFn:ce,isThenable:e=>e&&(U(e)||B(e))&&B(e.then)&&B(e.catch),setImmediate:pe,asap:he};function xe(e,a,n,t,s){Error.call(this),Error.captureStackTrace?Error.captureStackTrace(this,this.constructor):this.stack=(new Error).stack,this.message=e,this.name="AxiosError",a&&(this.code=a),n&&(this.config=n),t&&(this.request=t),s&&(this.response=s,this.status=s.status?s.status:null)}fe.inherits(xe,Error,{toJSON:function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:fe.toJSONObject(this.config),code:this.code,status:this.status}}});const ve=xe.prototype,be={};var ge,ye,we,_e;function ke(){if(_e)return we;_e=1;var n=e,t=a.Stream,s=function(){if(ye)return ge;ye=1;var n=a.Stream;function t(){this.source=null,this.dataSize=0,this.maxDataSize=1048576,this.pauseStream=!0,this._maxDataSizeExceeded=!1,this._released=!1,this._bufferedEvents=[]}return ge=t,e.inherits(t,n),t.create=function(e,a){var n=new this;for(var t in a=a||{})n[t]=a[t];n.source=e;var s=e.emit;return e.emit=function(){return n._handleEmit(arguments),s.apply(e,arguments)},e.on("error",(function(){})),n.pauseStream&&e.pause(),n},Object.defineProperty(t.prototype,"readable",{configurable:!0,enumerable:!0,get:function(){return this.source.readable}}),t.prototype.setEncoding=function(){return this.source.setEncoding.apply(this.source,arguments)},t.prototype.resume=function(){this._released||this.release(),this.source.resume()},t.prototype.pause=function(){this.source.pause()},t.prototype.release=function(){this._released=!0,this._bufferedEvents.forEach(function(e){this.emit.apply(this,e)}.bind(this)),this._bufferedEvents=[]},t.prototype.pipe=function(){var e=n.prototype.pipe.apply(this,arguments);return this.resume(),e},t.prototype._handleEmit=function(e){this._released?this.emit.apply(this,e):("data"===e[0]&&(this.dataSize+=e[1].length,this._checkIfMaxDataSizeExceeded()),this._bufferedEvents.push(e))},t.prototype._checkIfMaxDataSizeExceeded=function(){if(!(this._maxDataSizeExceeded||this.dataSize<=this.maxDataSize)){this._maxDataSizeExceeded=!0;var e="DelayedStream#maxDataSize of "+this.maxDataSize+" bytes exceeded.";this.emit("error",new Error(e))}},ge}();function i(){this.writable=!1,this.readable=!0,this.dataSize=0,this.maxDataSize=2097152,this.pauseStreams=!0,this._released=!1,this._streams=[],this._currentStream=null,this._insideLoop=!1,this._pendingNext=!1}return we=i,n.inherits(i,t),i.create=function(e){var a=new this;for(var n in e=e||{})a[n]=e[n];return a},i.isStreamLike=function(e){return"function"!=typeof e&&"string"!=typeof e&&"boolean"!=typeof e&&"number"!=typeof e&&!Buffer.isBuffer(e)},i.prototype.append=function(e){if(i.isStreamLike(e)){if(!(e instanceof s)){var a=s.create(e,{maxDataSize:1/0,pauseStream:this.pauseStreams});e.on("data",this._checkDataSize.bind(this)),e=a}this._handleErrors(e),this.pauseStreams&&e.pause()}return this._streams.push(e),this},i.prototype.pipe=function(e,a){return t.prototype.pipe.call(this,e,a),this.resume(),e},i.prototype._getNext=function(){if(this._currentStream=null,this._insideLoop)this._pendingNext=!0;else{this._insideLoop=!0;try{do{this._pendingNext=!1,this._realGetNext()}while(this._pendingNext)}finally{this._insideLoop=!1}}},i.prototype._realGetNext=function(){var e=this._streams.shift();void 0!==e?"function"==typeof e?e(function(e){i.isStreamLike(e)&&(e.on("data",this._checkDataSize.bind(this)),this._handleErrors(e)),this._pipeNext(e)}.bind(this)):this._pipeNext(e):this.end()},i.prototype._pipeNext=function(e){if(this._currentStream=e,i.isStreamLike(e))return e.on("end",this._getNext.bind(this)),void e.pipe(this,{end:!1});var a=e;this.write(a),this._getNext()},i.prototype._handleErrors=function(e){var a=this;e.on("error",(function(e){a._emitError(e)}))},i.prototype.write=function(e){this.emit("data",e)},i.prototype.pause=function(){this.pauseStreams&&(this.pauseStreams&&this._currentStream&&"function"==typeof this._currentStream.pause&&this._currentStream.pause(),this.emit("pause"))},i.prototype.resume=function(){this._released||(this._released=!0,this.writable=!0,this._getNext()),this.pauseStreams&&this._currentStream&&"function"==typeof this._currentStream.resume&&this._currentStream.resume(),this.emit("resume")},i.prototype.end=function(){this._reset(),this.emit("end")},i.prototype.destroy=function(){this._reset(),this.emit("close")},i.prototype._reset=function(){this.writable=!1,this._streams=[],this._currentStream=null},i.prototype._checkDataSize=function(){if(this._updateDataSize(),!(this.dataSize<=this.maxDataSize)){var e="DelayedStream#maxDataSize of "+this.maxDataSize+" bytes exceeded.";this._emitError(new Error(e))}},i.prototype._updateDataSize=function(){this.dataSize=0;var e=this;this._streams.forEach((function(a){a.dataSize&&(e.dataSize+=a.dataSize)})),this._currentStream&&this._currentStream.dataSize&&(this.dataSize+=this._currentStream.dataSize)},i.prototype._emitError=function(e){this._reset(),this.emit("error",e)},we}["ERR_BAD_OPTION_VALUE","ERR_BAD_OPTION","ECONNABORTED","ETIMEDOUT","ERR_NETWORK","ERR_FR_TOO_MANY_REDIRECTS","ERR_DEPRECATED","ERR_BAD_RESPONSE","ERR_BAD_REQUEST","ERR_CANCELED","ERR_NOT_SUPPORT","ERR_INVALID_URL"].forEach((e=>{be[e]={value:e}})),Object.defineProperties(xe,be),Object.defineProperty(ve,"isAxiosError",{value:!0}),xe.from=(e,a,n,t,s,i)=>{const o=Object.create(ve);return fe.toFlatObject(e,o,(function(e){return e!==Error.prototype}),(e=>"isAxiosError"!==e)),xe.call(o,e.message,a,n,t,s),o.cause=e,o.name=e.name,i&&Object.assign(o,i),o};var je,Re,Se,Ee,Te,qe,Ce,Ae,Oe,Fe,De,Pe,Be,Le,Ue,ze,Ie,Ne={},Me={"application/1d-interleaved-parityfec":{source:"iana"},"application/3gpdash-qoe-report+xml":{source:"iana",charset:"UTF-8",compressible:!0},"application/3gpp-ims+xml":{source:"iana",compressible:!0},"application/3gpphal+json":{source:"iana",compressible:!0},"application/3gpphalforms+json":{source:"iana",compressible:!0},"application/a2l":{source:"iana"},"application/ace+cbor":{source:"iana"},"application/activemessage":{source:"iana"},"application/activity+json":{source:"iana",compressible:!0},"application/alto-costmap+json":{source:"iana",compressible:!0},"application/alto-costmapfilter+json":{source:"iana",compressible:!0},"application/alto-directory+json":{source:"iana",compressible:!0},"application/alto-endpointcost+json":{source:"iana",compressible:!0},"application/alto-endpointcostparams+json":{source:"iana",compressible:!0},"application/alto-endpointprop+json":{source:"iana",compressible:!0},"application/alto-endpointpropparams+json":{source:"iana",compressible:!0},"application/alto-error+json":{source:"iana",compressible:!0},"application/alto-networkmap+json":{source:"iana",compressible:!0},"application/alto-networkmapfilter+json":{source:"iana",compressible:!0},"application/alto-updatestreamcontrol+json":{source:"iana",compressible:!0},"application/alto-updatestreamparams+json":{source:"iana",compressible:!0},"application/aml":{source:"iana"},"application/andrew-inset":{source:"iana",extensions:["ez"]},"application/applefile":{source:"iana"},"application/applixware":{source:"apache",extensions:["aw"]},"application/at+jwt":{source:"iana"},"application/atf":{source:"iana"},"application/atfx":{source:"iana"},"application/atom+xml":{source:"iana",compressible:!0,extensions:["atom"]},"application/atomcat+xml":{source:"iana",compressible:!0,extensions:["atomcat"]},"application/atomdeleted+xml":{source:"iana",compressible:!0,extensions:["atomdeleted"]},"application/atomicmail":{source:"iana"},"application/atomsvc+xml":{source:"iana",compressible:!0,extensions:["atomsvc"]},"application/atsc-dwd+xml":{source:"iana",compressible:!0,extensions:["dwd"]},"application/atsc-dynamic-event-message":{source:"iana"},"application/atsc-held+xml":{source:"iana",compressible:!0,extensions:["held"]},"application/atsc-rdt+json":{source:"iana",compressible:!0},"application/atsc-rsat+xml":{source:"iana",compressible:!0,extensions:["rsat"]},"application/atxml":{source:"iana"},"application/auth-policy+xml":{source:"iana",compressible:!0},"application/bacnet-xdd+zip":{source:"iana",compressible:!1},"application/batch-smtp":{source:"iana"},"application/bdoc":{compressible:!1,extensions:["bdoc"]},"application/beep+xml":{source:"iana",charset:"UTF-8",compressible:!0},"application/calendar+json":{source:"iana",compressible:!0},"application/calendar+xml":{source:"iana",compressible:!0,extensions:["xcs"]},"application/call-completion":{source:"iana"},"application/cals-1840":{source:"iana"},"application/captive+json":{source:"iana",compressible:!0},"application/cbor":{source:"iana"},"application/cbor-seq":{source:"iana"},"application/cccex":{source:"iana"},"application/ccmp+xml":{source:"iana",compressible:!0},"application/ccxml+xml":{source:"iana",compressible:!0,extensions:["ccxml"]},"application/cdfx+xml":{source:"iana",compressible:!0,extensions:["cdfx"]},"application/cdmi-capability":{source:"iana",extensions:["cdmia"]},"application/cdmi-container":{source:"iana",extensions:["cdmic"]},"application/cdmi-domain":{source:"iana",extensions:["cdmid"]},"application/cdmi-object":{source:"iana",extensions:["cdmio"]},"application/cdmi-queue":{source:"iana",extensions:["cdmiq"]},"application/cdni":{source:"iana"},"application/cea":{source:"iana"},"application/cea-2018+xml":{source:"iana",compressible:!0},"application/cellml+xml":{source:"iana",compressible:!0},"application/cfw":{source:"iana"},"application/city+json":{source:"iana",compressible:!0},"application/clr":{source:"iana"},"application/clue+xml":{source:"iana",compressible:!0},"application/clue_info+xml":{source:"iana",compressible:!0},"application/cms":{source:"iana"},"application/cnrp+xml":{source:"iana",compressible:!0},"application/coap-group+json":{source:"iana",compressible:!0},"application/coap-payload":{source:"iana"},"application/commonground":{source:"iana"},"application/conference-info+xml":{source:"iana",compressible:!0},"application/cose":{source:"iana"},"application/cose-key":{source:"iana"},"application/cose-key-set":{source:"iana"},"application/cpl+xml":{source:"iana",compressible:!0,extensions:["cpl"]},"application/csrattrs":{source:"iana"},"application/csta+xml":{source:"iana",compressible:!0},"application/cstadata+xml":{source:"iana",compressible:!0},"application/csvm+json":{source:"iana",compressible:!0},"application/cu-seeme":{source:"apache",extensions:["cu"]},"application/cwt":{source:"iana"},"application/cybercash":{source:"iana"},"application/dart":{compressible:!0},"application/dash+xml":{source:"iana",compressible:!0,extensions:["mpd"]},"application/dash-patch+xml":{source:"iana",compressible:!0,extensions:["mpp"]},"application/dashdelta":{source:"iana"},"application/davmount+xml":{source:"iana",compressible:!0,extensions:["davmount"]},"application/dca-rft":{source:"iana"},"application/dcd":{source:"iana"},"application/dec-dx":{source:"iana"},"application/dialog-info+xml":{source:"iana",compressible:!0},"application/dicom":{source:"iana"},"application/dicom+json":{source:"iana",compressible:!0},"application/dicom+xml":{source:"iana",compressible:!0},"application/dii":{source:"iana"},"application/dit":{source:"iana"},"application/dns":{source:"iana"},"application/dns+json":{source:"iana",compressible:!0},"application/dns-message":{source:"iana"},"application/docbook+xml":{source:"apache",compressible:!0,extensions:["dbk"]},"application/dots+cbor":{source:"iana"},"application/dskpp+xml":{source:"iana",compressible:!0},"application/dssc+der":{source:"iana",extensions:["dssc"]},"application/dssc+xml":{source:"iana",compressible:!0,extensions:["xdssc"]},"application/dvcs":{source:"iana"},"application/ecmascript":{source:"iana",compressible:!0,extensions:["es","ecma"]},"application/edi-consent":{source:"iana"},"application/edi-x12":{source:"iana",compressible:!1},"application/edifact":{source:"iana",compressible:!1},"application/efi":{source:"iana"},"application/elm+json":{source:"iana",charset:"UTF-8",compressible:!0},"application/elm+xml":{source:"iana",compressible:!0},"application/emergencycalldata.cap+xml":{source:"iana",charset:"UTF-8",compressible:!0},"application/emergencycalldata.comment+xml":{source:"iana",compressible:!0},"application/emergencycalldata.control+xml":{source:"iana",compressible:!0},"application/emergencycalldata.deviceinfo+xml":{source:"iana",compressible:!0},"application/emergencycalldata.ecall.msd":{source:"iana"},"application/emergencycalldata.providerinfo+xml":{source:"iana",compressible:!0},"application/emergencycalldata.serviceinfo+xml":{source:"iana",compressible:!0},"application/emergencycalldata.subscriberinfo+xml":{source:"iana",compressible:!0},"application/emergencycalldata.veds+xml":{source:"iana",compressible:!0},"application/emma+xml":{source:"iana",compressible:!0,extensions:["emma"]},"application/emotionml+xml":{source:"iana",compressible:!0,extensions:["emotionml"]},"application/encaprtp":{source:"iana"},"application/epp+xml":{source:"iana",compressible:!0},"application/epub+zip":{source:"iana",compressible:!1,extensions:["epub"]},"application/eshop":{source:"iana"},"application/exi":{source:"iana",extensions:["exi"]},"application/expect-ct-report+json":{source:"iana",compressible:!0},"application/express":{source:"iana",extensions:["exp"]},"application/fastinfoset":{source:"iana"},"application/fastsoap":{source:"iana"},"application/fdt+xml":{source:"iana",compressible:!0,extensions:["fdt"]},"application/fhir+json":{source:"iana",charset:"UTF-8",compressible:!0},"application/fhir+xml":{source:"iana",charset:"UTF-8",compressible:!0},"application/fido.trusted-apps+json":{compressible:!0},"application/fits":{source:"iana"},"application/flexfec":{source:"iana"},"application/font-sfnt":{source:"iana"},"application/font-tdpfr":{source:"iana",extensions:["pfr"]},"application/font-woff":{source:"iana",compressible:!1},"application/framework-attributes+xml":{source:"iana",compressible:!0},"application/geo+json":{source:"iana",compressible:!0,extensions:["geojson"]},"application/geo+json-seq":{source:"iana"},"application/geopackage+sqlite3":{source:"iana"},"application/geoxacml+xml":{source:"iana",compressible:!0},"application/gltf-buffer":{source:"iana"},"application/gml+xml":{source:"iana",compressible:!0,extensions:["gml"]},"application/gpx+xml":{source:"apache",compressible:!0,extensions:["gpx"]},"application/gxf":{source:"apache",extensions:["gxf"]},"application/gzip":{source:"iana",compressible:!1,extensions:["gz"]},"application/h224":{source:"iana"},"application/held+xml":{source:"iana",compressible:!0},"application/hjson":{extensions:["hjson"]},"application/http":{source:"iana"},"application/hyperstudio":{source:"iana",extensions:["stk"]},"application/ibe-key-request+xml":{source:"iana",compressible:!0},"application/ibe-pkg-reply+xml":{source:"iana",compressible:!0},"application/ibe-pp-data":{source:"iana"},"application/iges":{source:"iana"},"application/im-iscomposing+xml":{source:"iana",charset:"UTF-8",compressible:!0},"application/index":{source:"iana"},"application/index.cmd":{source:"iana"},"application/index.obj":{source:"iana"},"application/index.response":{source:"iana"},"application/index.vnd":{source:"iana"},"application/inkml+xml":{source:"iana",compressible:!0,extensions:["ink","inkml"]},"application/iotp":{source:"iana"},"application/ipfix":{source:"iana",extensions:["ipfix"]},"application/ipp":{source:"iana"},"application/isup":{source:"iana"},"application/its+xml":{source:"iana",compressible:!0,extensions:["its"]},"application/java-archive":{source:"apache",compressible:!1,extensions:["jar","war","ear"]},"application/java-serialized-object":{source:"apache",compressible:!1,extensions:["ser"]},"application/java-vm":{source:"apache",compressible:!1,extensions:["class"]},"application/javascript":{source:"iana",charset:"UTF-8",compressible:!0,extensions:["js","mjs"]},"application/jf2feed+json":{source:"iana",compressible:!0},"application/jose":{source:"iana"},"application/jose+json":{source:"iana",compressible:!0},"application/jrd+json":{source:"iana",compressible:!0},"application/jscalendar+json":{source:"iana",compressible:!0},"application/json":{source:"iana",charset:"UTF-8",compressible:!0,extensions:["json","map"]},"application/json-patch+json":{source:"iana",compressible:!0},"application/json-seq":{source:"iana"},"application/json5":{extensions:["json5"]},"application/jsonml+json":{source:"apache",compressible:!0,extensions:["jsonml"]},"application/jwk+json":{source:"iana",compressible:!0},"application/jwk-set+json":{source:"iana",compressible:!0},"application/jwt":{source:"iana"},"application/kpml-request+xml":{source:"iana",compressible:!0},"application/kpml-response+xml":{source:"iana",compressible:!0},"application/ld+json":{source:"iana",compressible:!0,extensions:["jsonld"]},"application/lgr+xml":{source:"iana",compressible:!0,extensions:["lgr"]},"application/link-format":{source:"iana"},"application/load-control+xml":{source:"iana",compressible:!0},"application/lost+xml":{source:"iana",compressible:!0,extensions:["lostxml"]},"application/lostsync+xml":{source:"iana",compressible:!0},"application/lpf+zip":{source:"iana",compressible:!1},"application/lxf":{source:"iana"},"application/mac-binhex40":{source:"iana",extensions:["hqx"]},"application/mac-compactpro":{source:"apache",extensions:["cpt"]},"application/macwriteii":{source:"iana"},"application/mads+xml":{source:"iana",compressible:!0,extensions:["mads"]},"application/manifest+json":{source:"iana",charset:"UTF-8",compressible:!0,extensions:["webmanifest"]},"application/marc":{source:"iana",extensions:["mrc"]},"application/marcxml+xml":{source:"iana",compressible:!0,extensions:["mrcx"]},"application/mathematica":{source:"iana",extensions:["ma","nb","mb"]},"application/mathml+xml":{source:"iana",compressible:!0,extensions:["mathml"]},"application/mathml-content+xml":{source:"iana",compressible:!0},"application/mathml-presentation+xml":{source:"iana",compressible:!0},"application/mbms-associated-procedure-description+xml":{source:"iana",compressible:!0},"application/mbms-deregister+xml":{source:"iana",compressible:!0},"application/mbms-envelope+xml":{source:"iana",compressible:!0},"application/mbms-msk+xml":{source:"iana",compressible:!0},"application/mbms-msk-response+xml":{source:"iana",compressible:!0},"application/mbms-protection-description+xml":{source:"iana",compressible:!0},"application/mbms-reception-report+xml":{source:"iana",compressible:!0},"application/mbms-register+xml":{source:"iana",compressible:!0},"application/mbms-register-response+xml":{source:"iana",compressible:!0},"application/mbms-schedule+xml":{source:"iana",compressible:!0},"application/mbms-user-service-description+xml":{source:"iana",compressible:!0},"application/mbox":{source:"iana",extensions:["mbox"]},"application/media-policy-dataset+xml":{source:"iana",compressible:!0,extensions:["mpf"]},"application/media_control+xml":{source:"iana",compressible:!0},"application/mediaservercontrol+xml":{source:"iana",compressible:!0,extensions:["mscml"]},"application/merge-patch+json":{source:"iana",compressible:!0},"application/metalink+xml":{source:"apache",compressible:!0,extensions:["metalink"]},"application/metalink4+xml":{source:"iana",compressible:!0,extensions:["meta4"]},"application/mets+xml":{source:"iana",compressible:!0,extensions:["mets"]},"application/mf4":{source:"iana"},"application/mikey":{source:"iana"},"application/mipc":{source:"iana"},"application/missing-blocks+cbor-seq":{source:"iana"},"application/mmt-aei+xml":{source:"iana",compressible:!0,extensions:["maei"]},"application/mmt-usd+xml":{source:"iana",compressible:!0,extensions:["musd"]},"application/mods+xml":{source:"iana",compressible:!0,extensions:["mods"]},"application/moss-keys":{source:"iana"},"application/moss-signature":{source:"iana"},"application/mosskey-data":{source:"iana"},"application/mosskey-request":{source:"iana"},"application/mp21":{source:"iana",extensions:["m21","mp21"]},"application/mp4":{source:"iana",extensions:["mp4s","m4p"]},"application/mpeg4-generic":{source:"iana"},"application/mpeg4-iod":{source:"iana"},"application/mpeg4-iod-xmt":{source:"iana"},"application/mrb-consumer+xml":{source:"iana",compressible:!0},"application/mrb-publish+xml":{source:"iana",compressible:!0},"application/msc-ivr+xml":{source:"iana",charset:"UTF-8",compressible:!0},"application/msc-mixer+xml":{source:"iana",charset:"UTF-8",compressible:!0},"application/msword":{source:"iana",compressible:!1,extensions:["doc","dot"]},"application/mud+json":{source:"iana",compressible:!0},"application/multipart-core":{source:"iana"},"application/mxf":{source:"iana",extensions:["mxf"]},"application/n-quads":{source:"iana",extensions:["nq"]},"application/n-triples":{source:"iana",extensions:["nt"]},"application/nasdata":{source:"iana"},"application/news-checkgroups":{source:"iana",charset:"US-ASCII"},"application/news-groupinfo":{source:"iana",charset:"US-ASCII"},"application/news-transmission":{source:"iana"},"application/nlsml+xml":{source:"iana",compressible:!0},"application/node":{source:"iana",extensions:["cjs"]},"application/nss":{source:"iana"},"application/oauth-authz-req+jwt":{source:"iana"},"application/oblivious-dns-message":{source:"iana"},"application/ocsp-request":{source:"iana"},"application/ocsp-response":{source:"iana"},"application/octet-stream":{source:"iana",compressible:!1,extensions:["bin","dms","lrf","mar","so","dist","distz","pkg","bpk","dump","elc","deploy","exe","dll","deb","dmg","iso","img","msi","msp","msm","buffer"]},"application/oda":{source:"iana",extensions:["oda"]},"application/odm+xml":{source:"iana",compressible:!0},"application/odx":{source:"iana"},"application/oebps-package+xml":{source:"iana",compressible:!0,extensions:["opf"]},"application/ogg":{source:"iana",compressible:!1,extensions:["ogx"]},"application/omdoc+xml":{source:"apache",compressible:!0,extensions:["omdoc"]},"application/onenote":{source:"apache",extensions:["onetoc","onetoc2","onetmp","onepkg"]},"application/opc-nodeset+xml":{source:"iana",compressible:!0},"application/oscore":{source:"iana"},"application/oxps":{source:"iana",extensions:["oxps"]},"application/p21":{source:"iana"},"application/p21+zip":{source:"iana",compressible:!1},"application/p2p-overlay+xml":{source:"iana",compressible:!0,extensions:["relo"]},"application/parityfec":{source:"iana"},"application/passport":{source:"iana"},"application/patch-ops-error+xml":{source:"iana",compressible:!0,extensions:["xer"]},"application/pdf":{source:"iana",compressible:!1,extensions:["pdf"]},"application/pdx":{source:"iana"},"application/pem-certificate-chain":{source:"iana"},"application/pgp-encrypted":{source:"iana",compressible:!1,extensions:["pgp"]},"application/pgp-keys":{source:"iana",extensions:["asc"]},"application/pgp-signature":{source:"iana",extensions:["asc","sig"]},"application/pics-rules":{source:"apache",extensions:["prf"]},"application/pidf+xml":{source:"iana",charset:"UTF-8",compressible:!0},"application/pidf-diff+xml":{source:"iana",charset:"UTF-8",compressible:!0},"application/pkcs10":{source:"iana",extensions:["p10"]},"application/pkcs12":{source:"iana"},"application/pkcs7-mime":{source:"iana",extensions:["p7m","p7c"]},"application/pkcs7-signature":{source:"iana",extensions:["p7s"]},"application/pkcs8":{source:"iana",extensions:["p8"]},"application/pkcs8-encrypted":{source:"iana"},"application/pkix-attr-cert":{source:"iana",extensions:["ac"]},"application/pkix-cert":{source:"iana",extensions:["cer"]},"application/pkix-crl":{source:"iana",extensions:["crl"]},"application/pkix-pkipath":{source:"iana",extensions:["pkipath"]},"application/pkixcmp":{source:"iana",extensions:["pki"]},"application/pls+xml":{source:"iana",compressible:!0,extensions:["pls"]},"application/poc-settings+xml":{source:"iana",charset:"UTF-8",compressible:!0},"application/postscript":{source:"iana",compressible:!0,extensions:["ai","eps","ps"]},"application/ppsp-tracker+json":{source:"iana",compressible:!0},"application/problem+json":{source:"iana",compressible:!0},"application/problem+xml":{source:"iana",compressible:!0},"application/provenance+xml":{source:"iana",compressible:!0,extensions:["provx"]},"application/prs.alvestrand.titrax-sheet":{source:"iana"},"application/prs.cww":{source:"iana",extensions:["cww"]},"application/prs.cyn":{source:"iana",charset:"7-BIT"},"application/prs.hpub+zip":{source:"iana",compressible:!1},"application/prs.nprend":{source:"iana"},"application/prs.plucker":{source:"iana"},"application/prs.rdf-xml-crypt":{source:"iana"},"application/prs.xsf+xml":{source:"iana",compressible:!0},"application/pskc+xml":{source:"iana",compressible:!0,extensions:["pskcxml"]},"application/pvd+json":{source:"iana",compressible:!0},"application/qsig":{source:"iana"},"application/raml+yaml":{compressible:!0,extensions:["raml"]},"application/raptorfec":{source:"iana"},"application/rdap+json":{source:"iana",compressible:!0},"application/rdf+xml":{source:"iana",compressible:!0,extensions:["rdf","owl"]},"application/reginfo+xml":{source:"iana",compressible:!0,extensions:["rif"]},"application/relax-ng-compact-syntax":{source:"iana",extensions:["rnc"]},"application/remote-printing":{source:"iana"},"application/reputon+json":{source:"iana",compressible:!0},"application/resource-lists+xml":{source:"iana",compressible:!0,extensions:["rl"]},"application/resource-lists-diff+xml":{source:"iana",compressible:!0,extensions:["rld"]},"application/rfc+xml":{source:"iana",compressible:!0},"application/riscos":{source:"iana"},"application/rlmi+xml":{source:"iana",compressible:!0},"application/rls-services+xml":{source:"iana",compressible:!0,extensions:["rs"]},"application/route-apd+xml":{source:"iana",compressible:!0,extensions:["rapd"]},"application/route-s-tsid+xml":{source:"iana",compressible:!0,extensions:["sls"]},"application/route-usd+xml":{source:"iana",compressible:!0,extensions:["rusd"]},"application/rpki-ghostbusters":{source:"iana",extensions:["gbr"]},"application/rpki-manifest":{source:"iana",extensions:["mft"]},"application/rpki-publication":{source:"iana"},"application/rpki-roa":{source:"iana",extensions:["roa"]},"application/rpki-updown":{source:"iana"},"application/rsd+xml":{source:"apache",compressible:!0,extensions:["rsd"]},"application/rss+xml":{source:"apache",compressible:!0,extensions:["rss"]},"application/rtf":{source:"iana",compressible:!0,extensions:["rtf"]},"application/rtploopback":{source:"iana"},"application/rtx":{source:"iana"},"application/samlassertion+xml":{source:"iana",compressible:!0},"application/samlmetadata+xml":{source:"iana",compressible:!0},"application/sarif+json":{source:"iana",compressible:!0},"application/sarif-external-properties+json":{source:"iana",compressible:!0},"application/sbe":{source:"iana"},"application/sbml+xml":{source:"iana",compressible:!0,extensions:["sbml"]},"application/scaip+xml":{source:"iana",compressible:!0},"application/scim+json":{source:"iana",compressible:!0},"application/scvp-cv-request":{source:"iana",extensions:["scq"]},"application/scvp-cv-response":{source:"iana",extensions:["scs"]},"application/scvp-vp-request":{source:"iana",extensions:["spq"]},"application/scvp-vp-response":{source:"iana",extensions:["spp"]},"application/sdp":{source:"iana",extensions:["sdp"]},"application/secevent+jwt":{source:"iana"},"application/senml+cbor":{source:"iana"},"application/senml+json":{source:"iana",compressible:!0},"application/senml+xml":{source:"iana",compressible:!0,extensions:["senmlx"]},"application/senml-etch+cbor":{source:"iana"},"application/senml-etch+json":{source:"iana",compressible:!0},"application/senml-exi":{source:"iana"},"application/sensml+cbor":{source:"iana"},"application/sensml+json":{source:"iana",compressible:!0},"application/sensml+xml":{source:"iana",compressible:!0,extensions:["sensmlx"]},"application/sensml-exi":{source:"iana"},"application/sep+xml":{source:"iana",compressible:!0},"application/sep-exi":{source:"iana"},"application/session-info":{source:"iana"},"application/set-payment":{source:"iana"},"application/set-payment-initiation":{source:"iana",extensions:["setpay"]},"application/set-registration":{source:"iana"},"application/set-registration-initiation":{source:"iana",extensions:["setreg"]},"application/sgml":{source:"iana"},"application/sgml-open-catalog":{source:"iana"},"application/shf+xml":{source:"iana",compressible:!0,extensions:["shf"]},"application/sieve":{source:"iana",extensions:["siv","sieve"]},"application/simple-filter+xml":{source:"iana",compressible:!0},"application/simple-message-summary":{source:"iana"},"application/simplesymbolcontainer":{source:"iana"},"application/sipc":{source:"iana"},"application/slate":{source:"iana"},"application/smil":{source:"iana"},"application/smil+xml":{source:"iana",compressible:!0,extensions:["smi","smil"]},"application/smpte336m":{source:"iana"},"application/soap+fastinfoset":{source:"iana"},"application/soap+xml":{source:"iana",compressible:!0},"application/sparql-query":{source:"iana",extensions:["rq"]},"application/sparql-results+xml":{source:"iana",compressible:!0,extensions:["srx"]},"application/spdx+json":{source:"iana",compressible:!0},"application/spirits-event+xml":{source:"iana",compressible:!0},"application/sql":{source:"iana"},"application/srgs":{source:"iana",extensions:["gram"]},"application/srgs+xml":{source:"iana",compressible:!0,extensions:["grxml"]},"application/sru+xml":{source:"iana",compressible:!0,extensions:["sru"]},"application/ssdl+xml":{source:"apache",compressible:!0,extensions:["ssdl"]},"application/ssml+xml":{source:"iana",compressible:!0,extensions:["ssml"]},"application/stix+json":{source:"iana",compressible:!0},"application/swid+xml":{source:"iana",compressible:!0,extensions:["swidtag"]},"application/tamp-apex-update":{source:"iana"},"application/tamp-apex-update-confirm":{source:"iana"},"application/tamp-community-update":{source:"iana"},"application/tamp-community-update-confirm":{source:"iana"},"application/tamp-error":{source:"iana"},"application/tamp-sequence-adjust":{source:"iana"},"application/tamp-sequence-adjust-confirm":{source:"iana"},"application/tamp-status-query":{source:"iana"},"application/tamp-status-response":{source:"iana"},"application/tamp-update":{source:"iana"},"application/tamp-update-confirm":{source:"iana"},"application/tar":{compressible:!0},"application/taxii+json":{source:"iana",compressible:!0},"application/td+json":{source:"iana",compressible:!0},"application/tei+xml":{source:"iana",compressible:!0,extensions:["tei","teicorpus"]},"application/tetra_isi":{source:"iana"},"application/thraud+xml":{source:"iana",compressible:!0,extensions:["tfi"]},"application/timestamp-query":{source:"iana"},"application/timestamp-reply":{source:"iana"},"application/timestamped-data":{source:"iana",extensions:["tsd"]},"application/tlsrpt+gzip":{source:"iana"},"application/tlsrpt+json":{source:"iana",compressible:!0},"application/tnauthlist":{source:"iana"},"application/token-introspection+jwt":{source:"iana"},"application/toml":{compressible:!0,extensions:["toml"]},"application/trickle-ice-sdpfrag":{source:"iana"},"application/trig":{source:"iana",extensions:["trig"]},"application/ttml+xml":{source:"iana",compressible:!0,extensions:["ttml"]},"application/tve-trigger":{source:"iana"},"application/tzif":{source:"iana"},"application/tzif-leap":{source:"iana"},"application/ubjson":{compressible:!1,extensions:["ubj"]},"application/ulpfec":{source:"iana"},"application/urc-grpsheet+xml":{source:"iana",compressible:!0},"application/urc-ressheet+xml":{source:"iana",compressible:!0,extensions:["rsheet"]},"application/urc-targetdesc+xml":{source:"iana",compressible:!0,extensions:["td"]},"application/urc-uisocketdesc+xml":{source:"iana",compressible:!0},"application/vcard+json":{source:"iana",compressible:!0},"application/vcard+xml":{source:"iana",compressible:!0},"application/vemmi":{source:"iana"},"application/vividence.scriptfile":{source:"apache"},"application/vnd.1000minds.decision-model+xml":{source:"iana",compressible:!0,extensions:["1km"]},"application/vnd.3gpp-prose+xml":{source:"iana",compressible:!0},"application/vnd.3gpp-prose-pc3ch+xml":{source:"iana",compressible:!0},"application/vnd.3gpp-v2x-local-service-information":{source:"iana"},"application/vnd.3gpp.5gnas":{source:"iana"},"application/vnd.3gpp.access-transfer-events+xml":{source:"iana",compressible:!0},"application/vnd.3gpp.bsf+xml":{source:"iana",compressible:!0},"application/vnd.3gpp.gmop+xml":{source:"iana",compressible:!0},"application/vnd.3gpp.gtpc":{source:"iana"},"application/vnd.3gpp.interworking-data":{source:"iana"},"application/vnd.3gpp.lpp":{source:"iana"},"application/vnd.3gpp.mc-signalling-ear":{source:"iana"},"application/vnd.3gpp.mcdata-affiliation-command+xml":{source:"iana",compressible:!0},"application/vnd.3gpp.mcdata-info+xml":{source:"iana",compressible:!0},"application/vnd.3gpp.mcdata-payload":{source:"iana"},"application/vnd.3gpp.mcdata-service-config+xml":{source:"iana",compressible:!0},"application/vnd.3gpp.mcdata-signalling":{source:"iana"},"application/vnd.3gpp.mcdata-ue-config+xml":{source:"iana",compressible:!0},"application/vnd.3gpp.mcdata-user-profile+xml":{source:"iana",compressible:!0},"application/vnd.3gpp.mcptt-affiliation-command+xml":{source:"iana",compressible:!0},"application/vnd.3gpp.mcptt-floor-request+xml":{source:"iana",compressible:!0},"application/vnd.3gpp.mcptt-info+xml":{source:"iana",compressible:!0},"application/vnd.3gpp.mcptt-location-info+xml":{source:"iana",compressible:!0},"application/vnd.3gpp.mcptt-mbms-usage-info+xml":{source:"iana",compressible:!0},"application/vnd.3gpp.mcptt-service-config+xml":{source:"iana",compressible:!0},"application/vnd.3gpp.mcptt-signed+xml":{source:"iana",compressible:!0},"application/vnd.3gpp.mcptt-ue-config+xml":{source:"iana",compressible:!0},"application/vnd.3gpp.mcptt-ue-init-config+xml":{source:"iana",compressible:!0},"application/vnd.3gpp.mcptt-user-profile+xml":{source:"iana",compressible:!0},"application/vnd.3gpp.mcvideo-affiliation-command+xml":{source:"iana",compressible:!0},"application/vnd.3gpp.mcvideo-affiliation-info+xml":{source:"iana",compressible:!0},"application/vnd.3gpp.mcvideo-info+xml":{source:"iana",compressible:!0},"application/vnd.3gpp.mcvideo-location-info+xml":{source:"iana",compressible:!0},"application/vnd.3gpp.mcvideo-mbms-usage-info+xml":{source:"iana",compressible:!0},"application/vnd.3gpp.mcvideo-service-config+xml":{source:"iana",compressible:!0},"application/vnd.3gpp.mcvideo-transmission-request+xml":{source:"iana",compressible:!0},"application/vnd.3gpp.mcvideo-ue-config+xml":{source:"iana",compressible:!0},"application/vnd.3gpp.mcvideo-user-profile+xml":{source:"iana",compressible:!0},"application/vnd.3gpp.mid-call+xml":{source:"iana",compressible:!0},"application/vnd.3gpp.ngap":{source:"iana"},"application/vnd.3gpp.pfcp":{source:"iana"},"application/vnd.3gpp.pic-bw-large":{source:"iana",extensions:["plb"]},"application/vnd.3gpp.pic-bw-small":{source:"iana",extensions:["psb"]},"application/vnd.3gpp.pic-bw-var":{source:"iana",extensions:["pvb"]},"application/vnd.3gpp.s1ap":{source:"iana"},"application/vnd.3gpp.sms":{source:"iana"},"application/vnd.3gpp.sms+xml":{source:"iana",compressible:!0},"application/vnd.3gpp.srvcc-ext+xml":{source:"iana",compressible:!0},"application/vnd.3gpp.srvcc-info+xml":{source:"iana",compressible:!0},"application/vnd.3gpp.state-and-event-info+xml":{source:"iana",compressible:!0},"application/vnd.3gpp.ussd+xml":{source:"iana",compressible:!0},"application/vnd.3gpp2.bcmcsinfo+xml":{source:"iana",compressible:!0},"application/vnd.3gpp2.sms":{source:"iana"},"application/vnd.3gpp2.tcap":{source:"iana",extensions:["tcap"]},"application/vnd.3lightssoftware.imagescal":{source:"iana"},"application/vnd.3m.post-it-notes":{source:"iana",extensions:["pwn"]},"application/vnd.accpac.simply.aso":{source:"iana",extensions:["aso"]},"application/vnd.accpac.simply.imp":{source:"iana",extensions:["imp"]},"application/vnd.acucobol":{source:"iana",extensions:["acu"]},"application/vnd.acucorp":{source:"iana",extensions:["atc","acutc"]},"application/vnd.adobe.air-application-installer-package+zip":{source:"apache",compressible:!1,extensions:["air"]},"application/vnd.adobe.flash.movie":{source:"iana"},"application/vnd.adobe.formscentral.fcdt":{source:"iana",extensions:["fcdt"]},"application/vnd.adobe.fxp":{source:"iana",extensions:["fxp","fxpl"]},"application/vnd.adobe.partial-upload":{source:"iana"},"application/vnd.adobe.xdp+xml":{source:"iana",compressible:!0,extensions:["xdp"]},"application/vnd.adobe.xfdf":{source:"iana",extensions:["xfdf"]},"application/vnd.aether.imp":{source:"iana"},"application/vnd.afpc.afplinedata":{source:"iana"},"application/vnd.afpc.afplinedata-pagedef":{source:"iana"},"application/vnd.afpc.cmoca-cmresource":{source:"iana"},"application/vnd.afpc.foca-charset":{source:"iana"},"application/vnd.afpc.foca-codedfont":{source:"iana"},"application/vnd.afpc.foca-codepage":{source:"iana"},"application/vnd.afpc.modca":{source:"iana"},"application/vnd.afpc.modca-cmtable":{source:"iana"},"application/vnd.afpc.modca-formdef":{source:"iana"},"application/vnd.afpc.modca-mediummap":{source:"iana"},"application/vnd.afpc.modca-objectcontainer":{source:"iana"},"application/vnd.afpc.modca-overlay":{source:"iana"},"application/vnd.afpc.modca-pagesegment":{source:"iana"},"application/vnd.age":{source:"iana",extensions:["age"]},"application/vnd.ah-barcode":{source:"iana"},"application/vnd.ahead.space":{source:"iana",extensions:["ahead"]},"application/vnd.airzip.filesecure.azf":{source:"iana",extensions:["azf"]},"application/vnd.airzip.filesecure.azs":{source:"iana",extensions:["azs"]},"application/vnd.amadeus+json":{source:"iana",compressible:!0},"application/vnd.amazon.ebook":{source:"apache",extensions:["azw"]},"application/vnd.amazon.mobi8-ebook":{source:"iana"},"application/vnd.americandynamics.acc":{source:"iana",extensions:["acc"]},"application/vnd.amiga.ami":{source:"iana",extensions:["ami"]},"application/vnd.amundsen.maze+xml":{source:"iana",compressible:!0},"application/vnd.android.ota":{source:"iana"},"application/vnd.android.package-archive":{source:"apache",compressible:!1,extensions:["apk"]},"application/vnd.anki":{source:"iana"},"application/vnd.anser-web-certificate-issue-initiation":{source:"iana",extensions:["cii"]},"application/vnd.anser-web-funds-transfer-initiation":{source:"apache",extensions:["fti"]},"application/vnd.antix.game-component":{source:"iana",extensions:["atx"]},"application/vnd.apache.arrow.file":{source:"iana"},"application/vnd.apache.arrow.stream":{source:"iana"},"application/vnd.apache.thrift.binary":{source:"iana"},"application/vnd.apache.thrift.compact":{source:"iana"},"application/vnd.apache.thrift.json":{source:"iana"},"application/vnd.api+json":{source:"iana",compressible:!0},"application/vnd.aplextor.warrp+json":{source:"iana",compressible:!0},"application/vnd.apothekende.reservation+json":{source:"iana",compressible:!0},"application/vnd.apple.installer+xml":{source:"iana",compressible:!0,extensions:["mpkg"]},"application/vnd.apple.keynote":{source:"iana",extensions:["key"]},"application/vnd.apple.mpegurl":{source:"iana",extensions:["m3u8"]},"application/vnd.apple.numbers":{source:"iana",extensions:["numbers"]},"application/vnd.apple.pages":{source:"iana",extensions:["pages"]},"application/vnd.apple.pkpass":{compressible:!1,extensions:["pkpass"]},"application/vnd.arastra.swi":{source:"iana"},"application/vnd.aristanetworks.swi":{source:"iana",extensions:["swi"]},"application/vnd.artisan+json":{source:"iana",compressible:!0},"application/vnd.artsquare":{source:"iana"},"application/vnd.astraea-software.iota":{source:"iana",extensions:["iota"]},"application/vnd.audiograph":{source:"iana",extensions:["aep"]},"application/vnd.autopackage":{source:"iana"},"application/vnd.avalon+json":{source:"iana",compressible:!0},"application/vnd.avistar+xml":{source:"iana",compressible:!0},"application/vnd.balsamiq.bmml+xml":{source:"iana",compressible:!0,extensions:["bmml"]},"application/vnd.balsamiq.bmpr":{source:"iana"},"application/vnd.banana-accounting":{source:"iana"},"application/vnd.bbf.usp.error":{source:"iana"},"application/vnd.bbf.usp.msg":{source:"iana"},"application/vnd.bbf.usp.msg+json":{source:"iana",compressible:!0},"application/vnd.bekitzur-stech+json":{source:"iana",compressible:!0},"application/vnd.bint.med-content":{source:"iana"},"application/vnd.biopax.rdf+xml":{source:"iana",compressible:!0},"application/vnd.blink-idb-value-wrapper":{source:"iana"},"application/vnd.blueice.multipass":{source:"iana",extensions:["mpm"]},"application/vnd.bluetooth.ep.oob":{source:"iana"},"application/vnd.bluetooth.le.oob":{source:"iana"},"application/vnd.bmi":{source:"iana",extensions:["bmi"]},"application/vnd.bpf":{source:"iana"},"application/vnd.bpf3":{source:"iana"},"application/vnd.businessobjects":{source:"iana",extensions:["rep"]},"application/vnd.byu.uapi+json":{source:"iana",compressible:!0},"application/vnd.cab-jscript":{source:"iana"},"application/vnd.canon-cpdl":{source:"iana"},"application/vnd.canon-lips":{source:"iana"},"application/vnd.capasystems-pg+json":{source:"iana",compressible:!0},"application/vnd.cendio.thinlinc.clientconf":{source:"iana"},"application/vnd.century-systems.tcp_stream":{source:"iana"},"application/vnd.chemdraw+xml":{source:"iana",compressible:!0,extensions:["cdxml"]},"application/vnd.chess-pgn":{source:"iana"},"application/vnd.chipnuts.karaoke-mmd":{source:"iana",extensions:["mmd"]},"application/vnd.ciedi":{source:"iana"},"application/vnd.cinderella":{source:"iana",extensions:["cdy"]},"application/vnd.cirpack.isdn-ext":{source:"iana"},"application/vnd.citationstyles.style+xml":{source:"iana",compressible:!0,extensions:["csl"]},"application/vnd.claymore":{source:"iana",extensions:["cla"]},"application/vnd.cloanto.rp9":{source:"iana",extensions:["rp9"]},"application/vnd.clonk.c4group":{source:"iana",extensions:["c4g","c4d","c4f","c4p","c4u"]},"application/vnd.cluetrust.cartomobile-config":{source:"iana",extensions:["c11amc"]},"application/vnd.cluetrust.cartomobile-config-pkg":{source:"iana",extensions:["c11amz"]},"application/vnd.coffeescript":{source:"iana"},"application/vnd.collabio.xodocuments.document":{source:"iana"},"application/vnd.collabio.xodocuments.document-template":{source:"iana"},"application/vnd.collabio.xodocuments.presentation":{source:"iana"},"application/vnd.collabio.xodocuments.presentation-template":{source:"iana"},"application/vnd.collabio.xodocuments.spreadsheet":{source:"iana"},"application/vnd.collabio.xodocuments.spreadsheet-template":{source:"iana"},"application/vnd.collection+json":{source:"iana",compressible:!0},"application/vnd.collection.doc+json":{source:"iana",compressible:!0},"application/vnd.collection.next+json":{source:"iana",compressible:!0},"application/vnd.comicbook+zip":{source:"iana",compressible:!1},"application/vnd.comicbook-rar":{source:"iana"},"application/vnd.commerce-battelle":{source:"iana"},"application/vnd.commonspace":{source:"iana",extensions:["csp"]},"application/vnd.contact.cmsg":{source:"iana",extensions:["cdbcmsg"]},"application/vnd.coreos.ignition+json":{source:"iana",compressible:!0},"application/vnd.cosmocaller":{source:"iana",extensions:["cmc"]},"application/vnd.crick.clicker":{source:"iana",extensions:["clkx"]},"application/vnd.crick.clicker.keyboard":{source:"iana",extensions:["clkk"]},"application/vnd.crick.clicker.palette":{source:"iana",extensions:["clkp"]},"application/vnd.crick.clicker.template":{source:"iana",extensions:["clkt"]},"application/vnd.crick.clicker.wordbank":{source:"iana",extensions:["clkw"]},"application/vnd.criticaltools.wbs+xml":{source:"iana",compressible:!0,extensions:["wbs"]},"application/vnd.cryptii.pipe+json":{source:"iana",compressible:!0},"application/vnd.crypto-shade-file":{source:"iana"},"application/vnd.cryptomator.encrypted":{source:"iana"},"application/vnd.cryptomator.vault":{source:"iana"},"application/vnd.ctc-posml":{source:"iana",extensions:["pml"]},"application/vnd.ctct.ws+xml":{source:"iana",compressible:!0},"application/vnd.cups-pdf":{source:"iana"},"application/vnd.cups-postscript":{source:"iana"},"application/vnd.cups-ppd":{source:"iana",extensions:["ppd"]},"application/vnd.cups-raster":{source:"iana"},"application/vnd.cups-raw":{source:"iana"},"application/vnd.curl":{source:"iana"},"application/vnd.curl.car":{source:"apache",extensions:["car"]},"application/vnd.curl.pcurl":{source:"apache",extensions:["pcurl"]},"application/vnd.cyan.dean.root+xml":{source:"iana",compressible:!0},"application/vnd.cybank":{source:"iana"},"application/vnd.cyclonedx+json":{source:"iana",compressible:!0},"application/vnd.cyclonedx+xml":{source:"iana",compressible:!0},"application/vnd.d2l.coursepackage1p0+zip":{source:"iana",compressible:!1},"application/vnd.d3m-dataset":{source:"iana"},"application/vnd.d3m-problem":{source:"iana"},"application/vnd.dart":{source:"iana",compressible:!0,extensions:["dart"]},"application/vnd.data-vision.rdz":{source:"iana",extensions:["rdz"]},"application/vnd.datapackage+json":{source:"iana",compressible:!0},"application/vnd.dataresource+json":{source:"iana",compressible:!0},"application/vnd.dbf":{source:"iana",extensions:["dbf"]},"application/vnd.debian.binary-package":{source:"iana"},"application/vnd.dece.data":{source:"iana",extensions:["uvf","uvvf","uvd","uvvd"]},"application/vnd.dece.ttml+xml":{source:"iana",compressible:!0,extensions:["uvt","uvvt"]},"application/vnd.dece.unspecified":{source:"iana",extensions:["uvx","uvvx"]},"application/vnd.dece.zip":{source:"iana",extensions:["uvz","uvvz"]},"application/vnd.denovo.fcselayout-link":{source:"iana",extensions:["fe_launch"]},"application/vnd.desmume.movie":{source:"iana"},"application/vnd.dir-bi.plate-dl-nosuffix":{source:"iana"},"application/vnd.dm.delegation+xml":{source:"iana",compressible:!0},"application/vnd.dna":{source:"iana",extensions:["dna"]},"application/vnd.document+json":{source:"iana",compressible:!0},"application/vnd.dolby.mlp":{source:"apache",extensions:["mlp"]},"application/vnd.dolby.mobile.1":{source:"iana"},"application/vnd.dolby.mobile.2":{source:"iana"},"application/vnd.doremir.scorecloud-binary-document":{source:"iana"},"application/vnd.dpgraph":{source:"iana",extensions:["dpg"]},"application/vnd.dreamfactory":{source:"iana",extensions:["dfac"]},"application/vnd.drive+json":{source:"iana",compressible:!0},"application/vnd.ds-keypoint":{source:"apache",extensions:["kpxx"]},"application/vnd.dtg.local":{source:"iana"},"application/vnd.dtg.local.flash":{source:"iana"},"application/vnd.dtg.local.html":{source:"iana"},"application/vnd.dvb.ait":{source:"iana",extensions:["ait"]},"application/vnd.dvb.dvbisl+xml":{source:"iana",compressible:!0},"application/vnd.dvb.dvbj":{source:"iana"},"application/vnd.dvb.esgcontainer":{source:"iana"},"application/vnd.dvb.ipdcdftnotifaccess":{source:"iana"},"application/vnd.dvb.ipdcesgaccess":{source:"iana"},"application/vnd.dvb.ipdcesgaccess2":{source:"iana"},"application/vnd.dvb.ipdcesgpdd":{source:"iana"},"application/vnd.dvb.ipdcroaming":{source:"iana"},"application/vnd.dvb.iptv.alfec-base":{source:"iana"},"application/vnd.dvb.iptv.alfec-enhancement":{source:"iana"},"application/vnd.dvb.notif-aggregate-root+xml":{source:"iana",compressible:!0},"application/vnd.dvb.notif-container+xml":{source:"iana",compressible:!0},"application/vnd.dvb.notif-generic+xml":{source:"iana",compressible:!0},"application/vnd.dvb.notif-ia-msglist+xml":{source:"iana",compressible:!0},"application/vnd.dvb.notif-ia-registration-request+xml":{source:"iana",compressible:!0},"application/vnd.dvb.notif-ia-registration-response+xml":{source:"iana",compressible:!0},"application/vnd.dvb.notif-init+xml":{source:"iana",compressible:!0},"application/vnd.dvb.pfr":{source:"iana"},"application/vnd.dvb.service":{source:"iana",extensions:["svc"]},"application/vnd.dxr":{source:"iana"},"application/vnd.dynageo":{source:"iana",extensions:["geo"]},"application/vnd.dzr":{source:"iana"},"application/vnd.easykaraoke.cdgdownload":{source:"iana"},"application/vnd.ecdis-update":{source:"iana"},"application/vnd.ecip.rlp":{source:"iana"},"application/vnd.eclipse.ditto+json":{source:"iana",compressible:!0},"application/vnd.ecowin.chart":{source:"iana",extensions:["mag"]},"application/vnd.ecowin.filerequest":{source:"iana"},"application/vnd.ecowin.fileupdate":{source:"iana"},"application/vnd.ecowin.series":{source:"iana"},"application/vnd.ecowin.seriesrequest":{source:"iana"},"application/vnd.ecowin.seriesupdate":{source:"iana"},"application/vnd.efi.img":{source:"iana"},"application/vnd.efi.iso":{source:"iana"},"application/vnd.emclient.accessrequest+xml":{source:"iana",compressible:!0},"application/vnd.enliven":{source:"iana",extensions:["nml"]},"application/vnd.enphase.envoy":{source:"iana"},"application/vnd.eprints.data+xml":{source:"iana",compressible:!0},"application/vnd.epson.esf":{source:"iana",extensions:["esf"]},"application/vnd.epson.msf":{source:"iana",extensions:["msf"]},"application/vnd.epson.quickanime":{source:"iana",extensions:["qam"]},"application/vnd.epson.salt":{source:"iana",extensions:["slt"]},"application/vnd.epson.ssf":{source:"iana",extensions:["ssf"]},"application/vnd.ericsson.quickcall":{source:"iana"},"application/vnd.espass-espass+zip":{source:"iana",compressible:!1},"application/vnd.eszigno3+xml":{source:"iana",compressible:!0,extensions:["es3","et3"]},"application/vnd.etsi.aoc+xml":{source:"iana",compressible:!0},"application/vnd.etsi.asic-e+zip":{source:"iana",compressible:!1},"application/vnd.etsi.asic-s+zip":{source:"iana",compressible:!1},"application/vnd.etsi.cug+xml":{source:"iana",compressible:!0},"application/vnd.etsi.iptvcommand+xml":{source:"iana",compressible:!0},"application/vnd.etsi.iptvdiscovery+xml":{source:"iana",compressible:!0},"application/vnd.etsi.iptvprofile+xml":{source:"iana",compressible:!0},"application/vnd.etsi.iptvsad-bc+xml":{source:"iana",compressible:!0},"application/vnd.etsi.iptvsad-cod+xml":{source:"iana",compressible:!0},"application/vnd.etsi.iptvsad-npvr+xml":{source:"iana",compressible:!0},"application/vnd.etsi.iptvservice+xml":{source:"iana",compressible:!0},"application/vnd.etsi.iptvsync+xml":{source:"iana",compressible:!0},"application/vnd.etsi.iptvueprofile+xml":{source:"iana",compressible:!0},"application/vnd.etsi.mcid+xml":{source:"iana",compressible:!0},"application/vnd.etsi.mheg5":{source:"iana"},"application/vnd.etsi.overload-control-policy-dataset+xml":{source:"iana",compressible:!0},"application/vnd.etsi.pstn+xml":{source:"iana",compressible:!0},"application/vnd.etsi.sci+xml":{source:"iana",compressible:!0},"application/vnd.etsi.simservs+xml":{source:"iana",compressible:!0},"application/vnd.etsi.timestamp-token":{source:"iana"},"application/vnd.etsi.tsl+xml":{source:"iana",compressible:!0},"application/vnd.etsi.tsl.der":{source:"iana"},"application/vnd.eu.kasparian.car+json":{source:"iana",compressible:!0},"application/vnd.eudora.data":{source:"iana"},"application/vnd.evolv.ecig.profile":{source:"iana"},"application/vnd.evolv.ecig.settings":{source:"iana"},"application/vnd.evolv.ecig.theme":{source:"iana"},"application/vnd.exstream-empower+zip":{source:"iana",compressible:!1},"application/vnd.exstream-package":{source:"iana"},"application/vnd.ezpix-album":{source:"iana",extensions:["ez2"]},"application/vnd.ezpix-package":{source:"iana",extensions:["ez3"]},"application/vnd.f-secure.mobile":{source:"iana"},"application/vnd.familysearch.gedcom+zip":{source:"iana",compressible:!1},"application/vnd.fastcopy-disk-image":{source:"iana"},"application/vnd.fdf":{source:"iana",extensions:["fdf"]},"application/vnd.fdsn.mseed":{source:"iana",extensions:["mseed"]},"application/vnd.fdsn.seed":{source:"iana",extensions:["seed","dataless"]},"application/vnd.ffsns":{source:"iana"},"application/vnd.ficlab.flb+zip":{source:"iana",compressible:!1},"application/vnd.filmit.zfc":{source:"iana"},"application/vnd.fints":{source:"iana"},"application/vnd.firemonkeys.cloudcell":{source:"iana"},"application/vnd.flographit":{source:"iana",extensions:["gph"]},"application/vnd.fluxtime.clip":{source:"iana",extensions:["ftc"]},"application/vnd.font-fontforge-sfd":{source:"iana"},"application/vnd.framemaker":{source:"iana",extensions:["fm","frame","maker","book"]},"application/vnd.frogans.fnc":{source:"iana",extensions:["fnc"]},"application/vnd.frogans.ltf":{source:"iana",extensions:["ltf"]},"application/vnd.fsc.weblaunch":{source:"iana",extensions:["fsc"]},"application/vnd.fujifilm.fb.docuworks":{source:"iana"},"application/vnd.fujifilm.fb.docuworks.binder":{source:"iana"},"application/vnd.fujifilm.fb.docuworks.container":{source:"iana"},"application/vnd.fujifilm.fb.jfi+xml":{source:"iana",compressible:!0},"application/vnd.fujitsu.oasys":{source:"iana",extensions:["oas"]},"application/vnd.fujitsu.oasys2":{source:"iana",extensions:["oa2"]},"application/vnd.fujitsu.oasys3":{source:"iana",extensions:["oa3"]},"application/vnd.fujitsu.oasysgp":{source:"iana",extensions:["fg5"]},"application/vnd.fujitsu.oasysprs":{source:"iana",extensions:["bh2"]},"application/vnd.fujixerox.art-ex":{source:"iana"},"application/vnd.fujixerox.art4":{source:"iana"},"application/vnd.fujixerox.ddd":{source:"iana",extensions:["ddd"]},"application/vnd.fujixerox.docuworks":{source:"iana",extensions:["xdw"]},"application/vnd.fujixerox.docuworks.binder":{source:"iana",extensions:["xbd"]},"application/vnd.fujixerox.docuworks.container":{source:"iana"},"application/vnd.fujixerox.hbpl":{source:"iana"},"application/vnd.fut-misnet":{source:"iana"},"application/vnd.futoin+cbor":{source:"iana"},"application/vnd.futoin+json":{source:"iana",compressible:!0},"application/vnd.fuzzysheet":{source:"iana",extensions:["fzs"]},"application/vnd.genomatix.tuxedo":{source:"iana",extensions:["txd"]},"application/vnd.gentics.grd+json":{source:"iana",compressible:!0},"application/vnd.geo+json":{source:"iana",compressible:!0},"application/vnd.geocube+xml":{source:"iana",compressible:!0},"application/vnd.geogebra.file":{source:"iana",extensions:["ggb"]},"application/vnd.geogebra.slides":{source:"iana"},"application/vnd.geogebra.tool":{source:"iana",extensions:["ggt"]},"application/vnd.geometry-explorer":{source:"iana",extensions:["gex","gre"]},"application/vnd.geonext":{source:"iana",extensions:["gxt"]},"application/vnd.geoplan":{source:"iana",extensions:["g2w"]},"application/vnd.geospace":{source:"iana",extensions:["g3w"]},"application/vnd.gerber":{source:"iana"},"application/vnd.globalplatform.card-content-mgt":{source:"iana"},"application/vnd.globalplatform.card-content-mgt-response":{source:"iana"},"application/vnd.gmx":{source:"iana",extensions:["gmx"]},"application/vnd.google-apps.document":{compressible:!1,extensions:["gdoc"]},"application/vnd.google-apps.presentation":{compressible:!1,extensions:["gslides"]},"application/vnd.google-apps.spreadsheet":{compressible:!1,extensions:["gsheet"]},"application/vnd.google-earth.kml+xml":{source:"iana",compressible:!0,extensions:["kml"]},"application/vnd.google-earth.kmz":{source:"iana",compressible:!1,extensions:["kmz"]},"application/vnd.gov.sk.e-form+xml":{source:"iana",compressible:!0},"application/vnd.gov.sk.e-form+zip":{source:"iana",compressible:!1},"application/vnd.gov.sk.xmldatacontainer+xml":{source:"iana",compressible:!0},"application/vnd.grafeq":{source:"iana",extensions:["gqf","gqs"]},"application/vnd.gridmp":{source:"iana"},"application/vnd.groove-account":{source:"iana",extensions:["gac"]},"application/vnd.groove-help":{source:"iana",extensions:["ghf"]},"application/vnd.groove-identity-message":{source:"iana",extensions:["gim"]},"application/vnd.groove-injector":{source:"iana",extensions:["grv"]},"application/vnd.groove-tool-message":{source:"iana",extensions:["gtm"]},"application/vnd.groove-tool-template":{source:"iana",extensions:["tpl"]},"application/vnd.groove-vcard":{source:"iana",extensions:["vcg"]},"application/vnd.hal+json":{source:"iana",compressible:!0},"application/vnd.hal+xml":{source:"iana",compressible:!0,extensions:["hal"]},"application/vnd.handheld-entertainment+xml":{source:"iana",compressible:!0,extensions:["zmm"]},"application/vnd.hbci":{source:"iana",extensions:["hbci"]},"application/vnd.hc+json":{source:"iana",compressible:!0},"application/vnd.hcl-bireports":{source:"iana"},"application/vnd.hdt":{source:"iana"},"application/vnd.heroku+json":{source:"iana",compressible:!0},"application/vnd.hhe.lesson-player":{source:"iana",extensions:["les"]},"application/vnd.hl7cda+xml":{source:"iana",charset:"UTF-8",compressible:!0},"application/vnd.hl7v2+xml":{source:"iana",charset:"UTF-8",compressible:!0},"application/vnd.hp-hpgl":{source:"iana",extensions:["hpgl"]},"application/vnd.hp-hpid":{source:"iana",extensions:["hpid"]},"application/vnd.hp-hps":{source:"iana",extensions:["hps"]},"application/vnd.hp-jlyt":{source:"iana",extensions:["jlt"]},"application/vnd.hp-pcl":{source:"iana",extensions:["pcl"]},"application/vnd.hp-pclxl":{source:"iana",extensions:["pclxl"]},"application/vnd.httphone":{source:"iana"},"application/vnd.hydrostatix.sof-data":{source:"iana",extensions:["sfd-hdstx"]},"application/vnd.hyper+json":{source:"iana",compressible:!0},"application/vnd.hyper-item+json":{source:"iana",compressible:!0},"application/vnd.hyperdrive+json":{source:"iana",compressible:!0},"application/vnd.hzn-3d-crossword":{source:"iana"},"application/vnd.ibm.afplinedata":{source:"iana"},"application/vnd.ibm.electronic-media":{source:"iana"},"application/vnd.ibm.minipay":{source:"iana",extensions:["mpy"]},"application/vnd.ibm.modcap":{source:"iana",extensions:["afp","listafp","list3820"]},"application/vnd.ibm.rights-management":{source:"iana",extensions:["irm"]},"application/vnd.ibm.secure-container":{source:"iana",extensions:["sc"]},"application/vnd.iccprofile":{source:"iana",extensions:["icc","icm"]},"application/vnd.ieee.1905":{source:"iana"},"application/vnd.igloader":{source:"iana",extensions:["igl"]},"application/vnd.imagemeter.folder+zip":{source:"iana",compressible:!1},"application/vnd.imagemeter.image+zip":{source:"iana",compressible:!1},"application/vnd.immervision-ivp":{source:"iana",extensions:["ivp"]},"application/vnd.immervision-ivu":{source:"iana",extensions:["ivu"]},"application/vnd.ims.imsccv1p1":{source:"iana"},"application/vnd.ims.imsccv1p2":{source:"iana"},"application/vnd.ims.imsccv1p3":{source:"iana"},"application/vnd.ims.lis.v2.result+json":{source:"iana",compressible:!0},"application/vnd.ims.lti.v2.toolconsumerprofile+json":{source:"iana",compressible:!0},"application/vnd.ims.lti.v2.toolproxy+json":{source:"iana",compressible:!0},"application/vnd.ims.lti.v2.toolproxy.id+json":{source:"iana",compressible:!0},"application/vnd.ims.lti.v2.toolsettings+json":{source:"iana",compressible:!0},"application/vnd.ims.lti.v2.toolsettings.simple+json":{source:"iana",compressible:!0},"application/vnd.informedcontrol.rms+xml":{source:"iana",compressible:!0},"application/vnd.informix-visionary":{source:"iana"},"application/vnd.infotech.project":{source:"iana"},"application/vnd.infotech.project+xml":{source:"iana",compressible:!0},"application/vnd.innopath.wamp.notification":{source:"iana"},"application/vnd.insors.igm":{source:"iana",extensions:["igm"]},"application/vnd.intercon.formnet":{source:"iana",extensions:["xpw","xpx"]},"application/vnd.intergeo":{source:"iana",extensions:["i2g"]},"application/vnd.intertrust.digibox":{source:"iana"},"application/vnd.intertrust.nncp":{source:"iana"},"application/vnd.intu.qbo":{source:"iana",extensions:["qbo"]},"application/vnd.intu.qfx":{source:"iana",extensions:["qfx"]},"application/vnd.iptc.g2.catalogitem+xml":{source:"iana",compressible:!0},"application/vnd.iptc.g2.conceptitem+xml":{source:"iana",compressible:!0},"application/vnd.iptc.g2.knowledgeitem+xml":{source:"iana",compressible:!0},"application/vnd.iptc.g2.newsitem+xml":{source:"iana",compressible:!0},"application/vnd.iptc.g2.newsmessage+xml":{source:"iana",compressible:!0},"application/vnd.iptc.g2.packageitem+xml":{source:"iana",compressible:!0},"application/vnd.iptc.g2.planningitem+xml":{source:"iana",compressible:!0},"application/vnd.ipunplugged.rcprofile":{source:"iana",extensions:["rcprofile"]},"application/vnd.irepository.package+xml":{source:"iana",compressible:!0,extensions:["irp"]},"application/vnd.is-xpr":{source:"iana",extensions:["xpr"]},"application/vnd.isac.fcs":{source:"iana",extensions:["fcs"]},"application/vnd.iso11783-10+zip":{source:"iana",compressible:!1},"application/vnd.jam":{source:"iana",extensions:["jam"]},"application/vnd.japannet-directory-service":{source:"iana"},"application/vnd.japannet-jpnstore-wakeup":{source:"iana"},"application/vnd.japannet-payment-wakeup":{source:"iana"},"application/vnd.japannet-registration":{source:"iana"},"application/vnd.japannet-registration-wakeup":{source:"iana"},"application/vnd.japannet-setstore-wakeup":{source:"iana"},"application/vnd.japannet-verification":{source:"iana"},"application/vnd.japannet-verification-wakeup":{source:"iana"},"application/vnd.jcp.javame.midlet-rms":{source:"iana",extensions:["rms"]},"application/vnd.jisp":{source:"iana",extensions:["jisp"]},"application/vnd.joost.joda-archive":{source:"iana",extensions:["joda"]},"application/vnd.jsk.isdn-ngn":{source:"iana"},"application/vnd.kahootz":{source:"iana",extensions:["ktz","ktr"]},"application/vnd.kde.karbon":{source:"iana",extensions:["karbon"]},"application/vnd.kde.kchart":{source:"iana",extensions:["chrt"]},"application/vnd.kde.kformula":{source:"iana",extensions:["kfo"]},"application/vnd.kde.kivio":{source:"iana",extensions:["flw"]},"application/vnd.kde.kontour":{source:"iana",extensions:["kon"]},"application/vnd.kde.kpresenter":{source:"iana",extensions:["kpr","kpt"]},"application/vnd.kde.kspread":{source:"iana",extensions:["ksp"]},"application/vnd.kde.kword":{source:"iana",extensions:["kwd","kwt"]},"application/vnd.kenameaapp":{source:"iana",extensions:["htke"]},"application/vnd.kidspiration":{source:"iana",extensions:["kia"]},"application/vnd.kinar":{source:"iana",extensions:["kne","knp"]},"application/vnd.koan":{source:"iana",extensions:["skp","skd","skt","skm"]},"application/vnd.kodak-descriptor":{source:"iana",extensions:["sse"]},"application/vnd.las":{source:"iana"},"application/vnd.las.las+json":{source:"iana",compressible:!0},"application/vnd.las.las+xml":{source:"iana",compressible:!0,extensions:["lasxml"]},"application/vnd.laszip":{source:"iana"},"application/vnd.leap+json":{source:"iana",compressible:!0},"application/vnd.liberty-request+xml":{source:"iana",compressible:!0},"application/vnd.llamagraphics.life-balance.desktop":{source:"iana",extensions:["lbd"]},"application/vnd.llamagraphics.life-balance.exchange+xml":{source:"iana",compressible:!0,extensions:["lbe"]},"application/vnd.logipipe.circuit+zip":{source:"iana",compressible:!1},"application/vnd.loom":{source:"iana"},"application/vnd.lotus-1-2-3":{source:"iana",extensions:["123"]},"application/vnd.lotus-approach":{source:"iana",extensions:["apr"]},"application/vnd.lotus-freelance":{source:"iana",extensions:["pre"]},"application/vnd.lotus-notes":{source:"iana",extensions:["nsf"]},"application/vnd.lotus-organizer":{source:"iana",extensions:["org"]},"application/vnd.lotus-screencam":{source:"iana",extensions:["scm"]},"application/vnd.lotus-wordpro":{source:"iana",extensions:["lwp"]},"application/vnd.macports.portpkg":{source:"iana",extensions:["portpkg"]},"application/vnd.mapbox-vector-tile":{source:"iana",extensions:["mvt"]},"application/vnd.marlin.drm.actiontoken+xml":{source:"iana",compressible:!0},"application/vnd.marlin.drm.conftoken+xml":{source:"iana",compressible:!0},"application/vnd.marlin.drm.license+xml":{source:"iana",compressible:!0},"application/vnd.marlin.drm.mdcf":{source:"iana"},"application/vnd.mason+json":{source:"iana",compressible:!0},"application/vnd.maxar.archive.3tz+zip":{source:"iana",compressible:!1},"application/vnd.maxmind.maxmind-db":{source:"iana"},"application/vnd.mcd":{source:"iana",extensions:["mcd"]},"application/vnd.medcalcdata":{source:"iana",extensions:["mc1"]},"application/vnd.mediastation.cdkey":{source:"iana",extensions:["cdkey"]},"application/vnd.meridian-slingshot":{source:"iana"},"application/vnd.mfer":{source:"iana",extensions:["mwf"]},"application/vnd.mfmp":{source:"iana",extensions:["mfm"]},"application/vnd.micro+json":{source:"iana",compressible:!0},"application/vnd.micrografx.flo":{source:"iana",extensions:["flo"]},"application/vnd.micrografx.igx":{source:"iana",extensions:["igx"]},"application/vnd.microsoft.portable-executable":{source:"iana"},"application/vnd.microsoft.windows.thumbnail-cache":{source:"iana"},"application/vnd.miele+json":{source:"iana",compressible:!0},"application/vnd.mif":{source:"iana",extensions:["mif"]},"application/vnd.minisoft-hp3000-save":{source:"iana"},"application/vnd.mitsubishi.misty-guard.trustweb":{source:"iana"},"application/vnd.mobius.daf":{source:"iana",extensions:["daf"]},"application/vnd.mobius.dis":{source:"iana",extensions:["dis"]},"application/vnd.mobius.mbk":{source:"iana",extensions:["mbk"]},"application/vnd.mobius.mqy":{source:"iana",extensions:["mqy"]},"application/vnd.mobius.msl":{source:"iana",extensions:["msl"]},"application/vnd.mobius.plc":{source:"iana",extensions:["plc"]},"application/vnd.mobius.txf":{source:"iana",extensions:["txf"]},"application/vnd.mophun.application":{source:"iana",extensions:["mpn"]},"application/vnd.mophun.certificate":{source:"iana",extensions:["mpc"]},"application/vnd.motorola.flexsuite":{source:"iana"},"application/vnd.motorola.flexsuite.adsi":{source:"iana"},"application/vnd.motorola.flexsuite.fis":{source:"iana"},"application/vnd.motorola.flexsuite.gotap":{source:"iana"},"application/vnd.motorola.flexsuite.kmr":{source:"iana"},"application/vnd.motorola.flexsuite.ttc":{source:"iana"},"application/vnd.motorola.flexsuite.wem":{source:"iana"},"application/vnd.motorola.iprm":{source:"iana"},"application/vnd.mozilla.xul+xml":{source:"iana",compressible:!0,extensions:["xul"]},"application/vnd.ms-3mfdocument":{source:"iana"},"application/vnd.ms-artgalry":{source:"iana",extensions:["cil"]},"application/vnd.ms-asf":{source:"iana"},"application/vnd.ms-cab-compressed":{source:"iana",extensions:["cab"]},"application/vnd.ms-color.iccprofile":{source:"apache"},"application/vnd.ms-excel":{source:"iana",compressible:!1,extensions:["xls","xlm","xla","xlc","xlt","xlw"]},"application/vnd.ms-excel.addin.macroenabled.12":{source:"iana",extensions:["xlam"]},"application/vnd.ms-excel.sheet.binary.macroenabled.12":{source:"iana",extensions:["xlsb"]},"application/vnd.ms-excel.sheet.macroenabled.12":{source:"iana",extensions:["xlsm"]},"application/vnd.ms-excel.template.macroenabled.12":{source:"iana",extensions:["xltm"]},"application/vnd.ms-fontobject":{source:"iana",compressible:!0,extensions:["eot"]},"application/vnd.ms-htmlhelp":{source:"iana",extensions:["chm"]},"application/vnd.ms-ims":{source:"iana",extensions:["ims"]},"application/vnd.ms-lrm":{source:"iana",extensions:["lrm"]},"application/vnd.ms-office.activex+xml":{source:"iana",compressible:!0},"application/vnd.ms-officetheme":{source:"iana",extensions:["thmx"]},"application/vnd.ms-opentype":{source:"apache",compressible:!0},"application/vnd.ms-outlook":{compressible:!1,extensions:["msg"]},"application/vnd.ms-package.obfuscated-opentype":{source:"apache"},"application/vnd.ms-pki.seccat":{source:"apache",extensions:["cat"]},"application/vnd.ms-pki.stl":{source:"apache",extensions:["stl"]},"application/vnd.ms-playready.initiator+xml":{source:"iana",compressible:!0},"application/vnd.ms-powerpoint":{source:"iana",compressible:!1,extensions:["ppt","pps","pot"]},"application/vnd.ms-powerpoint.addin.macroenabled.12":{source:"iana",extensions:["ppam"]},"application/vnd.ms-powerpoint.presentation.macroenabled.12":{source:"iana",extensions:["pptm"]},"application/vnd.ms-powerpoint.slide.macroenabled.12":{source:"iana",extensions:["sldm"]},"application/vnd.ms-powerpoint.slideshow.macroenabled.12":{source:"iana",extensions:["ppsm"]},"application/vnd.ms-powerpoint.template.macroenabled.12":{source:"iana",extensions:["potm"]},"application/vnd.ms-printdevicecapabilities+xml":{source:"iana",compressible:!0},"application/vnd.ms-printing.printticket+xml":{source:"apache",compressible:!0},"application/vnd.ms-printschematicket+xml":{source:"iana",compressible:!0},"application/vnd.ms-project":{source:"iana",extensions:["mpp","mpt"]},"application/vnd.ms-tnef":{source:"iana"},"application/vnd.ms-windows.devicepairing":{source:"iana"},"application/vnd.ms-windows.nwprinting.oob":{source:"iana"},"application/vnd.ms-windows.printerpairing":{source:"iana"},"application/vnd.ms-windows.wsd.oob":{source:"iana"},"application/vnd.ms-wmdrm.lic-chlg-req":{source:"iana"},"application/vnd.ms-wmdrm.lic-resp":{source:"iana"},"application/vnd.ms-wmdrm.meter-chlg-req":{source:"iana"},"application/vnd.ms-wmdrm.meter-resp":{source:"iana"},"application/vnd.ms-word.document.macroenabled.12":{source:"iana",extensions:["docm"]},"application/vnd.ms-word.template.macroenabled.12":{source:"iana",extensions:["dotm"]},"application/vnd.ms-works":{source:"iana",extensions:["wps","wks","wcm","wdb"]},"application/vnd.ms-wpl":{source:"iana",extensions:["wpl"]},"application/vnd.ms-xpsdocument":{source:"iana",compressible:!1,extensions:["xps"]},"application/vnd.msa-disk-image":{source:"iana"},"application/vnd.mseq":{source:"iana",extensions:["mseq"]},"application/vnd.msign":{source:"iana"},"application/vnd.multiad.creator":{source:"iana"},"application/vnd.multiad.creator.cif":{source:"iana"},"application/vnd.music-niff":{source:"iana"},"application/vnd.musician":{source:"iana",extensions:["mus"]},"application/vnd.muvee.style":{source:"iana",extensions:["msty"]},"application/vnd.mynfc":{source:"iana",extensions:["taglet"]},"application/vnd.nacamar.ybrid+json":{source:"iana",compressible:!0},"application/vnd.ncd.control":{source:"iana"},"application/vnd.ncd.reference":{source:"iana"},"application/vnd.nearst.inv+json":{source:"iana",compressible:!0},"application/vnd.nebumind.line":{source:"iana"},"application/vnd.nervana":{source:"iana"},"application/vnd.netfpx":{source:"iana"},"application/vnd.neurolanguage.nlu":{source:"iana",extensions:["nlu"]},"application/vnd.nimn":{source:"iana"},"application/vnd.nintendo.nitro.rom":{source:"iana"},"application/vnd.nintendo.snes.rom":{source:"iana"},"application/vnd.nitf":{source:"iana",extensions:["ntf","nitf"]},"application/vnd.noblenet-directory":{source:"iana",extensions:["nnd"]},"application/vnd.noblenet-sealer":{source:"iana",extensions:["nns"]},"application/vnd.noblenet-web":{source:"iana",extensions:["nnw"]},"application/vnd.nokia.catalogs":{source:"iana"},"application/vnd.nokia.conml+wbxml":{source:"iana"},"application/vnd.nokia.conml+xml":{source:"iana",compressible:!0},"application/vnd.nokia.iptv.config+xml":{source:"iana",compressible:!0},"application/vnd.nokia.isds-radio-presets":{source:"iana"},"application/vnd.nokia.landmark+wbxml":{source:"iana"},"application/vnd.nokia.landmark+xml":{source:"iana",compressible:!0},"application/vnd.nokia.landmarkcollection+xml":{source:"iana",compressible:!0},"application/vnd.nokia.n-gage.ac+xml":{source:"iana",compressible:!0,extensions:["ac"]},"application/vnd.nokia.n-gage.data":{source:"iana",extensions:["ngdat"]},"application/vnd.nokia.n-gage.symbian.install":{source:"iana",extensions:["n-gage"]},"application/vnd.nokia.ncd":{source:"iana"},"application/vnd.nokia.pcd+wbxml":{source:"iana"},"application/vnd.nokia.pcd+xml":{source:"iana",compressible:!0},"application/vnd.nokia.radio-preset":{source:"iana",extensions:["rpst"]},"application/vnd.nokia.radio-presets":{source:"iana",extensions:["rpss"]},"application/vnd.novadigm.edm":{source:"iana",extensions:["edm"]},"application/vnd.novadigm.edx":{source:"iana",extensions:["edx"]},"application/vnd.novadigm.ext":{source:"iana",extensions:["ext"]},"application/vnd.ntt-local.content-share":{source:"iana"},"application/vnd.ntt-local.file-transfer":{source:"iana"},"application/vnd.ntt-local.ogw_remote-access":{source:"iana"},"application/vnd.ntt-local.sip-ta_remote":{source:"iana"},"application/vnd.ntt-local.sip-ta_tcp_stream":{source:"iana"},"application/vnd.oasis.opendocument.chart":{source:"iana",extensions:["odc"]},"application/vnd.oasis.opendocument.chart-template":{source:"iana",extensions:["otc"]},"application/vnd.oasis.opendocument.database":{source:"iana",extensions:["odb"]},"application/vnd.oasis.opendocument.formula":{source:"iana",extensions:["odf"]},"application/vnd.oasis.opendocument.formula-template":{source:"iana",extensions:["odft"]},"application/vnd.oasis.opendocument.graphics":{source:"iana",compressible:!1,extensions:["odg"]},"application/vnd.oasis.opendocument.graphics-template":{source:"iana",extensions:["otg"]},"application/vnd.oasis.opendocument.image":{source:"iana",extensions:["odi"]},"application/vnd.oasis.opendocument.image-template":{source:"iana",extensions:["oti"]},"application/vnd.oasis.opendocument.presentation":{source:"iana",compressible:!1,extensions:["odp"]},"application/vnd.oasis.opendocument.presentation-template":{source:"iana",extensions:["otp"]},"application/vnd.oasis.opendocument.spreadsheet":{source:"iana",compressible:!1,extensions:["ods"]},"application/vnd.oasis.opendocument.spreadsheet-template":{source:"iana",extensions:["ots"]},"application/vnd.oasis.opendocument.text":{source:"iana",compressible:!1,extensions:["odt"]},"application/vnd.oasis.opendocument.text-master":{source:"iana",extensions:["odm"]},"application/vnd.oasis.opendocument.text-template":{source:"iana",extensions:["ott"]},"application/vnd.oasis.opendocument.text-web":{source:"iana",extensions:["oth"]},"application/vnd.obn":{source:"iana"},"application/vnd.ocf+cbor":{source:"iana"},"application/vnd.oci.image.manifest.v1+json":{source:"iana",compressible:!0},"application/vnd.oftn.l10n+json":{source:"iana",compressible:!0},"application/vnd.oipf.contentaccessdownload+xml":{source:"iana",compressible:!0},"application/vnd.oipf.contentaccessstreaming+xml":{source:"iana",compressible:!0},"application/vnd.oipf.cspg-hexbinary":{source:"iana"},"application/vnd.oipf.dae.svg+xml":{source:"iana",compressible:!0},"application/vnd.oipf.dae.xhtml+xml":{source:"iana",compressible:!0},"application/vnd.oipf.mippvcontrolmessage+xml":{source:"iana",compressible:!0},"application/vnd.oipf.pae.gem":{source:"iana"},"application/vnd.oipf.spdiscovery+xml":{source:"iana",compressible:!0},"application/vnd.oipf.spdlist+xml":{source:"iana",compressible:!0},"application/vnd.oipf.ueprofile+xml":{source:"iana",compressible:!0},"application/vnd.oipf.userprofile+xml":{source:"iana",compressible:!0},"application/vnd.olpc-sugar":{source:"iana",extensions:["xo"]},"application/vnd.oma-scws-config":{source:"iana"},"application/vnd.oma-scws-http-request":{source:"iana"},"application/vnd.oma-scws-http-response":{source:"iana"},"application/vnd.oma.bcast.associated-procedure-parameter+xml":{source:"iana",compressible:!0},"application/vnd.oma.bcast.drm-trigger+xml":{source:"iana",compressible:!0},"application/vnd.oma.bcast.imd+xml":{source:"iana",compressible:!0},"application/vnd.oma.bcast.ltkm":{source:"iana"},"application/vnd.oma.bcast.notification+xml":{source:"iana",compressible:!0},"application/vnd.oma.bcast.provisioningtrigger":{source:"iana"},"application/vnd.oma.bcast.sgboot":{source:"iana"},"application/vnd.oma.bcast.sgdd+xml":{source:"iana",compressible:!0},"application/vnd.oma.bcast.sgdu":{source:"iana"},"application/vnd.oma.bcast.simple-symbol-container":{source:"iana"},"application/vnd.oma.bcast.smartcard-trigger+xml":{source:"iana",compressible:!0},"application/vnd.oma.bcast.sprov+xml":{source:"iana",compressible:!0},"application/vnd.oma.bcast.stkm":{source:"iana"},"application/vnd.oma.cab-address-book+xml":{source:"iana",compressible:!0},"application/vnd.oma.cab-feature-handler+xml":{source:"iana",compressible:!0},"application/vnd.oma.cab-pcc+xml":{source:"iana",compressible:!0},"application/vnd.oma.cab-subs-invite+xml":{source:"iana",compressible:!0},"application/vnd.oma.cab-user-prefs+xml":{source:"iana",compressible:!0},"application/vnd.oma.dcd":{source:"iana"},"application/vnd.oma.dcdc":{source:"iana"},"application/vnd.oma.dd2+xml":{source:"iana",compressible:!0,extensions:["dd2"]},"application/vnd.oma.drm.risd+xml":{source:"iana",compressible:!0},"application/vnd.oma.group-usage-list+xml":{source:"iana",compressible:!0},"application/vnd.oma.lwm2m+cbor":{source:"iana"},"application/vnd.oma.lwm2m+json":{source:"iana",compressible:!0},"application/vnd.oma.lwm2m+tlv":{source:"iana"},"application/vnd.oma.pal+xml":{source:"iana",compressible:!0},"application/vnd.oma.poc.detailed-progress-report+xml":{source:"iana",compressible:!0},"application/vnd.oma.poc.final-report+xml":{source:"iana",compressible:!0},"application/vnd.oma.poc.groups+xml":{source:"iana",compressible:!0},"application/vnd.oma.poc.invocation-descriptor+xml":{source:"iana",compressible:!0},"application/vnd.oma.poc.optimized-progress-report+xml":{source:"iana",compressible:!0},"application/vnd.oma.push":{source:"iana"},"application/vnd.oma.scidm.messages+xml":{source:"iana",compressible:!0},"application/vnd.oma.xcap-directory+xml":{source:"iana",compressible:!0},"application/vnd.omads-email+xml":{source:"iana",charset:"UTF-8",compressible:!0},"application/vnd.omads-file+xml":{source:"iana",charset:"UTF-8",compressible:!0},"application/vnd.omads-folder+xml":{source:"iana",charset:"UTF-8",compressible:!0},"application/vnd.omaloc-supl-init":{source:"iana"},"application/vnd.onepager":{source:"iana"},"application/vnd.onepagertamp":{source:"iana"},"application/vnd.onepagertamx":{source:"iana"},"application/vnd.onepagertat":{source:"iana"},"application/vnd.onepagertatp":{source:"iana"},"application/vnd.onepagertatx":{source:"iana"},"application/vnd.openblox.game+xml":{source:"iana",compressible:!0,extensions:["obgx"]},"application/vnd.openblox.game-binary":{source:"iana"},"application/vnd.openeye.oeb":{source:"iana"},"application/vnd.openofficeorg.extension":{source:"apache",extensions:["oxt"]},"application/vnd.openstreetmap.data+xml":{source:"iana",compressible:!0,extensions:["osm"]},"application/vnd.opentimestamps.ots":{source:"iana"},"application/vnd.openxmlformats-officedocument.custom-properties+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.customxmlproperties+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.drawing+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.drawingml.chart+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.drawingml.chartshapes+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.drawingml.diagramcolors+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.drawingml.diagramdata+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.drawingml.diagramlayout+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.drawingml.diagramstyle+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.extended-properties+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.presentationml.commentauthors+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.presentationml.comments+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.presentationml.handoutmaster+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.presentationml.notesmaster+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.presentationml.notesslide+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.presentationml.presentation":{source:"iana",compressible:!1,extensions:["pptx"]},"application/vnd.openxmlformats-officedocument.presentationml.presentation.main+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.presentationml.presprops+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.presentationml.slide":{source:"iana",extensions:["sldx"]},"application/vnd.openxmlformats-officedocument.presentationml.slide+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.presentationml.slidelayout+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.presentationml.slidemaster+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.presentationml.slideshow":{source:"iana",extensions:["ppsx"]},"application/vnd.openxmlformats-officedocument.presentationml.slideshow.main+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.presentationml.slideupdateinfo+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.presentationml.tablestyles+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.presentationml.tags+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.presentationml.template":{source:"iana",extensions:["potx"]},"application/vnd.openxmlformats-officedocument.presentationml.template.main+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.presentationml.viewprops+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.spreadsheetml.calcchain+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.spreadsheetml.chartsheet+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.spreadsheetml.comments+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.spreadsheetml.connections+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.spreadsheetml.dialogsheet+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.spreadsheetml.externallink+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.spreadsheetml.pivotcachedefinition+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.spreadsheetml.pivotcacherecords+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.spreadsheetml.pivottable+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.spreadsheetml.querytable+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.spreadsheetml.revisionheaders+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.spreadsheetml.revisionlog+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.spreadsheetml.sharedstrings+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet":{source:"iana",compressible:!1,extensions:["xlsx"]},"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet.main+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.spreadsheetml.sheetmetadata+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.spreadsheetml.styles+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.spreadsheetml.table+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.spreadsheetml.tablesinglecells+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.spreadsheetml.template":{source:"iana",extensions:["xltx"]},"application/vnd.openxmlformats-officedocument.spreadsheetml.template.main+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.spreadsheetml.usernames+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.spreadsheetml.volatiledependencies+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.spreadsheetml.worksheet+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.theme+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.themeoverride+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.vmldrawing":{source:"iana"},"application/vnd.openxmlformats-officedocument.wordprocessingml.comments+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.wordprocessingml.document":{source:"iana",compressible:!1,extensions:["docx"]},"application/vnd.openxmlformats-officedocument.wordprocessingml.document.glossary+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.wordprocessingml.document.main+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.wordprocessingml.endnotes+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.wordprocessingml.fonttable+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.wordprocessingml.footer+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.wordprocessingml.footnotes+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.wordprocessingml.numbering+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.wordprocessingml.settings+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.wordprocessingml.styles+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.wordprocessingml.template":{source:"iana",extensions:["dotx"]},"application/vnd.openxmlformats-officedocument.wordprocessingml.template.main+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.wordprocessingml.websettings+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-package.core-properties+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-package.digital-signature-xmlsignature+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-package.relationships+xml":{source:"iana",compressible:!0},"application/vnd.oracle.resource+json":{source:"iana",compressible:!0},"application/vnd.orange.indata":{source:"iana"},"application/vnd.osa.netdeploy":{source:"iana"},"application/vnd.osgeo.mapguide.package":{source:"iana",extensions:["mgp"]},"application/vnd.osgi.bundle":{source:"iana"},"application/vnd.osgi.dp":{source:"iana",extensions:["dp"]},"application/vnd.osgi.subsystem":{source:"iana",extensions:["esa"]},"application/vnd.otps.ct-kip+xml":{source:"iana",compressible:!0},"application/vnd.oxli.countgraph":{source:"iana"},"application/vnd.pagerduty+json":{source:"iana",compressible:!0},"application/vnd.palm":{source:"iana",extensions:["pdb","pqa","oprc"]},"application/vnd.panoply":{source:"iana"},"application/vnd.paos.xml":{source:"iana"},"application/vnd.patentdive":{source:"iana"},"application/vnd.patientecommsdoc":{source:"iana"},"application/vnd.pawaafile":{source:"iana",extensions:["paw"]},"application/vnd.pcos":{source:"iana"},"application/vnd.pg.format":{source:"iana",extensions:["str"]},"application/vnd.pg.osasli":{source:"iana",extensions:["ei6"]},"application/vnd.piaccess.application-licence":{source:"iana"},"application/vnd.picsel":{source:"iana",extensions:["efif"]},"application/vnd.pmi.widget":{source:"iana",extensions:["wg"]},"application/vnd.poc.group-advertisement+xml":{source:"iana",compressible:!0},"application/vnd.pocketlearn":{source:"iana",extensions:["plf"]},"application/vnd.powerbuilder6":{source:"iana",extensions:["pbd"]},"application/vnd.powerbuilder6-s":{source:"iana"},"application/vnd.powerbuilder7":{source:"iana"},"application/vnd.powerbuilder7-s":{source:"iana"},"application/vnd.powerbuilder75":{source:"iana"},"application/vnd.powerbuilder75-s":{source:"iana"},"application/vnd.preminet":{source:"iana"},"application/vnd.previewsystems.box":{source:"iana",extensions:["box"]},"application/vnd.proteus.magazine":{source:"iana",extensions:["mgz"]},"application/vnd.psfs":{source:"iana"},"application/vnd.publishare-delta-tree":{source:"iana",extensions:["qps"]},"application/vnd.pvi.ptid1":{source:"iana",extensions:["ptid"]},"application/vnd.pwg-multiplexed":{source:"iana"},"application/vnd.pwg-xhtml-print+xml":{source:"iana",compressible:!0},"application/vnd.qualcomm.brew-app-res":{source:"iana"},"application/vnd.quarantainenet":{source:"iana"},"application/vnd.quark.quarkxpress":{source:"iana",extensions:["qxd","qxt","qwd","qwt","qxl","qxb"]},"application/vnd.quobject-quoxdocument":{source:"iana"},"application/vnd.radisys.moml+xml":{source:"iana",compressible:!0},"application/vnd.radisys.msml+xml":{source:"iana",compressible:!0},"application/vnd.radisys.msml-audit+xml":{source:"iana",compressible:!0},"application/vnd.radisys.msml-audit-conf+xml":{source:"iana",compressible:!0},"application/vnd.radisys.msml-audit-conn+xml":{source:"iana",compressible:!0},"application/vnd.radisys.msml-audit-dialog+xml":{source:"iana",compressible:!0},"application/vnd.radisys.msml-audit-stream+xml":{source:"iana",compressible:!0},"application/vnd.radisys.msml-conf+xml":{source:"iana",compressible:!0},"application/vnd.radisys.msml-dialog+xml":{source:"iana",compressible:!0},"application/vnd.radisys.msml-dialog-base+xml":{source:"iana",compressible:!0},"application/vnd.radisys.msml-dialog-fax-detect+xml":{source:"iana",compressible:!0},"application/vnd.radisys.msml-dialog-fax-sendrecv+xml":{source:"iana",compressible:!0},"application/vnd.radisys.msml-dialog-group+xml":{source:"iana",compressible:!0},"application/vnd.radisys.msml-dialog-speech+xml":{source:"iana",compressible:!0},"application/vnd.radisys.msml-dialog-transform+xml":{source:"iana",compressible:!0},"application/vnd.rainstor.data":{source:"iana"},"application/vnd.rapid":{source:"iana"},"application/vnd.rar":{source:"iana",extensions:["rar"]},"application/vnd.realvnc.bed":{source:"iana",extensions:["bed"]},"application/vnd.recordare.musicxml":{source:"iana",extensions:["mxl"]},"application/vnd.recordare.musicxml+xml":{source:"iana",compressible:!0,extensions:["musicxml"]},"application/vnd.renlearn.rlprint":{source:"iana"},"application/vnd.resilient.logic":{source:"iana"},"application/vnd.restful+json":{source:"iana",compressible:!0},"application/vnd.rig.cryptonote":{source:"iana",extensions:["cryptonote"]},"application/vnd.rim.cod":{source:"apache",extensions:["cod"]},"application/vnd.rn-realmedia":{source:"apache",extensions:["rm"]},"application/vnd.rn-realmedia-vbr":{source:"apache",extensions:["rmvb"]},"application/vnd.route66.link66+xml":{source:"iana",compressible:!0,extensions:["link66"]},"application/vnd.rs-274x":{source:"iana"},"application/vnd.ruckus.download":{source:"iana"},"application/vnd.s3sms":{source:"iana"},"application/vnd.sailingtracker.track":{source:"iana",extensions:["st"]},"application/vnd.sar":{source:"iana"},"application/vnd.sbm.cid":{source:"iana"},"application/vnd.sbm.mid2":{source:"iana"},"application/vnd.scribus":{source:"iana"},"application/vnd.sealed.3df":{source:"iana"},"application/vnd.sealed.csf":{source:"iana"},"application/vnd.sealed.doc":{source:"iana"},"application/vnd.sealed.eml":{source:"iana"},"application/vnd.sealed.mht":{source:"iana"},"application/vnd.sealed.net":{source:"iana"},"application/vnd.sealed.ppt":{source:"iana"},"application/vnd.sealed.tiff":{source:"iana"},"application/vnd.sealed.xls":{source:"iana"},"application/vnd.sealedmedia.softseal.html":{source:"iana"},"application/vnd.sealedmedia.softseal.pdf":{source:"iana"},"application/vnd.seemail":{source:"iana",extensions:["see"]},"application/vnd.seis+json":{source:"iana",compressible:!0},"application/vnd.sema":{source:"iana",extensions:["sema"]},"application/vnd.semd":{source:"iana",extensions:["semd"]},"application/vnd.semf":{source:"iana",extensions:["semf"]},"application/vnd.shade-save-file":{source:"iana"},"application/vnd.shana.informed.formdata":{source:"iana",extensions:["ifm"]},"application/vnd.shana.informed.formtemplate":{source:"iana",extensions:["itp"]},"application/vnd.shana.informed.interchange":{source:"iana",extensions:["iif"]},"application/vnd.shana.informed.package":{source:"iana",extensions:["ipk"]},"application/vnd.shootproof+json":{source:"iana",compressible:!0},"application/vnd.shopkick+json":{source:"iana",compressible:!0},"application/vnd.shp":{source:"iana"},"application/vnd.shx":{source:"iana"},"application/vnd.sigrok.session":{source:"iana"},"application/vnd.simtech-mindmapper":{source:"iana",extensions:["twd","twds"]},"application/vnd.siren+json":{source:"iana",compressible:!0},"application/vnd.smaf":{source:"iana",extensions:["mmf"]},"application/vnd.smart.notebook":{source:"iana"},"application/vnd.smart.teacher":{source:"iana",extensions:["teacher"]},"application/vnd.snesdev-page-table":{source:"iana"},"application/vnd.software602.filler.form+xml":{source:"iana",compressible:!0,extensions:["fo"]},"application/vnd.software602.filler.form-xml-zip":{source:"iana"},"application/vnd.solent.sdkm+xml":{source:"iana",compressible:!0,extensions:["sdkm","sdkd"]},"application/vnd.spotfire.dxp":{source:"iana",extensions:["dxp"]},"application/vnd.spotfire.sfs":{source:"iana",extensions:["sfs"]},"application/vnd.sqlite3":{source:"iana"},"application/vnd.sss-cod":{source:"iana"},"application/vnd.sss-dtf":{source:"iana"},"application/vnd.sss-ntf":{source:"iana"},"application/vnd.stardivision.calc":{source:"apache",extensions:["sdc"]},"application/vnd.stardivision.draw":{source:"apache",extensions:["sda"]},"application/vnd.stardivision.impress":{source:"apache",extensions:["sdd"]},"application/vnd.stardivision.math":{source:"apache",extensions:["smf"]},"application/vnd.stardivision.writer":{source:"apache",extensions:["sdw","vor"]},"application/vnd.stardivision.writer-global":{source:"apache",extensions:["sgl"]},"application/vnd.stepmania.package":{source:"iana",extensions:["smzip"]},"application/vnd.stepmania.stepchart":{source:"iana",extensions:["sm"]},"application/vnd.street-stream":{source:"iana"},"application/vnd.sun.wadl+xml":{source:"iana",compressible:!0,extensions:["wadl"]},"application/vnd.sun.xml.calc":{source:"apache",extensions:["sxc"]},"application/vnd.sun.xml.calc.template":{source:"apache",extensions:["stc"]},"application/vnd.sun.xml.draw":{source:"apache",extensions:["sxd"]},"application/vnd.sun.xml.draw.template":{source:"apache",extensions:["std"]},"application/vnd.sun.xml.impress":{source:"apache",extensions:["sxi"]},"application/vnd.sun.xml.impress.template":{source:"apache",extensions:["sti"]},"application/vnd.sun.xml.math":{source:"apache",extensions:["sxm"]},"application/vnd.sun.xml.writer":{source:"apache",extensions:["sxw"]},"application/vnd.sun.xml.writer.global":{source:"apache",extensions:["sxg"]},"application/vnd.sun.xml.writer.template":{source:"apache",extensions:["stw"]},"application/vnd.sus-calendar":{source:"iana",extensions:["sus","susp"]},"application/vnd.svd":{source:"iana",extensions:["svd"]},"application/vnd.swiftview-ics":{source:"iana"},"application/vnd.sycle+xml":{source:"iana",compressible:!0},"application/vnd.syft+json":{source:"iana",compressible:!0},"application/vnd.symbian.install":{source:"apache",extensions:["sis","sisx"]},"application/vnd.syncml+xml":{source:"iana",charset:"UTF-8",compressible:!0,extensions:["xsm"]},"application/vnd.syncml.dm+wbxml":{source:"iana",charset:"UTF-8",extensions:["bdm"]},"application/vnd.syncml.dm+xml":{source:"iana",charset:"UTF-8",compressible:!0,extensions:["xdm"]},"application/vnd.syncml.dm.notification":{source:"iana"},"application/vnd.syncml.dmddf+wbxml":{source:"iana"},"application/vnd.syncml.dmddf+xml":{source:"iana",charset:"UTF-8",compressible:!0,extensions:["ddf"]},"application/vnd.syncml.dmtnds+wbxml":{source:"iana"},"application/vnd.syncml.dmtnds+xml":{source:"iana",charset:"UTF-8",compressible:!0},"application/vnd.syncml.ds.notification":{source:"iana"},"application/vnd.tableschema+json":{source:"iana",compressible:!0},"application/vnd.tao.intent-module-archive":{source:"iana",extensions:["tao"]},"application/vnd.tcpdump.pcap":{source:"iana",extensions:["pcap","cap","dmp"]},"application/vnd.think-cell.ppttc+json":{source:"iana",compressible:!0},"application/vnd.tmd.mediaflex.api+xml":{source:"iana",compressible:!0},"application/vnd.tml":{source:"iana"},"application/vnd.tmobile-livetv":{source:"iana",extensions:["tmo"]},"application/vnd.tri.onesource":{source:"iana"},"application/vnd.trid.tpt":{source:"iana",extensions:["tpt"]},"application/vnd.triscape.mxs":{source:"iana",extensions:["mxs"]},"application/vnd.trueapp":{source:"iana",extensions:["tra"]},"application/vnd.truedoc":{source:"iana"},"application/vnd.ubisoft.webplayer":{source:"iana"},"application/vnd.ufdl":{source:"iana",extensions:["ufd","ufdl"]},"application/vnd.uiq.theme":{source:"iana",extensions:["utz"]},"application/vnd.umajin":{source:"iana",extensions:["umj"]},"application/vnd.unity":{source:"iana",extensions:["unityweb"]},"application/vnd.uoml+xml":{source:"iana",compressible:!0,extensions:["uoml"]},"application/vnd.uplanet.alert":{source:"iana"},"application/vnd.uplanet.alert-wbxml":{source:"iana"},"application/vnd.uplanet.bearer-choice":{source:"iana"},"application/vnd.uplanet.bearer-choice-wbxml":{source:"iana"},"application/vnd.uplanet.cacheop":{source:"iana"},"application/vnd.uplanet.cacheop-wbxml":{source:"iana"},"application/vnd.uplanet.channel":{source:"iana"},"application/vnd.uplanet.channel-wbxml":{source:"iana"},"application/vnd.uplanet.list":{source:"iana"},"application/vnd.uplanet.list-wbxml":{source:"iana"},"application/vnd.uplanet.listcmd":{source:"iana"},"application/vnd.uplanet.listcmd-wbxml":{source:"iana"},"application/vnd.uplanet.signal":{source:"iana"},"application/vnd.uri-map":{source:"iana"},"application/vnd.valve.source.material":{source:"iana"},"application/vnd.vcx":{source:"iana",extensions:["vcx"]},"application/vnd.vd-study":{source:"iana"},"application/vnd.vectorworks":{source:"iana"},"application/vnd.vel+json":{source:"iana",compressible:!0},"application/vnd.verimatrix.vcas":{source:"iana"},"application/vnd.veritone.aion+json":{source:"iana",compressible:!0},"application/vnd.veryant.thin":{source:"iana"},"application/vnd.ves.encrypted":{source:"iana"},"application/vnd.vidsoft.vidconference":{source:"iana"},"application/vnd.visio":{source:"iana",extensions:["vsd","vst","vss","vsw"]},"application/vnd.visionary":{source:"iana",extensions:["vis"]},"application/vnd.vividence.scriptfile":{source:"iana"},"application/vnd.vsf":{source:"iana",extensions:["vsf"]},"application/vnd.wap.sic":{source:"iana"},"application/vnd.wap.slc":{source:"iana"},"application/vnd.wap.wbxml":{source:"iana",charset:"UTF-8",extensions:["wbxml"]},"application/vnd.wap.wmlc":{source:"iana",extensions:["wmlc"]},"application/vnd.wap.wmlscriptc":{source:"iana",extensions:["wmlsc"]},"application/vnd.webturbo":{source:"iana",extensions:["wtb"]},"application/vnd.wfa.dpp":{source:"iana"},"application/vnd.wfa.p2p":{source:"iana"},"application/vnd.wfa.wsc":{source:"iana"},"application/vnd.windows.devicepairing":{source:"iana"},"application/vnd.wmc":{source:"iana"},"application/vnd.wmf.bootstrap":{source:"iana"},"application/vnd.wolfram.mathematica":{source:"iana"},"application/vnd.wolfram.mathematica.package":{source:"iana"},"application/vnd.wolfram.player":{source:"iana",extensions:["nbp"]},"application/vnd.wordperfect":{source:"iana",extensions:["wpd"]},"application/vnd.wqd":{source:"iana",extensions:["wqd"]},"application/vnd.wrq-hp3000-labelled":{source:"iana"},"application/vnd.wt.stf":{source:"iana",extensions:["stf"]},"application/vnd.wv.csp+wbxml":{source:"iana"},"application/vnd.wv.csp+xml":{source:"iana",compressible:!0},"application/vnd.wv.ssp+xml":{source:"iana",compressible:!0},"application/vnd.xacml+json":{source:"iana",compressible:!0},"application/vnd.xara":{source:"iana",extensions:["xar"]},"application/vnd.xfdl":{source:"iana",extensions:["xfdl"]},"application/vnd.xfdl.webform":{source:"iana"},"application/vnd.xmi+xml":{source:"iana",compressible:!0},"application/vnd.xmpie.cpkg":{source:"iana"},"application/vnd.xmpie.dpkg":{source:"iana"},"application/vnd.xmpie.plan":{source:"iana"},"application/vnd.xmpie.ppkg":{source:"iana"},"application/vnd.xmpie.xlim":{source:"iana"},"application/vnd.yamaha.hv-dic":{source:"iana",extensions:["hvd"]},"application/vnd.yamaha.hv-script":{source:"iana",extensions:["hvs"]},"application/vnd.yamaha.hv-voice":{source:"iana",extensions:["hvp"]},"application/vnd.yamaha.openscoreformat":{source:"iana",extensions:["osf"]},"application/vnd.yamaha.openscoreformat.osfpvg+xml":{source:"iana",compressible:!0,extensions:["osfpvg"]},"application/vnd.yamaha.remote-setup":{source:"iana"},"application/vnd.yamaha.smaf-audio":{source:"iana",extensions:["saf"]},"application/vnd.yamaha.smaf-phrase":{source:"iana",extensions:["spf"]},"application/vnd.yamaha.through-ngn":{source:"iana"},"application/vnd.yamaha.tunnel-udpencap":{source:"iana"},"application/vnd.yaoweme":{source:"iana"},"application/vnd.yellowriver-custom-menu":{source:"iana",extensions:["cmp"]},"application/vnd.youtube.yt":{source:"iana"},"application/vnd.zul":{source:"iana",extensions:["zir","zirz"]},"application/vnd.zzazz.deck+xml":{source:"iana",compressible:!0,extensions:["zaz"]},"application/voicexml+xml":{source:"iana",compressible:!0,extensions:["vxml"]},"application/voucher-cms+json":{source:"iana",compressible:!0},"application/vq-rtcpxr":{source:"iana"},"application/wasm":{source:"iana",compressible:!0,extensions:["wasm"]},"application/watcherinfo+xml":{source:"iana",compressible:!0,extensions:["wif"]},"application/webpush-options+json":{source:"iana",compressible:!0},"application/whoispp-query":{source:"iana"},"application/whoispp-response":{source:"iana"},"application/widget":{source:"iana",extensions:["wgt"]},"application/winhlp":{source:"apache",extensions:["hlp"]},"application/wita":{source:"iana"},"application/wordperfect5.1":{source:"iana"},"application/wsdl+xml":{source:"iana",compressible:!0,extensions:["wsdl"]},"application/wspolicy+xml":{source:"iana",compressible:!0,extensions:["wspolicy"]},"application/x-7z-compressed":{source:"apache",compressible:!1,extensions:["7z"]},"application/x-abiword":{source:"apache",extensions:["abw"]},"application/x-ace-compressed":{source:"apache",extensions:["ace"]},"application/x-amf":{source:"apache"},"application/x-apple-diskimage":{source:"apache",extensions:["dmg"]},"application/x-arj":{compressible:!1,extensions:["arj"]},"application/x-authorware-bin":{source:"apache",extensions:["aab","x32","u32","vox"]},"application/x-authorware-map":{source:"apache",extensions:["aam"]},"application/x-authorware-seg":{source:"apache",extensions:["aas"]},"application/x-bcpio":{source:"apache",extensions:["bcpio"]},"application/x-bdoc":{compressible:!1,extensions:["bdoc"]},"application/x-bittorrent":{source:"apache",extensions:["torrent"]},"application/x-blorb":{source:"apache",extensions:["blb","blorb"]},"application/x-bzip":{source:"apache",compressible:!1,extensions:["bz"]},"application/x-bzip2":{source:"apache",compressible:!1,extensions:["bz2","boz"]},"application/x-cbr":{source:"apache",extensions:["cbr","cba","cbt","cbz","cb7"]},"application/x-cdlink":{source:"apache",extensions:["vcd"]},"application/x-cfs-compressed":{source:"apache",extensions:["cfs"]},"application/x-chat":{source:"apache",extensions:["chat"]},"application/x-chess-pgn":{source:"apache",extensions:["pgn"]},"application/x-chrome-extension":{extensions:["crx"]},"application/x-cocoa":{source:"nginx",extensions:["cco"]},"application/x-compress":{source:"apache"},"application/x-conference":{source:"apache",extensions:["nsc"]},"application/x-cpio":{source:"apache",extensions:["cpio"]},"application/x-csh":{source:"apache",extensions:["csh"]},"application/x-deb":{compressible:!1},"application/x-debian-package":{source:"apache",extensions:["deb","udeb"]},"application/x-dgc-compressed":{source:"apache",extensions:["dgc"]},"application/x-director":{source:"apache",extensions:["dir","dcr","dxr","cst","cct","cxt","w3d","fgd","swa"]},"application/x-doom":{source:"apache",extensions:["wad"]},"application/x-dtbncx+xml":{source:"apache",compressible:!0,extensions:["ncx"]},"application/x-dtbook+xml":{source:"apache",compressible:!0,extensions:["dtb"]},"application/x-dtbresource+xml":{source:"apache",compressible:!0,extensions:["res"]},"application/x-dvi":{source:"apache",compressible:!1,extensions:["dvi"]},"application/x-envoy":{source:"apache",extensions:["evy"]},"application/x-eva":{source:"apache",extensions:["eva"]},"application/x-font-bdf":{source:"apache",extensions:["bdf"]},"application/x-font-dos":{source:"apache"},"application/x-font-framemaker":{source:"apache"},"application/x-font-ghostscript":{source:"apache",extensions:["gsf"]},"application/x-font-libgrx":{source:"apache"},"application/x-font-linux-psf":{source:"apache",extensions:["psf"]},"application/x-font-pcf":{source:"apache",extensions:["pcf"]},"application/x-font-snf":{source:"apache",extensions:["snf"]},"application/x-font-speedo":{source:"apache"},"application/x-font-sunos-news":{source:"apache"},"application/x-font-type1":{source:"apache",extensions:["pfa","pfb","pfm","afm"]},"application/x-font-vfont":{source:"apache"},"application/x-freearc":{source:"apache",extensions:["arc"]},"application/x-futuresplash":{source:"apache",extensions:["spl"]},"application/x-gca-compressed":{source:"apache",extensions:["gca"]},"application/x-glulx":{source:"apache",extensions:["ulx"]},"application/x-gnumeric":{source:"apache",extensions:["gnumeric"]},"application/x-gramps-xml":{source:"apache",extensions:["gramps"]},"application/x-gtar":{source:"apache",extensions:["gtar"]},"application/x-gzip":{source:"apache"},"application/x-hdf":{source:"apache",extensions:["hdf"]},"application/x-httpd-php":{compressible:!0,extensions:["php"]},"application/x-install-instructions":{source:"apache",extensions:["install"]},"application/x-iso9660-image":{source:"apache",extensions:["iso"]},"application/x-iwork-keynote-sffkey":{extensions:["key"]},"application/x-iwork-numbers-sffnumbers":{extensions:["numbers"]},"application/x-iwork-pages-sffpages":{extensions:["pages"]},"application/x-java-archive-diff":{source:"nginx",extensions:["jardiff"]},"application/x-java-jnlp-file":{source:"apache",compressible:!1,extensions:["jnlp"]},"application/x-javascript":{compressible:!0},"application/x-keepass2":{extensions:["kdbx"]},"application/x-latex":{source:"apache",compressible:!1,extensions:["latex"]},"application/x-lua-bytecode":{extensions:["luac"]},"application/x-lzh-compressed":{source:"apache",extensions:["lzh","lha"]},"application/x-makeself":{source:"nginx",extensions:["run"]},"application/x-mie":{source:"apache",extensions:["mie"]},"application/x-mobipocket-ebook":{source:"apache",extensions:["prc","mobi"]},"application/x-mpegurl":{compressible:!1},"application/x-ms-application":{source:"apache",extensions:["application"]},"application/x-ms-shortcut":{source:"apache",extensions:["lnk"]},"application/x-ms-wmd":{source:"apache",extensions:["wmd"]},"application/x-ms-wmz":{source:"apache",extensions:["wmz"]},"application/x-ms-xbap":{source:"apache",extensions:["xbap"]},"application/x-msaccess":{source:"apache",extensions:["mdb"]},"application/x-msbinder":{source:"apache",extensions:["obd"]},"application/x-mscardfile":{source:"apache",extensions:["crd"]},"application/x-msclip":{source:"apache",extensions:["clp"]},"application/x-msdos-program":{extensions:["exe"]},"application/x-msdownload":{source:"apache",extensions:["exe","dll","com","bat","msi"]},"application/x-msmediaview":{source:"apache",extensions:["mvb","m13","m14"]},"application/x-msmetafile":{source:"apache",extensions:["wmf","wmz","emf","emz"]},"application/x-msmoney":{source:"apache",extensions:["mny"]},"application/x-mspublisher":{source:"apache",extensions:["pub"]},"application/x-msschedule":{source:"apache",extensions:["scd"]},"application/x-msterminal":{source:"apache",extensions:["trm"]},"application/x-mswrite":{source:"apache",extensions:["wri"]},"application/x-netcdf":{source:"apache",extensions:["nc","cdf"]},"application/x-ns-proxy-autoconfig":{compressible:!0,extensions:["pac"]},"application/x-nzb":{source:"apache",extensions:["nzb"]},"application/x-perl":{source:"nginx",extensions:["pl","pm"]},"application/x-pilot":{source:"nginx",extensions:["prc","pdb"]},"application/x-pkcs12":{source:"apache",compressible:!1,extensions:["p12","pfx"]},"application/x-pkcs7-certificates":{source:"apache",extensions:["p7b","spc"]},"application/x-pkcs7-certreqresp":{source:"apache",extensions:["p7r"]},"application/x-pki-message":{source:"iana"},"application/x-rar-compressed":{source:"apache",compressible:!1,extensions:["rar"]},"application/x-redhat-package-manager":{source:"nginx",extensions:["rpm"]},"application/x-research-info-systems":{source:"apache",extensions:["ris"]},"application/x-sea":{source:"nginx",extensions:["sea"]},"application/x-sh":{source:"apache",compressible:!0,extensions:["sh"]},"application/x-shar":{source:"apache",extensions:["shar"]},"application/x-shockwave-flash":{source:"apache",compressible:!1,extensions:["swf"]},"application/x-silverlight-app":{source:"apache",extensions:["xap"]},"application/x-sql":{source:"apache",extensions:["sql"]},"application/x-stuffit":{source:"apache",compressible:!1,extensions:["sit"]},"application/x-stuffitx":{source:"apache",extensions:["sitx"]},"application/x-subrip":{source:"apache",extensions:["srt"]},"application/x-sv4cpio":{source:"apache",extensions:["sv4cpio"]},"application/x-sv4crc":{source:"apache",extensions:["sv4crc"]},"application/x-t3vm-image":{source:"apache",extensions:["t3"]},"application/x-tads":{source:"apache",extensions:["gam"]},"application/x-tar":{source:"apache",compressible:!0,extensions:["tar"]},"application/x-tcl":{source:"apache",extensions:["tcl","tk"]},"application/x-tex":{source:"apache",extensions:["tex"]},"application/x-tex-tfm":{source:"apache",extensions:["tfm"]},"application/x-texinfo":{source:"apache",extensions:["texinfo","texi"]},"application/x-tgif":{source:"apache",extensions:["obj"]},"application/x-ustar":{source:"apache",extensions:["ustar"]},"application/x-virtualbox-hdd":{compressible:!0,extensions:["hdd"]},"application/x-virtualbox-ova":{compressible:!0,extensions:["ova"]},"application/x-virtualbox-ovf":{compressible:!0,extensions:["ovf"]},"application/x-virtualbox-vbox":{compressible:!0,extensions:["vbox"]},"application/x-virtualbox-vbox-extpack":{compressible:!1,extensions:["vbox-extpack"]},"application/x-virtualbox-vdi":{compressible:!0,extensions:["vdi"]},"application/x-virtualbox-vhd":{compressible:!0,extensions:["vhd"]},"application/x-virtualbox-vmdk":{compressible:!0,extensions:["vmdk"]},"application/x-wais-source":{source:"apache",extensions:["src"]},"application/x-web-app-manifest+json":{compressible:!0,extensions:["webapp"]},"application/x-www-form-urlencoded":{source:"iana",compressible:!0},"application/x-x509-ca-cert":{source:"iana",extensions:["der","crt","pem"]},"application/x-x509-ca-ra-cert":{source:"iana"},"application/x-x509-next-ca-cert":{source:"iana"},"application/x-xfig":{source:"apache",extensions:["fig"]},"application/x-xliff+xml":{source:"apache",compressible:!0,extensions:["xlf"]},"application/x-xpinstall":{source:"apache",compressible:!1,extensions:["xpi"]},"application/x-xz":{source:"apache",extensions:["xz"]},"application/x-zmachine":{source:"apache",extensions:["z1","z2","z3","z4","z5","z6","z7","z8"]},"application/x400-bp":{source:"iana"},"application/xacml+xml":{source:"iana",compressible:!0},"application/xaml+xml":{source:"apache",compressible:!0,extensions:["xaml"]},"application/xcap-att+xml":{source:"iana",compressible:!0,extensions:["xav"]},"application/xcap-caps+xml":{source:"iana",compressible:!0,extensions:["xca"]},"application/xcap-diff+xml":{source:"iana",compressible:!0,extensions:["xdf"]},"application/xcap-el+xml":{source:"iana",compressible:!0,extensions:["xel"]},"application/xcap-error+xml":{source:"iana",compressible:!0},"application/xcap-ns+xml":{source:"iana",compressible:!0,extensions:["xns"]},"application/xcon-conference-info+xml":{source:"iana",compressible:!0},"application/xcon-conference-info-diff+xml":{source:"iana",compressible:!0},"application/xenc+xml":{source:"iana",compressible:!0,extensions:["xenc"]},"application/xhtml+xml":{source:"iana",compressible:!0,extensions:["xhtml","xht"]},"application/xhtml-voice+xml":{source:"apache",compressible:!0},"application/xliff+xml":{source:"iana",compressible:!0,extensions:["xlf"]},"application/xml":{source:"iana",compressible:!0,extensions:["xml","xsl","xsd","rng"]},"application/xml-dtd":{source:"iana",compressible:!0,extensions:["dtd"]},"application/xml-external-parsed-entity":{source:"iana"},"application/xml-patch+xml":{source:"iana",compressible:!0},"application/xmpp+xml":{source:"iana",compressible:!0},"application/xop+xml":{source:"iana",compressible:!0,extensions:["xop"]},"application/xproc+xml":{source:"apache",compressible:!0,extensions:["xpl"]},"application/xslt+xml":{source:"iana",compressible:!0,extensions:["xsl","xslt"]},"application/xspf+xml":{source:"apache",compressible:!0,extensions:["xspf"]},"application/xv+xml":{source:"iana",compressible:!0,extensions:["mxml","xhvml","xvml","xvm"]},"application/yang":{source:"iana",extensions:["yang"]},"application/yang-data+json":{source:"iana",compressible:!0},"application/yang-data+xml":{source:"iana",compressible:!0},"application/yang-patch+json":{source:"iana",compressible:!0},"application/yang-patch+xml":{source:"iana",compressible:!0},"application/yin+xml":{source:"iana",compressible:!0,extensions:["yin"]},"application/zip":{source:"iana",compressible:!1,extensions:["zip"]},"application/zlib":{source:"iana"},"application/zstd":{source:"iana"},"audio/1d-interleaved-parityfec":{source:"iana"},"audio/32kadpcm":{source:"iana"},"audio/3gpp":{source:"iana",compressible:!1,extensions:["3gpp"]},"audio/3gpp2":{source:"iana"},"audio/aac":{source:"iana"},"audio/ac3":{source:"iana"},"audio/adpcm":{source:"apache",extensions:["adp"]},"audio/amr":{source:"iana",extensions:["amr"]},"audio/amr-wb":{source:"iana"},"audio/amr-wb+":{source:"iana"},"audio/aptx":{source:"iana"},"audio/asc":{source:"iana"},"audio/atrac-advanced-lossless":{source:"iana"},"audio/atrac-x":{source:"iana"},"audio/atrac3":{source:"iana"},"audio/basic":{source:"iana",compressible:!1,extensions:["au","snd"]},"audio/bv16":{source:"iana"},"audio/bv32":{source:"iana"},"audio/clearmode":{source:"iana"},"audio/cn":{source:"iana"},"audio/dat12":{source:"iana"},"audio/dls":{source:"iana"},"audio/dsr-es201108":{source:"iana"},"audio/dsr-es202050":{source:"iana"},"audio/dsr-es202211":{source:"iana"},"audio/dsr-es202212":{source:"iana"},"audio/dv":{source:"iana"},"audio/dvi4":{source:"iana"},"audio/eac3":{source:"iana"},"audio/encaprtp":{source:"iana"},"audio/evrc":{source:"iana"},"audio/evrc-qcp":{source:"iana"},"audio/evrc0":{source:"iana"},"audio/evrc1":{source:"iana"},"audio/evrcb":{source:"iana"},"audio/evrcb0":{source:"iana"},"audio/evrcb1":{source:"iana"},"audio/evrcnw":{source:"iana"},"audio/evrcnw0":{source:"iana"},"audio/evrcnw1":{source:"iana"},"audio/evrcwb":{source:"iana"},"audio/evrcwb0":{source:"iana"},"audio/evrcwb1":{source:"iana"},"audio/evs":{source:"iana"},"audio/flexfec":{source:"iana"},"audio/fwdred":{source:"iana"},"audio/g711-0":{source:"iana"},"audio/g719":{source:"iana"},"audio/g722":{source:"iana"},"audio/g7221":{source:"iana"},"audio/g723":{source:"iana"},"audio/g726-16":{source:"iana"},"audio/g726-24":{source:"iana"},"audio/g726-32":{source:"iana"},"audio/g726-40":{source:"iana"},"audio/g728":{source:"iana"},"audio/g729":{source:"iana"},"audio/g7291":{source:"iana"},"audio/g729d":{source:"iana"},"audio/g729e":{source:"iana"},"audio/gsm":{source:"iana"},"audio/gsm-efr":{source:"iana"},"audio/gsm-hr-08":{source:"iana"},"audio/ilbc":{source:"iana"},"audio/ip-mr_v2.5":{source:"iana"},"audio/isac":{source:"apache"},"audio/l16":{source:"iana"},"audio/l20":{source:"iana"},"audio/l24":{source:"iana",compressible:!1},"audio/l8":{source:"iana"},"audio/lpc":{source:"iana"},"audio/melp":{source:"iana"},"audio/melp1200":{source:"iana"},"audio/melp2400":{source:"iana"},"audio/melp600":{source:"iana"},"audio/mhas":{source:"iana"},"audio/midi":{source:"apache",extensions:["mid","midi","kar","rmi"]},"audio/mobile-xmf":{source:"iana",extensions:["mxmf"]},"audio/mp3":{compressible:!1,extensions:["mp3"]},"audio/mp4":{source:"iana",compressible:!1,extensions:["m4a","mp4a"]},"audio/mp4a-latm":{source:"iana"},"audio/mpa":{source:"iana"},"audio/mpa-robust":{source:"iana"},"audio/mpeg":{source:"iana",compressible:!1,extensions:["mpga","mp2","mp2a","mp3","m2a","m3a"]},"audio/mpeg4-generic":{source:"iana"},"audio/musepack":{source:"apache"},"audio/ogg":{source:"iana",compressible:!1,extensions:["oga","ogg","spx","opus"]},"audio/opus":{source:"iana"},"audio/parityfec":{source:"iana"},"audio/pcma":{source:"iana"},"audio/pcma-wb":{source:"iana"},"audio/pcmu":{source:"iana"},"audio/pcmu-wb":{source:"iana"},"audio/prs.sid":{source:"iana"},"audio/qcelp":{source:"iana"},"audio/raptorfec":{source:"iana"},"audio/red":{source:"iana"},"audio/rtp-enc-aescm128":{source:"iana"},"audio/rtp-midi":{source:"iana"},"audio/rtploopback":{source:"iana"},"audio/rtx":{source:"iana"},"audio/s3m":{source:"apache",extensions:["s3m"]},"audio/scip":{source:"iana"},"audio/silk":{source:"apache",extensions:["sil"]},"audio/smv":{source:"iana"},"audio/smv-qcp":{source:"iana"},"audio/smv0":{source:"iana"},"audio/sofa":{source:"iana"},"audio/sp-midi":{source:"iana"},"audio/speex":{source:"iana"},"audio/t140c":{source:"iana"},"audio/t38":{source:"iana"},"audio/telephone-event":{source:"iana"},"audio/tetra_acelp":{source:"iana"},"audio/tetra_acelp_bb":{source:"iana"},"audio/tone":{source:"iana"},"audio/tsvcis":{source:"iana"},"audio/uemclip":{source:"iana"},"audio/ulpfec":{source:"iana"},"audio/usac":{source:"iana"},"audio/vdvi":{source:"iana"},"audio/vmr-wb":{source:"iana"},"audio/vnd.3gpp.iufp":{source:"iana"},"audio/vnd.4sb":{source:"iana"},"audio/vnd.audiokoz":{source:"iana"},"audio/vnd.celp":{source:"iana"},"audio/vnd.cisco.nse":{source:"iana"},"audio/vnd.cmles.radio-events":{source:"iana"},"audio/vnd.cns.anp1":{source:"iana"},"audio/vnd.cns.inf1":{source:"iana"},"audio/vnd.dece.audio":{source:"iana",extensions:["uva","uvva"]},"audio/vnd.digital-winds":{source:"iana",extensions:["eol"]},"audio/vnd.dlna.adts":{source:"iana"},"audio/vnd.dolby.heaac.1":{source:"iana"},"audio/vnd.dolby.heaac.2":{source:"iana"},"audio/vnd.dolby.mlp":{source:"iana"},"audio/vnd.dolby.mps":{source:"iana"},"audio/vnd.dolby.pl2":{source:"iana"},"audio/vnd.dolby.pl2x":{source:"iana"},"audio/vnd.dolby.pl2z":{source:"iana"},"audio/vnd.dolby.pulse.1":{source:"iana"},"audio/vnd.dra":{source:"iana",extensions:["dra"]},"audio/vnd.dts":{source:"iana",extensions:["dts"]},"audio/vnd.dts.hd":{source:"iana",extensions:["dtshd"]},"audio/vnd.dts.uhd":{source:"iana"},"audio/vnd.dvb.file":{source:"iana"},"audio/vnd.everad.plj":{source:"iana"},"audio/vnd.hns.audio":{source:"iana"},"audio/vnd.lucent.voice":{source:"iana",extensions:["lvp"]},"audio/vnd.ms-playready.media.pya":{source:"iana",extensions:["pya"]},"audio/vnd.nokia.mobile-xmf":{source:"iana"},"audio/vnd.nortel.vbk":{source:"iana"},"audio/vnd.nuera.ecelp4800":{source:"iana",extensions:["ecelp4800"]},"audio/vnd.nuera.ecelp7470":{source:"iana",extensions:["ecelp7470"]},"audio/vnd.nuera.ecelp9600":{source:"iana",extensions:["ecelp9600"]},"audio/vnd.octel.sbc":{source:"iana"},"audio/vnd.presonus.multitrack":{source:"iana"},"audio/vnd.qcelp":{source:"iana"},"audio/vnd.rhetorex.32kadpcm":{source:"iana"},"audio/vnd.rip":{source:"iana",extensions:["rip"]},"audio/vnd.rn-realaudio":{compressible:!1},"audio/vnd.sealedmedia.softseal.mpeg":{source:"iana"},"audio/vnd.vmx.cvsd":{source:"iana"},"audio/vnd.wave":{compressible:!1},"audio/vorbis":{source:"iana",compressible:!1},"audio/vorbis-config":{source:"iana"},"audio/wav":{compressible:!1,extensions:["wav"]},"audio/wave":{compressible:!1,extensions:["wav"]},"audio/webm":{source:"apache",compressible:!1,extensions:["weba"]},"audio/x-aac":{source:"apache",compressible:!1,extensions:["aac"]},"audio/x-aiff":{source:"apache",extensions:["aif","aiff","aifc"]},"audio/x-caf":{source:"apache",compressible:!1,extensions:["caf"]},"audio/x-flac":{source:"apache",extensions:["flac"]},"audio/x-m4a":{source:"nginx",extensions:["m4a"]},"audio/x-matroska":{source:"apache",extensions:["mka"]},"audio/x-mpegurl":{source:"apache",extensions:["m3u"]},"audio/x-ms-wax":{source:"apache",extensions:["wax"]},"audio/x-ms-wma":{source:"apache",extensions:["wma"]},"audio/x-pn-realaudio":{source:"apache",extensions:["ram","ra"]},"audio/x-pn-realaudio-plugin":{source:"apache",extensions:["rmp"]},"audio/x-realaudio":{source:"nginx",extensions:["ra"]},"audio/x-tta":{source:"apache"},"audio/x-wav":{source:"apache",extensions:["wav"]},"audio/xm":{source:"apache",extensions:["xm"]},"chemical/x-cdx":{source:"apache",extensions:["cdx"]},"chemical/x-cif":{source:"apache",extensions:["cif"]},"chemical/x-cmdf":{source:"apache",extensions:["cmdf"]},"chemical/x-cml":{source:"apache",extensions:["cml"]},"chemical/x-csml":{source:"apache",extensions:["csml"]},"chemical/x-pdb":{source:"apache"},"chemical/x-xyz":{source:"apache",extensions:["xyz"]},"font/collection":{source:"iana",extensions:["ttc"]},"font/otf":{source:"iana",compressible:!0,extensions:["otf"]},"font/sfnt":{source:"iana"},"font/ttf":{source:"iana",compressible:!0,extensions:["ttf"]},"font/woff":{source:"iana",extensions:["woff"]},"font/woff2":{source:"iana",extensions:["woff2"]},"image/aces":{source:"iana",extensions:["exr"]},"image/apng":{compressible:!1,extensions:["apng"]},"image/avci":{source:"iana",extensions:["avci"]},"image/avcs":{source:"iana",extensions:["avcs"]},"image/avif":{source:"iana",compressible:!1,extensions:["avif"]},"image/bmp":{source:"iana",compressible:!0,extensions:["bmp"]},"image/cgm":{source:"iana",extensions:["cgm"]},"image/dicom-rle":{source:"iana",extensions:["drle"]},"image/emf":{source:"iana",extensions:["emf"]},"image/fits":{source:"iana",extensions:["fits"]},"image/g3fax":{source:"iana",extensions:["g3"]},"image/gif":{source:"iana",compressible:!1,extensions:["gif"]},"image/heic":{source:"iana",extensions:["heic"]},"image/heic-sequence":{source:"iana",extensions:["heics"]},"image/heif":{source:"iana",extensions:["heif"]},"image/heif-sequence":{source:"iana",extensions:["heifs"]},"image/hej2k":{source:"iana",extensions:["hej2"]},"image/hsj2":{source:"iana",extensions:["hsj2"]},"image/ief":{source:"iana",extensions:["ief"]},"image/jls":{source:"iana",extensions:["jls"]},"image/jp2":{source:"iana",compressible:!1,extensions:["jp2","jpg2"]},"image/jpeg":{source:"iana",compressible:!1,extensions:["jpeg","jpg","jpe"]},"image/jph":{source:"iana",extensions:["jph"]},"image/jphc":{source:"iana",extensions:["jhc"]},"image/jpm":{source:"iana",compressible:!1,extensions:["jpm"]},"image/jpx":{source:"iana",compressible:!1,extensions:["jpx","jpf"]},"image/jxr":{source:"iana",extensions:["jxr"]},"image/jxra":{source:"iana",extensions:["jxra"]},"image/jxrs":{source:"iana",extensions:["jxrs"]},"image/jxs":{source:"iana",extensions:["jxs"]},"image/jxsc":{source:"iana",extensions:["jxsc"]},"image/jxsi":{source:"iana",extensions:["jxsi"]},"image/jxss":{source:"iana",extensions:["jxss"]},"image/ktx":{source:"iana",extensions:["ktx"]},"image/ktx2":{source:"iana",extensions:["ktx2"]},"image/naplps":{source:"iana"},"image/pjpeg":{compressible:!1},"image/png":{source:"iana",compressible:!1,extensions:["png"]},"image/prs.btif":{source:"iana",extensions:["btif"]},"image/prs.pti":{source:"iana",extensions:["pti"]},"image/pwg-raster":{source:"iana"},"image/sgi":{source:"apache",extensions:["sgi"]},"image/svg+xml":{source:"iana",compressible:!0,extensions:["svg","svgz"]},"image/t38":{source:"iana",extensions:["t38"]},"image/tiff":{source:"iana",compressible:!1,extensions:["tif","tiff"]},"image/tiff-fx":{source:"iana",extensions:["tfx"]},"image/vnd.adobe.photoshop":{source:"iana",compressible:!0,extensions:["psd"]},"image/vnd.airzip.accelerator.azv":{source:"iana",extensions:["azv"]},"image/vnd.cns.inf2":{source:"iana"},"image/vnd.dece.graphic":{source:"iana",extensions:["uvi","uvvi","uvg","uvvg"]},"image/vnd.djvu":{source:"iana",extensions:["djvu","djv"]},"image/vnd.dvb.subtitle":{source:"iana",extensions:["sub"]},"image/vnd.dwg":{source:"iana",extensions:["dwg"]},"image/vnd.dxf":{source:"iana",extensions:["dxf"]},"image/vnd.fastbidsheet":{source:"iana",extensions:["fbs"]},"image/vnd.fpx":{source:"iana",extensions:["fpx"]},"image/vnd.fst":{source:"iana",extensions:["fst"]},"image/vnd.fujixerox.edmics-mmr":{source:"iana",extensions:["mmr"]},"image/vnd.fujixerox.edmics-rlc":{source:"iana",extensions:["rlc"]},"image/vnd.globalgraphics.pgb":{source:"iana"},"image/vnd.microsoft.icon":{source:"iana",compressible:!0,extensions:["ico"]},"image/vnd.mix":{source:"iana"},"image/vnd.mozilla.apng":{source:"iana"},"image/vnd.ms-dds":{compressible:!0,extensions:["dds"]},"image/vnd.ms-modi":{source:"iana",extensions:["mdi"]},"image/vnd.ms-photo":{source:"apache",extensions:["wdp"]},"image/vnd.net-fpx":{source:"iana",extensions:["npx"]},"image/vnd.pco.b16":{source:"iana",extensions:["b16"]},"image/vnd.radiance":{source:"iana"},"image/vnd.sealed.png":{source:"iana"},"image/vnd.sealedmedia.softseal.gif":{source:"iana"},"image/vnd.sealedmedia.softseal.jpg":{source:"iana"},"image/vnd.svf":{source:"iana"},"image/vnd.tencent.tap":{source:"iana",extensions:["tap"]},"image/vnd.valve.source.texture":{source:"iana",extensions:["vtf"]},"image/vnd.wap.wbmp":{source:"iana",extensions:["wbmp"]},"image/vnd.xiff":{source:"iana",extensions:["xif"]},"image/vnd.zbrush.pcx":{source:"iana",extensions:["pcx"]},"image/webp":{source:"apache",extensions:["webp"]},"image/wmf":{source:"iana",extensions:["wmf"]},"image/x-3ds":{source:"apache",extensions:["3ds"]},"image/x-cmu-raster":{source:"apache",extensions:["ras"]},"image/x-cmx":{source:"apache",extensions:["cmx"]},"image/x-freehand":{source:"apache",extensions:["fh","fhc","fh4","fh5","fh7"]},"image/x-icon":{source:"apache",compressible:!0,extensions:["ico"]},"image/x-jng":{source:"nginx",extensions:["jng"]},"image/x-mrsid-image":{source:"apache",extensions:["sid"]},"image/x-ms-bmp":{source:"nginx",compressible:!0,extensions:["bmp"]},"image/x-pcx":{source:"apache",extensions:["pcx"]},"image/x-pict":{source:"apache",extensions:["pic","pct"]},"image/x-portable-anymap":{source:"apache",extensions:["pnm"]},"image/x-portable-bitmap":{source:"apache",extensions:["pbm"]},"image/x-portable-graymap":{source:"apache",extensions:["pgm"]},"image/x-portable-pixmap":{source:"apache",extensions:["ppm"]},"image/x-rgb":{source:"apache",extensions:["rgb"]},"image/x-tga":{source:"apache",extensions:["tga"]},"image/x-xbitmap":{source:"apache",extensions:["xbm"]},"image/x-xcf":{compressible:!1},"image/x-xpixmap":{source:"apache",extensions:["xpm"]},"image/x-xwindowdump":{source:"apache",extensions:["xwd"]},"message/cpim":{source:"iana"},"message/delivery-status":{source:"iana"},"message/disposition-notification":{source:"iana",extensions:["disposition-notification"]},"message/external-body":{source:"iana"},"message/feedback-report":{source:"iana"},"message/global":{source:"iana",extensions:["u8msg"]},"message/global-delivery-status":{source:"iana",extensions:["u8dsn"]},"message/global-disposition-notification":{source:"iana",extensions:["u8mdn"]},"message/global-headers":{source:"iana",extensions:["u8hdr"]},"message/http":{source:"iana",compressible:!1},"message/imdn+xml":{source:"iana",compressible:!0},"message/news":{source:"iana"},"message/partial":{source:"iana",compressible:!1},"message/rfc822":{source:"iana",compressible:!0,extensions:["eml","mime"]},"message/s-http":{source:"iana"},"message/sip":{source:"iana"},"message/sipfrag":{source:"iana"},"message/tracking-status":{source:"iana"},"message/vnd.si.simp":{source:"iana"},"message/vnd.wfa.wsc":{source:"iana",extensions:["wsc"]},"model/3mf":{source:"iana",extensions:["3mf"]},"model/e57":{source:"iana"},"model/gltf+json":{source:"iana",compressible:!0,extensions:["gltf"]},"model/gltf-binary":{source:"iana",compressible:!0,extensions:["glb"]},"model/iges":{source:"iana",compressible:!1,extensions:["igs","iges"]},"model/mesh":{source:"iana",compressible:!1,extensions:["msh","mesh","silo"]},"model/mtl":{source:"iana",extensions:["mtl"]},"model/obj":{source:"iana",extensions:["obj"]},"model/step":{source:"iana"},"model/step+xml":{source:"iana",compressible:!0,extensions:["stpx"]},"model/step+zip":{source:"iana",compressible:!1,extensions:["stpz"]},"model/step-xml+zip":{source:"iana",compressible:!1,extensions:["stpxz"]},"model/stl":{source:"iana",extensions:["stl"]},"model/vnd.collada+xml":{source:"iana",compressible:!0,extensions:["dae"]},"model/vnd.dwf":{source:"iana",extensions:["dwf"]},"model/vnd.flatland.3dml":{source:"iana"},"model/vnd.gdl":{source:"iana",extensions:["gdl"]},"model/vnd.gs-gdl":{source:"apache"},"model/vnd.gs.gdl":{source:"iana"},"model/vnd.gtw":{source:"iana",extensions:["gtw"]},"model/vnd.moml+xml":{source:"iana",compressible:!0},"model/vnd.mts":{source:"iana",extensions:["mts"]},"model/vnd.opengex":{source:"iana",extensions:["ogex"]},"model/vnd.parasolid.transmit.binary":{source:"iana",extensions:["x_b"]},"model/vnd.parasolid.transmit.text":{source:"iana",extensions:["x_t"]},"model/vnd.pytha.pyox":{source:"iana"},"model/vnd.rosette.annotated-data-model":{source:"iana"},"model/vnd.sap.vds":{source:"iana",extensions:["vds"]},"model/vnd.usdz+zip":{source:"iana",compressible:!1,extensions:["usdz"]},"model/vnd.valve.source.compiled-map":{source:"iana",extensions:["bsp"]},"model/vnd.vtu":{source:"iana",extensions:["vtu"]},"model/vrml":{source:"iana",compressible:!1,extensions:["wrl","vrml"]},"model/x3d+binary":{source:"apache",compressible:!1,extensions:["x3db","x3dbz"]},"model/x3d+fastinfoset":{source:"iana",extensions:["x3db"]},"model/x3d+vrml":{source:"apache",compressible:!1,extensions:["x3dv","x3dvz"]},"model/x3d+xml":{source:"iana",compressible:!0,extensions:["x3d","x3dz"]},"model/x3d-vrml":{source:"iana",extensions:["x3dv"]},"multipart/alternative":{source:"iana",compressible:!1},"multipart/appledouble":{source:"iana"},"multipart/byteranges":{source:"iana"},"multipart/digest":{source:"iana"},"multipart/encrypted":{source:"iana",compressible:!1},"multipart/form-data":{source:"iana",compressible:!1},"multipart/header-set":{source:"iana"},"multipart/mixed":{source:"iana"},"multipart/multilingual":{source:"iana"},"multipart/parallel":{source:"iana"},"multipart/related":{source:"iana",compressible:!1},"multipart/report":{source:"iana"},"multipart/signed":{source:"iana",compressible:!1},"multipart/vnd.bint.med-plus":{source:"iana"},"multipart/voice-message":{source:"iana"},"multipart/x-mixed-replace":{source:"iana"},"text/1d-interleaved-parityfec":{source:"iana"},"text/cache-manifest":{source:"iana",compressible:!0,extensions:["appcache","manifest"]},"text/calendar":{source:"iana",extensions:["ics","ifb"]},"text/calender":{compressible:!0},"text/cmd":{compressible:!0},"text/coffeescript":{extensions:["coffee","litcoffee"]},"text/cql":{source:"iana"},"text/cql-expression":{source:"iana"},"text/cql-identifier":{source:"iana"},"text/css":{source:"iana",charset:"UTF-8",compressible:!0,extensions:["css"]},"text/csv":{source:"iana",compressible:!0,extensions:["csv"]},"text/csv-schema":{source:"iana"},"text/directory":{source:"iana"},"text/dns":{source:"iana"},"text/ecmascript":{source:"iana"},"text/encaprtp":{source:"iana"},"text/enriched":{source:"iana"},"text/fhirpath":{source:"iana"},"text/flexfec":{source:"iana"},"text/fwdred":{source:"iana"},"text/gff3":{source:"iana"},"text/grammar-ref-list":{source:"iana"},"text/html":{source:"iana",compressible:!0,extensions:["html","htm","shtml"]},"text/jade":{extensions:["jade"]},"text/javascript":{source:"iana",compressible:!0},"text/jcr-cnd":{source:"iana"},"text/jsx":{compressible:!0,extensions:["jsx"]},"text/less":{compressible:!0,extensions:["less"]},"text/markdown":{source:"iana",compressible:!0,extensions:["markdown","md"]},"text/mathml":{source:"nginx",extensions:["mml"]},"text/mdx":{compressible:!0,extensions:["mdx"]},"text/mizar":{source:"iana"},"text/n3":{source:"iana",charset:"UTF-8",compressible:!0,extensions:["n3"]},"text/parameters":{source:"iana",charset:"UTF-8"},"text/parityfec":{source:"iana"},"text/plain":{source:"iana",compressible:!0,extensions:["txt","text","conf","def","list","log","in","ini"]},"text/provenance-notation":{source:"iana",charset:"UTF-8"},"text/prs.fallenstein.rst":{source:"iana"},"text/prs.lines.tag":{source:"iana",extensions:["dsc"]},"text/prs.prop.logic":{source:"iana"},"text/raptorfec":{source:"iana"},"text/red":{source:"iana"},"text/rfc822-headers":{source:"iana"},"text/richtext":{source:"iana",compressible:!0,extensions:["rtx"]},"text/rtf":{source:"iana",compressible:!0,extensions:["rtf"]},"text/rtp-enc-aescm128":{source:"iana"},"text/rtploopback":{source:"iana"},"text/rtx":{source:"iana"},"text/sgml":{source:"iana",extensions:["sgml","sgm"]},"text/shaclc":{source:"iana"},"text/shex":{source:"iana",extensions:["shex"]},"text/slim":{extensions:["slim","slm"]},"text/spdx":{source:"iana",extensions:["spdx"]},"text/strings":{source:"iana"},"text/stylus":{extensions:["stylus","styl"]},"text/t140":{source:"iana"},"text/tab-separated-values":{source:"iana",compressible:!0,extensions:["tsv"]},"text/troff":{source:"iana",extensions:["t","tr","roff","man","me","ms"]},"text/turtle":{source:"iana",charset:"UTF-8",extensions:["ttl"]},"text/ulpfec":{source:"iana"},"text/uri-list":{source:"iana",compressible:!0,extensions:["uri","uris","urls"]},"text/vcard":{source:"iana",compressible:!0,extensions:["vcard"]},"text/vnd.a":{source:"iana"},"text/vnd.abc":{source:"iana"},"text/vnd.ascii-art":{source:"iana"},"text/vnd.curl":{source:"iana",extensions:["curl"]},"text/vnd.curl.dcurl":{source:"apache",extensions:["dcurl"]},"text/vnd.curl.mcurl":{source:"apache",extensions:["mcurl"]},"text/vnd.curl.scurl":{source:"apache",extensions:["scurl"]},"text/vnd.debian.copyright":{source:"iana",charset:"UTF-8"},"text/vnd.dmclientscript":{source:"iana"},"text/vnd.dvb.subtitle":{source:"iana",extensions:["sub"]},"text/vnd.esmertec.theme-descriptor":{source:"iana",charset:"UTF-8"},"text/vnd.familysearch.gedcom":{source:"iana",extensions:["ged"]},"text/vnd.ficlab.flt":{source:"iana"},"text/vnd.fly":{source:"iana",extensions:["fly"]},"text/vnd.fmi.flexstor":{source:"iana",extensions:["flx"]},"text/vnd.gml":{source:"iana"},"text/vnd.graphviz":{source:"iana",extensions:["gv"]},"text/vnd.hans":{source:"iana"},"text/vnd.hgl":{source:"iana"},"text/vnd.in3d.3dml":{source:"iana",extensions:["3dml"]},"text/vnd.in3d.spot":{source:"iana",extensions:["spot"]},"text/vnd.iptc.newsml":{source:"iana"},"text/vnd.iptc.nitf":{source:"iana"},"text/vnd.latex-z":{source:"iana"},"text/vnd.motorola.reflex":{source:"iana"},"text/vnd.ms-mediapackage":{source:"iana"},"text/vnd.net2phone.commcenter.command":{source:"iana"},"text/vnd.radisys.msml-basic-layout":{source:"iana"},"text/vnd.senx.warpscript":{source:"iana"},"text/vnd.si.uricatalogue":{source:"iana"},"text/vnd.sosi":{source:"iana"},"text/vnd.sun.j2me.app-descriptor":{source:"iana",charset:"UTF-8",extensions:["jad"]},"text/vnd.trolltech.linguist":{source:"iana",charset:"UTF-8"},"text/vnd.wap.si":{source:"iana"},"text/vnd.wap.sl":{source:"iana"},"text/vnd.wap.wml":{source:"iana",extensions:["wml"]},"text/vnd.wap.wmlscript":{source:"iana",extensions:["wmls"]},"text/vtt":{source:"iana",charset:"UTF-8",compressible:!0,extensions:["vtt"]},"text/x-asm":{source:"apache",extensions:["s","asm"]},"text/x-c":{source:"apache",extensions:["c","cc","cxx","cpp","h","hh","dic"]},"text/x-component":{source:"nginx",extensions:["htc"]},"text/x-fortran":{source:"apache",extensions:["f","for","f77","f90"]},"text/x-gwt-rpc":{compressible:!0},"text/x-handlebars-template":{extensions:["hbs"]},"text/x-java-source":{source:"apache",extensions:["java"]},"text/x-jquery-tmpl":{compressible:!0},"text/x-lua":{extensions:["lua"]},"text/x-markdown":{compressible:!0,extensions:["mkd"]},"text/x-nfo":{source:"apache",extensions:["nfo"]},"text/x-opml":{source:"apache",extensions:["opml"]},"text/x-org":{compressible:!0,extensions:["org"]},"text/x-pascal":{source:"apache",extensions:["p","pas"]},"text/x-processing":{compressible:!0,extensions:["pde"]},"text/x-sass":{extensions:["sass"]},"text/x-scss":{extensions:["scss"]},"text/x-setext":{source:"apache",extensions:["etx"]},"text/x-sfv":{source:"apache",extensions:["sfv"]},"text/x-suse-ymp":{compressible:!0,extensions:["ymp"]},"text/x-uuencode":{source:"apache",extensions:["uu"]},"text/x-vcalendar":{source:"apache",extensions:["vcs"]},"text/x-vcard":{source:"apache",extensions:["vcf"]},"text/xml":{source:"iana",compressible:!0,extensions:["xml"]},"text/xml-external-parsed-entity":{source:"iana"},"text/yaml":{compressible:!0,extensions:["yaml","yml"]},"video/1d-interleaved-parityfec":{source:"iana"},"video/3gpp":{source:"iana",extensions:["3gp","3gpp"]},"video/3gpp-tt":{source:"iana"},"video/3gpp2":{source:"iana",extensions:["3g2"]},"video/av1":{source:"iana"},"video/bmpeg":{source:"iana"},"video/bt656":{source:"iana"},"video/celb":{source:"iana"},"video/dv":{source:"iana"},"video/encaprtp":{source:"iana"},"video/ffv1":{source:"iana"},"video/flexfec":{source:"iana"},"video/h261":{source:"iana",extensions:["h261"]},"video/h263":{source:"iana",extensions:["h263"]},"video/h263-1998":{source:"iana"},"video/h263-2000":{source:"iana"},"video/h264":{source:"iana",extensions:["h264"]},"video/h264-rcdo":{source:"iana"},"video/h264-svc":{source:"iana"},"video/h265":{source:"iana"},"video/iso.segment":{source:"iana",extensions:["m4s"]},"video/jpeg":{source:"iana",extensions:["jpgv"]},"video/jpeg2000":{source:"iana"},"video/jpm":{source:"apache",extensions:["jpm","jpgm"]},"video/jxsv":{source:"iana"},"video/mj2":{source:"iana",extensions:["mj2","mjp2"]},"video/mp1s":{source:"iana"},"video/mp2p":{source:"iana"},"video/mp2t":{source:"iana",extensions:["ts"]},"video/mp4":{source:"iana",compressible:!1,extensions:["mp4","mp4v","mpg4"]},"video/mp4v-es":{source:"iana"},"video/mpeg":{source:"iana",compressible:!1,extensions:["mpeg","mpg","mpe","m1v","m2v"]},"video/mpeg4-generic":{source:"iana"},"video/mpv":{source:"iana"},"video/nv":{source:"iana"},"video/ogg":{source:"iana",compressible:!1,extensions:["ogv"]},"video/parityfec":{source:"iana"},"video/pointer":{source:"iana"},"video/quicktime":{source:"iana",compressible:!1,extensions:["qt","mov"]},"video/raptorfec":{source:"iana"},"video/raw":{source:"iana"},"video/rtp-enc-aescm128":{source:"iana"},"video/rtploopback":{source:"iana"},"video/rtx":{source:"iana"},"video/scip":{source:"iana"},"video/smpte291":{source:"iana"},"video/smpte292m":{source:"iana"},"video/ulpfec":{source:"iana"},"video/vc1":{source:"iana"},"video/vc2":{source:"iana"},"video/vnd.cctv":{source:"iana"},"video/vnd.dece.hd":{source:"iana",extensions:["uvh","uvvh"]},"video/vnd.dece.mobile":{source:"iana",extensions:["uvm","uvvm"]},"video/vnd.dece.mp4":{source:"iana"},"video/vnd.dece.pd":{source:"iana",extensions:["uvp","uvvp"]},"video/vnd.dece.sd":{source:"iana",extensions:["uvs","uvvs"]},"video/vnd.dece.video":{source:"iana",extensions:["uvv","uvvv"]},"video/vnd.directv.mpeg":{source:"iana"},"video/vnd.directv.mpeg-tts":{source:"iana"},"video/vnd.dlna.mpeg-tts":{source:"iana"},"video/vnd.dvb.file":{source:"iana",extensions:["dvb"]},"video/vnd.fvt":{source:"iana",extensions:["fvt"]},"video/vnd.hns.video":{source:"iana"},"video/vnd.iptvforum.1dparityfec-1010":{source:"iana"},"video/vnd.iptvforum.1dparityfec-2005":{source:"iana"},"video/vnd.iptvforum.2dparityfec-1010":{source:"iana"},"video/vnd.iptvforum.2dparityfec-2005":{source:"iana"},"video/vnd.iptvforum.ttsavc":{source:"iana"},"video/vnd.iptvforum.ttsmpeg2":{source:"iana"},"video/vnd.motorola.video":{source:"iana"},"video/vnd.motorola.videop":{source:"iana"},"video/vnd.mpegurl":{source:"iana",extensions:["mxu","m4u"]},"video/vnd.ms-playready.media.pyv":{source:"iana",extensions:["pyv"]},"video/vnd.nokia.interleaved-multimedia":{source:"iana"},"video/vnd.nokia.mp4vr":{source:"iana"},"video/vnd.nokia.videovoip":{source:"iana"},"video/vnd.objectvideo":{source:"iana"},"video/vnd.radgamettools.bink":{source:"iana"},"video/vnd.radgamettools.smacker":{source:"iana"},"video/vnd.sealed.mpeg1":{source:"iana"},"video/vnd.sealed.mpeg4":{source:"iana"},"video/vnd.sealed.swf":{source:"iana"},"video/vnd.sealedmedia.softseal.mov":{source:"iana"},"video/vnd.uvvu.mp4":{source:"iana",extensions:["uvu","uvvu"]},"video/vnd.vivo":{source:"iana",extensions:["viv"]},"video/vnd.youtube.yt":{source:"iana"},"video/vp8":{source:"iana"},"video/vp9":{source:"iana"},"video/webm":{source:"apache",compressible:!1,extensions:["webm"]},"video/x-f4v":{source:"apache",extensions:["f4v"]},"video/x-fli":{source:"apache",extensions:["fli"]},"video/x-flv":{source:"apache",compressible:!1,extensions:["flv"]},"video/x-m4v":{source:"apache",extensions:["m4v"]},"video/x-matroska":{source:"apache",compressible:!1,extensions:["mkv","mk3d","mks"]},"video/x-mng":{source:"apache",extensions:["mng"]},"video/x-ms-asf":{source:"apache",extensions:["asf","asx"]},"video/x-ms-vob":{source:"apache",extensions:["vob"]},"video/x-ms-wm":{source:"apache",extensions:["wm"]},"video/x-ms-wmv":{source:"apache",compressible:!1,extensions:["wmv"]},"video/x-ms-wmx":{source:"apache",extensions:["wmx"]},"video/x-ms-wvx":{source:"apache",extensions:["wvx"]},"video/x-msvideo":{source:"apache",extensions:["avi"]},"video/x-sgi-movie":{source:"apache",extensions:["movie"]},"video/x-smv":{source:"apache",extensions:["smv"]},"x-conference/x-cooltalk":{source:"apache",extensions:["ice"]},"x-shader/x-fragment":{compressible:!0},"x-shader/x-vertex":{compressible:!0}};function $e(){return Se||(Se=1,function(e){var a,n,s,i=Re?je:(Re=1,je=Me),o=t.extname,r=/^\s*([^;\s]*)(?:;|\s|$)/,c=/^text\//i;function p(e){if(!e||"string"!=typeof e)return!1;var a=r.exec(e),n=a&&i[a[1].toLowerCase()];return n&&n.charset?n.charset:!(!a||!c.test(a[1]))&&"UTF-8"}e.charset=p,e.charsets={lookup:p},e.contentType=function(a){if(!a||"string"!=typeof a)return!1;var n=-1===a.indexOf("/")?e.lookup(a):a;if(!n)return!1;if(-1===n.indexOf("charset")){var t=e.charset(n);t&&(n+="; charset="+t.toLowerCase())}return n},e.extension=function(a){if(!a||"string"!=typeof a)return!1;var n=r.exec(a),t=n&&e.extensions[n[1].toLowerCase()];if(!t||!t.length)return!1;return t[0]},e.extensions=Object.create(null),e.lookup=function(a){if(!a||"string"!=typeof a)return!1;var n=o("x."+a).toLowerCase().substr(1);if(!n)return!1;return e.types[n]||!1},e.types=Object.create(null),a=e.extensions,n=e.types,s=["nginx","apache",void 0,"iana"],Object.keys(i).forEach((function(e){var t=i[e],o=t.extensions;if(o&&o.length){a[e]=o;for(var r=0;r<o.length;r++){var c=o[r];if(n[c]){var p=s.indexOf(i[n[c]].source),l=s.indexOf(t.source);if("application/octet-stream"!==n[c]&&(p>l||p===l&&"application/"===n[c].substr(0,12)))continue}n[c]=e}}}))}(Ne)),Ne}function We(){if(Ce)return qe;Ce=1;var e=Te?Ee:(Te=1,Ee=function(e){var a="function"==typeof setImmediate?setImmediate:"object"==typeof process&&"function"==typeof process.nextTick?process.nextTick:null;a?a(e):setTimeout(e,0)});return qe=function(a){var n=!1;return e((function(){n=!0})),function(t,s){n?a(t,s):e((function(){a(t,s)}))}}}function He(){if(Oe)return Ae;function e(e){"function"==typeof this.jobs[e]&&this.jobs[e]()}return Oe=1,Ae=function(a){Object.keys(a.jobs).forEach(e.bind(a)),a.jobs={}}}function Ve(){if(De)return Fe;De=1;var e=We(),a=He();return Fe=function(n,t,s,i){var o=s.keyedList?s.keyedList[s.index]:s.index;s.jobs[o]=function(a,n,t,s){var i;i=2==a.length?a(t,e(s)):a(t,n,e(s));return i}(t,o,n[o],(function(e,n){o in s.jobs&&(delete s.jobs[o],e?a(s):s.results[o]=n,i(e,s.results))}))}}function Ge(){if(Be)return Pe;return Be=1,Pe=function(e,a){var n=!Array.isArray(e),t={index:0,keyedList:n||a?Object.keys(e):null,jobs:{},results:n?{}:[],size:n?Object.keys(e).length:e.length};a&&t.keyedList.sort(n?a:function(n,t){return a(e[n],e[t])});return t}}function Je(){if(Ue)return Le;Ue=1;var e=He(),a=We();return Le=function(n){if(!Object.keys(this.jobs).length)return;this.index=this.size,e(this),a(n)(null,this.results)}}function Ke(){if(Ie)return ze;Ie=1;var e=Ve(),a=Ge(),n=Je();return ze=function(t,s,i){var o=a(t);for(;o.index<(o.keyedList||t).length;)e(t,s,o,(function(e,a){e?i(e,a):0!==Object.keys(o.jobs).length||i(null,o.results)})),o.index++;return n.bind(o,i)}}var Qe,Ye,Xe,Ze,ea,aa,na,ta,sa,ia,oa,ra,ca,pa,la,ua,da,ma,ha,fa,xa,va,ba,ga,ya,wa,_a,ka,ja,Ra,Sa,Ea,Ta,qa,Ca,Aa,Oa,Fa,Da,Pa,Ba,La,Ua,za,Ia,Na,Ma,$a,Wa,Ha,Va,Ga,Ja,Ka,Qa,Ya,Xa,Za,en,an,nn,tn,sn,on,rn,cn,pn,ln,un,dn,mn,hn,fn,xn,vn,bn,gn,yn,wn,_n,kn,jn={exports:{}};function Rn(){if(Qe)return jn.exports;Qe=1;var e=Ve(),a=Ge(),n=Je();function t(e,a){return e<a?-1:e>a?1:0}return jn.exports=function(t,s,i,o){var r=a(t,i);return e(t,s,r,(function a(n,i){n?o(n,i):(r.index++,r.index<(r.keyedList||t).length?e(t,s,r,a):o(null,r.results))})),n.bind(r,o)},jn.exports.ascending=t,jn.exports.descending=function(e,a){return-1*t(e,a)},jn.exports}function Sn(){if(Xe)return Ye;Xe=1;var e=Rn();return Ye=function(a,n,t){return e(a,n,null,t)}}function En(){return ea?Ze:(ea=1,Ze={parallel:Ke(),serial:Sn(),serialOrdered:Rn()})}function Tn(){return na?aa:(na=1,aa=Object)}function qn(){return sa?ta:(sa=1,ta=Error)}function Cn(){return oa?ia:(oa=1,ia=EvalError)}function An(){return ca?ra:(ca=1,ra=RangeError)}function On(){return la?pa:(la=1,pa=ReferenceError)}function Fn(){return da?ua:(da=1,ua=SyntaxError)}function Dn(){return ha?ma:(ha=1,ma=TypeError)}function Pn(){return xa?fa:(xa=1,fa=URIError)}function Bn(){return ba?va:(ba=1,va=Math.abs)}function Ln(){return ya?ga:(ya=1,ga=Math.floor)}function Un(){return _a?wa:(_a=1,wa=Math.max)}function zn(){return ja?ka:(ja=1,ka=Math.min)}function In(){return Sa?Ra:(Sa=1,Ra=Math.pow)}function Nn(){return Ta?Ea:(Ta=1,Ea=Math.round)}function Mn(){return Ca?qa:(Ca=1,qa=Number.isNaN||function(e){return e!=e})}function $n(){if(Oa)return Aa;Oa=1;var e=Mn();return Aa=function(a){return e(a)||0===a?a:a<0?-1:1}}function Wn(){return Da?Fa:(Da=1,Fa=Object.getOwnPropertyDescriptor)}function Hn(){if(Ba)return Pa;Ba=1;var e=Wn();if(e)try{e([],"length")}catch(a){e=null}return Pa=e}function Vn(){if(Ua)return La;Ua=1;var e=Object.defineProperty||!1;if(e)try{e({},"a",{value:1})}catch(a){e=!1}return La=e}function Gn(){return Ia?za:(Ia=1,za=function(){if("function"!=typeof Symbol||"function"!=typeof Object.getOwnPropertySymbols)return!1;if("symbol"==typeof Symbol.iterator)return!0;var e={},a=Symbol("test"),n=Object(a);if("string"==typeof a)return!1;if("[object Symbol]"!==Object.prototype.toString.call(a))return!1;if("[object Symbol]"!==Object.prototype.toString.call(n))return!1;for(var t in e[a]=42,e)return!1;if("function"==typeof Object.keys&&0!==Object.keys(e).length)return!1;if("function"==typeof Object.getOwnPropertyNames&&0!==Object.getOwnPropertyNames(e).length)return!1;var s=Object.getOwnPropertySymbols(e);if(1!==s.length||s[0]!==a)return!1;if(!Object.prototype.propertyIsEnumerable.call(e,a))return!1;if("function"==typeof Object.getOwnPropertyDescriptor){var i=Object.getOwnPropertyDescriptor(e,a);if(42!==i.value||!0!==i.enumerable)return!1}return!0})}function Jn(){if(Ma)return Na;Ma=1;var e="undefined"!=typeof Symbol&&Symbol,a=Gn();return Na=function(){return"function"==typeof e&&("function"==typeof Symbol&&("symbol"==typeof e("foo")&&("symbol"==typeof Symbol("bar")&&a())))}}function Kn(){return Wa?$a:(Wa=1,$a="undefined"!=typeof Reflect&&Reflect.getPrototypeOf||null)}function Qn(){return Va?Ha:(Va=1,Ha=Tn().getPrototypeOf||null)}function Yn(){if(Ja)return Ga;Ja=1;var e=Object.prototype.toString,a=Math.max,n=function(e,a){for(var n=[],t=0;t<e.length;t+=1)n[t]=e[t];for(var s=0;s<a.length;s+=1)n[s+e.length]=a[s];return n};return Ga=function(t){var s=this;if("function"!=typeof s||"[object Function]"!==e.apply(s))throw new TypeError("Function.prototype.bind called on incompatible "+s);for(var i,o=function(e,a){for(var n=[],t=a,s=0;t<e.length;t+=1,s+=1)n[s]=e[t];return n}(arguments,1),r=a(0,s.length-o.length),c=[],p=0;p<r;p++)c[p]="$"+p;if(i=Function("binder","return function ("+function(e,a){for(var n="",t=0;t<e.length;t+=1)n+=e[t],t+1<e.length&&(n+=a);return n}(c,",")+"){ return binder.apply(this,arguments); }")((function(){if(this instanceof i){var e=s.apply(this,n(o,arguments));return Object(e)===e?e:this}return s.apply(t,n(o,arguments))})),s.prototype){var l=function(){};l.prototype=s.prototype,i.prototype=new l,l.prototype=null}return i},Ga}function Xn(){if(Qa)return Ka;Qa=1;var e=Yn();return Ka=Function.prototype.bind||e}function Zn(){return Xa?Ya:(Xa=1,Ya=Function.prototype.call)}function et(){return en?Za:(en=1,Za=Function.prototype.apply)}function at(){if(sn)return tn;sn=1;var e=Xn(),a=et(),n=Zn(),t=nn?an:(nn=1,an="undefined"!=typeof Reflect&&Reflect&&Reflect.apply);return tn=t||e.call(n,a)}function nt(){if(pn)return cn;pn=1;var e,a=function(){if(rn)return on;rn=1;var e=Xn(),a=Dn(),n=Zn(),t=at();return on=function(s){if(s.length<1||"function"!=typeof s[0])throw new a("a function is required");return t(e,n,s)}}(),n=Hn();try{e=[].__proto__===Array.prototype}catch(e){if(!e||"object"!=typeof e||!("code"in e)||"ERR_PROTO_ACCESS"!==e.code)throw e}var t=!!e&&n&&n(Object.prototype,"__proto__"),s=Object,i=s.getPrototypeOf;return cn=t&&"function"==typeof t.get?a([t.get]):"function"==typeof i&&function(e){return i(null==e?e:s(e))}}function tt(){if(un)return ln;un=1;var e=Kn(),a=Qn(),n=nt();return ln=e?function(a){return e(a)}:a?function(e){if(!e||"object"!=typeof e&&"function"!=typeof e)throw new TypeError("getProto: not an object");return a(e)}:n?function(e){return n(e)}:null}function st(){if(mn)return dn;mn=1;var e=Function.prototype.call,a=Object.prototype.hasOwnProperty,n=Xn();return dn=n.call(e,a)}function it(){if(fn)return hn;var e;fn=1;var a=Tn(),n=qn(),t=Cn(),s=An(),i=On(),o=Fn(),r=Dn(),c=Pn(),p=Bn(),l=Ln(),u=Un(),d=zn(),m=In(),h=Nn(),f=$n(),x=Function,v=function(e){try{return x('"use strict"; return ('+e+").constructor;")()}catch(e){}},b=Hn(),g=Vn(),y=function(){throw new r},w=b?function(){try{return y}catch(e){try{return b(arguments,"callee").get}catch(e){return y}}}():y,_=Jn()(),k=tt(),j=Qn(),R=Kn(),S=et(),E=Zn(),T={},q="undefined"!=typeof Uint8Array&&k?k(Uint8Array):e,C={__proto__:null,"%AggregateError%":"undefined"==typeof AggregateError?e:AggregateError,"%Array%":Array,"%ArrayBuffer%":"undefined"==typeof ArrayBuffer?e:ArrayBuffer,"%ArrayIteratorPrototype%":_&&k?k([][Symbol.iterator]()):e,"%AsyncFromSyncIteratorPrototype%":e,"%AsyncFunction%":T,"%AsyncGenerator%":T,"%AsyncGeneratorFunction%":T,"%AsyncIteratorPrototype%":T,"%Atomics%":"undefined"==typeof Atomics?e:Atomics,"%BigInt%":"undefined"==typeof BigInt?e:BigInt,"%BigInt64Array%":"undefined"==typeof BigInt64Array?e:BigInt64Array,"%BigUint64Array%":"undefined"==typeof BigUint64Array?e:BigUint64Array,"%Boolean%":Boolean,"%DataView%":"undefined"==typeof DataView?e:DataView,"%Date%":Date,"%decodeURI%":decodeURI,"%decodeURIComponent%":decodeURIComponent,"%encodeURI%":encodeURI,"%encodeURIComponent%":encodeURIComponent,"%Error%":n,"%eval%":eval,"%EvalError%":t,"%Float32Array%":"undefined"==typeof Float32Array?e:Float32Array,"%Float64Array%":"undefined"==typeof Float64Array?e:Float64Array,"%FinalizationRegistry%":"undefined"==typeof FinalizationRegistry?e:FinalizationRegistry,"%Function%":x,"%GeneratorFunction%":T,"%Int8Array%":"undefined"==typeof Int8Array?e:Int8Array,"%Int16Array%":"undefined"==typeof Int16Array?e:Int16Array,"%Int32Array%":"undefined"==typeof Int32Array?e:Int32Array,"%isFinite%":isFinite,"%isNaN%":isNaN,"%IteratorPrototype%":_&&k?k(k([][Symbol.iterator]())):e,"%JSON%":"object"==typeof JSON?JSON:e,"%Map%":"undefined"==typeof Map?e:Map,"%MapIteratorPrototype%":"undefined"!=typeof Map&&_&&k?k((new Map)[Symbol.iterator]()):e,"%Math%":Math,"%Number%":Number,"%Object%":a,"%Object.getOwnPropertyDescriptor%":b,"%parseFloat%":parseFloat,"%parseInt%":parseInt,"%Promise%":"undefined"==typeof Promise?e:Promise,"%Proxy%":"undefined"==typeof Proxy?e:Proxy,"%RangeError%":s,"%ReferenceError%":i,"%Reflect%":"undefined"==typeof Reflect?e:Reflect,"%RegExp%":RegExp,"%Set%":"undefined"==typeof Set?e:Set,"%SetIteratorPrototype%":"undefined"!=typeof Set&&_&&k?k((new Set)[Symbol.iterator]()):e,"%SharedArrayBuffer%":"undefined"==typeof SharedArrayBuffer?e:SharedArrayBuffer,"%String%":String,"%StringIteratorPrototype%":_&&k?k(""[Symbol.iterator]()):e,"%Symbol%":_?Symbol:e,"%SyntaxError%":o,"%ThrowTypeError%":w,"%TypedArray%":q,"%TypeError%":r,"%Uint8Array%":"undefined"==typeof Uint8Array?e:Uint8Array,"%Uint8ClampedArray%":"undefined"==typeof Uint8ClampedArray?e:Uint8ClampedArray,"%Uint16Array%":"undefined"==typeof Uint16Array?e:Uint16Array,"%Uint32Array%":"undefined"==typeof Uint32Array?e:Uint32Array,"%URIError%":c,"%WeakMap%":"undefined"==typeof WeakMap?e:WeakMap,"%WeakRef%":"undefined"==typeof WeakRef?e:WeakRef,"%WeakSet%":"undefined"==typeof WeakSet?e:WeakSet,"%Function.prototype.call%":E,"%Function.prototype.apply%":S,"%Object.defineProperty%":g,"%Object.getPrototypeOf%":j,"%Math.abs%":p,"%Math.floor%":l,"%Math.max%":u,"%Math.min%":d,"%Math.pow%":m,"%Math.round%":h,"%Math.sign%":f,"%Reflect.getPrototypeOf%":R};if(k)try{null.error}catch(e){var A=k(k(e));C["%Error.prototype%"]=A}var O=function e(a){var n;if("%AsyncFunction%"===a)n=v("async function () {}");else if("%GeneratorFunction%"===a)n=v("function* () {}");else if("%AsyncGeneratorFunction%"===a)n=v("async function* () {}");else if("%AsyncGenerator%"===a){var t=e("%AsyncGeneratorFunction%");t&&(n=t.prototype)}else if("%AsyncIteratorPrototype%"===a){var s=e("%AsyncGenerator%");s&&k&&(n=k(s.prototype))}return C[a]=n,n},F={__proto__:null,"%ArrayBufferPrototype%":["ArrayBuffer","prototype"],"%ArrayPrototype%":["Array","prototype"],"%ArrayProto_entries%":["Array","prototype","entries"],"%ArrayProto_forEach%":["Array","prototype","forEach"],"%ArrayProto_keys%":["Array","prototype","keys"],"%ArrayProto_values%":["Array","prototype","values"],"%AsyncFunctionPrototype%":["AsyncFunction","prototype"],"%AsyncGenerator%":["AsyncGeneratorFunction","prototype"],"%AsyncGeneratorPrototype%":["AsyncGeneratorFunction","prototype","prototype"],"%BooleanPrototype%":["Boolean","prototype"],"%DataViewPrototype%":["DataView","prototype"],"%DatePrototype%":["Date","prototype"],"%ErrorPrototype%":["Error","prototype"],"%EvalErrorPrototype%":["EvalError","prototype"],"%Float32ArrayPrototype%":["Float32Array","prototype"],"%Float64ArrayPrototype%":["Float64Array","prototype"],"%FunctionPrototype%":["Function","prototype"],"%Generator%":["GeneratorFunction","prototype"],"%GeneratorPrototype%":["GeneratorFunction","prototype","prototype"],"%Int8ArrayPrototype%":["Int8Array","prototype"],"%Int16ArrayPrototype%":["Int16Array","prototype"],"%Int32ArrayPrototype%":["Int32Array","prototype"],"%JSONParse%":["JSON","parse"],"%JSONStringify%":["JSON","stringify"],"%MapPrototype%":["Map","prototype"],"%NumberPrototype%":["Number","prototype"],"%ObjectPrototype%":["Object","prototype"],"%ObjProto_toString%":["Object","prototype","toString"],"%ObjProto_valueOf%":["Object","prototype","valueOf"],"%PromisePrototype%":["Promise","prototype"],"%PromiseProto_then%":["Promise","prototype","then"],"%Promise_all%":["Promise","all"],"%Promise_reject%":["Promise","reject"],"%Promise_resolve%":["Promise","resolve"],"%RangeErrorPrototype%":["RangeError","prototype"],"%ReferenceErrorPrototype%":["ReferenceError","prototype"],"%RegExpPrototype%":["RegExp","prototype"],"%SetPrototype%":["Set","prototype"],"%SharedArrayBufferPrototype%":["SharedArrayBuffer","prototype"],"%StringPrototype%":["String","prototype"],"%SymbolPrototype%":["Symbol","prototype"],"%SyntaxErrorPrototype%":["SyntaxError","prototype"],"%TypedArrayPrototype%":["TypedArray","prototype"],"%TypeErrorPrototype%":["TypeError","prototype"],"%Uint8ArrayPrototype%":["Uint8Array","prototype"],"%Uint8ClampedArrayPrototype%":["Uint8ClampedArray","prototype"],"%Uint16ArrayPrototype%":["Uint16Array","prototype"],"%Uint32ArrayPrototype%":["Uint32Array","prototype"],"%URIErrorPrototype%":["URIError","prototype"],"%WeakMapPrototype%":["WeakMap","prototype"],"%WeakSetPrototype%":["WeakSet","prototype"]},D=Xn(),P=st(),B=D.call(E,Array.prototype.concat),L=D.call(S,Array.prototype.splice),U=D.call(E,String.prototype.replace),z=D.call(E,String.prototype.slice),I=D.call(E,RegExp.prototype.exec),N=/[^%.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|%$))/g,M=/\\(\\)?/g,$=function(e,a){var n,t=e;if(P(F,t)&&(t="%"+(n=F[t])[0]+"%"),P(C,t)){var s=C[t];if(s===T&&(s=O(t)),void 0===s&&!a)throw new r("intrinsic "+e+" exists, but is not available. Please file an issue!");return{alias:n,name:t,value:s}}throw new o("intrinsic "+e+" does not exist!")};return hn=function(e,a){if("string"!=typeof e||0===e.length)throw new r("intrinsic name must be a non-empty string");if(arguments.length>1&&"boolean"!=typeof a)throw new r('"allowMissing" argument must be a boolean');if(null===I(/^%?[^%]*%?$/,e))throw new o("`%` may not be present anywhere but at the beginning and end of the intrinsic name");var n=function(e){var a=z(e,0,1),n=z(e,-1);if("%"===a&&"%"!==n)throw new o("invalid intrinsic syntax, expected closing `%`");if("%"===n&&"%"!==a)throw new o("invalid intrinsic syntax, expected opening `%`");var t=[];return U(e,N,(function(e,a,n,s){t[t.length]=n?U(s,M,"$1"):a||e})),t}(e),t=n.length>0?n[0]:"",s=$("%"+t+"%",a),i=s.name,c=s.value,p=!1,l=s.alias;l&&(t=l[0],L(n,B([0,1],l)));for(var u=1,d=!0;u<n.length;u+=1){var m=n[u],h=z(m,0,1),f=z(m,-1);if(('"'===h||"'"===h||"`"===h||'"'===f||"'"===f||"`"===f)&&h!==f)throw new o("property names with quotes must have matching quotes");if("constructor"!==m&&d||(p=!0),P(C,i="%"+(t+="."+m)+"%"))c=C[i];else if(null!=c){if(!(m in c)){if(!a)throw new r("base intrinsic for "+e+" exists, but the property is not available.");return}if(b&&u+1>=n.length){var x=b(c,m);c=(d=!!x)&&"get"in x&&!("originalValue"in x.get)?x.get:c[m]}else d=P(c,m),c=c[m];d&&!p&&(C[i]=c)}}return c},hn}function ot(){if(gn)return bn;gn=1;var e=it()("%Object.defineProperty%",!0),a=function(){if(vn)return xn;vn=1;var e=Gn();return xn=function(){return e()&&!!Symbol.toStringTag}}()(),n=st(),t=Dn(),s=a?Symbol.toStringTag:null;return bn=function(a,i){var o=arguments.length>2&&!!arguments[2]&&arguments[2].force,r=arguments.length>2&&!!arguments[2]&&arguments[2].nonConfigurable;if(void 0!==o&&"boolean"!=typeof o||void 0!==r&&"boolean"!=typeof r)throw new t("if provided, the `overrideIfSet` and `nonConfigurable` options must be booleans");!s||!o&&n(a,s)||(e?e(a,s,{configurable:!r,enumerable:!1,value:i,writable:!1}):a[s]=i)},bn}function rt(){return wn||(wn=1,yn=function(e,a){return Object.keys(a).forEach((function(n){e[n]=e[n]||a[n]})),e}),yn}var ct=function(){if(kn)return _n;kn=1;var n=ke(),c=e,p=t,l=s,u=i,d=o.parse,m=r,h=a.Stream,f=$e(),x=En(),v=ot(),b=rt();function g(e){if(!(this instanceof g))return new g(e);for(var a in this._overheadLength=0,this._valueLength=0,this._valuesToMeasure=[],n.call(this),e=e||{})this[a]=e[a]}return _n=g,c.inherits(g,n),g.LINE_BREAK="\r\n",g.DEFAULT_CONTENT_TYPE="application/octet-stream",g.prototype.append=function(e,a,t){"string"==typeof(t=t||{})&&(t={filename:t});var s=n.prototype.append.bind(this);if("number"==typeof a&&(a=""+a),Array.isArray(a))this._error(new Error("Arrays are not supported."));else{var i=this._multiPartHeader(e,a,t),o=this._multiPartFooter();s(i),s(a),s(o),this._trackLength(i,a,t)}},g.prototype._trackLength=function(e,a,n){var t=0;null!=n.knownLength?t+=+n.knownLength:Buffer.isBuffer(a)?t=a.length:"string"==typeof a&&(t=Buffer.byteLength(a)),this._valueLength+=t,this._overheadLength+=Buffer.byteLength(e)+g.LINE_BREAK.length,a&&(a.path||a.readable&&Object.prototype.hasOwnProperty.call(a,"httpVersion")||a instanceof h)&&(n.knownLength||this._valuesToMeasure.push(a))},g.prototype._lengthRetriever=function(e,a){Object.prototype.hasOwnProperty.call(e,"fd")?null!=e.end&&e.end!=1/0&&null!=e.start?a(null,e.end+1-(e.start?e.start:0)):m.stat(e.path,(function(n,t){var s;n?a(n):(s=t.size-(e.start?e.start:0),a(null,s))})):Object.prototype.hasOwnProperty.call(e,"httpVersion")?a(null,+e.headers["content-length"]):Object.prototype.hasOwnProperty.call(e,"httpModule")?(e.on("response",(function(n){e.pause(),a(null,+n.headers["content-length"])})),e.resume()):a("Unknown stream")},g.prototype._multiPartHeader=function(e,a,n){if("string"==typeof n.header)return n.header;var t,s=this._getContentDisposition(a,n),i=this._getContentType(a,n),o="",r={"Content-Disposition":["form-data",'name="'+e+'"'].concat(s||[]),"Content-Type":[].concat(i||[])};for(var c in"object"==typeof n.header&&b(r,n.header),r)if(Object.prototype.hasOwnProperty.call(r,c)){if(null==(t=r[c]))continue;Array.isArray(t)||(t=[t]),t.length&&(o+=c+": "+t.join("; ")+g.LINE_BREAK)}return"--"+this.getBoundary()+g.LINE_BREAK+o+g.LINE_BREAK},g.prototype._getContentDisposition=function(e,a){var n,t;return"string"==typeof a.filepath?n=p.normalize(a.filepath).replace(/\\/g,"/"):a.filename||e.name||e.path?n=p.basename(a.filename||e.name||e.path):e.readable&&Object.prototype.hasOwnProperty.call(e,"httpVersion")&&(n=p.basename(e.client._httpMessage.path||"")),n&&(t='filename="'+n+'"'),t},g.prototype._getContentType=function(e,a){var n=a.contentType;return!n&&e.name&&(n=f.lookup(e.name)),!n&&e.path&&(n=f.lookup(e.path)),!n&&e.readable&&Object.prototype.hasOwnProperty.call(e,"httpVersion")&&(n=e.headers["content-type"]),n||!a.filepath&&!a.filename||(n=f.lookup(a.filepath||a.filename)),n||"object"!=typeof e||(n=g.DEFAULT_CONTENT_TYPE),n},g.prototype._multiPartFooter=function(){return function(e){var a=g.LINE_BREAK;0===this._streams.length&&(a+=this._lastBoundary()),e(a)}.bind(this)},g.prototype._lastBoundary=function(){return"--"+this.getBoundary()+"--"+g.LINE_BREAK},g.prototype.getHeaders=function(e){var a,n={"content-type":"multipart/form-data; boundary="+this.getBoundary()};for(a in e)Object.prototype.hasOwnProperty.call(e,a)&&(n[a.toLowerCase()]=e[a]);return n},g.prototype.setBoundary=function(e){this._boundary=e},g.prototype.getBoundary=function(){return this._boundary||this._generateBoundary(),this._boundary},g.prototype.getBuffer=function(){for(var e=new Buffer.alloc(0),a=this.getBoundary(),n=0,t=this._streams.length;n<t;n++)"function"!=typeof this._streams[n]&&(e=Buffer.isBuffer(this._streams[n])?Buffer.concat([e,this._streams[n]]):Buffer.concat([e,Buffer.from(this._streams[n])]),"string"==typeof this._streams[n]&&this._streams[n].substring(2,a.length+2)===a||(e=Buffer.concat([e,Buffer.from(g.LINE_BREAK)])));return Buffer.concat([e,Buffer.from(this._lastBoundary())])},g.prototype._generateBoundary=function(){for(var e="--------------------------",a=0;a<24;a++)e+=Math.floor(10*Math.random()).toString(16);this._boundary=e},g.prototype.getLengthSync=function(){var e=this._overheadLength+this._valueLength;return this._streams.length&&(e+=this._lastBoundary().length),this.hasKnownLength()||this._error(new Error("Cannot calculate proper length in synchronous way.")),e},g.prototype.hasKnownLength=function(){var e=!0;return this._valuesToMeasure.length&&(e=!1),e},g.prototype.getLength=function(e){var a=this._overheadLength+this._valueLength;this._streams.length&&(a+=this._lastBoundary().length),this._valuesToMeasure.length?x.parallel(this._valuesToMeasure,this._lengthRetriever,(function(n,t){n?e(n):(t.forEach((function(e){a+=e})),e(null,a))})):process.nextTick(e.bind(this,null,a))},g.prototype.submit=function(e,a){var n,t,s={method:"post"};return"string"==typeof e?(e=d(e),t=b({port:e.port,path:e.pathname,host:e.hostname,protocol:e.protocol},s)):(t=b(e,s)).port||(t.port="https:"==t.protocol?443:80),t.headers=this.getHeaders(e.headers),n="https:"==t.protocol?u.request(t):l.request(t),this.getLength(function(e,t){if(e&&"Unknown stream"!==e)this._error(e);else if(t&&n.setHeader("Content-Length",t),this.pipe(n),a){var s,i=function(e,t){return n.removeListener("error",i),n.removeListener("response",s),a.call(this,e,t)};s=i.bind(this,null),n.on("error",i),n.on("response",s)}}.bind(this)),n},g.prototype._error=function(e){this.error||(this.error=e,this.pause(),this.emit("error",e))},g.prototype.toString=function(){return"[object FormData]"},v(g,"FormData"),_n}(),pt=h(ct);function lt(e){return fe.isPlainObject(e)||fe.isArray(e)}function ut(e){return fe.endsWith(e,"[]")?e.slice(0,-2):e}function dt(e,a,n){return e?e.concat(a).map((function(e,a){return e=ut(e),!n&&a?"["+e+"]":e})).join(n?".":""):a}const mt=fe.toFlatObject(fe,{},null,(function(e){return/^is[A-Z]/.test(e)}));function ht(e,a,n){if(!fe.isObject(e))throw new TypeError("target must be an object");a=a||new(pt||FormData);const t=(n=fe.toFlatObject(n,{metaTokens:!0,dots:!1,indexes:!1},!1,(function(e,a){return!fe.isUndefined(a[e])}))).metaTokens,s=n.visitor||p,i=n.dots,o=n.indexes,r=(n.Blob||"undefined"!=typeof Blob&&Blob)&&fe.isSpecCompliantForm(a);if(!fe.isFunction(s))throw new TypeError("visitor must be a function");function c(e){if(null===e)return"";if(fe.isDate(e))return e.toISOString();if(!r&&fe.isBlob(e))throw new xe("Blob is not supported. Use a Buffer instead.");return fe.isArrayBuffer(e)||fe.isTypedArray(e)?r&&"function"==typeof Blob?new Blob([e]):Buffer.from(e):e}function p(e,n,s){let r=e;if(e&&!s&&"object"==typeof e)if(fe.endsWith(n,"{}"))n=t?n:n.slice(0,-2),e=JSON.stringify(e);else if(fe.isArray(e)&&function(e){return fe.isArray(e)&&!e.some(lt)}(e)||(fe.isFileList(e)||fe.endsWith(n,"[]"))&&(r=fe.toArray(e)))return n=ut(n),r.forEach((function(e,t){!fe.isUndefined(e)&&null!==e&&a.append(!0===o?dt([n],t,i):null===o?n:n+"[]",c(e))})),!1;return!!lt(e)||(a.append(dt(s,n,i),c(e)),!1)}const l=[],u=Object.assign(mt,{defaultVisitor:p,convertValue:c,isVisitable:lt});if(!fe.isObject(e))throw new TypeError("data must be an object");return function e(n,t){if(!fe.isUndefined(n)){if(-1!==l.indexOf(n))throw Error("Circular reference detected in "+t.join("."));l.push(n),fe.forEach(n,(function(n,i){!0===(!(fe.isUndefined(n)||null===n)&&s.call(a,n,fe.isString(i)?i.trim():i,t,u))&&e(n,t?t.concat(i):[i])})),l.pop()}}(e),a}function ft(e){const a={"!":"%21","'":"%27","(":"%28",")":"%29","~":"%7E","%20":"+","%00":"\0"};return encodeURIComponent(e).replace(/[!'()~]|%20|%00/g,(function(e){return a[e]}))}function xt(e,a){this._pairs=[],e&&ht(e,this,a)}const vt=xt.prototype;function bt(e){return encodeURIComponent(e).replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}function gt(e,a,n){if(!a)return e;const t=n&&n.encode||bt;fe.isFunction(n)&&(n={serialize:n});const s=n&&n.serialize;let i;if(i=s?s(a,n):fe.isURLSearchParams(a)?a.toString():new xt(a,n).toString(t),i){const a=e.indexOf("#");-1!==a&&(e=e.slice(0,a)),e+=(-1===e.indexOf("?")?"?":"&")+i}return e}vt.append=function(e,a){this._pairs.push([e,a])},vt.toString=function(e){const a=e?function(a){return e.call(this,a,ft)}:ft;return this._pairs.map((function(e){return a(e[0])+"="+a(e[1])}),"").join("&")};class yt{constructor(){this.handlers=[]}use(e,a,n){return this.handlers.push({fulfilled:e,rejected:a,synchronous:!!n&&n.synchronous,runWhen:n?n.runWhen:null}),this.handlers.length-1}eject(e){this.handlers[e]&&(this.handlers[e]=null)}clear(){this.handlers&&(this.handlers=[])}forEach(e){fe.forEach(this.handlers,(function(a){null!==a&&e(a)}))}}var wt={silentJSONParsing:!0,forcedJSONParsing:!0,clarifyTimeoutError:!1},_t={isNode:!0,classes:{URLSearchParams:o.URLSearchParams,FormData:pt,Blob:"undefined"!=typeof Blob&&Blob||null},protocols:["http","https","file","data"]};const kt="undefined"!=typeof window&&"undefined"!=typeof document,jt="object"==typeof navigator&&navigator||void 0,Rt=kt&&(!jt||["ReactNative","NativeScript","NS"].indexOf(jt.product)<0),St="undefined"!=typeof WorkerGlobalScope&&self instanceof WorkerGlobalScope&&"function"==typeof self.importScripts,Et=kt&&window.location.href||"http://localhost";var Tt={...Object.freeze({__proto__:null,hasBrowserEnv:kt,hasStandardBrowserEnv:Rt,hasStandardBrowserWebWorkerEnv:St,navigator:jt,origin:Et}),..._t};function qt(e){function a(e,n,t,s){let i=e[s++];if("__proto__"===i)return!0;const o=Number.isFinite(+i),r=s>=e.length;if(i=!i&&fe.isArray(t)?t.length:i,r)return fe.hasOwnProp(t,i)?t[i]=[t[i],n]:t[i]=n,!o;t[i]&&fe.isObject(t[i])||(t[i]=[]);return a(e,n,t[i],s)&&fe.isArray(t[i])&&(t[i]=function(e){const a={},n=Object.keys(e);let t;const s=n.length;let i;for(t=0;t<s;t++)i=n[t],a[i]=e[i];return a}(t[i])),!o}if(fe.isFormData(e)&&fe.isFunction(e.entries)){const n={};return fe.forEachEntry(e,((e,t)=>{a(function(e){return fe.matchAll(/\w+|\[(\w*)]/g,e).map((e=>"[]"===e[0]?"":e[1]||e[0]))}(e),t,n,0)})),n}return null}const Ct={transitional:wt,adapter:["xhr","http","fetch"],transformRequest:[function(e,a){const n=a.getContentType()||"",t=n.indexOf("application/json")>-1,s=fe.isObject(e);s&&fe.isHTMLForm(e)&&(e=new FormData(e));if(fe.isFormData(e))return t?JSON.stringify(qt(e)):e;if(fe.isArrayBuffer(e)||fe.isBuffer(e)||fe.isStream(e)||fe.isFile(e)||fe.isBlob(e)||fe.isReadableStream(e))return e;if(fe.isArrayBufferView(e))return e.buffer;if(fe.isURLSearchParams(e))return a.setContentType("application/x-www-form-urlencoded;charset=utf-8",!1),e.toString();let i;if(s){if(n.indexOf("application/x-www-form-urlencoded")>-1)return function(e,a){return ht(e,new Tt.classes.URLSearchParams,Object.assign({visitor:function(e,a,n,t){return Tt.isNode&&fe.isBuffer(e)?(this.append(a,e.toString("base64")),!1):t.defaultVisitor.apply(this,arguments)}},a))}(e,this.formSerializer).toString();if((i=fe.isFileList(e))||n.indexOf("multipart/form-data")>-1){const a=this.env&&this.env.FormData;return ht(i?{"files[]":e}:e,a&&new a,this.formSerializer)}}return s||t?(a.setContentType("application/json",!1),function(e,a,n){if(fe.isString(e))try{return(a||JSON.parse)(e),fe.trim(e)}catch(e){if("SyntaxError"!==e.name)throw e}return(n||JSON.stringify)(e)}(e)):e}],transformResponse:[function(e){const a=this.transitional||Ct.transitional,n=a&&a.forcedJSONParsing,t="json"===this.responseType;if(fe.isResponse(e)||fe.isReadableStream(e))return e;if(e&&fe.isString(e)&&(n&&!this.responseType||t)){const n=!(a&&a.silentJSONParsing)&&t;try{return JSON.parse(e)}catch(e){if(n){if("SyntaxError"===e.name)throw xe.from(e,xe.ERR_BAD_RESPONSE,this,null,this.response);throw e}}}return e}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,maxBodyLength:-1,env:{FormData:Tt.classes.FormData,Blob:Tt.classes.Blob},validateStatus:function(e){return e>=200&&e<300},headers:{common:{Accept:"application/json, text/plain, */*","Content-Type":void 0}}};fe.forEach(["delete","get","head","post","put","patch"],(e=>{Ct.headers[e]={}}));const At=fe.toObjectSet(["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"]);const Ot=Symbol("internals");function Ft(e){return e&&String(e).trim().toLowerCase()}function Dt(e){return!1===e||null==e?e:fe.isArray(e)?e.map(Dt):String(e)}function Pt(e,a,n,t,s){return fe.isFunction(t)?t.call(this,a,n):(s&&(a=n),fe.isString(a)?fe.isString(t)?-1!==a.indexOf(t):fe.isRegExp(t)?t.test(a):void 0:void 0)}let Bt=class{constructor(e){e&&this.set(e)}set(e,a,n){const t=this;function s(e,a,n){const s=Ft(a);if(!s)throw new Error("header name must be a non-empty string");const i=fe.findKey(t,s);(!i||void 0===t[i]||!0===n||void 0===n&&!1!==t[i])&&(t[i||a]=Dt(e))}const i=(e,a)=>fe.forEach(e,((e,n)=>s(e,n,a)));if(fe.isPlainObject(e)||e instanceof this.constructor)i(e,a);else if(fe.isString(e)&&(e=e.trim())&&!/^[-_a-zA-Z0-9^`|~,!#$%&'*+.]+$/.test(e.trim()))i((e=>{const a={};let n,t,s;return e&&e.split("\n").forEach((function(e){s=e.indexOf(":"),n=e.substring(0,s).trim().toLowerCase(),t=e.substring(s+1).trim(),!n||a[n]&&At[n]||("set-cookie"===n?a[n]?a[n].push(t):a[n]=[t]:a[n]=a[n]?a[n]+", "+t:t)})),a})(e),a);else if(fe.isHeaders(e))for(const[a,t]of e.entries())s(t,a,n);else null!=e&&s(a,e,n);return this}get(e,a){if(e=Ft(e)){const n=fe.findKey(this,e);if(n){const e=this[n];if(!a)return e;if(!0===a)return function(e){const a=Object.create(null),n=/([^\s,;=]+)\s*(?:=\s*([^,;]+))?/g;let t;for(;t=n.exec(e);)a[t[1]]=t[2];return a}(e);if(fe.isFunction(a))return a.call(this,e,n);if(fe.isRegExp(a))return a.exec(e);throw new TypeError("parser must be boolean|regexp|function")}}}has(e,a){if(e=Ft(e)){const n=fe.findKey(this,e);return!(!n||void 0===this[n]||a&&!Pt(0,this[n],n,a))}return!1}delete(e,a){const n=this;let t=!1;function s(e){if(e=Ft(e)){const s=fe.findKey(n,e);!s||a&&!Pt(0,n[s],s,a)||(delete n[s],t=!0)}}return fe.isArray(e)?e.forEach(s):s(e),t}clear(e){const a=Object.keys(this);let n=a.length,t=!1;for(;n--;){const s=a[n];e&&!Pt(0,this[s],s,e,!0)||(delete this[s],t=!0)}return t}normalize(e){const a=this,n={};return fe.forEach(this,((t,s)=>{const i=fe.findKey(n,s);if(i)return a[i]=Dt(t),void delete a[s];const o=e?function(e){return e.trim().toLowerCase().replace(/([a-z\d])(\w*)/g,((e,a,n)=>a.toUpperCase()+n))}(s):String(s).trim();o!==s&&delete a[s],a[o]=Dt(t),n[o]=!0})),this}concat(...e){return this.constructor.concat(this,...e)}toJSON(e){const a=Object.create(null);return fe.forEach(this,((n,t)=>{null!=n&&!1!==n&&(a[t]=e&&fe.isArray(n)?n.join(", "):n)})),a}[Symbol.iterator](){return Object.entries(this.toJSON())[Symbol.iterator]()}toString(){return Object.entries(this.toJSON()).map((([e,a])=>e+": "+a)).join("\n")}get[Symbol.toStringTag](){return"AxiosHeaders"}static from(e){return e instanceof this?e:new this(e)}static concat(e,...a){const n=new this(e);return a.forEach((e=>n.set(e))),n}static accessor(e){const a=(this[Ot]=this[Ot]={accessors:{}}).accessors,n=this.prototype;function t(e){const t=Ft(e);a[t]||(!function(e,a){const n=fe.toCamelCase(" "+a);["get","set","has"].forEach((t=>{Object.defineProperty(e,t+n,{value:function(e,n,s){return this[t].call(this,a,e,n,s)},configurable:!0})}))}(n,e),a[t]=!0)}return fe.isArray(e)?e.forEach(t):t(e),this}};function Lt(e,a){const n=this||Ct,t=a||n,s=Bt.from(t.headers);let i=t.data;return fe.forEach(e,(function(e){i=e.call(n,i,s.normalize(),a?a.status:void 0)})),s.normalize(),i}function Ut(e){return!(!e||!e.__CANCEL__)}function zt(e,a,n){xe.call(this,null==e?"canceled":e,xe.ERR_CANCELED,a,n),this.name="CanceledError"}function It(e,a,n){const t=n.config.validateStatus;n.status&&t&&!t(n.status)?a(new xe("Request failed with status code "+n.status,[xe.ERR_BAD_REQUEST,xe.ERR_BAD_RESPONSE][Math.floor(n.status/100)-4],n.config,n.request,n)):e(n)}function Nt(e,a){return e&&!/^([a-z][a-z\d+\-.]*:)?\/\//i.test(a)?function(e,a){return a?e.replace(/\/?\/$/,"")+"/"+a.replace(/^\/+/,""):e}(e,a):a}Bt.accessor(["Content-Type","Content-Length","Accept","Accept-Encoding","User-Agent","Authorization"]),fe.reduceDescriptors(Bt.prototype,(({value:e},a)=>{let n=a[0].toUpperCase()+a.slice(1);return{get:()=>e,set(e){this[n]=e}}})),fe.freezeMethods(Bt),fe.inherits(zt,xe,{__CANCEL__:!0});var Mt,$t={};var Wt,Ht,Vt,Gt,Jt,Kt=h(function(){if(Mt)return $t;Mt=1;var e=o.parse,a={ftp:21,gopher:70,http:80,https:443,ws:80,wss:443},n=String.prototype.endsWith||function(e){return e.length<=this.length&&-1!==this.indexOf(e,this.length-e.length)};function t(e){return process.env[e.toLowerCase()]||process.env[e.toUpperCase()]||""}return $t.getProxyForUrl=function(s){var i="string"==typeof s?e(s):s||{},o=i.protocol,r=i.host,c=i.port;if("string"!=typeof r||!r||"string"!=typeof o)return"";if(o=o.split(":",1)[0],!function(e,a){var s=(t("npm_config_no_proxy")||t("no_proxy")).toLowerCase();if(!s)return!0;if("*"===s)return!1;return s.split(/[,\s]/).every((function(t){if(!t)return!0;var s=t.match(/^(.+):(\d+)$/),i=s?s[1]:t,o=s?parseInt(s[2]):0;return!(!o||o===a)||(/^[.*]/.test(i)?("*"===i.charAt(0)&&(i=i.slice(1)),!n.call(e,i)):e!==i)}))}(r=r.replace(/:\d*$/,""),c=parseInt(c)||a[o]||0))return"";var p=t("npm_config_"+o+"_proxy")||t(o+"_proxy")||t("npm_config_proxy")||t("all_proxy");return p&&-1===p.indexOf("://")&&(p=o+"://"+p),p},$t}()),Qt={exports:{}},Yt={exports:{}},Xt={exports:{}};function Zt(){if(Ht)return Wt;Ht=1;var e=1e3,a=60*e,n=60*a,t=24*n,s=7*t,i=365.25*t;function o(e,a,n,t){var s=a>=1.5*n;return Math.round(e/n)+" "+t+(s?"s":"")}return Wt=function(r,c){c=c||{};var p=typeof r;if("string"===p&&r.length>0)return function(o){if((o=String(o)).length>100)return;var r=/^(-?(?:\d+)?\.?\d+) *(milliseconds?|msecs?|ms|seconds?|secs?|s|minutes?|mins?|m|hours?|hrs?|h|days?|d|weeks?|w|years?|yrs?|y)?$/i.exec(o);if(!r)return;var c=parseFloat(r[1]);switch((r[2]||"ms").toLowerCase()){case"years":case"year":case"yrs":case"yr":case"y":return c*i;case"weeks":case"week":case"w":return c*s;case"days":case"day":case"d":return c*t;case"hours":case"hour":case"hrs":case"hr":case"h":return c*n;case"minutes":case"minute":case"mins":case"min":case"m":return c*a;case"seconds":case"second":case"secs":case"sec":case"s":return c*e;case"milliseconds":case"millisecond":case"msecs":case"msec":case"ms":return c;default:return}}(r);if("number"===p&&isFinite(r))return c.long?function(s){var i=Math.abs(s);if(i>=t)return o(s,i,t,"day");if(i>=n)return o(s,i,n,"hour");if(i>=a)return o(s,i,a,"minute");if(i>=e)return o(s,i,e,"second");return s+" ms"}(r):function(s){var i=Math.abs(s);if(i>=t)return Math.round(s/t)+"d";if(i>=n)return Math.round(s/n)+"h";if(i>=a)return Math.round(s/a)+"m";if(i>=e)return Math.round(s/e)+"s";return s+"ms"}(r);throw new Error("val is not a non-empty string or a valid number. val="+JSON.stringify(r))},Wt}function es(){if(Gt)return Vt;return Gt=1,Vt=function(e){function a(e){let t,s,i,o=null;function r(...e){if(!r.enabled)return;const n=r,s=Number(new Date),i=s-(t||s);n.diff=i,n.prev=t,n.curr=s,t=s,e[0]=a.coerce(e[0]),"string"!=typeof e[0]&&e.unshift("%O");let o=0;e[0]=e[0].replace(/%([a-zA-Z%])/g,((t,s)=>{if("%%"===t)return"%";o++;const i=a.formatters[s];if("function"==typeof i){const a=e[o];t=i.call(n,a),e.splice(o,1),o--}return t})),a.formatArgs.call(n,e);(n.log||a.log).apply(n,e)}return r.namespace=e,r.useColors=a.useColors(),r.color=a.selectColor(e),r.extend=n,r.destroy=a.destroy,Object.defineProperty(r,"enabled",{enumerable:!0,configurable:!1,get:()=>null!==o?o:(s!==a.namespaces&&(s=a.namespaces,i=a.enabled(e)),i),set:e=>{o=e}}),"function"==typeof a.init&&a.init(r),r}function n(e,n){const t=a(this.namespace+(void 0===n?":":n)+e);return t.log=this.log,t}function t(e,a){let n=0,t=0,s=-1,i=0;for(;n<e.length;)if(t<a.length&&(a[t]===e[n]||"*"===a[t]))"*"===a[t]?(s=t,i=n,t++):(n++,t++);else{if(-1===s)return!1;t=s+1,i++,n=i}for(;t<a.length&&"*"===a[t];)t++;return t===a.length}return a.debug=a,a.default=a,a.coerce=function(e){if(e instanceof Error)return e.stack||e.message;return e},a.disable=function(){const e=[...a.names,...a.skips.map((e=>"-"+e))].join(",");return a.enable(""),e},a.enable=function(e){a.save(e),a.namespaces=e,a.names=[],a.skips=[];const n=("string"==typeof e?e:"").trim().replace(" ",",").split(",").filter(Boolean);for(const e of n)"-"===e[0]?a.skips.push(e.slice(1)):a.names.push(e)},a.enabled=function(e){for(const n of a.skips)if(t(e,n))return!1;for(const n of a.names)if(t(e,n))return!0;return!1},a.humanize=Zt(),a.destroy=function(){console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`.")},Object.keys(e).forEach((n=>{a[n]=e[n]})),a.names=[],a.skips=[],a.formatters={},a.selectColor=function(e){let n=0;for(let a=0;a<e.length;a++)n=(n<<5)-n+e.charCodeAt(a),n|=0;return a.colors[Math.abs(n)%a.colors.length]},a.enable(a.load()),a},Vt}var as,ns,ts,ss,is,os,rs,cs,ps,ls={exports:{}};function us(){return ns?as:(ns=1,as=(e,a=process.argv)=>{const n=e.startsWith("-")?"":1===e.length?"-":"--",t=a.indexOf(n+e),s=a.indexOf("--");return-1!==t&&(-1===s||t<s)})}function ds(){return is||(is=1,function(a,n){const t=p,s=e;n.init=function(e){e.inspectOpts={};const a=Object.keys(n.inspectOpts);for(let t=0;t<a.length;t++)e.inspectOpts[a[t]]=n.inspectOpts[a[t]]},n.log=function(...e){return process.stderr.write(s.formatWithOptions(n.inspectOpts,...e)+"\n")},n.formatArgs=function(e){const{namespace:t,useColors:s}=this;if(s){const n=this.color,s="[3"+(n<8?n:"8;5;"+n),i=`  ${s};1m${t} [0m`;e[0]=i+e[0].split("\n").join("\n"+i),e.push(s+"m+"+a.exports.humanize(this.diff)+"[0m")}else e[0]=function(){if(n.inspectOpts.hideDate)return"";return(new Date).toISOString()+" "}()+t+" "+e[0]},n.save=function(e){e?process.env.DEBUG=e:delete process.env.DEBUG},n.load=function(){return process.env.DEBUG},n.useColors=function(){return"colors"in n.inspectOpts?Boolean(n.inspectOpts.colors):t.isatty(process.stderr.fd)},n.destroy=s.deprecate((()=>{}),"Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`."),n.colors=[6,2,3,4,5,1];try{const e=function(){if(ss)return ts;ss=1;const e=l,a=p,n=us(),{env:t}=process;let s;function i(e){return 0!==e&&{level:e,hasBasic:!0,has256:e>=2,has16m:e>=3}}function o(a,i){if(0===s)return 0;if(n("color=16m")||n("color=full")||n("color=truecolor"))return 3;if(n("color=256"))return 2;if(a&&!i&&void 0===s)return 0;const o=s||0;if("dumb"===t.TERM)return o;if("win32"===process.platform){const a=e.release().split(".");return Number(a[0])>=10&&Number(a[2])>=10586?Number(a[2])>=14931?3:2:1}if("CI"in t)return["TRAVIS","CIRCLECI","APPVEYOR","GITLAB_CI","GITHUB_ACTIONS","BUILDKITE"].some((e=>e in t))||"codeship"===t.CI_NAME?1:o;if("TEAMCITY_VERSION"in t)return/^(9\.(0*[1-9]\d*)\.|\d{2,}\.)/.test(t.TEAMCITY_VERSION)?1:0;if("truecolor"===t.COLORTERM)return 3;if("TERM_PROGRAM"in t){const e=parseInt((t.TERM_PROGRAM_VERSION||"").split(".")[0],10);switch(t.TERM_PROGRAM){case"iTerm.app":return e>=3?3:2;case"Apple_Terminal":return 2}}return/-256(color)?$/i.test(t.TERM)?2:/^screen|^xterm|^vt100|^vt220|^rxvt|color|ansi|cygwin|linux/i.test(t.TERM)||"COLORTERM"in t?1:o}return n("no-color")||n("no-colors")||n("color=false")||n("color=never")?s=0:(n("color")||n("colors")||n("color=true")||n("color=always"))&&(s=1),"FORCE_COLOR"in t&&(s="true"===t.FORCE_COLOR?1:"false"===t.FORCE_COLOR?0:0===t.FORCE_COLOR.length?1:Math.min(parseInt(t.FORCE_COLOR,10),3)),ts={supportsColor:function(e){return i(o(e,e&&e.isTTY))},stdout:i(o(!0,a.isatty(1))),stderr:i(o(!0,a.isatty(2)))},ts}();e&&(e.stderr||e).level>=2&&(n.colors=[20,21,26,27,32,33,38,39,40,41,42,43,44,45,56,57,62,63,68,69,74,75,76,77,78,79,80,81,92,93,98,99,112,113,128,129,134,135,148,149,160,161,162,163,164,165,166,167,168,169,170,171,172,173,178,179,184,185,196,197,198,199,200,201,202,203,204,205,206,207,208,209,214,215,220,221])}catch(e){}n.inspectOpts=Object.keys(process.env).filter((e=>/^debug_/i.test(e))).reduce(((e,a)=>{const n=a.substring(6).toLowerCase().replace(/_([a-z])/g,((e,a)=>a.toUpperCase()));let t=process.env[a];return t=!!/^(yes|on|true|enabled)$/i.test(t)||!/^(no|off|false|disabled)$/i.test(t)&&("null"===t?null:Number(t)),e[n]=t,e}),{}),a.exports=es()(n);const{formatters:i}=a.exports;i.o=function(e){return this.inspectOpts.colors=this.useColors,s.inspect(e,this.inspectOpts).split("\n").map((e=>e.trim())).join(" ")},i.O=function(e){return this.inspectOpts.colors=this.useColors,s.inspect(e,this.inspectOpts)}}(ls,ls.exports)),ls.exports}function ms(){return os||(os=1,"undefined"==typeof process||"renderer"===process.type||!0===process.browser||process.__nwjs?Yt.exports=(Jt||(Jt=1,function(e,a){a.formatArgs=function(a){if(a[0]=(this.useColors?"%c":"")+this.namespace+(this.useColors?" %c":" ")+a[0]+(this.useColors?"%c ":" ")+"+"+e.exports.humanize(this.diff),!this.useColors)return;const n="color: "+this.color;a.splice(1,0,n,"color: inherit");let t=0,s=0;a[0].replace(/%[a-zA-Z%]/g,(e=>{"%%"!==e&&(t++,"%c"===e&&(s=t))})),a.splice(s,0,n)},a.save=function(e){try{e?a.storage.setItem("debug",e):a.storage.removeItem("debug")}catch(e){}},a.load=function(){let e;try{e=a.storage.getItem("debug")}catch(e){}return!e&&"undefined"!=typeof process&&"env"in process&&(e=process.env.DEBUG),e},a.useColors=function(){if("undefined"!=typeof window&&window.process&&("renderer"===window.process.type||window.process.__nwjs))return!0;if("undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/(edge|trident)\/(\d+)/))return!1;let e;return"undefined"!=typeof document&&document.documentElement&&document.documentElement.style&&document.documentElement.style.WebkitAppearance||"undefined"!=typeof window&&window.console&&(window.console.firebug||window.console.exception&&window.console.table)||"undefined"!=typeof navigator&&navigator.userAgent&&(e=navigator.userAgent.toLowerCase().match(/firefox\/(\d+)/))&&parseInt(e[1],10)>=31||"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/applewebkit\/(\d+)/)},a.storage=function(){try{return localStorage}catch(e){}}(),a.destroy=(()=>{let e=!1;return()=>{e||(e=!0,console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`."))}})(),a.colors=["#0000CC","#0000FF","#0033CC","#0033FF","#0066CC","#0066FF","#0099CC","#0099FF","#00CC00","#00CC33","#00CC66","#00CC99","#00CCCC","#00CCFF","#3300CC","#3300FF","#3333CC","#3333FF","#3366CC","#3366FF","#3399CC","#3399FF","#33CC00","#33CC33","#33CC66","#33CC99","#33CCCC","#33CCFF","#6600CC","#6600FF","#6633CC","#6633FF","#66CC00","#66CC33","#9900CC","#9900FF","#9933CC","#9933FF","#99CC00","#99CC33","#CC0000","#CC0033","#CC0066","#CC0099","#CC00CC","#CC00FF","#CC3300","#CC3333","#CC3366","#CC3399","#CC33CC","#CC33FF","#CC6600","#CC6633","#CC9900","#CC9933","#CCCC00","#CCCC33","#FF0000","#FF0033","#FF0066","#FF0099","#FF00CC","#FF00FF","#FF3300","#FF3333","#FF3366","#FF3399","#FF33CC","#FF33FF","#FF6600","#FF6633","#FF9900","#FF9933","#FFCC00","#FFCC33"],a.log=console.debug||console.log||(()=>{}),e.exports=es()(a);const{formatters:n}=e.exports;n.j=function(e){try{return JSON.stringify(e)}catch(e){return"[UnexpectedJSONParseError]: "+e.message}}}(Xt,Xt.exports)),Xt.exports):Yt.exports=ds()),Yt.exports}var hs=function(){if(ps)return Qt.exports;ps=1;var e,n,t,r=o,p=r.URL,l=s,u=i,d=a.Writable,m=c,h=function(){return cs||(cs=1,rs=function(){if(!e){try{e=ms()("follow-redirects")}catch(e){}"function"!=typeof e&&(e=function(){})}e.apply(null,arguments)}),rs;var e}();e="undefined"!=typeof process,n="undefined"!=typeof window&&"undefined"!=typeof document,t=P(Error.captureStackTrace),e||!n&&t||console.warn("The follow-redirects package should be excluded from browser builds.");var f=!1;try{m(new p(""))}catch(e){f="ERR_INVALID_URL"===e.code}var x=["auth","host","hostname","href","path","pathname","port","protocol","query","search","hash"],v=["abort","aborted","connect","error","socket","timeout"],b=Object.create(null);v.forEach((function(e){b[e]=function(a,n,t){this._redirectable.emit(e,a,n,t)}}));var g=O("ERR_INVALID_URL","Invalid URL",TypeError),y=O("ERR_FR_REDIRECTION_FAILURE","Redirected request failed"),w=O("ERR_FR_TOO_MANY_REDIRECTS","Maximum number of redirects exceeded",y),_=O("ERR_FR_MAX_BODY_LENGTH_EXCEEDED","Request body larger than maxBodyLength limit"),k=O("ERR_STREAM_WRITE_AFTER_END","write after end"),j=d.prototype.destroy||E;function R(e,a){d.call(this),this._sanitizeOptions(e),this._options=e,this._ended=!1,this._ending=!1,this._redirectCount=0,this._redirects=[],this._requestBodyLength=0,this._requestBodyBuffers=[],a&&this.on("response",a);var n=this;this._onNativeResponse=function(e){try{n._processResponse(e)}catch(e){n.emit("error",e instanceof y?e:new y({cause:e}))}},this._performRequest()}function S(e){var a={maxRedirects:21,maxBodyLength:10485760},n={};return Object.keys(e).forEach((function(t){var s=t+":",i=n[s]=e[t],o=a[t]=Object.create(i);Object.defineProperties(o,{request:{value:function(e,t,i){var o;return o=e,p&&o instanceof p?e=C(e):D(e)?e=C(T(e)):(i=t,t=q(e),e={protocol:s}),P(t)&&(i=t,t=null),(t=Object.assign({maxRedirects:a.maxRedirects,maxBodyLength:a.maxBodyLength},e,t)).nativeProtocols=n,D(t.host)||D(t.hostname)||(t.hostname="::1"),m.equal(t.protocol,s,"protocol mismatch"),h("options",t),new R(t,i)},configurable:!0,enumerable:!0,writable:!0},get:{value:function(e,a,n){var t=o.request(e,a,n);return t.end(),t},configurable:!0,enumerable:!0,writable:!0}})})),a}function E(){}function T(e){var a;if(f)a=new p(e);else if(!D((a=q(r.parse(e))).protocol))throw new g({input:e});return a}function q(e){if(/^\[/.test(e.hostname)&&!/^\[[:0-9a-f]+\]$/i.test(e.hostname))throw new g({input:e.href||e});if(/^\[/.test(e.host)&&!/^\[[:0-9a-f]+\](:\d+)?$/i.test(e.host))throw new g({input:e.href||e});return e}function C(e,a){var n=a||{};for(var t of x)n[t]=e[t];return n.hostname.startsWith("[")&&(n.hostname=n.hostname.slice(1,-1)),""!==n.port&&(n.port=Number(n.port)),n.path=n.search?n.pathname+n.search:n.pathname,n}function A(e,a){var n;for(var t in a)e.test(t)&&(n=a[t],delete a[t]);return null==n?void 0:String(n).trim()}function O(e,a,n){function t(n){P(Error.captureStackTrace)&&Error.captureStackTrace(this,this.constructor),Object.assign(this,n||{}),this.code=e,this.message=this.cause?a+": "+this.cause.message:a}return t.prototype=new(n||Error),Object.defineProperties(t.prototype,{constructor:{value:t,enumerable:!1},name:{value:"Error ["+e+"]",enumerable:!1}}),t}function F(e,a){for(var n of v)e.removeListener(n,b[n]);e.on("error",E),e.destroy(a)}function D(e){return"string"==typeof e||e instanceof String}function P(e){return"function"==typeof e}return R.prototype=Object.create(d.prototype),R.prototype.abort=function(){F(this._currentRequest),this._currentRequest.abort(),this.emit("abort")},R.prototype.destroy=function(e){return F(this._currentRequest,e),j.call(this,e),this},R.prototype.write=function(e,a,n){if(this._ending)throw new k;if(!D(e)&&("object"!=typeof(t=e)||!("length"in t)))throw new TypeError("data should be a string, Buffer or Uint8Array");var t;P(a)&&(n=a,a=null),0!==e.length?this._requestBodyLength+e.length<=this._options.maxBodyLength?(this._requestBodyLength+=e.length,this._requestBodyBuffers.push({data:e,encoding:a}),this._currentRequest.write(e,a,n)):(this.emit("error",new _),this.abort()):n&&n()},R.prototype.end=function(e,a,n){if(P(e)?(n=e,e=a=null):P(a)&&(n=a,a=null),e){var t=this,s=this._currentRequest;this.write(e,a,(function(){t._ended=!0,s.end(null,null,n)})),this._ending=!0}else this._ended=this._ending=!0,this._currentRequest.end(null,null,n)},R.prototype.setHeader=function(e,a){this._options.headers[e]=a,this._currentRequest.setHeader(e,a)},R.prototype.removeHeader=function(e){delete this._options.headers[e],this._currentRequest.removeHeader(e)},R.prototype.setTimeout=function(e,a){var n=this;function t(a){a.setTimeout(e),a.removeListener("timeout",a.destroy),a.addListener("timeout",a.destroy)}function s(a){n._timeout&&clearTimeout(n._timeout),n._timeout=setTimeout((function(){n.emit("timeout"),i()}),e),t(a)}function i(){n._timeout&&(clearTimeout(n._timeout),n._timeout=null),n.removeListener("abort",i),n.removeListener("error",i),n.removeListener("response",i),n.removeListener("close",i),a&&n.removeListener("timeout",a),n.socket||n._currentRequest.removeListener("socket",s)}return a&&this.on("timeout",a),this.socket?s(this.socket):this._currentRequest.once("socket",s),this.on("socket",t),this.on("abort",i),this.on("error",i),this.on("response",i),this.on("close",i),this},["flushHeaders","getHeader","setNoDelay","setSocketKeepAlive"].forEach((function(e){R.prototype[e]=function(a,n){return this._currentRequest[e](a,n)}})),["aborted","connection","socket"].forEach((function(e){Object.defineProperty(R.prototype,e,{get:function(){return this._currentRequest[e]}})})),R.prototype._sanitizeOptions=function(e){if(e.headers||(e.headers={}),e.host&&(e.hostname||(e.hostname=e.host),delete e.host),!e.pathname&&e.path){var a=e.path.indexOf("?");a<0?e.pathname=e.path:(e.pathname=e.path.substring(0,a),e.search=e.path.substring(a))}},R.prototype._performRequest=function(){var e=this._options.protocol,a=this._options.nativeProtocols[e];if(!a)throw new TypeError("Unsupported protocol "+e);if(this._options.agents){var n=e.slice(0,-1);this._options.agent=this._options.agents[n]}var t=this._currentRequest=a.request(this._options,this._onNativeResponse);for(var s of(t._redirectable=this,v))t.on(s,b[s]);if(this._currentUrl=/^\//.test(this._options.path)?r.format(this._options):this._options.path,this._isRedirect){var i=0,o=this,c=this._requestBodyBuffers;!function e(a){if(t===o._currentRequest)if(a)o.emit("error",a);else if(i<c.length){var n=c[i++];t.finished||t.write(n.data,n.encoding,e)}else o._ended&&t.end()}()}},R.prototype._processResponse=function(e){var a=e.statusCode;this._options.trackRedirects&&this._redirects.push({url:this._currentUrl,headers:e.headers,statusCode:a});var n,t=e.headers.location;if(!t||!1===this._options.followRedirects||a<300||a>=400)return e.responseUrl=this._currentUrl,e.redirects=this._redirects,this.emit("response",e),void(this._requestBodyBuffers=[]);if(F(this._currentRequest),e.destroy(),++this._redirectCount>this._options.maxRedirects)throw new w;var s=this._options.beforeRedirect;s&&(n=Object.assign({Host:e.req.getHeader("host")},this._options.headers));var i=this._options.method;((301===a||302===a)&&"POST"===this._options.method||303===a&&!/^(?:GET|HEAD)$/.test(this._options.method))&&(this._options.method="GET",this._requestBodyBuffers=[],A(/^content-/i,this._options.headers));var o,c,l=A(/^host$/i,this._options.headers),u=T(this._currentUrl),d=l||u.host,x=/^\w+:/.test(t)?this._currentUrl:r.format(Object.assign(u,{host:d})),v=(o=t,c=x,f?new p(o,c):T(r.resolve(c,o)));if(h("redirecting to",v.href),this._isRedirect=!0,C(v,this._options),(v.protocol!==u.protocol&&"https:"!==v.protocol||v.host!==d&&!function(e,a){m(D(e)&&D(a));var n=e.length-a.length-1;return n>0&&"."===e[n]&&e.endsWith(a)}(v.host,d))&&A(/^(?:(?:proxy-)?authorization|cookie)$/i,this._options.headers),P(s)){var b={headers:e.headers,statusCode:a},g={url:x,method:i,headers:n};s(this._options,b,g),this._sanitizeOptions(this._options)}this._performRequest()},Qt.exports=S({http:l,https:u}),Qt.exports.wrap=S,Qt.exports}(),fs=h(hs);const xs="1.7.9";function vs(e){const a=/^([-+\w]{1,25})(:?\/\/|:)/.exec(e);return a&&a[1]||""}const bs=/^(?:([^;]+);)?(?:[^;]+;)?(base64|),([\s\S]*)$/;const gs=Symbol("internals");class ys extends a.Transform{constructor(e){super({readableHighWaterMark:(e=fe.toFlatObject(e,{maxRate:0,chunkSize:65536,minChunkSize:100,timeWindow:500,ticksRate:2,samplesCount:15},null,((e,a)=>!fe.isUndefined(a[e])))).chunkSize});const a=this[gs]={timeWindow:e.timeWindow,chunkSize:e.chunkSize,maxRate:e.maxRate,minChunkSize:e.minChunkSize,bytesSeen:0,isCaptured:!1,notifiedBytesLoaded:0,ts:Date.now(),bytes:0,onReadCallback:null};this.on("newListener",(e=>{"progress"===e&&(a.isCaptured||(a.isCaptured=!0))}))}_read(e){const a=this[gs];return a.onReadCallback&&a.onReadCallback(),super._read(e)}_transform(e,a,n){const t=this[gs],s=t.maxRate,i=this.readableHighWaterMark,o=t.timeWindow,r=s/(1e3/o),c=!1!==t.minChunkSize?Math.max(t.minChunkSize,.01*r):0,p=(e,a)=>{const n=Buffer.byteLength(e);t.bytesSeen+=n,t.bytes+=n,t.isCaptured&&this.emit("progress",t.bytesSeen),this.push(e)?process.nextTick(a):t.onReadCallback=()=>{t.onReadCallback=null,process.nextTick(a)}},l=(e,a)=>{const n=Buffer.byteLength(e);let l,u=null,d=i,m=0;if(s){const e=Date.now();(!t.ts||(m=e-t.ts)>=o)&&(t.ts=e,l=r-t.bytes,t.bytes=l<0?-l:0,m=0),l=r-t.bytes}if(s){if(l<=0)return setTimeout((()=>{a(null,e)}),o-m);l<d&&(d=l)}d&&n>d&&n-d>c&&(u=e.subarray(d),e=e.subarray(0,d)),p(e,u?()=>{process.nextTick(a,null,u)}:a)};l(e,(function e(a,t){if(a)return n(a);t?l(t,e):n(null)}))}}const{asyncIterator:ws}=Symbol,_s=async function*(e){e.stream?yield*e.stream():e.arrayBuffer?yield await e.arrayBuffer():e[ws]?yield*e[ws]():yield e},ks=fe.ALPHABET.ALPHA_DIGIT+"-_",js="function"==typeof TextEncoder?new TextEncoder:new e.TextEncoder,Rs="\r\n",Ss=js.encode(Rs);class Es{constructor(e,a){const{escapeName:n}=this.constructor,t=fe.isString(a);let s=`Content-Disposition: form-data; name="${n(e)}"${!t&&a.name?`; filename="${n(a.name)}"`:""}${Rs}`;t?a=js.encode(String(a).replace(/\r?\n|\r\n?/g,Rs)):s+=`Content-Type: ${a.type||"application/octet-stream"}${Rs}`,this.headers=js.encode(s+Rs),this.contentLength=t?a.byteLength:a.size,this.size=this.headers.byteLength+this.contentLength+2,this.name=e,this.value=a}async*encode(){yield this.headers;const{value:e}=this;fe.isTypedArray(e)?yield e:yield*_s(e),yield Ss}static escapeName(e){return String(e).replace(/[\r\n"]/g,(e=>({"\r":"%0D","\n":"%0A",'"':"%22"}[e])))}}class Ts extends a.Transform{__transform(e,a,n){this.push(e),n()}_transform(e,a,n){if(0!==e.length&&(this._transform=this.__transform,120!==e[0])){const e=Buffer.alloc(2);e[0]=120,e[1]=156,this.push(e,a)}this.__transform(e,a,n)}}const qs=(e,a)=>fe.isAsyncFn(e)?function(...n){const t=n.pop();e.apply(this,n).then((e=>{try{a?t(null,...a(e)):t(null,e)}catch(e){t(e)}}),t)}:e;const Cs=(e,a,n=3)=>{let t=0;const s=function(e,a){e=e||10;const n=new Array(e),t=new Array(e);let s,i=0,o=0;return a=void 0!==a?a:1e3,function(r){const c=Date.now(),p=t[o];s||(s=c),n[i]=r,t[i]=c;let l=o,u=0;for(;l!==i;)u+=n[l++],l%=e;if(i=(i+1)%e,i===o&&(o=(o+1)%e),c-s<a)return;const d=p&&c-p;return d?Math.round(1e3*u/d):void 0}}(50,250);return function(e,a){let n,t,s=0,i=1e3/a;const o=(a,i=Date.now())=>{s=i,n=null,t&&(clearTimeout(t),t=null),e.apply(null,a)};return[(...e)=>{const a=Date.now(),r=a-s;r>=i?o(e,a):(n=e,t||(t=setTimeout((()=>{t=null,o(n)}),i-r)))},()=>n&&o(n)]}((n=>{const i=n.loaded,o=n.lengthComputable?n.total:void 0,r=i-t,c=s(r);t=i;e({loaded:i,total:o,progress:o?i/o:void 0,bytes:r,rate:c||void 0,estimated:c&&o&&i<=o?(o-i)/c:void 0,event:n,lengthComputable:null!=o,[a?"download":"upload"]:!0})}),n)},As=(e,a)=>{const n=null!=e;return[t=>a[0]({lengthComputable:n,total:e,loaded:t}),a[1]]},Os=e=>(...a)=>fe.asap((()=>e(...a))),Fs={flush:u.constants.Z_SYNC_FLUSH,finishFlush:u.constants.Z_SYNC_FLUSH},Ds={flush:u.constants.BROTLI_OPERATION_FLUSH,finishFlush:u.constants.BROTLI_OPERATION_FLUSH},Ps=fe.isFunction(u.createBrotliDecompress),{http:Bs,https:Ls}=fs,Us=/https:?/,zs=Tt.protocols.map((e=>e+":")),Is=(e,[a,n])=>(e.on("end",n).on("error",n),a);function Ns(e,a){e.beforeRedirects.proxy&&e.beforeRedirects.proxy(e),e.beforeRedirects.config&&e.beforeRedirects.config(e,a)}function Ms(e,a,n){let t=a;if(!t&&!1!==t){const e=Kt.getProxyForUrl(n);e&&(t=new URL(e))}if(t){if(t.username&&(t.auth=(t.username||"")+":"+(t.password||"")),t.auth){(t.auth.username||t.auth.password)&&(t.auth=(t.auth.username||"")+":"+(t.auth.password||""));const a=Buffer.from(t.auth,"utf8").toString("base64");e.headers["Proxy-Authorization"]="Basic "+a}e.headers.host=e.hostname+(e.port?":"+e.port:"");const a=t.hostname||t.host;e.hostname=a,e.host=a,e.port=t.port,e.path=n,t.protocol&&(e.protocol=t.protocol.includes(":")?t.protocol:`${t.protocol}:`)}e.beforeRedirects.proxy=function(e){Ms(e,a,e.href)}}const $s="undefined"!=typeof process&&"process"===fe.kindOf(process),Ws=(e,a)=>(({address:e,family:a})=>{if(!fe.isString(e))throw TypeError("address must be a string");return{address:e,family:a||(e.indexOf(".")<0?6:4)}})(fe.isObject(e)?e:{address:e,family:a});var Hs=$s&&function(t){return o=async function(o,r,c){let{data:p,lookup:l,family:m}=t;const{responseType:h,responseEncoding:f}=t,x=t.method.toUpperCase();let v,b,g=!1;if(l){const e=qs(l,(e=>fe.isArray(e)?e:[e]));l=(a,n,t)=>{e(a,n,((e,a,s)=>{if(e)return t(e);const i=fe.isArray(a)?a.map((e=>Ws(e))):[Ws(a,s)];n.all?t(e,i):t(e,i[0].address,i[0].family)}))}}const y=new d,w=()=>{t.cancelToken&&t.cancelToken.unsubscribe(_),t.signal&&t.signal.removeEventListener("abort",_),y.removeAllListeners()};function _(e){y.emit("abort",!e||e.type?new zt(null,t,b):e)}c(((e,a)=>{v=!0,a&&(g=!0,w())})),y.once("abort",r),(t.cancelToken||t.signal)&&(t.cancelToken&&t.cancelToken.subscribe(_),t.signal&&(t.signal.aborted?_():t.signal.addEventListener("abort",_)));const k=Nt(t.baseURL,t.url),j=new URL(k,Tt.hasBrowserEnv?Tt.origin:void 0),R=j.protocol||zs[0];if("data:"===R){let e;if("GET"!==x)return It(o,r,{status:405,statusText:"method not allowed",headers:{},config:t});try{e=function(e,a,n){const t=n&&n.Blob||Tt.classes.Blob,s=vs(e);if(void 0===a&&t&&(a=!0),"data"===s){e=s.length?e.slice(s.length+1):e;const n=bs.exec(e);if(!n)throw new xe("Invalid URL",xe.ERR_INVALID_URL);const i=n[1],o=n[2],r=n[3],c=Buffer.from(decodeURIComponent(r),o?"base64":"utf8");if(a){if(!t)throw new xe("Blob is not supported",xe.ERR_NOT_SUPPORT);return new t([c],{type:i})}return c}throw new xe("Unsupported protocol "+s,xe.ERR_NOT_SUPPORT)}(t.url,"blob"===h,{Blob:t.env&&t.env.Blob})}catch(e){throw xe.from(e,xe.ERR_BAD_REQUEST,t)}return"text"===h?(e=e.toString(f),f&&"utf8"!==f||(e=fe.stripBOM(e))):"stream"===h&&(e=a.Readable.from(e)),It(o,r,{data:e,status:200,statusText:"OK",headers:new Bt,config:t})}if(-1===zs.indexOf(R))return r(new xe("Unsupported protocol "+R,xe.ERR_BAD_REQUEST,t));const S=Bt.from(t.headers).normalize();S.set("User-Agent","axios/"+xs,!1);const{onUploadProgress:E,onDownloadProgress:T}=t,q=t.maxRate;let C,A;if(fe.isSpecCompliantForm(p)){const e=S.getContentType(/boundary=([-_\w\d]{10,70})/i);p=((e,a,t)=>{const{tag:s="form-data-boundary",size:i=25,boundary:o=s+"-"+fe.generateString(i,ks)}=t||{};if(!fe.isFormData(e))throw TypeError("FormData instance required");if(o.length<1||o.length>70)throw Error("boundary must be 10-70 characters long");const r=js.encode("--"+o+Rs),c=js.encode("--"+o+"--"+Rs+Rs);let p=c.byteLength;const l=Array.from(e.entries()).map((([e,a])=>{const n=new Es(e,a);return p+=n.size,n}));p+=r.byteLength*l.length,p=fe.toFiniteNumber(p);const u={"Content-Type":`multipart/form-data; boundary=${o}`};return Number.isFinite(p)&&(u["Content-Length"]=p),a&&a(u),n.from(async function*(){for(const e of l)yield r,yield*e.encode();yield c}())})(p,(e=>{S.set(e)}),{tag:`axios-${xs}-boundary`,boundary:e&&e[1]||void 0})}else if(fe.isFormData(p)&&fe.isFunction(p.getHeaders)){if(S.set(p.getHeaders()),!S.hasContentLength())try{const a=await e.promisify(p.getLength).call(p);Number.isFinite(a)&&a>=0&&S.setContentLength(a)}catch(e){}}else if(fe.isBlob(p)||fe.isFile(p))p.size&&S.setContentType(p.type||"application/octet-stream"),S.setContentLength(p.size||0),p=a.Readable.from(_s(p));else if(p&&!fe.isStream(p)){if(Buffer.isBuffer(p));else if(fe.isArrayBuffer(p))p=Buffer.from(new Uint8Array(p));else{if(!fe.isString(p))return r(new xe("Data after transformation must be a string, an ArrayBuffer, a Buffer, or a Stream",xe.ERR_BAD_REQUEST,t));p=Buffer.from(p,"utf-8")}if(S.setContentLength(p.length,!1),t.maxBodyLength>-1&&p.length>t.maxBodyLength)return r(new xe("Request body larger than maxBodyLength limit",xe.ERR_BAD_REQUEST,t))}const O=fe.toFiniteNumber(S.getContentLength());let F,D;fe.isArray(q)?(C=q[0],A=q[1]):C=A=q,p&&(E||C)&&(fe.isStream(p)||(p=a.Readable.from(p,{objectMode:!1})),p=a.pipeline([p,new ys({maxRate:fe.toFiniteNumber(C)})],fe.noop),E&&p.on("progress",Is(p,As(O,Cs(Os(E),!1,3))))),t.auth&&(F=(t.auth.username||"")+":"+(t.auth.password||"")),!F&&j.username&&(F=j.username+":"+j.password),F&&S.delete("authorization");try{D=gt(j.pathname+j.search,t.params,t.paramsSerializer).replace(/^\?/,"")}catch(e){const a=new Error(e.message);return a.config=t,a.url=t.url,a.exists=!0,r(a)}S.set("Accept-Encoding","gzip, compress, deflate"+(Ps?", br":""),!1);const P={path:D,method:x,headers:S.toJSON(),agents:{http:t.httpAgent,https:t.httpsAgent},auth:F,protocol:R,family:m,beforeRedirect:Ns,beforeRedirects:{}};let B;!fe.isUndefined(l)&&(P.lookup=l),t.socketPath?P.socketPath=t.socketPath:(P.hostname=j.hostname.startsWith("[")?j.hostname.slice(1,-1):j.hostname,P.port=j.port,Ms(P,t.proxy,R+"//"+j.hostname+(j.port?":"+j.port:"")+P.path));const L=Us.test(P.protocol);if(P.agent=L?t.httpsAgent:t.httpAgent,t.transport?B=t.transport:0===t.maxRedirects?B=L?i:s:(t.maxRedirects&&(P.maxRedirects=t.maxRedirects),t.beforeRedirect&&(P.beforeRedirects.config=t.beforeRedirect),B=L?Ls:Bs),t.maxBodyLength>-1?P.maxBodyLength=t.maxBodyLength:P.maxBodyLength=1/0,t.insecureHTTPParser&&(P.insecureHTTPParser=t.insecureHTTPParser),b=B.request(P,(function(e){if(b.destroyed)return;const n=[e],s=+e.headers["content-length"];if(T||A){const e=new ys({maxRate:fe.toFiniteNumber(A)});T&&e.on("progress",Is(e,As(s,Cs(Os(T),!0,3)))),n.push(e)}let i=e;const c=e.req||b;if(!1!==t.decompress&&e.headers["content-encoding"])switch("HEAD"!==x&&204!==e.statusCode||delete e.headers["content-encoding"],(e.headers["content-encoding"]||"").toLowerCase()){case"gzip":case"x-gzip":case"compress":case"x-compress":n.push(u.createUnzip(Fs)),delete e.headers["content-encoding"];break;case"deflate":n.push(new Ts),n.push(u.createUnzip(Fs)),delete e.headers["content-encoding"];break;case"br":Ps&&(n.push(u.createBrotliDecompress(Ds)),delete e.headers["content-encoding"])}i=n.length>1?a.pipeline(n,fe.noop):n[0];const p=a.finished(i,(()=>{p(),w()})),l={status:e.statusCode,statusText:e.statusMessage,headers:new Bt(e.headers),config:t,request:c};if("stream"===h)l.data=i,It(o,r,l);else{const e=[];let a=0;i.on("data",(function(n){e.push(n),a+=n.length,t.maxContentLength>-1&&a>t.maxContentLength&&(g=!0,i.destroy(),r(new xe("maxContentLength size of "+t.maxContentLength+" exceeded",xe.ERR_BAD_RESPONSE,t,c)))})),i.on("aborted",(function(){if(g)return;const e=new xe("stream has been aborted",xe.ERR_BAD_RESPONSE,t,c);i.destroy(e),r(e)})),i.on("error",(function(e){b.destroyed||r(xe.from(e,null,t,c))})),i.on("end",(function(){try{let a=1===e.length?e[0]:Buffer.concat(e);"arraybuffer"!==h&&(a=a.toString(f),f&&"utf8"!==f||(a=fe.stripBOM(a))),l.data=a}catch(e){return r(xe.from(e,null,t,l.request,l))}It(o,r,l)}))}y.once("abort",(e=>{i.destroyed||(i.emit("error",e),i.destroy())}))})),y.once("abort",(e=>{r(e),b.destroy(e)})),b.on("error",(function(e){r(xe.from(e,null,t,b))})),b.on("socket",(function(e){e.setKeepAlive(!0,6e4)})),t.timeout){const e=parseInt(t.timeout,10);if(Number.isNaN(e))return void r(new xe("error trying to parse `config.timeout` to int",xe.ERR_BAD_OPTION_VALUE,t,b));b.setTimeout(e,(function(){if(v)return;let e=t.timeout?"timeout of "+t.timeout+"ms exceeded":"timeout exceeded";const a=t.transitional||wt;t.timeoutErrorMessage&&(e=t.timeoutErrorMessage),r(new xe(e,a.clarifyTimeoutError?xe.ETIMEDOUT:xe.ECONNABORTED,t,b)),_()}))}if(fe.isStream(p)){let e=!1,a=!1;p.on("end",(()=>{e=!0})),p.once("error",(e=>{a=!0,b.destroy(e)})),p.on("close",(()=>{e||a||_(new zt("Request stream has been aborted",t,b))})),p.pipe(b)}else b.end(p)},new Promise(((e,a)=>{let n,t;const s=(e,a)=>{t||(t=!0,n&&n(e,a))},i=e=>{s(e,!0),a(e)};o((a=>{s(a),e(a)}),i,(e=>n=e)).catch(i)}));var o},Vs=Tt.hasStandardBrowserEnv?((e,a)=>n=>(n=new URL(n,Tt.origin),e.protocol===n.protocol&&e.host===n.host&&(a||e.port===n.port)))(new URL(Tt.origin),Tt.navigator&&/(msie|trident)/i.test(Tt.navigator.userAgent)):()=>!0,Gs=Tt.hasStandardBrowserEnv?{write(e,a,n,t,s,i){const o=[e+"="+encodeURIComponent(a)];fe.isNumber(n)&&o.push("expires="+new Date(n).toGMTString()),fe.isString(t)&&o.push("path="+t),fe.isString(s)&&o.push("domain="+s),!0===i&&o.push("secure"),document.cookie=o.join("; ")},read(e){const a=document.cookie.match(new RegExp("(^|;\\s*)("+e+")=([^;]*)"));return a?decodeURIComponent(a[3]):null},remove(e){this.write(e,"",Date.now()-864e5)}}:{write(){},read:()=>null,remove(){}};const Js=e=>e instanceof Bt?{...e}:e;function Ks(e,a){a=a||{};const n={};function t(e,a,n,t){return fe.isPlainObject(e)&&fe.isPlainObject(a)?fe.merge.call({caseless:t},e,a):fe.isPlainObject(a)?fe.merge({},a):fe.isArray(a)?a.slice():a}function s(e,a,n,s){return fe.isUndefined(a)?fe.isUndefined(e)?void 0:t(void 0,e,0,s):t(e,a,0,s)}function i(e,a){if(!fe.isUndefined(a))return t(void 0,a)}function o(e,a){return fe.isUndefined(a)?fe.isUndefined(e)?void 0:t(void 0,e):t(void 0,a)}function r(n,s,i){return i in a?t(n,s):i in e?t(void 0,n):void 0}const c={url:i,method:i,data:i,baseURL:o,transformRequest:o,transformResponse:o,paramsSerializer:o,timeout:o,timeoutMessage:o,withCredentials:o,withXSRFToken:o,adapter:o,responseType:o,xsrfCookieName:o,xsrfHeaderName:o,onUploadProgress:o,onDownloadProgress:o,decompress:o,maxContentLength:o,maxBodyLength:o,beforeRedirect:o,transport:o,httpAgent:o,httpsAgent:o,cancelToken:o,socketPath:o,responseEncoding:o,validateStatus:r,headers:(e,a,n)=>s(Js(e),Js(a),0,!0)};return fe.forEach(Object.keys(Object.assign({},e,a)),(function(t){const i=c[t]||s,o=i(e[t],a[t],t);fe.isUndefined(o)&&i!==r||(n[t]=o)})),n}var Qs=e=>{const a=Ks({},e);let n,{data:t,withXSRFToken:s,xsrfHeaderName:i,xsrfCookieName:o,headers:r,auth:c}=a;if(a.headers=r=Bt.from(r),a.url=gt(Nt(a.baseURL,a.url),e.params,e.paramsSerializer),c&&r.set("Authorization","Basic "+btoa((c.username||"")+":"+(c.password?unescape(encodeURIComponent(c.password)):""))),fe.isFormData(t))if(Tt.hasStandardBrowserEnv||Tt.hasStandardBrowserWebWorkerEnv)r.setContentType(void 0);else if(!1!==(n=r.getContentType())){const[e,...a]=n?n.split(";").map((e=>e.trim())).filter(Boolean):[];r.setContentType([e||"multipart/form-data",...a].join("; "))}if(Tt.hasStandardBrowserEnv&&(s&&fe.isFunction(s)&&(s=s(a)),s||!1!==s&&Vs(a.url))){const e=i&&o&&Gs.read(o);e&&r.set(i,e)}return a};var Ys="undefined"!=typeof XMLHttpRequest&&function(e){return new Promise((function(a,n){const t=Qs(e);let s=t.data;const i=Bt.from(t.headers).normalize();let o,r,c,p,l,{responseType:u,onUploadProgress:d,onDownloadProgress:m}=t;function h(){p&&p(),l&&l(),t.cancelToken&&t.cancelToken.unsubscribe(o),t.signal&&t.signal.removeEventListener("abort",o)}let f=new XMLHttpRequest;function x(){if(!f)return;const t=Bt.from("getAllResponseHeaders"in f&&f.getAllResponseHeaders());It((function(e){a(e),h()}),(function(e){n(e),h()}),{data:u&&"text"!==u&&"json"!==u?f.response:f.responseText,status:f.status,statusText:f.statusText,headers:t,config:e,request:f}),f=null}f.open(t.method.toUpperCase(),t.url,!0),f.timeout=t.timeout,"onloadend"in f?f.onloadend=x:f.onreadystatechange=function(){f&&4===f.readyState&&(0!==f.status||f.responseURL&&0===f.responseURL.indexOf("file:"))&&setTimeout(x)},f.onabort=function(){f&&(n(new xe("Request aborted",xe.ECONNABORTED,e,f)),f=null)},f.onerror=function(){n(new xe("Network Error",xe.ERR_NETWORK,e,f)),f=null},f.ontimeout=function(){let a=t.timeout?"timeout of "+t.timeout+"ms exceeded":"timeout exceeded";const s=t.transitional||wt;t.timeoutErrorMessage&&(a=t.timeoutErrorMessage),n(new xe(a,s.clarifyTimeoutError?xe.ETIMEDOUT:xe.ECONNABORTED,e,f)),f=null},void 0===s&&i.setContentType(null),"setRequestHeader"in f&&fe.forEach(i.toJSON(),(function(e,a){f.setRequestHeader(a,e)})),fe.isUndefined(t.withCredentials)||(f.withCredentials=!!t.withCredentials),u&&"json"!==u&&(f.responseType=t.responseType),m&&([c,l]=Cs(m,!0),f.addEventListener("progress",c)),d&&f.upload&&([r,p]=Cs(d),f.upload.addEventListener("progress",r),f.upload.addEventListener("loadend",p)),(t.cancelToken||t.signal)&&(o=a=>{f&&(n(!a||a.type?new zt(null,e,f):a),f.abort(),f=null)},t.cancelToken&&t.cancelToken.subscribe(o),t.signal&&(t.signal.aborted?o():t.signal.addEventListener("abort",o)));const v=vs(t.url);v&&-1===Tt.protocols.indexOf(v)?n(new xe("Unsupported protocol "+v+":",xe.ERR_BAD_REQUEST,e)):f.send(s||null)}))};const Xs=(e,a)=>{const{length:n}=e=e?e.filter(Boolean):[];if(a||n){let n,t=new AbortController;const s=function(e){if(!n){n=!0,o();const a=e instanceof Error?e:this.reason;t.abort(a instanceof xe?a:new zt(a instanceof Error?a.message:a))}};let i=a&&setTimeout((()=>{i=null,s(new xe(`timeout ${a} of ms exceeded`,xe.ETIMEDOUT))}),a);const o=()=>{e&&(i&&clearTimeout(i),i=null,e.forEach((e=>{e.unsubscribe?e.unsubscribe(s):e.removeEventListener("abort",s)})),e=null)};e.forEach((e=>e.addEventListener("abort",s)));const{signal:r}=t;return r.unsubscribe=()=>fe.asap(o),r}},Zs=function*(e,a){let n=e.byteLength;if(n<a)return void(yield e);let t,s=0;for(;s<n;)t=s+a,yield e.slice(s,t),s=t},ei=async function*(e){if(e[Symbol.asyncIterator])return void(yield*e);const a=e.getReader();try{for(;;){const{done:e,value:n}=await a.read();if(e)break;yield n}}finally{await a.cancel()}},ai=(e,a,n,t)=>{const s=async function*(e,a){for await(const n of ei(e))yield*Zs(n,a)}(e,a);let i,o=0,r=e=>{i||(i=!0,t&&t(e))};return new ReadableStream({async pull(e){try{const{done:a,value:t}=await s.next();if(a)return r(),void e.close();let i=t.byteLength;if(n){let e=o+=i;n(e)}e.enqueue(new Uint8Array(t))}catch(e){throw r(e),e}},cancel:e=>(r(e),s.return())},{highWaterMark:2})},ni="function"==typeof fetch&&"function"==typeof Request&&"function"==typeof Response,ti=ni&&"function"==typeof ReadableStream,si=ni&&("function"==typeof TextEncoder?(ii=new TextEncoder,e=>ii.encode(e)):async e=>new Uint8Array(await new Response(e).arrayBuffer()));var ii;const oi=(e,...a)=>{try{return!!e(...a)}catch(e){return!1}},ri=ti&&oi((()=>{let e=!1;const a=new Request(Tt.origin,{body:new ReadableStream,method:"POST",get duplex(){return e=!0,"half"}}).headers.has("Content-Type");return e&&!a})),ci=ti&&oi((()=>fe.isReadableStream(new Response("").body))),pi={stream:ci&&(e=>e.body)};var li;ni&&(li=new Response,["text","arrayBuffer","blob","formData","stream"].forEach((e=>{!pi[e]&&(pi[e]=fe.isFunction(li[e])?a=>a[e]():(a,n)=>{throw new xe(`Response type '${e}' is not supported`,xe.ERR_NOT_SUPPORT,n)})})));const ui=async(e,a)=>{const n=fe.toFiniteNumber(e.getContentLength());return null==n?(async e=>{if(null==e)return 0;if(fe.isBlob(e))return e.size;if(fe.isSpecCompliantForm(e)){const a=new Request(Tt.origin,{method:"POST",body:e});return(await a.arrayBuffer()).byteLength}return fe.isArrayBufferView(e)||fe.isArrayBuffer(e)?e.byteLength:(fe.isURLSearchParams(e)&&(e+=""),fe.isString(e)?(await si(e)).byteLength:void 0)})(a):n};const di={http:Hs,xhr:Ys,fetch:ni&&(async e=>{let{url:a,method:n,data:t,signal:s,cancelToken:i,timeout:o,onDownloadProgress:r,onUploadProgress:c,responseType:p,headers:l,withCredentials:u="same-origin",fetchOptions:d}=Qs(e);p=p?(p+"").toLowerCase():"text";let m,h=Xs([s,i&&i.toAbortSignal()],o);const f=h&&h.unsubscribe&&(()=>{h.unsubscribe()});let x;try{if(c&&ri&&"get"!==n&&"head"!==n&&0!==(x=await ui(l,t))){let e,n=new Request(a,{method:"POST",body:t,duplex:"half"});if(fe.isFormData(t)&&(e=n.headers.get("content-type"))&&l.setContentType(e),n.body){const[e,a]=As(x,Cs(Os(c)));t=ai(n.body,65536,e,a)}}fe.isString(u)||(u=u?"include":"omit");const s="credentials"in Request.prototype;m=new Request(a,{...d,signal:h,method:n.toUpperCase(),headers:l.normalize().toJSON(),body:t,duplex:"half",credentials:s?u:void 0});let i=await fetch(m);const o=ci&&("stream"===p||"response"===p);if(ci&&(r||o&&f)){const e={};["status","statusText","headers"].forEach((a=>{e[a]=i[a]}));const a=fe.toFiniteNumber(i.headers.get("content-length")),[n,t]=r&&As(a,Cs(Os(r),!0))||[];i=new Response(ai(i.body,65536,n,(()=>{t&&t(),f&&f()})),e)}p=p||"text";let v=await pi[fe.findKey(pi,p)||"text"](i,e);return!o&&f&&f(),await new Promise(((a,n)=>{It(a,n,{data:v,headers:Bt.from(i.headers),status:i.status,statusText:i.statusText,config:e,request:m})}))}catch(a){if(f&&f(),a&&"TypeError"===a.name&&/fetch/i.test(a.message))throw Object.assign(new xe("Network Error",xe.ERR_NETWORK,e,m),{cause:a.cause||a});throw xe.from(a,a&&a.code,e,m)}})};fe.forEach(di,((e,a)=>{if(e){try{Object.defineProperty(e,"name",{value:a})}catch(e){}Object.defineProperty(e,"adapterName",{value:a})}}));const mi=e=>`- ${e}`,hi=e=>fe.isFunction(e)||null===e||!1===e;var fi=e=>{e=fe.isArray(e)?e:[e];const{length:a}=e;let n,t;const s={};for(let i=0;i<a;i++){let a;if(n=e[i],t=n,!hi(n)&&(t=di[(a=String(n)).toLowerCase()],void 0===t))throw new xe(`Unknown adapter '${a}'`);if(t)break;s[a||"#"+i]=t}if(!t){const e=Object.entries(s).map((([e,a])=>`adapter ${e} `+(!1===a?"is not supported by the environment":"is not available in the build")));throw new xe("There is no suitable adapter to dispatch the request "+(a?e.length>1?"since :\n"+e.map(mi).join("\n"):" "+mi(e[0]):"as no adapter specified"),"ERR_NOT_SUPPORT")}return t};function xi(e){if(e.cancelToken&&e.cancelToken.throwIfRequested(),e.signal&&e.signal.aborted)throw new zt(null,e)}function vi(e){xi(e),e.headers=Bt.from(e.headers),e.data=Lt.call(e,e.transformRequest),-1!==["post","put","patch"].indexOf(e.method)&&e.headers.setContentType("application/x-www-form-urlencoded",!1);return fi(e.adapter||Ct.adapter)(e).then((function(a){return xi(e),a.data=Lt.call(e,e.transformResponse,a),a.headers=Bt.from(a.headers),a}),(function(a){return Ut(a)||(xi(e),a&&a.response&&(a.response.data=Lt.call(e,e.transformResponse,a.response),a.response.headers=Bt.from(a.response.headers))),Promise.reject(a)}))}const bi={};["object","boolean","number","function","string","symbol"].forEach(((e,a)=>{bi[e]=function(n){return typeof n===e||"a"+(a<1?"n ":" ")+e}}));const gi={};bi.transitional=function(e,a,n){function t(e,a){return"[Axios v1.7.9] Transitional option '"+e+"'"+a+(n?". "+n:"")}return(n,s,i)=>{if(!1===e)throw new xe(t(s," has been removed"+(a?" in "+a:"")),xe.ERR_DEPRECATED);return a&&!gi[s]&&(gi[s]=!0,console.warn(t(s," has been deprecated since v"+a+" and will be removed in the near future"))),!e||e(n,s,i)}},bi.spelling=function(e){return(a,n)=>(console.warn(`${n} is likely a misspelling of ${e}`),!0)};var yi={assertOptions:function(e,a,n){if("object"!=typeof e)throw new xe("options must be an object",xe.ERR_BAD_OPTION_VALUE);const t=Object.keys(e);let s=t.length;for(;s-- >0;){const i=t[s],o=a[i];if(o){const a=e[i],n=void 0===a||o(a,i,e);if(!0!==n)throw new xe("option "+i+" must be "+n,xe.ERR_BAD_OPTION_VALUE)}else if(!0!==n)throw new xe("Unknown option "+i,xe.ERR_BAD_OPTION)}},validators:bi};const wi=yi.validators;let _i=class{constructor(e){this.defaults=e,this.interceptors={request:new yt,response:new yt}}async request(e,a){try{return await this._request(e,a)}catch(e){if(e instanceof Error){let a={};Error.captureStackTrace?Error.captureStackTrace(a):a=new Error;const n=a.stack?a.stack.replace(/^.+\n/,""):"";try{e.stack?n&&!String(e.stack).endsWith(n.replace(/^.+\n.+\n/,""))&&(e.stack+="\n"+n):e.stack=n}catch(e){}}throw e}}_request(e,a){"string"==typeof e?(a=a||{}).url=e:a=e||{},a=Ks(this.defaults,a);const{transitional:n,paramsSerializer:t,headers:s}=a;void 0!==n&&yi.assertOptions(n,{silentJSONParsing:wi.transitional(wi.boolean),forcedJSONParsing:wi.transitional(wi.boolean),clarifyTimeoutError:wi.transitional(wi.boolean)},!1),null!=t&&(fe.isFunction(t)?a.paramsSerializer={serialize:t}:yi.assertOptions(t,{encode:wi.function,serialize:wi.function},!0)),yi.assertOptions(a,{baseUrl:wi.spelling("baseURL"),withXsrfToken:wi.spelling("withXSRFToken")},!0),a.method=(a.method||this.defaults.method||"get").toLowerCase();let i=s&&fe.merge(s.common,s[a.method]);s&&fe.forEach(["delete","get","head","post","put","patch","common"],(e=>{delete s[e]})),a.headers=Bt.concat(i,s);const o=[];let r=!0;this.interceptors.request.forEach((function(e){"function"==typeof e.runWhen&&!1===e.runWhen(a)||(r=r&&e.synchronous,o.unshift(e.fulfilled,e.rejected))}));const c=[];let p;this.interceptors.response.forEach((function(e){c.push(e.fulfilled,e.rejected)}));let l,u=0;if(!r){const e=[vi.bind(this),void 0];for(e.unshift.apply(e,o),e.push.apply(e,c),l=e.length,p=Promise.resolve(a);u<l;)p=p.then(e[u++],e[u++]);return p}l=o.length;let d=a;for(u=0;u<l;){const e=o[u++],a=o[u++];try{d=e(d)}catch(e){a.call(this,e);break}}try{p=vi.call(this,d)}catch(e){return Promise.reject(e)}for(u=0,l=c.length;u<l;)p=p.then(c[u++],c[u++]);return p}getUri(e){return gt(Nt((e=Ks(this.defaults,e)).baseURL,e.url),e.params,e.paramsSerializer)}};fe.forEach(["delete","get","head","options"],(function(e){_i.prototype[e]=function(a,n){return this.request(Ks(n||{},{method:e,url:a,data:(n||{}).data}))}})),fe.forEach(["post","put","patch"],(function(e){function a(a){return function(n,t,s){return this.request(Ks(s||{},{method:e,headers:a?{"Content-Type":"multipart/form-data"}:{},url:n,data:t}))}}_i.prototype[e]=a(),_i.prototype[e+"Form"]=a(!0)}));const ki={Continue:100,SwitchingProtocols:101,Processing:102,EarlyHints:103,Ok:200,Created:201,Accepted:202,NonAuthoritativeInformation:203,NoContent:204,ResetContent:205,PartialContent:206,MultiStatus:207,AlreadyReported:208,ImUsed:226,MultipleChoices:300,MovedPermanently:301,Found:302,SeeOther:303,NotModified:304,UseProxy:305,Unused:306,TemporaryRedirect:307,PermanentRedirect:308,BadRequest:400,Unauthorized:401,PaymentRequired:402,Forbidden:403,NotFound:404,MethodNotAllowed:405,NotAcceptable:406,ProxyAuthenticationRequired:407,RequestTimeout:408,Conflict:409,Gone:410,LengthRequired:411,PreconditionFailed:412,PayloadTooLarge:413,UriTooLong:414,UnsupportedMediaType:415,RangeNotSatisfiable:416,ExpectationFailed:417,ImATeapot:418,MisdirectedRequest:421,UnprocessableEntity:422,Locked:423,FailedDependency:424,TooEarly:425,UpgradeRequired:426,PreconditionRequired:428,TooManyRequests:429,RequestHeaderFieldsTooLarge:431,UnavailableForLegalReasons:451,InternalServerError:500,NotImplemented:501,BadGateway:502,ServiceUnavailable:503,GatewayTimeout:504,HttpVersionNotSupported:505,VariantAlsoNegotiates:506,InsufficientStorage:507,LoopDetected:508,NotExtended:510,NetworkAuthenticationRequired:511};Object.entries(ki).forEach((([e,a])=>{ki[a]=e}));const ji=function e(a){const n=new _i(a),t=R(_i.prototype.request,n);return fe.extend(t,_i.prototype,n,{allOwnKeys:!0}),fe.extend(t,n,null,{allOwnKeys:!0}),t.create=function(n){return e(Ks(a,n))},t}(Ct);ji.Axios=_i,ji.CanceledError=zt,ji.CancelToken=class e{constructor(e){if("function"!=typeof e)throw new TypeError("executor must be a function.");let a;this.promise=new Promise((function(e){a=e}));const n=this;this.promise.then((e=>{if(!n._listeners)return;let a=n._listeners.length;for(;a-- >0;)n._listeners[a](e);n._listeners=null})),this.promise.then=e=>{let a;const t=new Promise((e=>{n.subscribe(e),a=e})).then(e);return t.cancel=function(){n.unsubscribe(a)},t},e((function(e,t,s){n.reason||(n.reason=new zt(e,t,s),a(n.reason))}))}throwIfRequested(){if(this.reason)throw this.reason}subscribe(e){this.reason?e(this.reason):this._listeners?this._listeners.push(e):this._listeners=[e]}unsubscribe(e){if(!this._listeners)return;const a=this._listeners.indexOf(e);-1!==a&&this._listeners.splice(a,1)}toAbortSignal(){const e=new AbortController,a=a=>{e.abort(a)};return this.subscribe(a),e.signal.unsubscribe=()=>this.unsubscribe(a),e.signal}static source(){let a;return{token:new e((function(e){a=e})),cancel:a}}},ji.isCancel=Ut,ji.VERSION=xs,ji.toFormData=ht,ji.AxiosError=xe,ji.Cancel=ji.CanceledError,ji.all=function(e){return Promise.all(e)},ji.spread=function(e){return function(a){return e.apply(null,a)}},ji.isAxiosError=function(e){return fe.isObject(e)&&!0===e.isAxiosError},ji.mergeConfig=Ks,ji.AxiosHeaders=Bt,ji.formToJSON=e=>qt(fe.isHTMLForm(e)?new FormData(e):e),ji.getAdapter=fi,ji.HttpStatusCode=ki,ji.default=ji;const{Axios:Ri,AxiosError:Si,CanceledError:Ei,isCancel:Ti,CancelToken:qi,VERSION:Ci,all:Ai,Cancel:Oi,isAxiosError:Fi,spread:Di,toFormData:Pi,AxiosHeaders:Bi,HttpStatusCode:Li,formToJSON:Ui,getAdapter:zi,mergeConfig:Ii}=ji;class Ni extends Error{status;stack;details;type;static getUserDataError(e,a){return new this({status:400,statusText:e,body:{message:a}})}constructor({status:e,statusText:a,message:n,body:t={}}){let s="",i="";"string"==typeof t?s=t:(s=t?.message||"",i=t?.error||""),super(),this.stack="",this.status=e,this.message=n||i||a||"",this.details=s,this.type="MailgunAPIError"}}class Mi{_stream;size;constructor(e,a){this._stream=e,this.size=a}stream(){return this._stream}get[Symbol.toStringTag](){return"Blob"}}class $i{getAttachmentOptions(e){const{filename:a,contentType:n,knownLength:t}=e;return{...a?{filename:a}:{filename:"file"},...n&&{contentType:n},...t&&{knownLength:t}}}getFileInfo(e){const{name:a,type:n,size:t}=e;return this.getAttachmentOptions({filename:a,contentType:n,knownLength:t})}getCustomFileInfo(e){const{filename:a,contentType:n,knownLength:t}=e;return this.getAttachmentOptions({filename:a,contentType:n,knownLength:t})}getBufferInfo(e){const{byteLength:a}=e;return this.getAttachmentOptions({filename:"file",contentType:"",knownLength:a})}isStream(e){return"object"==typeof e&&"function"==typeof e.pipe}isCustomFile(e){return"object"==typeof e&&!!e.data}isBrowserFile(e){return"object"==typeof e&&(!!e.name||"undefined"!=typeof Blob&&e instanceof Blob)}isBuffer(e){return"undefined"!=typeof Buffer&&Buffer.isBuffer(e)}getAttachmentInfo(e){const a=this.isBrowserFile(e),n=this.isCustomFile(e);if(!("string"==typeof e)){if(a)return this.getFileInfo(e);if("undefined"!=typeof Buffer&&Buffer.isBuffer(e))return this.getBufferInfo(e);if(n)return this.getCustomFileInfo(e)}return{filename:"file",contentType:void 0,knownLength:void 0}}convertToFDexpectedShape(e){const a=this.isStream(e),n=this.isBrowserFile(e),t=this.isCustomFile(e);let s;if(a||"string"==typeof e||n||this.isBuffer(e))s=e;else{if(!t)throw Ni.getUserDataError("Unknown attachment type "+typeof e,'The "attachment" property expects either Buffer, Blob, or String.\n          Also, It is possible to provide an object that has the property "data" with a value that is equal to one of the types counted before.\n          Additionally, you may use an array to send more than one attachment.');s=e.data}return s}getBlobFromStream(e,a){return new Mi(e,a)}}class Wi{FormDataConstructor;fileKeys;attachmentsHandler;constructor(e){this.FormDataConstructor=e,this.fileKeys=["attachment","inline","multipleValidationFile"],this.attachmentsHandler=new $i}createFormData(e){if(!e)throw new Error("Please provide data object");return Object.keys(e).filter((function(a){return e[a]})).reduce(((a,n)=>{if(this.fileKeys.includes(n)){const t=e[n];if(this.isMessageAttachment(t))return this.addFilesToFD(n,t,a),a;throw Ni.getUserDataError(`Unknown value ${e[n]} with type ${typeof e[n]} for property "${n}"`,`The key "${n}" should have type of Buffer, Stream, File, or String `)}if("message"===n){const t=e[n];if(!t||!this.isMIME(t))throw Ni.getUserDataError(`Unknown data type for "${n}" property`,"The mime data should have type of Buffer, String or Blob");return this.addMimeDataToFD(n,t,a),a}return this.addCommonPropertyToFD(n,e[n],a),a}),new this.FormDataConstructor)}addMimeDataToFD(e,a,n){if("string"!=typeof a){if(this.isFormDataPackage(n)){n.append(e,a,{filename:"MimeMessage"})}else if(void 0!==typeof Blob){const t=n;if(a instanceof Blob)return void t.append(e,a,"MimeMessage");if(this.attachmentsHandler.isBuffer(a)){const n=new Blob([a]);t.append(e,n,"MimeMessage")}}}else n.append(e,a)}isMIME(e){return"string"==typeof e||"undefined"!=typeof Blob&&e instanceof Blob||this.attachmentsHandler.isBuffer(e)||"undefined"!=typeof ReadableStream&&e instanceof ReadableStream}isFormDataPackage(e){return"object"==typeof e&&null!==e&&"function"==typeof e.getHeaders}isMessageAttachment(e){return this.attachmentsHandler.isCustomFile(e)||"string"==typeof e||"undefined"!=typeof File&&e instanceof File||"undefined"!=typeof Blob&&e instanceof Blob||this.attachmentsHandler.isBuffer(e)||this.attachmentsHandler.isStream(e)||Array.isArray(e)&&e.every((a=>this.attachmentsHandler.isCustomFile(a)||"undefined"!=typeof File&&a instanceof File||"undefined"!=typeof Blob&&e instanceof Blob||this.attachmentsHandler.isBuffer(a)||this.attachmentsHandler.isStream(a)))}addFilesToFD(e,a,n){const t=(e,a,t)=>{const s="multipleValidationFile"===e?"file":e,i=this.attachmentsHandler.convertToFDexpectedShape(a),o=this.attachmentsHandler.getAttachmentInfo(a);if(this.isFormDataPackage(t)){const e=t,a="string"==typeof i?Buffer.from(i):i;e.append(s,a,o)}else if(void 0!==typeof Blob){const e=n;if("string"==typeof i||this.attachmentsHandler.isBuffer(i)){const a=new Blob([i]);return void e.append(s,a,o.filename)}if(i instanceof Blob)return void e.append(s,i,o.filename);if(this.attachmentsHandler.isStream(i)){const a=this.attachmentsHandler.getBlobFromStream(i,o.knownLength);e.set(s,a,o.filename)}}};Array.isArray(a)?a.forEach((function(a){t(e,a,n)})):t(e,a,n)}addCommonPropertyToFD(e,a,n){const t=(e,a)=>{if(this.isFormDataPackage(n))return"object"==typeof a?(console.warn('The received value is an object. \n"JSON.Stringify" will be used to avoid TypeError \nTo remove this warning: \nConsider switching to built-in FormData or converting the value on your own.\n'),n.append(e,JSON.stringify(a))):n.append(e,a);if("string"==typeof a)return n.append(e,a);if(void 0!==typeof Blob&&a instanceof Blob)return n.append(e,a);throw Ni.getUserDataError("Unknown value type for Form Data. String or Blob expected","Browser compliant FormData allows only string or Blob values for properties that are not attachments.")};Array.isArray(a)?a.forEach((function(a){t(e,a)})):null!=a&&t(e,a)}}class Hi{request;static SUBACCOUNT_HEADER="X-Mailgun-On-Behalf-Of";constructor(e){this.request=e}list(e){return this.request.get("/v5/accounts/subaccounts",e).then((e=>e.body))}get(e){return this.request.get(`/v5/accounts/subaccounts/${e}`).then((e=>e.body))}create(e){return this.request.postWithFD("/v5/accounts/subaccounts",{name:e}).then((e=>e.body))}enable(e){return this.request.post(`/v5/accounts/subaccounts/${e}/enable`).then((e=>e.body))}disable(e){return this.request.post(`/v5/accounts/subaccounts/${e}/disable`).then((e=>e.body))}}let Vi=class{username;key;url;timeout;headers;formDataBuilder;maxBodyLength;proxy;constructor(e,a){this.username=e.username,this.key=e.key,this.url=e.url,this.timeout=e.timeout,this.headers=this.makeHeadersFromObject(e.headers),this.formDataBuilder=new Wi(a),this.maxBodyLength=********,this.proxy=e?.proxy}async request(e,a,n){const t={...n};delete t?.headers;const s=this.joinAndTransformHeaders(n),i={...t};if(t?.query&&Object.getOwnPropertyNames(t?.query).length>0&&(i.params=new URLSearchParams(t.query),delete i.query),t?.body){const e=t?.body;i.data=e,delete i.body}let o;const r=j(this.url,a);try{o=await ji.request({method:e.toLocaleUpperCase(),timeout:this.timeout,url:r,headers:s,...i,maxBodyLength:this.maxBodyLength,proxy:this.proxy})}catch(e){const a=e;throw new Ni({status:a?.response?.status||400,statusText:a?.response?.statusText||a.code,body:a?.response?.data||a.message})}return await this.getResponseBody(o)}async getResponseBody(e){const a={body:{},status:e?.status};if("string"==typeof e.data){if("Mailgun Magnificent API"===e.data)throw new Ni({status:400,statusText:"Incorrect url",body:e.data});a.body={message:e.data}}else a.body=e.data;return a}joinAndTransformHeaders(e){const a=new Bi,n=y.encode(`${this.username}:${this.key}`);a.setAuthorization(`Basic ${n}`),a.set(this.headers);const t=e&&e.headers,s=this.makeHeadersFromObject(t);return a.set(s),a}makeHeadersFromObject(e={}){let a=new Bi;return a=Object.entries(e).reduce(((e,a)=>{const[n,t]=a;return e.set(n,t),e}),a),a}setSubaccountHeader(e){const a=this.makeHeadersFromObject({...this.headers,[Hi.SUBACCOUNT_HEADER]:e});this.headers.set(a)}resetSubaccountHeader(){this.headers.delete(Hi.SUBACCOUNT_HEADER)}query(e,a,n,t){return this.request(e,a,{query:n,...t})}command(e,a,n,t,s=!0){let i={};s&&(i={"Content-Type":"application/x-www-form-urlencoded"});const o={...i,body:n,...t};return this.request(e,a,o)}get(e,a,n){return this.query("get",e,a,n)}post(e,a,n){return this.command("post",e,a,n)}postWithFD(e,a){const n=this.formDataBuilder.createFormData(a);return this.command("post",e,n,{headers:{"Content-Type":"multipart/form-data"}},!1)}putWithFD(e,a){const n=this.formDataBuilder.createFormData(a);return this.command("put",e,n,{headers:{"Content-Type":"multipart/form-data"}},!1)}patchWithFD(e,a){const n=this.formDataBuilder.createFormData(a);return this.command("patch",e,n,{headers:{"Content-Type":"multipart/form-data"}},!1)}put(e,a,n){return this.command("put",e,a,n)}delete(e,a){return this.command("delete",e,a)}};class Gi{name;require_tls;skip_verification;state;wildcard;spam_action;created_at;smtp_password;smtp_login;type;receiving_dns_records;sending_dns_records;id;is_disabled;web_prefix;web_scheme;use_automatic_sender_security;dkim_host;mailfrom_host;constructor(e,a,n){this.name=e.name,this.require_tls=e.require_tls,this.skip_verification=e.skip_verification,this.state=e.state,this.wildcard=e.wildcard,this.spam_action=e.spam_action,this.created_at=new Date(e.created_at),this.smtp_password=e.smtp_password,this.smtp_login=e.smtp_login,this.type=e.type,this.receiving_dns_records=a||null,this.sending_dns_records=n||null,this.id=e.id,this.is_disabled=e.is_disabled,this.web_prefix=e.web_prefix,this.web_scheme=e.web_scheme,this.use_automatic_sender_security=e.use_automatic_sender_security;const t=["dkim_host","mailfrom_host"].reduce(((a,n)=>{if(e[n]){a[n]=e[n]}return a}),{});Object.assign(this,t)}}class Ji{request;domainCredentials;domainTemplates;domainTags;domainTracking;logger;constructor(e,a,n,t,s,i=console){this.request=e,this.domainCredentials=a,this.domainTemplates=n,this.domainTags=t,this.logger=i,this.domainTracking=s}_handleBoolValues(e){const a=e,n=Object.keys(a).reduce(((e,n)=>{const t=n;if("boolean"==typeof a[t]){const n=a[t];e[t]="true"===n.toString()?"true":"false"}return e}),{});return{...e,...n}}_parseMessage(e){return e.body}parseDomainList(e){return e.body&&e.body.items?e.body.items.map((function(e){return new Gi(e)})):[]}_parseDomain(e){return new Gi(e.body.domain,e.body.receiving_dns_records,e.body.sending_dns_records)}list(e){return this.request.get("/v4/domains",e).then((e=>this.parseDomainList(e)))}get(e,a){const n=a?{"h:extended":a?.extended??!1,"h:with_dns":a?.with_dns??!0}:{};return this.request.get(`/v4/domains/${e}`,n).then((e=>this._parseDomain(e)))}create(e){const a=this._handleBoolValues(e);return this.request.postWithFD("/v4/domains",a).then((e=>this._parseDomain(e)))}update(e,a){const n=this._handleBoolValues(a);return this.request.putWithFD(`/v4/domains/${e}`,n).then((e=>this._parseDomain(e)))}verify(e){return this.request.put(`/v4/domains/${e}/verify`).then((e=>this._parseDomain(e)))}destroy(e){return this.request.delete(`/v3/domains/${e}`).then((e=>this._parseMessage(e)))}getConnection(e){return this.request.get(`/v3/domains/${e}/connection`).then((e=>e)).then((e=>e.body))}updateConnection(e,a){return this.request.put(`/v3/domains/${e}/connection`,a).then((e=>e)).then((e=>e.body))}getTracking(e){return this.logger.warn("\n      'domains.getTracking' method is deprecated, and will be removed. Please use 'domains.domainTracking.getTracking' instead.\n    "),this.domainTracking.getTracking(e)}updateTracking(e,a,n){return this.logger.warn("\n      'domains.updateTracking' method is deprecated, and will be removed. Please use 'domains.domainTracking.updateTracking' instead.\n    "),this.domainTracking.updateTracking(e,a,n)}getIps(e){return this.logger.warn('"domains.getIps" method is deprecated and will be removed in the future releases.'),this.request.get(j("/v3/domains",e,"ips")).then((e=>e?.body?.items))}assignIp(e,a){return this.logger.warn('"domains.assignIp" method is deprecated and will be removed in the future releases.'),this.request.postWithFD(j("/v3/domains",e,"ips"),{ip:a})}deleteIp(e,a){return this.logger.warn('"domains.deleteIp" method is deprecated and will be moved into the IpsClient in the future releases.'),this.request.delete(j("/v3/domains",e,"ips",a))}linkIpPool(e,a){return this.logger.warn('"domains.linkIpPool" method is deprecated, and will be removed in the future releases.'),this.request.postWithFD(j("/v3/domains",e,"ips"),{pool_id:a})}unlinkIpPoll(e,a){this.logger.warn('"domains.unlinkIpPoll" method is deprecated, and will be moved into the IpsClient in the future releases.');let n="";if(a.pool_id&&a.ip)throw Ni.getUserDataError("Too much data for replacement","Please specify either pool_id or ip (not both)");return a.pool_id?n=`?pool_id=${a.pool_id}`:a.ip&&(n=`?ip=${a.ip}`),this.request.delete(j("/v3/domains",e,"ips","ip_pool",n))}updateDKIMAuthority(e,a){return this.request.put(`/v3/domains/${e}/dkim_authority`,{},{query:`self=${a.self}`}).then((e=>e)).then((e=>e.body))}async updateDKIMSelector(e,a){const n=await this.request.put(`/v3/domains/${e}/dkim_selector`,{},{query:`dkim_selector=${a.dkimSelector}`});return{status:n.status,message:n?.body?.message}}updateWebPrefix(e,a){return this.logger.warn('"domains.updateWebPrefix" method is deprecated, please use domains.update to set new "web_prefix". Current method will be removed in the future releases.'),this.request.put(`/v3/domains/${e}/web_prefix`,{},{query:`web_prefix=${a.webPrefix}`}).then((e=>e))}}class Ki{request;constructor(e){e&&(this.request=e)}parsePage(e,a,n,t){const s=new URL(a),{searchParams:i}=s,o=a&&"string"==typeof a&&a.split(n).pop()||"";let r=null;return t&&(r=i.has(t)?i.get(t):void 0),{id:e,page:"?"===n?`?${o}`:o,iteratorPosition:r,url:a}}parsePageLinks(e,a,n){return Object.entries(e.body.paging).reduce(((e,[t,s])=>(e[t]=this.parsePage(t,s,a,n),e)),{})}updateUrlAndQuery(e,a){let n=e;const t={...a};return t.page&&(n=j(e,t.page),delete t.page),{url:n,updatedQuery:t}}async requestListWithPages(e,a,n){const{url:t,updatedQuery:s}=this.updateUrlAndQuery(e,a);if(this.request){const e=await this.request.get(t,s);return this.parseList(e,n)}throw new Ni({status:500,statusText:"Request property is empty",body:{message:""}})}}class Qi extends Ki{request;constructor(e){super(e),this.request=e}parseList(e){const a={};return a.items=e.body.items,a.pages=this.parsePageLinks(e,"/"),a.status=e.status,a}async get(e,a){return this.requestListWithPages(j("/v3",e,"events"),a)}}class Yi{start;end;resolution;stats;constructor(e){this.start=new Date(e.start),this.end=new Date(e.end),this.resolution=e.resolution,this.stats=e.stats.map((function(e){const a={...e};return a.time=new Date(e.time),a}))}}class Xi{request;logger;constructor(e,a=console){this.request=e,this.logger=a}convertDateToUTC(e,a){return this.logger.warn(`Date:"${a}" was auto-converted to UTC time zone.\nValue "${a.toUTCString()}" will be used for request.\nConsider using string type for property "${e}" to avoid auto-converting`),[e,a.toUTCString()]}prepareSearchParams(e){let a=[];return"object"==typeof e&&Object.keys(e).length&&(a=Object.entries(e).reduce(((e,a)=>{const[n,t]=a;if(Array.isArray(t)&&t.length){const a=t.map((e=>[n,e]));return[...e,...a]}return t instanceof Date?(e.push(this.convertDateToUTC(n,t)),e):("string"==typeof t&&e.push([n,t]),e)}),[])),a}parseStats(e){return new Yi(e.body)}getDomain(e,a){const n=this.prepareSearchParams(a);return this.request.get(j("/v3",e,"stats/total"),n).then(this.parseStats)}getAccount(e){const a=this.prepareSearchParams(e);return this.request.get("/v3/stats/total",a).then(this.parseStats)}}var Zi,eo,ao,no;!function(e){e.HOUR="hour",e.DAY="day",e.MONTH="month"}(Zi=Zi||(Zi={})),function(e){e.BOUNCES="bounces",e.COMPLAINTS="complaints",e.UNSUBSCRIBES="unsubscribes",e.WHITELISTS="whitelists"}(eo=eo||(eo={})),function(e){e.CLICKED="clicked",e.COMPLAINED="complained",e.DELIVERED="delivered",e.OPENED="opened",e.PERMANENT_FAIL="permanent_fail",e.TEMPORARY_FAIL="temporary_fail",e.UNSUBSCRIBED="unsubscribe"}(ao=ao||(ao={})),function(e){e.YES="yes",e.NO="no"}(no=no||(no={}));class to{type;constructor(e){this.type=e}}class so extends to{address;code;error;created_at;constructor(e){super(eo.BOUNCES),this.address=e.address,this.code=+e.code,this.error=e.error,this.created_at=new Date(e.created_at)}}class io extends to{address;created_at;constructor(e){super(eo.COMPLAINTS),this.address=e.address,this.created_at=new Date(e.created_at)}}class oo extends to{address;tags;created_at;constructor(e){super(eo.UNSUBSCRIBES),this.address=e.address,this.tags=e.tags,this.created_at=new Date(e.created_at)}}class ro extends to{value;reason;createdAt;constructor(e){super(eo.WHITELISTS),this.value=e.value,this.reason=e.reason,this.createdAt=new Date(e.createdAt)}}const co={headers:{"Content-Type":"application/json"}};class po extends Ki{request;models;constructor(e){super(e),this.request=e,this.models={bounces:so,complaints:io,unsubscribes:oo,whitelists:ro}}parseList(e,a){const n={};return n.items=e.body.items?.map((e=>new a(e)))||[],n.pages=this.parsePageLinks(e,"?","address"),n.status=e.status,n}_parseItem(e,a){return new a(e)}createWhiteList(e,a,n){if(n)throw Ni.getUserDataError("Data property should be an object","Whitelist's creation process does not support multiple creations. Data property should be an object");return this.request.postWithFD(j("v3",e,"whitelists"),a).then(this.prepareResponse)}createUnsubscribe(e,a){if(Array.isArray(a)){if(a.some((e=>e.tag)))throw Ni.getUserDataError("Tag property should not be used for creating multiple unsubscribes.","Tag property can be used only if one unsubscribe provided as second argument of create method. Please use tags instead.");return this.request.post(j("v3",e,"unsubscribes"),JSON.stringify(a),co).then(this.prepareResponse)}if(a?.tags)throw Ni.getUserDataError("Tags property should not be used for creating one unsubscribe.","Tags property can be used if you provides an array of unsubscribes as second argument of create method. Please use tag instead");if(Array.isArray(a.tag))throw Ni.getUserDataError("Tag property can not be an array","Please use array of unsubscribes as second argument of create method to be able to provide few tags");return this.request.postWithFD(j("v3",e,"unsubscribes"),a).then(this.prepareResponse)}getModel(e){if(e in this.models)return this.models[e];throw Ni.getUserDataError("Unknown type value","Type may be only one of [bounces, complaints, unsubscribes, whitelists]")}prepareResponse(e){return{message:e.body.message,type:e.body.type||"",value:e.body.value||"",status:e.status}}async list(e,a,n){const t=this.getModel(a);return this.requestListWithPages(j("v3",e,a),n,t)}get(e,a,n){const t=this.getModel(a);return this.request.get(j("v3",e,a,encodeURIComponent(n))).then((e=>this._parseItem(e.body,t)))}create(e,a,n){let t;this.getModel(a);const s=Array.isArray(n);return"whitelists"===a?this.createWhiteList(e,n,s):"unsubscribes"===a?this.createUnsubscribe(e,n):(t=s?[...n]:[n],this.request.post(j("v3",e,a),JSON.stringify(t),co).then(this.prepareResponse))}destroy(e,a,n){return this.getModel(a),this.request.delete(j("v3",e,a,encodeURIComponent(n))).then((e=>({message:e.body.message,value:e.body.value||"",address:e.body.address||"",status:e.status})))}}class lo{id;url;urls;constructor(e,a,n){this.id=e,this.url=a,this.urls=n}}class uo{request;constructor(e){this.request=e}_parseWebhookList(e){return e.body.webhooks}_parseWebhookWithID(e){return function(a){const n=a?.body?.webhook;let t=n?.url,s=n?.urls;return t||(t=s&&s.length?s[0]:void 0),s&&0!==s.length||!t||(s=[t]),new lo(e,t,s)}}_parseWebhookTest(e){return{code:e.body.code,message:e.body.message}}list(e,a){return this.request.get(j("/v3/domains",e,"webhooks"),a).then(this._parseWebhookList)}get(e,a){return this.request.get(j("/v3/domains",e,"webhooks",a)).then(this._parseWebhookWithID(a))}create(e,a,n,t=!1){return t?this.request.putWithFD(j("/v3/domains",e,"webhooks",a,"test"),{url:n}).then(this._parseWebhookTest):this.request.postWithFD(j("/v3/domains",e,"webhooks"),{id:a,url:n}).then(this._parseWebhookWithID(a))}update(e,a,n){return this.request.putWithFD(j("/v3/domains",e,"webhooks",a),{url:n}).then(this._parseWebhookWithID(a))}destroy(e,a){return this.request.delete(j("/v3/domains",e,"webhooks",a)).then(this._parseWebhookWithID(a))}}class mo{request;constructor(e){this.request=e}prepareBooleanValues(e){const a=new Set(["o:testmode","t:text","o:dkim","o:tracking","o:tracking-clicks","o:tracking-opens","o:require-tls","o:skip-verification"]);if(!e||0===Object.keys(e).length)throw Ni.getUserDataError("Message data object can not be empty","Message data object can not be empty");return Object.keys(e).reduce(((n,t)=>(a.has(t)&&"boolean"==typeof e[t]?n[t]=e[t]?"yes":"no":n[t]=e[t],n)),{})}_parseResponse(e){return{status:e.status,...e.body}}create(e,a){if(a.message)return this.request.postWithFD(`/v3/${e}/messages.mime`,a).then(this._parseResponse);const n=this.prepareBooleanValues(a);return this.request.postWithFD(`/v3/${e}/messages`,n).then(this._parseResponse)}}class ho{request;constructor(e){this.request=e}list(e){return this.request.get("/v3/routes",e).then((e=>e.body.items))}get(e){return this.request.get(`/v3/routes/${e}`).then((e=>e.body.route))}create(e){return this.request.postWithFD("/v3/routes",e).then((e=>e.body.route))}update(e,a){return this.request.putWithFD(`/v3/routes/${e}`,a).then((e=>e.body))}destroy(e){return this.request.delete(`/v3/routes/${e}`).then((e=>e.body))}}class fo{multipleValidation;request;constructor(e,a){this.request=e,this.multipleValidation=a}async get(e){const a={address:e};return(await this.request.get("/v4/address/validate",a)).body}}class xo{request;constructor(e){this.request=e}async list(e){const a=await this.request.get("/v3/ips",e);return this.parseIpsResponse(a)}async get(e){const a=await this.request.get(`/v3/ips/${e}`);return this.parseIpsResponse(a)}parseIpsResponse(e){return e.body}}class vo{request;constructor(e){this.request=e}list(){return this.request.get("/v1/ip_pools").then((e=>this.parseIpPoolsResponse(e)))}async create(e){const a=await this.request.postWithFD("/v1/ip_pools",e);return{status:a.status,...a.body}}async update(e,a){const n=await this.request.patchWithFD(`/v1/ip_pools/${e}`,a);return{status:n.status,...n.body}}async delete(e,a){const n=await this.request.delete(`/v1/ip_pools/${e}`,a);return{status:n.status,...n.body}}parseIpPoolsResponse(e){return{status:e.status,...e.body}}}class bo extends Ki{baseRoute;request;members;constructor(e,a){super(e),this.request=e,this.baseRoute="/v3/lists",this.members=a}parseValidationResult(e,a){return{status:e,validationResult:{...a,created_at:new Date(1e3*a.created_at)}}}parseList(e){const a={};return a.items=e.body.items,a.pages=this.parsePageLinks(e,"?","address"),a.status=e.status,a}async list(e){return this.requestListWithPages(`${this.baseRoute}/pages`,e)}get(e){return this.request.get(`${this.baseRoute}/${e}`).then((e=>e.body.list))}create(e){return this.request.postWithFD(this.baseRoute,e).then((e=>e.body.list))}update(e,a){return this.request.putWithFD(`${this.baseRoute}/${e}`,a).then((e=>e.body.list))}destroy(e){return this.request.delete(`${this.baseRoute}/${e}`).then((e=>e.body))}validate(e){return this.request.post(`${this.baseRoute}/${e}/validate`,{}).then((e=>({status:e.status,...e.body})))}validationResult(e){return this.request.get(`${this.baseRoute}/${e}/validate`).then((e=>this.parseValidationResult(e.status,e.body)))}cancelValidation(e){return this.request.delete(`${this.baseRoute}/${e}/validate`).then((e=>({status:e.status,message:e.body.message})))}}class go extends Ki{baseRoute;request;constructor(e){super(e),this.request=e,this.baseRoute="/v3/lists"}checkAndUpdateData(e){const a={...e};return"object"==typeof e.vars&&(a.vars=JSON.stringify(a.vars)),"boolean"==typeof e.subscribed&&(a.subscribed=e.subscribed?"yes":"no"),a}parseList(e){const a={};return a.items=e.body.items,a.pages=this.parsePageLinks(e,"?","address"),a}async listMembers(e,a){return this.requestListWithPages(`${this.baseRoute}/${e}/members/pages`,a)}getMember(e,a){return this.request.get(`${this.baseRoute}/${e}/members/${a}`).then((e=>e.body.member))}createMember(e,a){const n=this.checkAndUpdateData(a);return this.request.postWithFD(`${this.baseRoute}/${e}/members`,n).then((e=>e.body.member))}createMembers(e,a){const n={members:Array.isArray(a.members)?JSON.stringify(a.members):a.members,upsert:a.upsert};return this.request.postWithFD(`${this.baseRoute}/${e}/members.json`,n).then((e=>e.body))}updateMember(e,a,n){const t=this.checkAndUpdateData(n);return this.request.putWithFD(`${this.baseRoute}/${e}/members/${a}`,t).then((e=>e.body.member))}destroyMember(e,a){return this.request.delete(`${this.baseRoute}/${e}/members/${a}`).then((e=>e.body))}}class yo{baseRoute;request;constructor(e){this.request=e,this.baseRoute="/v3/domains/"}_parseDomainCredentialsList(e){return{items:e.body.items,totalCount:e.body.total_count}}_parseMessageResponse(e){return{status:e.status,message:e.body.message}}_parseDeletedResponse(e){return{status:e.status,message:e.body.message,spec:e.body.spec}}list(e,a){return this.request.get(j(this.baseRoute,e,"/credentials"),a).then((e=>this._parseDomainCredentialsList(e)))}create(e,a){return this.request.postWithFD(`${this.baseRoute}${e}/credentials`,a).then((e=>this._parseMessageResponse(e)))}update(e,a,n){return this.request.putWithFD(`${this.baseRoute}${e}/credentials/${a}`,n).then((e=>this._parseMessageResponse(e)))}destroy(e,a){return this.request.delete(`${this.baseRoute}${e}/credentials/${a}`).then((e=>this._parseDeletedResponse(e)))}}class wo{createdAt;id;quantity;recordsProcessed;status;downloadUrl;responseStatusCode;summary;constructor(e,a){this.createdAt=new Date(e.created_at),this.id=e.id,this.quantity=e.quantity,this.recordsProcessed=e.records_processed,this.status=e.status,this.responseStatusCode=a,e.download_url&&(this.downloadUrl={csv:e.download_url?.csv,json:e.download_url?.json}),e.summary&&(this.summary={result:{catchAll:e.summary.result.catch_all,deliverable:e.summary.result.deliverable,doNotSend:e.summary.result.do_not_send,undeliverable:e.summary.result.undeliverable,unknown:e.summary.result.unknown},risk:{high:e.summary.risk.high,low:e.summary.risk.low,medium:e.summary.risk.medium,unknown:e.summary.risk.unknown}})}}class _o extends Ki{request;attachmentsHandler;constructor(e){super(),this.request=e,this.attachmentsHandler=new $i}handleResponse(e){return{status:e.status,...e?.body}}parseList(e){const a={};return a.jobs=e.body.jobs.map((a=>new wo(a,e.status))),a.pages=this.parsePageLinks(e,"?","pivot"),a.total=e.body.total,a.status=e.status,a}async list(e){return this.requestListWithPages("/v4/address/validate/bulk",e)}async get(e){const a=await this.request.get(`/v4/address/validate/bulk/${e}`);return new wo(a.body,a.status)}convertToExpectedShape(e){let a;return a=this.attachmentsHandler.isBuffer(e.file)?{multipleValidationFile:e.file}:"string"==typeof e.file?{multipleValidationFile:{data:e.file}}:(this.attachmentsHandler.isStream(e.file),{multipleValidationFile:e.file}),a}async create(e,a){if(!a||!a.file)throw Ni.getUserDataError('"file" property expected.','Make sure second argument has "file" property.');const n=this.convertToExpectedShape(a),t=await this.request.postWithFD(`/v4/address/validate/bulk/${e}`,n);return this.handleResponse(t)}async destroy(e){const a=await this.request.delete(`/v4/address/validate/bulk/${e}`);return this.handleResponse(a)}}class ko{name;description;createdAt;createdBy;id;version;versions;constructor(e){this.name=e.name,this.description=e.description,this.createdAt=e.createdAt?new Date(e.createdAt):"",this.createdBy=e.createdBy,this.id=e.id,e.version&&(this.version=e.version,this.version&&e.version.createdAt&&(this.version.createdAt=new Date(e.version.createdAt))),e.versions&&e.versions.length&&(this.versions=e.versions.map((e=>{const a={...e};return a.createdAt=new Date(e.createdAt),a})))}}class jo extends Ki{baseRoute;request;constructor(e){super(e),this.request=e,this.baseRoute="/v3/"}parseCreationResponse(e){return new ko(e.body.template)}parseCreationVersionResponse(e){const a={};return a.status=e.status,a.message=e.body.message,e.body&&e.body.template&&(a.template=new ko(e.body.template)),a}parseMutationResponse(e){const a={};return a.status=e.status,a.message=e.body.message,e.body&&e.body.template&&(a.templateName=e.body.template.name),a}parseNotificationResponse(e){const a={};return a.status=e.status,a.message=e.body.message,a}parseMutateTemplateVersionResponse(e){const a={};return a.status=e.status,a.message=e.body.message,e.body.template&&(a.templateName=e.body.template.name,a.templateVersion={tag:e.body.template.version.tag}),a}parseList(e){const a={};return a.items=e.body.items.map((e=>new ko(e))),a.pages=this.parsePageLinks(e,"?","p"),a.status=e.status,a}parseListTemplateVersions(e){const a={};return a.template=new ko(e.body.template),a.pages=this.parsePageLinks(e,"?","p"),a}async list(e,a){return this.requestListWithPages(j(this.baseRoute,e,"/templates"),a)}get(e,a,n){return this.request.get(j(this.baseRoute,e,"/templates/",a),n).then((e=>new ko(e.body.template)))}create(e,a){return this.request.postWithFD(j(this.baseRoute,e,"/templates"),a).then((e=>this.parseCreationResponse(e)))}update(e,a,n){return this.request.putWithFD(j(this.baseRoute,e,"/templates/",a),n).then((e=>this.parseMutationResponse(e)))}destroy(e,a){return this.request.delete(j(this.baseRoute,e,"/templates/",a)).then((e=>this.parseMutationResponse(e)))}destroyAll(e){return this.request.delete(j(this.baseRoute,e,"/templates")).then((e=>this.parseNotificationResponse(e)))}listVersions(e,a,n){return this.request.get(j(this.baseRoute,e,"/templates",a,"/versions"),n).then((e=>this.parseListTemplateVersions(e)))}getVersion(e,a,n){return this.request.get(j(this.baseRoute,e,"/templates/",a,"/versions/",n)).then((e=>new ko(e.body.template)))}createVersion(e,a,n){return this.request.postWithFD(j(this.baseRoute,e,"/templates/",a,"/versions"),n).then((e=>this.parseCreationVersionResponse(e)))}updateVersion(e,a,n,t){return this.request.putWithFD(j(this.baseRoute,e,"/templates/",a,"/versions/",n),t).then((e=>this.parseMutateTemplateVersionResponse(e)))}destroyVersion(e,a,n){return this.request.delete(j(this.baseRoute,e,"/templates/",a,"/versions/",n)).then((e=>this.parseMutateTemplateVersionResponse(e)))}}class Ro{tag;description;"first-seen";"last-seen";constructor(e){this.tag=e.tag,this.description=e.description,this["first-seen"]=new Date(e["first-seen"]),this["last-seen"]=new Date(e["last-seen"])}}class So{tag;description;start;end;resolution;stats;constructor(e){this.tag=e.body.tag,this.description=e.body.description,this.start=new Date(e.body.start),this.end=new Date(e.body.end),this.resolution=e.body.resolution,this.stats=e.body.stats.map((function(e){return{...e,time:new Date(e.time)}}))}}class Eo extends Ki{baseRoute;request;constructor(e){super(e),this.request=e,this.baseRoute="/v3/"}parseList(e){const a={};return a.items=e.body.items.map((e=>new Ro(e))),a.pages=this.parsePageLinks(e,"?","tag"),a.status=e.status,a}_parseTagStatistic(e){return new So(e)}async list(e,a){return this.requestListWithPages(j(this.baseRoute,e,"/tags"),a)}get(e,a){return this.request.get(j(this.baseRoute,e,"/tags",a)).then((e=>new Ro(e.body)))}update(e,a,n){return this.request.put(j(this.baseRoute,e,"/tags",a),n).then((e=>e.body))}destroy(e,a){return this.request.delete(`${this.baseRoute}${e}/tags/${a}`).then((e=>({message:e.body.message,status:e.status})))}statistic(e,a,n){return this.request.get(j(this.baseRoute,e,"/tags",a,"stats"),n).then((e=>this._parseTagStatistic(e)))}countries(e,a){return this.request.get(j(this.baseRoute,e,"/tags",a,"stats/aggregates/countries")).then((e=>e.body))}providers(e,a){return this.request.get(j(this.baseRoute,e,"/tags",a,"stats/aggregates/providers")).then((e=>e.body))}devices(e,a){return this.request.get(j(this.baseRoute,e,"/tags",a,"stats/aggregates/devices")).then((e=>e.body))}}class To extends Ki{request;attributes;filters;logger;constructor(e,a,n,t=console){super(e),this.request=e,this.attributes=a,this.filters=n,this.logger=t}convertDateToUTC(e,a){return this.logger.warn(`Date: "${a}" was auto-converted to UTC time zone.\nValue "${a.toISOString()}" will be used for request.\nConsider using string type for property "${e}" to avoid auto-converting`),a.toISOString()}prepareQueryData(e){const a=e,n=Object.keys(a).reduce(((n,t)=>{const s=t;if(a[s]&&"object"==typeof a[s]){const a=e[s];n[s]=this.convertDateToUTC(s,a)}return n}),{});return{...e,...n}}prepareResult(e){let a={};return a={...this.prepareSeedList(e.body),status:e.status},a}prepareSeedList(e){let a;const n={created_at:new Date(e.created_at),updated_at:new Date(e.updated_at),last_result_at:new Date(e.last_result_at)};a=e.Seeds?e.Seeds.map((e=>{let a={};const n={created_at:new Date(e.created_at),updated_at:new Date(e.updated_at),max_email_count_hit_at:new Date(e.max_email_count_hit_at),last_sent_to_at:new Date(e.last_sent_to_at),last_delivered_at:new Date(e.last_delivered_at)};return a={...e,...n},a})):null;const t={...e,Seeds:a,...n};return delete t.Id,t}parseList(e){const a={items:[]};return a.items=e.body.items?.map((e=>this.prepareSeedList(e))),a.pages=this.parsePageLinks(e,"?","address"),a.status=e.status,a}async list(e){const a=this.prepareQueryData(e),n=await this.request.get("/v4/inbox/seedlists",a);return{...this.parseList(n),status:200}}async get(e){const a=await this.request.get(`/v4/inbox/seedlists/${e}`);return{...this.prepareSeedList(a.body.seedlist),status:a.status}}async create(e){const a=await this.request.postWithFD("/v4/inbox/seedlists",e);return this.prepareResult(a)}async update(e,a){const n=await this.request.put(`/v4/inbox/seedlists/${e}`,a);return this.prepareResult(n)}async destroy(e){return this.request.delete(`/v4/inbox/seedlists/${e}`)}}class qo{request;seedsLists;results;providers;constructor(e,a,n,t){this.request=e,this.seedsLists=a,this.seedsLists=a,this.results=n,this.providers=t}async runTest(e){const a=await this.request.post("/v4/inbox/tests",e);return{...a.body,status:a.status}}}class Co extends Ki{request;attributes;filters;sharing;logger;constructor(e,a,n,t,s=console){super(e),this.request=e,this.attributes=a,this.filters=n,this.sharing=t,this.logger=s}convertDateToUTC(e,a){return this.logger.warn(`Date: "${a}" was auto-converted to UTC time zone.\nValue "${a.toISOString()}" will be used for request.\nConsider using string type for property "${e}" to avoid auto-converting`),a.toISOString()}prepareQueryData(e){const a=e,n=Object.keys(a).reduce(((n,t)=>{const s=t;if(a[s]&&"object"==typeof a[s]){const a=e[s];n[s]=this.convertDateToUTC(s,a)}return n}),{});return{...e,...n}}prepareInboxPlacementsResult(e){let a={};const n={created_at:new Date(e.created_at),updated_at:new Date(e.updated_at),sharing_expires_at:new Date(e.sharing_expires_at)};e.Box&&(a={...e.Box,created_at:new Date(e.Box.created_at),updated_at:new Date(e.Box.updated_at),last_result_at:new Date(e.Box.last_result_at)},delete a.ID);const t={...e,Box:a,...n,id:e.Id};return delete t.ID,t}parseList(e){const a={};return a.items=e.body.items.map((e=>this.prepareInboxPlacementsResult(e))),a.pages=this.parsePageLinks(e,"?","address"),a.status=e.status,a}async list(e){const a=this.prepareQueryData(e),n=await this.request.get("/v4/inbox/results",a);return this.parseList(n)}async get(e){const a=await this.request.get(`/v4/inbox/results/${e}`),n=this.prepareInboxPlacementsResult(a.body.result);return{status:a.status,inboxPlacementResult:n}}async destroy(e){const a=await this.request.delete(`/v4/inbox/results/${e}`);return{status:a.status,...a.body}}async getResultByShareId(e){const a=await this.request.get(`/v4/inbox/sharing/public/${e}`),n=this.prepareInboxPlacementsResult(a.body.result);return{status:a.status,inboxPlacementResult:n}}}class Ao{request;path;constructor(e,a){this.path=a,this.request=e}async list(){const e=await this.request.get(this.path);return{items:e.body.items,status:e.status}}async get(e){const a=await this.request.get(`${this.path}/${e}`);return{...a.body,status:a.status}}}class Oo{request;path;constructor(e,a){this.request=e,this.path=a}async list(){const e=await this.request.get(this.path);return{status:e.status,supported_filters:e.body.supported_filters}}}class Fo{request;constructor(e){this.request=e}prepareInboxPlacementsResultSharing(e){const a={expires_at:new Date(e.expires_at)};return{...e,...a}}async get(e){const a=await this.request.get(`/v4/inbox/sharing/${e}`),n=this.prepareInboxPlacementsResultSharing(a.body.sharing);return{status:a.status,...n}}async update(e,a){const n=await this.request.put(`/v4/inbox/sharing/${e}`,{},{query:`enabled=${a.enabled}`});return{...this.prepareInboxPlacementsResultSharing(n.body.sharing),status:n.status}}}class Do{request;path;constructor(e){this.path="/v4/inbox/providers",this.request=e}parseList(e){const a={};return a.items=e.body.items.map((e=>{const a={created_at:new Date(e.created_at),updated_at:new Date(e.updated_at)};return{...e,...a}})),a.status=e.status,a}async list(){const e=await this.request.get(this.path);return this.parseList(e)}}class Po{request;logger;constructor(e,a=console){this.request=e,this.logger=a}convertDateToUTC(e,a){return this.logger.warn(`Date:"${a}" was auto-converted to UTC time zone.\nValue "${a.toUTCString()}" will be used for request.\nConsider using string type for property "${e}" to avoid auto-converting`),a.toUTCString()}prepareQuery(e){let a,n;if(e){const t=e?.start,s=e?.end;a=t instanceof Date?this.convertDateToUTC("start",t):t??"",n=s&&s instanceof Date?this.convertDateToUTC("end",s):s??""}return{...e,start:a,end:n}}handleResponse(e){const a=e.body,n=Date.parse(a.start)?new Date(a.start):null,t=Date.parse(a.end)?new Date(a.end):null;return{...a,status:e.status,start:n,end:t}}async getAccount(e){const a=this.prepareQuery(e),n=await this.request.post("/v1/analytics/metrics",a);return this.handleResponse(n)}async getAccountUsage(e){const a=this.prepareQuery(e),n=await this.request.post("/v1/analytics/usage/metrics",a);return this.handleResponse(n)}}class Bo{request;constructor(e){this.request=e}_parseTrackingSettings(e){return e.body.tracking}_parseTrackingUpdate(e){return e.body}_isOpenTrackingInfoWitPlace(e){return"object"==typeof e&&"place_at_the_top"in e}async get(e){const a=await this.request.get(`/v2/x509/${e}/status`);return{...a.body,responseStatusCode:a.status}}async generate(e){const a=await this.request.post(`/v2/x509/${e}`);return{...a.body,status:a.status}}async regenerate(e){const a=await this.request.put(`/v2/x509/${e}`);return{...a.body,status:a.status}}async getTracking(e){const a=await this.request.get(j("/v3/domains",e,"tracking"));return this._parseTrackingSettings(a)}async updateTracking(e,a,n){const t={...n};"boolean"==typeof n?.active&&(t.active=n?.active?"yes":"no"),this._isOpenTrackingInfoWitPlace(n)&&"boolean"==typeof n?.place_at_the_top&&(t.place_at_the_top=n?.place_at_the_top?"yes":"no");const s=await this.request.putWithFD(j("/v3/domains",e,"tracking",a),t);return this._parseTrackingUpdate(s)}}class Lo{request;domains;webhooks;events;stats;metrics;suppressions;messages;routes;validate;ips;ip_pools;lists;subaccounts;inboxPlacements;constructor(e,a){const n={...e};if(n.url||(n.url="https://api.mailgun.net"),!n.username)throw new Error('Parameter "username" is required');if(!n.key)throw new Error('Parameter "key" is required');this.request=new Vi(n,a);const t=new go(this.request),s=new yo(this.request),i=new jo(this.request),o=new Eo(this.request),r=new Bo(this.request),c=new _o(this.request),p=new Fo(this.request),l=new Ao(this.request,"/v4/inbox/seedlists/a"),u=new Ao(this.request,"/v4/inbox/results/a"),d=new Oo(this.request,"/v4/inbox/seedlists/_filters"),m=new Oo(this.request,"/v4/inbox/results/_filters"),h=new To(this.request,l,d),f=new Co(this.request,u,m,p),x=new Do(this.request);this.domains=new Ji(this.request,s,i,o,r),this.webhooks=new uo(this.request),this.events=new Qi(this.request),this.stats=new Xi(this.request),this.metrics=new Po(this.request),this.suppressions=new po(this.request),this.messages=new mo(this.request),this.routes=new ho(this.request),this.ips=new xo(this.request),this.ip_pools=new vo(this.request),this.lists=new bo(this.request,t),this.validate=new fo(this.request,c),this.subaccounts=new Hi(this.request),this.inboxPlacements=new qo(this.request,h,f,x)}setSubaccount(e){this.request?.setSubaccountHeader(e)}resetSubaccount(){this.request?.resetSubaccountHeader()}}class Uo{static get default(){return this}formData;constructor(e){this.formData=e}client(e){return new Lo(e,this.formData)}}export{Uo as default};
