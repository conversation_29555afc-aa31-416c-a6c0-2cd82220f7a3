export declare enum Resolution {
    HOUR = "hour",
    DAY = "day",
    MONTH = "month"
}
export declare enum SuppressionModels {
    BOUNCES = "bounces",
    COMPLAINTS = "complaints",
    UNSUBSCRIBES = "unsubscribes",
    WHITELISTS = "whitelists"
}
export declare enum WebhooksIds {
    CLICKED = "clicked",
    COMPLAINED = "complained",
    DELIVERED = "delivered",
    OPENED = "opened",
    PERMANENT_FAIL = "permanent_fail",
    TEMPORARY_FAIL = "temporary_fail",
    UNSUBSCRIBED = "unsubscribe"
}
export declare enum YesNo {
    YES = "yes",
    NO = "no"
}
