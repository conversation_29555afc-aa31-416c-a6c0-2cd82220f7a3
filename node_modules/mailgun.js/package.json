{"name": "mailgun.js", "version": "12.0.1", "author": "Mailgun", "license": "MIT", "type": "module", "keywords": ["mailgun", "email"], "private": false, "repository": {"type": "git", "url": "git://github.com/mailgun/mailgun.js.git"}, "bugs": {"url": "https://github.com/mailgun/mailgun.js/issues"}, "homepage": "https://github.com/mailgun/mailgun.js#readme", "exports": {".": {"node": {"import": "./ESM/mailgun.node.js", "require": "./CJS/mailgun.node.cjs", "default": "./CJS/mailgun.node.js"}, "browser": {"import": "./ESM/mailgun.browser.js", "require": "./AMD/mailgun.amd.js", "default": "./AMD/mailgun.amd.js"}, "default": {"types": "./Types/index.d.ts"}}, "./definitions": {"node": {"import": "./ESM/definitions.node.js", "require": "./CJS/definitions.cjs", "default": "./CJS/definitions.js"}, "browser": {"import": "./ESM/definitions.browser.js", "require": "./AMD/definitions.js", "default": "./AMD/definitions.js"}, "default": {"types": "./Types/definitions.d.ts"}}}, "typesVersions": {"*": {"*": ["Types/*"]}}, "dependencies": {"axios": "^1.7.4", "base-64": "^1.0.0", "url-join": "^4.0.1"}, "engines": {"node": ">=18.0.0"}, "contributors": [{"name": "<PERSON>", "url": "https://github.com/bradgignac"}, {"name": "<PERSON>", "url": "https://github.com/eddywashere"}]}