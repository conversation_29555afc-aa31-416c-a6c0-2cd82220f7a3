<template>
  <div v-if="walkthroughStore.isWalkthroughActive" class="dashboard-walkthrough">
    <!-- Overlay that dims the background -->
    <div class="walkthrough-overlay" @click="skipWalkthrough"></div>
    
    <!-- Spotlight effect that highlights the current target element -->
    <div v-if="targetElement" class="spotlight" :style="spotlightStyle"></div>
    
    <!-- Walkthrough step component -->
    <WalkthroughStep
      v-if="walkthroughStore.currentStep && targetElement"
      :title="walkthroughStore.currentStep.title"
      :content="walkthroughStore.currentStep.content"
      :position="walkthroughStore.currentStep.position"
      :target-rect="targetRect"
      :current-step="walkthroughStore.currentStepIndex"
      :total-steps="walkthroughStore.steps.length"
      @next="nextStep"
      @previous="previousStep"
      @skip="skipWalkthrough"
      @complete="completeWalkthrough"
    />
    
    <!-- Restart button (only visible when walkthrough is not active) -->
    <button v-if="!walkthroughStore.isWalkthroughActive && walkthroughStore.hasSeenWalkthrough" 
            class="btn btn-sm btn-outline-primary restart-walkthrough-btn"
            @click="startWalkthrough">
      <i class="bi bi-info-circle me-1"></i> Restart Walkthrough
    </button>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted, nextTick } from 'vue';
import { useWalkthroughStore } from '@/stores/walkthrough';
import WalkthroughStep from './WalkthroughStep.vue';

const walkthroughStore = useWalkthroughStore();
const targetElement = ref(null);
const targetRect = ref(null);

// Computed property for spotlight style
const spotlightStyle = computed(() => {
  if (!targetRect.value) return {};
  
  const { left, top, width, height } = targetRect.value;
  return {
    left: `${left}px`,
    top: `${top}px`,
    width: `${width}px`,
    height: `${height}px`
  };
});

// Watch for changes in the current step
watch(() => walkthroughStore.currentStepIndex, async () => {
  await nextTick();
  updateTargetElement();
}, { immediate: true });

// Watch for walkthrough activation
watch(() => walkthroughStore.isWalkthroughActive, async (isActive) => {
  if (isActive) {
    await nextTick();
    updateTargetElement();
  }
});

// Methods
function updateTargetElement() {
  if (!walkthroughStore.currentStep) return;
  
  const targetSelector = walkthroughStore.currentStep.target;
  let element;
  
  // Handle special case for components that might not have a direct CSS selector
  if (targetSelector === 'ProfileTile') {
    element = document.querySelector('.profile-tile');
  } else {
    element = document.querySelector(targetSelector);
  }
  
  if (element) {
    targetElement.value = element;
    updateTargetRect();
  } else {
    console.warn(`Target element not found: ${targetSelector}`);
    targetElement.value = null;
    targetRect.value = null;
  }
}

function updateTargetRect() {
  if (!targetElement.value) return;
  
  const rect = targetElement.value.getBoundingClientRect();
  targetRect.value = {
    left: rect.left,
    top: rect.top,
    width: rect.width,
    height: rect.height
  };
}

// Event handlers
function startWalkthrough() {
  walkthroughStore.startWalkthrough();
}

function nextStep() {
  walkthroughStore.nextStep();
}

function previousStep() {
  walkthroughStore.previousStep();
}

function skipWalkthrough() {
  walkthroughStore.skipWalkthrough();
}

function completeWalkthrough() {
  walkthroughStore.completeWalkthrough();
}

// Check if user has seen the walkthrough before
onMounted(() => {
  // Start walkthrough automatically if user hasn't seen it before
  if (!walkthroughStore.hasSeenWalkthrough) {
    // Delay the start to ensure all dashboard elements are rendered
    setTimeout(() => {
      walkthroughStore.startWalkthrough();
    }, 1000);
  }
  
  // Add window resize event listener to update target rect
  window.addEventListener('resize', updateTargetRect);
  
  // Clean up event listener on component unmount
  onUnmounted(() => {
    window.removeEventListener('resize', updateTargetRect);
  });
});
</script>

<style scoped lang="scss">
.dashboard-walkthrough {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1050;
  pointer-events: none;
  
  .walkthrough-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 1051;
    pointer-events: auto;
  }
  
  .spotlight {
    position: absolute;
    box-shadow: 0 0 0 9999px rgba(0, 0, 0, 0.7);
    border-radius: 4px;
    z-index: 1052;
    pointer-events: none;
    transition: all 0.3s ease;
    
    &::after {
      content: '';
      position: absolute;
      top: -4px;
      left: -4px;
      right: -4px;
      bottom: -4px;
      border: 2px solid var(--bs-primary);
      border-radius: 6px;
      animation: pulse 2s infinite;
    }
  }
  
  .restart-walkthrough-btn {
    position: fixed;
    bottom: 20px;
    right: 20px;
    z-index: 1055;
    pointer-events: auto;
    opacity: 0.8;
    transition: opacity 0.3s ease;
    
    &:hover {
      opacity: 1;
    }
  }
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(var(--bs-primary-rgb), 0.4);
  }
  70% {
    box-shadow: 0 0 0 6px rgba(var(--bs-primary-rgb), 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(var(--bs-primary-rgb), 0);
  }
}
</style>
