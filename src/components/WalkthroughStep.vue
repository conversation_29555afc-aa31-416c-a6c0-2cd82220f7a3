<template>
  <div class="walkthrough-step" :class="[`position-${position}`, { 'with-arrow': showArrow }]" :style="stepStyle">
    <div class="step-header">
      <h5 class="step-title">{{ title }}</h5>
      <button class="close-btn" @click="$emit('skip')">
        <i class="bi bi-x-lg"></i>
      </button>
    </div>
    <div class="step-content">
      <p>{{ content }}</p>
    </div>
    <div class="step-footer">
      <div class="step-progress">
        <span>{{ currentStep + 1 }} of {{ totalSteps }}</span>
      </div>
      <div class="step-actions">
        <button v-if="currentStep > 0" class="btn btn-sm btn-outline-primary" @click="$emit('previous')">
          <i class="bi bi-arrow-left me-1"></i> Previous
        </button>
        <button v-if="currentStep < totalSteps - 1" class="btn btn-sm btn-primary" @click="$emit('next')">
          Next <i class="bi bi-arrow-right ms-1"></i>
        </button>
        <button v-else class="btn btn-sm btn-success" @click="$emit('complete')">
          Finish <i class="bi bi-check-lg ms-1"></i>
        </button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue';

const props = defineProps({
  title: {
    type: String,
    required: true
  },
  content: {
    type: String,
    required: true
  },
  position: {
    type: String,
    default: 'bottom',
    validator: (value) => ['top', 'bottom', 'left', 'right'].includes(value)
  },
  targetRect: {
    type: Object,
    default: () => ({})
  },
  currentStep: {
    type: Number,
    required: true
  },
  totalSteps: {
    type: Number,
    required: true
  },
  showArrow: {
    type: Boolean,
    default: true
  }
});

const emit = defineEmits(['next', 'previous', 'skip', 'complete']);

// Calculate position based on target element
const stepStyle = computed(() => {
  if (!props.targetRect || !props.targetRect.width) {
    return {};
  }

  const { left, top, width, height } = props.targetRect;
  const margin = 20; // Space between target and tooltip

  switch (props.position) {
    case 'top':
      return {
        left: `${left + width / 2}px`,
        top: `${top - margin}px`,
        transform: 'translate(-50%, -100%)'
      };
    case 'bottom':
      return {
        left: `${left + width / 2}px`,
        top: `${top + height + margin}px`,
        transform: 'translateX(-50%)'
      };
    case 'left':
      return {
        left: `${left - margin}px`,
        top: `${top + height / 2}px`,
        transform: 'translate(-100%, -50%)'
      };
    case 'right':
      return {
        left: `${left + width + margin}px`,
        top: `${top + height / 2}px`,
        transform: 'translateY(-50%)'
      };
    default:
      return {};
  }
});
</script>

<style scoped lang="scss">
.walkthrough-step {
  position: fixed;
  z-index: 1060;
  background-color: var(--bs-body-bg);
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  width: 320px;
  padding: 1rem;
  border: 1px solid rgba(var(--bs-primary-rgb), 0.2);
  
  &.with-arrow {
    &::before {
      content: '';
      position: absolute;
      width: 12px;
      height: 12px;
      background-color: var(--bs-body-bg);
      border: 1px solid rgba(var(--bs-primary-rgb), 0.2);
      transform: rotate(45deg);
    }
  }
  
  &.position-top.with-arrow::before {
    bottom: -6px;
    left: 50%;
    transform: translateX(-50%) rotate(45deg);
    border-top: none;
    border-left: none;
  }
  
  &.position-bottom.with-arrow::before {
    top: -6px;
    left: 50%;
    transform: translateX(-50%) rotate(45deg);
    border-bottom: none;
    border-right: none;
  }
  
  &.position-left.with-arrow::before {
    right: -6px;
    top: 50%;
    transform: translateY(-50%) rotate(45deg);
    border-left: none;
    border-bottom: none;
  }
  
  &.position-right.with-arrow::before {
    left: -6px;
    top: 50%;
    transform: translateY(-50%) rotate(45deg);
    border-right: none;
    border-top: none;
  }

  .step-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.75rem;
    
    .step-title {
      margin: 0;
      font-weight: 600;
      color: var(--bs-primary);
    }
    
    .close-btn {
      background: none;
      border: none;
      color: var(--bs-secondary);
      cursor: pointer;
      padding: 0.25rem;
      border-radius: 4px;
      display: flex;
      align-items: center;
      justify-content: center;
      
      &:hover {
        color: var(--bs-danger);
        background-color: rgba(var(--bs-danger-rgb), 0.1);
      }
    }
  }
  
  .step-content {
    margin-bottom: 1rem;
    
    p {
      margin: 0;
      font-size: 0.95rem;
      line-height: 1.5;
      color: var(--bs-body-color);
    }
  }
  
  .step-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    
    .step-progress {
      font-size: 0.85rem;
      color: var(--bs-secondary);
    }
    
    .step-actions {
      display: flex;
      gap: 0.5rem;
    }
  }
}
</style>
